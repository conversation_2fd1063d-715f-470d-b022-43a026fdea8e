package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.RuleHeaderDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.enums.CodeEnum;
import com.trinasolar.scp.bbom.domain.enums.TimeSlotEnum;
import com.trinasolar.scp.bbom.domain.excel.ExcelHead;
import com.trinasolar.scp.bbom.domain.excel.ExcelMain;
import com.trinasolar.scp.bbom.domain.query.BatteryWorkShopQuery;
import com.trinasolar.scp.bbom.domain.query.RuleHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.RuleHeaderSaveDTO;
import com.trinasolar.scp.bbom.service.repository.RuleHeaderRepository;
import com.trinasolar.scp.bbom.service.service.RuleHeaderService;
import com.trinasolar.scp.bbom.service.service.RuleLineService;
import com.trinasolar.scp.bbom.service.util.AttrUtil;
import com.trinasolar.scp.bbom.service.util.CodeUtil;
import com.trinasolar.scp.bbom.service.util.ExcelTreandsUtil;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * BOM规则头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 15:49:10
 */
@Slf4j
@Service("ruleHeaderService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "scpRedisCacheManager")
public class RuleHeaderServiceImpl implements RuleHeaderService {
    private static final QRuleHeader qRuleHeader = QRuleHeader.ruleHeader;

    private static final QRuleDpValue qRuleDpValue = QRuleDpValue.ruleDpValue;

    private static final QRuleLine qRuleLine = QRuleLine.ruleLine;

    private static final QRuleDpDetail qRuleDpDetail = QRuleDpDetail.ruleDpDetail;

    private static final QItems qItems = QItems.items;

    private final RuleHeaderRepository ruleHeaderRepository;

    private final RuleLineService ruleLineService;

    private final LovUtil lovUtil;

    private final AttrUtil attrUtil;

    private final JPAQueryFactory jpaQueryFactory;

    private final RuleHeaderDEConvert ruleHeaderDEConvert;

    private static void categoryExport(HttpServletResponse response, ExcelMain excelMain, String exportName) throws IOException {
        ExcelTreandsUtil.setExportResponseHeader(response, exportName);
        ExcelTreandsUtil.export(response, excelMain);
    }

    @Override
    public Page<RuleHeader> queryByPage(RuleHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        // 自定义查询条件
        if (query != null) {
            //料号
            if (StringUtils.isNotEmpty(query.getItemCode())) {
                booleanBuilder.and(
                        jpaQueryFactory.select(qRuleLine).from(qRuleLine)
                                .where(qRuleLine.ruleHeaderId.eq(qRuleHeader.ruleHeaderId)
                                        .and(jpaQueryFactory.select(qRuleDpDetail).from(qRuleDpDetail)
                                                .where(qRuleLine.ruleLineId.eq(qRuleDpDetail.ruleLineId)
                                                        .and(
                                                                jpaQueryFactory.select(qRuleDpValue).from(qRuleDpValue)
                                                                        .where(qRuleDpDetail.ruleDetailId.eq(qRuleDpValue.ruleDetailId)
                                                                                .and(qRuleDpValue.attrValue.eq(query.getItemCode())
                                                                                )).exists()
                                                        )).exists())).exists());
            }
            if (StringUtils.isNotEmpty(query.getRuleNumber())) {
                booleanBuilder.and(qRuleHeader.ruleNumber.like("%" + query.getRuleNumber() + "%"));
            }
            if (query.getRuleCategoryId() != null) {
                booleanBuilder.and(qRuleHeader.ruleCategoryId.eq(query.getRuleCategoryId()));
            }
            if (query.getControlObjectId() != null) {
                booleanBuilder.and(qRuleHeader.controlObjectId.eq(query.getControlObjectId()));
            }
            if (query.getControlSubjectId() != null) {
                booleanBuilder.and(qRuleHeader.controlSubjectId.eq(query.getControlSubjectId()));
            }
            if (StringUtils.isNotBlank(query.getRuleName())) {
                booleanBuilder.and(qRuleHeader.ruleName.like("%" + query.getRuleName() + "%"));
            }

            if (StringUtils.isNotBlank(query.getTimeSlot())) {
                TimeSlotEnum timeSlotEnum = TimeSlotEnum.getByValue(query.getTimeSlot());
                if (timeSlotEnum != null) {
                    if (timeSlotEnum == TimeSlotEnum.PRESENT) {
                        LocalDate now = LocalDate.now();
                        booleanBuilder.and(qRuleHeader.effectiveStartDate.before(now).and(
                                qRuleHeader.effectiveEndDate.isNull().or(
                                        qRuleHeader.effectiveEndDate.after(now)
                                )
                        ));
                    } else if (timeSlotEnum == TimeSlotEnum.PRESENT_AND_FUTURE) {
                        LocalDate now = LocalDate.now();
                        booleanBuilder.and(
                                qRuleHeader.effectiveEndDate.isNull().or(
                                        qRuleHeader.effectiveEndDate.after(now)
                                )
                        );
                    }
                }
            }
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, Optional.ofNullable(query).map(PageDTO::getPageSize).orElse(10), sort);

        Page<RuleHeader> ruleHeaders = ruleHeaderRepository.findAll(booleanBuilder, pageable);
        // 设置lov和attr的信息
        ruleHeaders.get().forEach(this::setLovAndAttrInfo);
        return ruleHeaders;
    }

    private void setLovAndAttrInfo(RuleHeader item) {
        lovUtil.setLovLineName(item, item.getRuleCategoryId(), "ruleCategoryName");

        LovLineDTO attr = LovUtils.get("ATTR", item.getControlObjectId());
        if (attr != null) {
            item.setControlObjectName(attr.getLovName());
        } else {
            LovLineDTO lov = LovUtils.get("", item.getControlObjectId());
            if (lov != null) {
                item.setControlObjectName(lov.getLovName());
            }
        }
    }

    @Override
    public RuleHeaderDTO queryById(Long id) {
        RuleHeader queryObj = ruleHeaderRepository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }
        setLovAndAttrInfo(queryObj);

        RuleHeaderDTO result = new RuleHeaderDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public RuleHeaderDTO save(RuleHeaderSaveDTO saveDTO) {
        RuleHeader newObj;
        if (saveDTO.getRuleHeaderId() != null) {
            newObj = ruleHeaderRepository.findById(saveDTO.getRuleHeaderId()).orElse(new RuleHeader());
        } else {
            newObj = new RuleHeader();
            newObj.setRuleNumber(CodeUtil.getCode(CodeEnum.RULE_HEADER));
        }

        BeanUtils.copyProperties(saveDTO, newObj, "ruleNumber", "ruleHeaderId");

        ruleHeaderRepository.save(newObj);

        return this.queryById(newObj.getRuleHeaderId());
    }

    @Override
    public void deleteById(Long id) {
        List<RuleLineDTO> ruleLineDTOS = ruleLineService.listLinesByHeaderId(id);
        if (CollectionUtils.isNotEmpty(ruleLineDTOS)) {
            throw new BizException("bbom_hasRuleLIne_canNotDel");
        }

        ruleHeaderRepository.deleteById(id);
    }

    @Override
    public void export(HttpServletResponse response, RuleHeaderQuery query) throws IOException {
        List<RuleHeader> content = queryByPage(query).getContent();
        // 先将RuleHeader转化为DTO,然后设置RuleLines
        AtomicInteger atomicInteger = new AtomicInteger(1);
        List<RuleHeaderDTO> headers = content.stream().map(h -> {
            setLovAndAttrInfo(h);

            RuleHeaderDTO dto = new RuleHeaderDTO();
            BeanUtils.copyProperties(h, dto);
            dto.setNo(atomicInteger.getAndIncrement());
            List<RuleLineDTO> ruleLineDTOS = ruleLineService.listLinesByHeaderId(dto.getRuleHeaderId());
            dto.setRuleLineDTOList(ruleLineDTOS);
            return dto;
        }).collect(Collectors.toList());

        int dpColMax = 0;
        int mtlCatColMax = 0;
        int mtlCatDetailsColMax = 0;
        int mtlDetailsColMax = 0;
        boolean hasRuleLineAttrValue = false;
        for (RuleHeaderDTO header : headers) {
            for (RuleLineDTO ruleLineDTO : header.getRuleLineDTOList()) {
                if (dpColMax < ruleLineDTO.getDetails().size()) {
                    dpColMax = ruleLineDTO.getDetails().size();
                }
                if (mtlCatColMax < ruleLineDTO.getControlObjectHeaders().size()) {
                    mtlCatColMax = ruleLineDTO.getControlObjectHeaders().size();
                }

                for (RuleControlObjectHeaderDTO controlObjectHeaderDTO : ruleLineDTO.getControlObjectHeaders()) {
                    if (mtlCatDetailsColMax < controlObjectHeaderDTO.getDetails().size()) {
                        mtlCatDetailsColMax = controlObjectHeaderDTO.getDetails().size();
                    }
                }
//                if (mtlDetailsColMax < ruleLineDTO.getMtlDetails().size()) {
//                    mtlDetailsColMax = ruleLineDTO.getMtlDetails().size();
//                }
                if (StringUtils.isNotBlank(ruleLineDTO.getAttrValue())) {
                    hasRuleLineAttrValue = true;
                }
            }
        }

        log.info("dpColMax : {}, mtlCatColMax: {}, mtlCatDetailsColMax: {}", dpColMax, mtlCatColMax, mtlCatDetailsColMax);

        //需要导出的数据
        String fileName = "BOM规则导出" + LocalDateTimeUtil.formatNormal(LocalDateTime.now());
        ExcelUtils.exportEx(response, fileName, "sheet", mtlHead(dpColMax, mtlCatColMax, mtlCatDetailsColMax, mtlDetailsColMax, hasRuleLineAttrValue), mtlDataList(headers, dpColMax, mtlCatColMax, mtlCatDetailsColMax, mtlDetailsColMax, query, hasRuleLineAttrValue));
    }

    private List<List<String>> mtlHead(int dpColMax, int mtlCatColMax, int mtlCatDetailsColMax, int mtlDetailsColMax, boolean hasRuleLineAttrValue) {
        List<List<String>> list = ListUtils.newArrayList();

        // 头
        list.add(ListUtils.newArrayList("No."));
        list.add(ListUtils.newArrayList("规则分类"));
        list.add(ListUtils.newArrayList("规则名称"));
        list.add(ListUtils.newArrayList("规则编码"));
        list.add(ListUtils.newArrayList("管控主体"));
        list.add(ListUtils.newArrayList("管控对象"));
        list.add(ListUtils.newArrayList("管控目的"));
        list.add(ListUtils.newArrayList("生效时间"));
        list.add(ListUtils.newArrayList("失效时间"));

        list.add(ListUtils.newArrayList("行 No."));
        // 循环
        for (int i = 0; i < dpColMax; i++) {
            list.add(ListUtils.newArrayList("因子" + (i + 1)));
            list.add(ListUtils.newArrayList("条件项"));
            list.add(ListUtils.newArrayList("值\\范围"));
        }
        for (int i = 0; i < mtlDetailsColMax; i++) {
            list.add(ListUtils.newArrayList("配料因子" + (i + 1)));
            list.add(ListUtils.newArrayList("属性"));
            list.add(ListUtils.newArrayList("条件项"));
            list.add(ListUtils.newArrayList("值\\范围"));
        }
        list.add(ListUtils.newArrayList("管控对象"));

        if (hasRuleLineAttrValue) {
            list.add(ListUtils.newArrayList("值"));
        }
        // 循环
        for (int i = 0; i < mtlCatColMax; i++) {
            list.add(ListUtils.newArrayList("管控对象 " + (i + 1)));
            // 循环
            for (int j = 0; j < mtlCatDetailsColMax; j++) {
                list.add(ListUtils.newArrayList("属性" + (j + 1)));
            }
            list.add(ListUtils.newArrayList("BOM提示"));
            list.add(ListUtils.newArrayList("备注"));
        }
        list.add(ListUtils.newArrayList("搭配对象"));
        list.add(ListUtils.newArrayList("排斥对象"));

        list.add(ListUtils.newArrayList("有效日期_起"));
        list.add(ListUtils.newArrayList("有效日期_止"));

        log.info("mtlHead : {}", list);
        return list;
    }

    private List<List<Object>> mtlDataList(List<RuleHeaderDTO> content, int dpColMax, int mtlCatColMax, int mtlCatDetailsColMax, int mtlDetailsColMax, RuleHeaderQuery query, boolean hasRuleLineAttrValue) {
        List<List<Object>> list = ListUtils.newArrayList();
        for (RuleHeaderDTO ruleHeaderDTO : content) {
            List<RuleLineDTO> ruleLineDTOList = ruleHeaderDTO.getRuleLineDTOList();
            if (ruleLineDTOList == null || ruleLineDTOList.isEmpty()) {
                List<Object> data = ListUtils.newArrayList();
                data.add(ruleHeaderDTO.getNo());
                data.add(ruleHeaderDTO.getRuleCategoryName());
                data.add(ruleHeaderDTO.getRuleName());
                data.add(ruleHeaderDTO.getRuleNumber());
                data.add(query.getControlSubjectName());
                data.add(ruleHeaderDTO.getControlObjectName());
                data.add(ruleHeaderDTO.getControlPurposeName());
                data.add(LocalDateTimeUtil.formatNormal(ruleHeaderDTO.getEffectiveStartDate()));
                data.add(LocalDateTimeUtil.formatNormal(ruleHeaderDTO.getEffectiveEndDate()));
                list.add(data);
            } else {
                for (RuleLineDTO ruleLineDTO : ruleLineDTOList) {
                    List<Object> data = ListUtils.newArrayList();
                    data.add(ruleHeaderDTO.getNo());
                    data.add(ruleHeaderDTO.getRuleCategoryName());
                    data.add(ruleHeaderDTO.getRuleName());
                    data.add(ruleHeaderDTO.getRuleNumber());
                    data.add(query.getControlSubjectName());
                    data.add(ruleHeaderDTO.getControlObjectName());
                    data.add(ruleHeaderDTO.getControlPurposeName());
                    data.add(LocalDateTimeUtil.formatNormal(ruleHeaderDTO.getEffectiveStartDate()));
                    data.add(LocalDateTimeUtil.formatNormal(ruleHeaderDTO.getEffectiveEndDate()));

                    data.add(ruleLineDTO.getNo()); // 行号
                    List<RuleDpDetailDTO> dpDetails = ruleLineDTO.getDetails();
                    for (int i = 0; i < dpColMax; i++) {
                        if (i >= dpDetails.size()) {
                            data.add("");
                            data.add("");
                            data.add("");
                        } else {
                            RuleDpDetailDTO applyMtlDpDetailDTO = dpDetails.get(i);
                            data.add(applyMtlDpDetailDTO.getDpFiledName());
                            data.add(applyMtlDpDetailDTO.getAttrOperatorName());
                            data.add(applyMtlDpDetailDTO.getValues().stream().map(item -> {
                                        if (StringUtils.isNotBlank(item.getAttrValueName())) {
                                            return item.getAttrValueName();
                                        } else {
                                            return item.getAttrValue();
                                        }
                                    })
                                    .collect(Collectors.joining(",")));
                        }
                    }
//                    List<RuleMtlDetailDTO> mtlDetails = ruleLineDTO.getMtlDetails();
//                    for (int i = 0; i < mtlDetailsColMax; i++) {
//                        if (i >= mtlDetails.size()) {
//                            data.add("");
//                            data.add("");
//                            data.add("");
//                            data.add("");
//                        } else {
//                            RuleMtlDetailDTO mtlDetailDTO = mtlDetails.get(i);
//                            data.add(mtlDetailDTO.getMaterialsObject());
//                            data.add(mtlDetailDTO.getMaterialsAttrFiled());
//                            data.add(mtlDetailDTO.getAttrOperatorName());
//                            data.add(mtlDetailDTO.getValues().stream().map(RuleMtlValueDTO::getAttrValueName)
//                                    .collect(Collectors.joining(",")));
//                        }
//                    }
                    data.add(ruleLineDTO.getControlObject());
                    if (hasRuleLineAttrValue) {
                        LovLineDTO lovLineDTO = LovUtils.get(ruleLineDTO.getAttrValueId());
                        if (lovLineDTO == null) {
                            data.add(ruleLineDTO.getAttrValue());
                        } else {
                            data.add(lovLineDTO.getLovName());
                        }
                    }
                    for (int i = 0; i < mtlCatColMax; i++) {
                        List<RuleControlObjectHeaderDTO> mtlCatHeaders = ruleLineDTO.getControlObjectHeaders();
                        if (i >= mtlCatHeaders.size()) {
                            data.add("");
                            for (int j = 0; j < mtlCatDetailsColMax; j++) {
                                data.add("");
                            }
                            data.add("");
                            data.add("");
                        } else {
                            RuleControlObjectHeaderDTO applyMtlCatHeaderDTO = mtlCatHeaders.get(i);
                            data.add(applyMtlCatHeaderDTO.getControlObject());
                            for (int j = 0; j < mtlCatDetailsColMax; j++) {
                                List<RuleControlObjectDetailDTO> mtlCatDetails = applyMtlCatHeaderDTO.getDetails();
                                if (j >= mtlCatDetails.size()) {
                                    data.add("");
                                } else {
                                    RuleControlObjectDetailDTO applyMtlCatDetailDTO = mtlCatDetails.get(j);
                                    data.add(applyMtlCatDetailDTO.getMaterialsAttrFiled() + ":" + applyMtlCatDetailDTO.getAttrOperatorName()
                                            + ":" + applyMtlCatDetailDTO.getValues()
                                            .stream().filter(item -> StringUtils.isNotBlank(item.getAttrValue())).map(item -> {
                                                String attrValue = item.getAttrValue();
                                                try {
                                                    LovLineDTO lineDTO = LovUtils.get(Long.parseLong(item.getAttrValue()));
                                                    attrValue = lineDTO.getLovName();
                                                } catch (Exception e) {
                                                    // 跳过
                                                }
                                                StringBuilder result = new StringBuilder(attrValue);

                                                if (StringUtils.isNotBlank(item.getAttrValueTo())) {
                                                    result.append(" ~ ").append(item.getAttrValueTo());
                                                }
                                                return result.toString();
                                            }).collect(Collectors.joining(",")));

                                }
                            }
                            data.add(Objects.equals(applyMtlCatHeaderDTO.getBomPrompt(), "N") ? "否" : "是");
                            data.add(applyMtlCatHeaderDTO.getRemark());
                        }

                    }


//                    data.add(ruleLineDTO.getMatchRules().stream().map(item -> {
//                        for (RuleLineDTO lineDTO : ruleLineDTOList) {
//                            if (lineDTO.getRuleLineId().equals(item.getTargetRuleId())) {
//                                return lineDTO.getNo().toString();
//                            }
//                        }
//                        return "";
//                    }).collect(Collectors.joining(",")));
//                    data.add(ruleLineDTO.getRejectRules().stream().map(item -> {
//                        for (RuleLineDTO lineDTO : ruleLineDTOList) {
//                            if (lineDTO.getRuleLineId().equals(item.getTargetRuleId())) {
//                                return lineDTO.getNo().toString();
//                            }
//                        }
//                        return "";
//                    }).collect(Collectors.joining(",")));

                    data.add(LocalDateTimeUtil.formatNormal(ruleLineDTO.getEffectiveStartDate()));
                    data.add(LocalDateTimeUtil.formatNormal(ruleLineDTO.getEffectiveEndDate()));

                    list.add(data);
                }
            }
        }
        return list;
    }

    @Override
    public RuleHeader getByRuleNumber(String ruleNumber) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qRuleHeader.ruleNumber.eq(ruleNumber));
        return ruleHeaderRepository.findOne(booleanBuilder).orElse(null);
    }

    /**
     * 根据lov值 条件查询获取规则行
     *
     * @param query
     * @return
     */
    @Override
    public List<RuleLineDTO> listLinesByLov(RuleHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (query.getRuleCategoryId() != null) {
            booleanBuilder.and(qRuleHeader.ruleCategoryId.eq(query.getRuleCategoryId()));
        }
        if (CollectionUtils.isNotEmpty(query.getRuleNumberList())) {
            booleanBuilder.and(qRuleHeader.ruleNumber.in(query.getRuleNumberList()));
        }
        //获取规则头
        List<RuleHeader> ruleHeaders = IterableUtils.toList(ruleHeaderRepository.findAll(booleanBuilder));
        List<Long> ids = ruleHeaders.stream().map(RuleHeader::getRuleHeaderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            return ruleLineService.listLinesByHeaderIds(ids);
        }
        return Lists.newArrayList();
    }

    @Override
    @SneakyThrows
    public void exportRuleDetail(RuleHeaderQuery query, HttpServletResponse response) {
        List<RuleHeader> dtos = queryByPage(query).getContent();
        List<AttrTypeLineDTO> attrTypeLineDTOS = attrUtil.queryAttrTypeLinesByHeaderCode("CELL_RESTRICTED")
                .stream().sorted(Comparator.comparing(AttrTypeLineDTO::getColNo)).collect(Collectors.toList());
        List<List<String>> datas = getDatas(dtos, attrTypeLineDTOS);
        // 改为动态导出
        String fileName = "电池片投产限制规则_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        try {
            ExcelUtils.setExportResponseHeader(response, fileName);
            // 文件输出位置

            ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write(response.getOutputStream())
                    .registerConverter(new LocalDateConverter())
                    .registerConverter(new LocalDateTimeConverter())
                    .registerConverter(new LongStringConverter());

            ExcelWriter writer = excelWriterBuilder.build();
            // 动态添加表头，适用一些表头动态变化的场景
            WriteSheet sheet1 = new WriteSheet();
            sheet1.setSheetName("数据");
            sheet1.setSheetNo(0);
            // 创建一个表格，用于 Sheet 中使用
            WriteTable table = new WriteTable();
            table.setTableNo(1);
            table.setHead(excelHead(attrTypeLineDTOS));
            // 写数据
            writer.write(datas, sheet1, table);

            writer.finish();
        } finally {
            response.getOutputStream().close();
        }
    }

    private List<List<String>> getDatas(List<RuleHeader> dtos, List<AttrTypeLineDTO> attrTypeLineDTOS) {
        List<List<String>> result = new ArrayList<>();
        AtomicInteger serialNumber = new AtomicInteger(1);
        for (RuleHeader header : dtos) {
            List<RuleLine> tupleList = jpaQueryFactory.select(qRuleLine)
                    .from(qRuleLine)
                    .where(qRuleLine.isDeleted.eq(0).and(qRuleLine.ruleHeaderId.eq(header.getRuleHeaderId()))).fetch();
            if (CollectionUtils.isNotEmpty(tupleList)) {
                tupleList.stream().forEach(tupe -> {
                    ArrayList<String> strings = new ArrayList<>();
                    strings.add(String.valueOf(serialNumber.getAndIncrement()));
                    strings.add(header.getRuleCategoryName());
                    strings.add(header.getRuleName());
                    strings.add(header.getRuleNumber());
                    strings.add(tupe.getCode());
                    List<RuleDpDetail> ruleDpValueList = jpaQueryFactory.select(qRuleDpDetail)
                            .from(qRuleDpDetail)
                            .where(qRuleDpDetail.ruleLineId.eq(tupe.getRuleLineId()).and(qRuleDpDetail.isDeleted.eq(0))).fetch();
                    //分组
                    Map<Long, RuleDpDetail> dpDetailMap = ruleDpValueList.stream().
                            collect(Collectors.toMap(RuleDpDetail::getDpFiledId, Function.identity(), (key1, key2) -> key2));
                    for (AttrTypeLineDTO attrTypeLineDTO : attrTypeLineDTOS) {
                        if (dpDetailMap.containsKey(attrTypeLineDTO.getAttrLineId())) {
                            RuleDpDetail dpDetail = dpDetailMap.get(attrTypeLineDTO.getAttrLineId());
                            List<RuleDpValue> dpList = returnList(dpDetail.getRuleDetailId());
                            List<String> stringList = new ArrayList<>();
                            dpList.stream().forEach(x -> {
                                LovLineDTO lovLineDTO = LovUtils.get(x.getAttrValueId());
                                if (null != lovLineDTO) {
                                    stringList.add(lovLineDTO.getLovName());
                                } else {
                                    if (x.getAttrValue() == null) {
                                        stringList.add("");
                                    } else {
                                        stringList.add(x.getAttrValue());
                                    }
                                }
                            });
                            strings.add(String.join(",", stringList));
                        } else {
                            strings.add("");
                        }

                    }
                    strings.add(tupe.getRemark());
                    strings.add(Optional.ofNullable(tupe.getEffectiveStartDate()).map(LocalDate::toString).orElse(""));
                    strings.add(Optional.ofNullable(tupe.getEffectiveEndDate()).map(LocalDate::toString).orElse(""));
                    result.add(strings);
                });
            }
        }

        return result;
    }

    private List<List<String>> excelHead(List<AttrTypeLineDTO> attrTypeLineDTOS) {
        List<List<String>> list = new ArrayList<List<String>>();
        list.add(Lists.newArrayList("序号"));
        list.add(Lists.newArrayList("规则分类名称"));
        list.add(Lists.newArrayList("规则名称"));
        list.add(Lists.newArrayList("规则编码"));
        list.add(Lists.newArrayList("行规则编码"));
        for (AttrTypeLineDTO attrTypeLineDTO : attrTypeLineDTOS) {
            list.add(Lists.newArrayList(attrTypeLineDTO.getAttrCnName()));
        }
        list.add(Lists.newArrayList("备注"));
        list.add(Lists.newArrayList("开始时间"));
        list.add(Lists.newArrayList("结束时间"));
        return list;
    }

    @Override
    @SneakyThrows
    public void exportRule(RuleHeaderQuery query, HttpServletResponse response, String exportName) {
        LovLineDTO lovLineDTO = LovUtils.get("CELL_Rule_CLASSIFICATION", query.getRuleCategoryId());
        if ("电池片投产限制规则".equals(lovLineDTO.getLovName())) {
            exportRuleDetail(query, response);
        } else {
            extracted(query, response, lovLineDTO.getLovName());
        }

    }

    /**
     * 品类规则
     * 特殊区域规则
     * 需求转换规则
     * 包装规则
     * 材料规则
     *
     * @param query
     * @param response
     * @param exportName
     * @throws IOException
     */
    private void extracted(RuleHeaderQuery query, HttpServletResponse response, String exportName) throws IOException {
        List<RuleHeader> entities = queryByPage(query).getContent();
        List<RuleHeaderDTO> dtos = ruleHeaderDEConvert.toDto(entities);
        //规则因子
        //管控对象
        //遍历头查询行数据 集合添加每行的size


        for (RuleHeaderDTO header : dtos) {
            List<RuleLineDTO> ruleLineDTOList = ruleLineService.listLinesByHeaderId(header.getRuleHeaderId());
            header.setRuleLineDTOList(ruleLineDTOList);
        }

        Integer detailsMaxSize = 1;
        Integer controlObjectMaxSize = 1;
        Integer controlObjectDetailMaxSize = 1;
        if (CollectionUtils.isNotEmpty(dtos)) {
            detailsMaxSize = dtos.stream().flatMap(i -> i.getRuleLineDTOList().stream())
                    .map(i -> i.getDetails().size())
                    .max(Integer::compareTo)
                    .orElse(1);
            controlObjectMaxSize = dtos.stream().flatMap(i -> i.getRuleLineDTOList().stream())
                    .map(i -> i.getControlObjectHeaders().size())
                    .max(Integer::compareTo)
                    .orElse(1);
            controlObjectDetailMaxSize = dtos.stream().flatMap(i -> i.getRuleLineDTOList().stream())
                    .flatMap(i -> i.getControlObjectHeaders().stream())
                    .map(i -> i.getDetails().size())
                    .max(Integer::compareTo)
                    .orElse(1);
        }

        // 取值最大的size数量 动态生成表格头
        //设置动态列
        List<ExcelHead> excelHeads = buildExcelHead(detailsMaxSize, controlObjectMaxSize, controlObjectDetailMaxSize);
        List<Map<String, Object>> data = Lists.newArrayList();
        //设置动态列
        AtomicInteger atomicIntegers = new AtomicInteger(1);
        for (RuleHeaderDTO header : dtos) {
            List<RuleLineDTO> ruleLineDTOList = header.getRuleLineDTOList();
            for (RuleLineDTO ruleLineDTO : ruleLineDTOList) {
                Map<String, Object> map = BeanUtil.beanToMap(header);
                map.put("no", atomicIntegers.getAndIncrement());
                if (CollectionUtils.isNotEmpty(ruleLineDTOList)) {
                    AtomicInteger atomicInteger = new AtomicInteger(1);
                    ruleLineDTO.getDetails().stream().forEach(details -> {
                        Integer num = atomicInteger.getAndIncrement();
                        map.put("dpFiledName" + num, details.getDpFiledName());
                        map.put("attrOperatorName" + num, details.getAttrOperatorName());
                        map.put("attrValue" + num, details.getValues().stream().map(RuleDpValueDTO::getAttrValueName).collect(Collectors.joining(",")));
                    });
                    AtomicInteger atomicIntegerControl = new AtomicInteger(1);
                    ruleLineDTO.getControlObjectHeaders().stream().forEach(controlObject -> {
                        Integer num = atomicIntegerControl.getAndIncrement();
                        map.put("controlObjectName" + num, controlObject.getControlObject());
                        int dNum = 1;
                        for (RuleControlObjectDetailDTO detail : controlObject.getDetails()) {
                            map.put("controlObjectValue" + dNum, detail.getMaterialsAttrFiled() + ":" + detail.getValues().stream().map(RuleControlObjectValueDTO::getAttrValueName).collect(Collectors.joining(",")));
                        }
                    });
                }
                data.add(map);
            }
        }
        ExcelMain excelMain = ExcelMain.builder()
                .excelHeads(excelHeads)
                .data(data)
                .build();

        categoryExport(response, excelMain, exportName);
    }

    /**
     * 构建excel动态表头
     * 规则因子 detailsMaxSzieList
     * 管控对象 controlObjectMaxSzieList
     *
     * @param detailsMaxSzie
     * @param controlObjectMaxSzie
     * @param controlObjectDetailMaxSize
     * @return
     */
    private List<ExcelHead> buildExcelHead(Integer detailsMaxSzie, Integer controlObjectMaxSzie, Integer controlObjectDetailMaxSize) {
        List<ExcelHead> excelHeads = Lists.newArrayList(
                ExcelHead.builder()
                        .merge(false)
                        .label("序号")
                        .prop("no")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("规则分类名称")
                        .prop("ruleCategoryName")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("规则名称")
                        .prop("ruleName")
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label("规则编码")
                        .prop("ruleNumber")
                        .build()
        );

        if (0 != detailsMaxSzie) {
            IntStream.rangeClosed(1, detailsMaxSzie).forEach(i -> {
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label("规则因子" + i)
                                .prop("dpFiledName" + i)
                                .build()
                );
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label("条件项")
                                .prop("attrOperatorName" + i)
                                .build()
                );
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label("值/范围")
                                .prop("attrValue" + i)
                                .build()
                );
            });
        } else {
            excelHeads.add(
                    ExcelHead.builder()
                            .merge(false)
                            .label("规则因子")
                            .prop("dpFiledName")
                            .build()
            );
            excelHeads.add(
                    ExcelHead.builder()
                            .merge(false)
                            .label("条件项")
                            .prop("attrOperatorName")
                            .build()
            );
            excelHeads.add(
                    ExcelHead.builder()
                            .merge(false)
                            .label("值/范围")
                            .prop("attrValue")
                            .build()
            );
        }
        if (0 != controlObjectMaxSzie) {
            IntStream.rangeClosed(1, controlObjectMaxSzie).forEach(i -> {
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label("管控对象" + i)
                                .prop("controlObjectName" + i)
                                .build()
                );
                if (0 != controlObjectDetailMaxSize) {
                    IntStream.rangeClosed(1, controlObjectDetailMaxSize).forEach(b -> {
                        excelHeads.add(
                                ExcelHead.builder()
                                        .merge(false)
                                        .label("属性" + b)
                                        .prop("controlObjectValue" + b)
                                        .build()
                        );
                    });
                }

            });
        } else {
            excelHeads.add(
                    ExcelHead.builder()
                            .merge(false)
                            .label("管控对象")
                            .prop("controlObjectName")
                            .build()
            );
            if (0 != controlObjectDetailMaxSize) {
                IntStream.rangeClosed(1, controlObjectDetailMaxSize).forEach(b -> {
                    excelHeads.add(
                            ExcelHead.builder()
                                    .merge(false)
                                    .label("属性")
                                    .prop("controlObjectValue" + b)
                                    .build()
                    );
                });
            }
        }
       /* excelHeads.add(
                ExcelHead.builder()
                        .merge(false)
                        .label("备注")
                        .prop("remark")
                        .build()
        );*/
        excelHeads.add(
                ExcelHead.builder()
                        .merge(false)
                        .label("有效日期_起")
                        .prop("effectiveStartDate")
                        .build()
        );
        excelHeads.add(
                ExcelHead.builder()
                        .merge(false)
                        .label("有效日期_止")
                        .prop("effectiveEndDate")
                        .build()
        );
        return excelHeads;
    }

    public List<RuleDpValue> returnList(Long ruleDetailId) {
        return jpaQueryFactory.select(qRuleDpValue)
                .from(qRuleDpValue)
                .where(qRuleDpValue.ruleDetailId.eq(ruleDetailId))
                .where(qRuleDpValue.isDeleted.eq(0)).fetch();
    }

    @Override
    public Map<String, List<String>> getBatteryWorkShopInfo(List<BatteryWorkShopQuery> queryList) {
        Map<String, List<String>> batteryWorkShopMap = new HashMap<>();
        //获取电池片投产规则
        JPAQuery<Long> ruleHeaderJPAQuery = jpaQueryFactory.select(qRuleHeader.ruleCategoryId).
                from(qRuleHeader).where(qRuleHeader.ruleName.eq("电池片投产限制规则"));
        Long ruleCategoryId = ruleHeaderJPAQuery.fetchFirst();
        //查询该规则下面的明细数据
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qRuleHeader.ruleCategoryId.eq(ruleCategoryId));
        Iterable<RuleHeader> ruleHeaders = ruleHeaderRepository.findAll(booleanBuilder);
        List<RuleHeaderDTO> ruleHeaderDTOS = ruleHeaderDEConvert.toDto(IterableUtils.toList(ruleHeaders));
        List<Long> ruleHeaderIdList = ruleHeaderDTOS.stream().map(RuleHeaderDTO::getRuleHeaderId).collect(Collectors.toList());
        List<RuleLineByCompentWorkShopDTO> shopDTOList = addRuleLineByCompentWorkShopDTOByRuleHeaderIds(ruleHeaderIdList);
        if (!CollectionUtils.isEmpty(shopDTOList)) {
            List<String> itemcodeList = shopDTOList.stream().map(RuleLineByCompentWorkShopDTO::getItemCode).distinct().collect(Collectors.toList());
            List<Items> itemsList = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.categorySegment2.eq("自产电池片")
                            .and(qItems.organizationId.eq(82L))
                            .and(qItems.itemStatus.eq("Active"))
                            .and(qItems.itemCode.in(itemcodeList))).fetch();
            if (CollectionUtils.isEmpty(itemsList)) {
                return batteryWorkShopMap;
            }
            for (BatteryWorkShopQuery query : queryList) {
                //根据品类+P/N型查询82、自产电池片、有效的5A料号数据
                List<String> stringList = itemsList.stream().filter(item -> item.getSegment3().equals(query.getSegment3()) && item.getSegment1().equals(query.getSegment1())).map(Items::getItemCode).collect(Collectors.toList());
                shopDTOList.forEach(list -> {
                    if (stringList.contains(list.getItemCode()) && (CollectionUtils.isNotEmpty(list.getCompentWorkShopList()) && list.getCompentWorkShopList().contains(query.getCompentWorkShop()))) {
                        if (CollectionUtils.isNotEmpty(batteryWorkShopMap.get(String.join("/", query.getSegment3(), query.getSegment1(), query.getCompentWorkShop())))) {
                            batteryWorkShopMap.get(String.join("/", query.getSegment3(), query.getSegment1(), query.getCompentWorkShop())).addAll(list.getBatteryWorkShopList());
                        } else {
                            batteryWorkShopMap.put(String.join("/", query.getSegment3(), query.getSegment1(), query.getCompentWorkShop()), list.getBatteryWorkShopList());
                        }
                    }
                });
            }
            batteryWorkShopMap.keySet().forEach(map -> {
                batteryWorkShopMap.put(map, batteryWorkShopMap.get(map).stream().distinct().collect(Collectors.toList()));
            });
        }
        return batteryWorkShopMap;
    }

    @Override
    @Cacheable(cacheNames = "RuleHeaderService_listLineByRuleCategoryId", key = "#p0", unless = "#result == null", condition = "#p0!=null")
    public List<RuleLineDTO> listLineByRuleCategoryId(Long ruleCategoryId) {
        RuleHeaderQuery query = new RuleHeaderQuery();
        query.setRuleCategoryId(ruleCategoryId);
        List<RuleLineDTO> ruleLineDTOS = listLinesByLov(query);
        return ruleLineDTOS;
    }

    public List<RuleLineByCompentWorkShopDTO> addRuleLineByCompentWorkShopDTOByRuleHeaderIds(List<Long> ruleHeaderIds) {
        List<RuleLineDTO> ruleLineDTOList = ruleLineService.listLinesByHeaderIds(ruleHeaderIds);
        return ruleLineDTOList.stream().map(x -> {
            if (CollectionUtils.isNotEmpty(x.getDetails())) {
                RuleLineByCompentWorkShopDTO workShopDTO = new RuleLineByCompentWorkShopDTO();
                x.getDetails().stream().filter(q -> q.getDpFiledName().equals("电池片料号") || q.getDpFiledName().equals("组件车间") || q.getDpFiledName().equals("电池车间")).forEach(y -> {
                    //如果电池车间料号存在stringList集合中
                    if ("电池片料号".equals(y.getDpFiledName())) {
                        workShopDTO.setItemCode(y.getValues().get(0).getAttrValue());
                    }
                    if ("组件车间".equals(y.getDpFiledName())) {
                        if (CollectionUtils.isNotEmpty(y.getValues())) {
                            List<String> list = new ArrayList<>();
                            y.getValues().stream().forEach(p -> {
                                list.add(p.getAttrValue());
                            });
                            workShopDTO.setCompentWorkShopList(list);
                        }
                    }
                    if ("电池车间".equals(y.getDpFiledName())) {
                        if (CollectionUtils.isNotEmpty(y.getValues())) {
                            List<String> list = new ArrayList<>();
                            y.getValues().stream().forEach(p -> {
                                list.add(p.getAttrValue());
                            });
                            workShopDTO.setBatteryWorkShopList(list);
                        }
                    }

                });
                return workShopDTO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
