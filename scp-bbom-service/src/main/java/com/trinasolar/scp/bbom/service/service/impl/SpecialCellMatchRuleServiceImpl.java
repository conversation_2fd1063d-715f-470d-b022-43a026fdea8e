package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.SpecialCellMatchRuleDEConvert;
import com.trinasolar.scp.bbom.domain.dto.SpecialCellMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.entity.QSpecialCellMatchRule;
import com.trinasolar.scp.bbom.domain.entity.SpecialCellMatchRule;
import com.trinasolar.scp.bbom.domain.excel.SpecialCellMatchRuleExcelDTO;
import com.trinasolar.scp.bbom.domain.query.SpecialCellMatchRuleQuery;
import com.trinasolar.scp.bbom.domain.save.SpecialCellMatchRuleSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.repository.SpecialCellMatchRuleRepository;
import com.trinasolar.scp.bbom.service.service.SpecialCellMatchRuleService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/27
 */
@Slf4j
@Service("SpecialCellMatchRuleService")
@RequiredArgsConstructor
public class SpecialCellMatchRuleServiceImpl implements SpecialCellMatchRuleService {
    private final SpecialCellMatchRuleRepository repository;
    private final SpecialCellMatchRuleDEConvert convert;
    private static final QSpecialCellMatchRule aSpecialCellMatchRule = QSpecialCellMatchRule.specialCellMatchRule;

    @Override
    public Page<SpecialCellMatchRuleDTO> queryByPage(SpecialCellMatchRuleQuery query) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Page<SpecialCellMatchRule> page = repository.findAll(booleanBuilder, pageable);
        List<SpecialCellMatchRuleDTO> ruleDTOS = convert.toDto(page.getContent());
        return new PageImpl(ruleDTOS, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, SpecialCellMatchRuleQuery query) {
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(aSpecialCellMatchRule.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getRegionalCountry())) {
            booleanBuilder.and(aSpecialCellMatchRule.regionalCountry.eq(query.getRegionalCountry()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(aSpecialCellMatchRule.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getCellSource())) {
            booleanBuilder.and(aSpecialCellMatchRule.cellSource.eq(query.getCellSource()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(aSpecialCellMatchRule.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(aSpecialCellMatchRule.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
    }

    @Override
    public SpecialCellMatchRuleDTO queryById(Long id) {
        SpecialCellMatchRule queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public SpecialCellMatchRuleDTO save(SpecialCellMatchRuleSaveDTO saveDTO) {
        SpecialCellMatchRule newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new SpecialCellMatchRule());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<SpecialCellMatchRuleExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, SpecialCellMatchRuleExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        this.checkInput(excelDtos);
        List<SpecialCellMatchRule> saveList = convert.excelDtoToSaveDto(excelDtos);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        repository.saveAll(saveList);
    }

    private void checkInput(List<SpecialCellMatchRuleExcelDTO> excelDTOS) {
        final int[] i = {2};
        List<String> errors = new ArrayList<>();
        List<String> groupKeyList = Lists.newLinkedList();
        for (SpecialCellMatchRuleExcelDTO excelDTO : excelDTOS) {
            checkNullField(excelDTO, i);

            LovLineDTO lovLineDTOCellsType = LovUtils.getByName(LovHeaderCodeConstant.APS_BATTERY_TYPE, excelDTO.getCellsTypeName());
            if (lovLineDTOCellsType == null) {
                String message = MessageHelper.getMessage("special.cell.match.rule.cellstype.not.exists",new Object[]{i[0], excelDTO.getCellsTypeName()}).getDesc();
                errors.add(message);
            } else {
                excelDTO.setCellsType(lovLineDTOCellsType.getLovValue());
            }

            if (StringUtils.isNotEmpty(excelDTO.getRegionalCountryName())) {
                LovLineDTO lovLineDTORegionalCountry = LovUtils.getByName(LovHeaderCodeConstant.AOP_COUNTRY, excelDTO.getRegionalCountryName());
                if (lovLineDTORegionalCountry == null) {
                    String message = MessageHelper.getMessage("special.cell.match.rule.regionalcountry.not.exists",new Object[]{i[0], excelDTO.getRegionalCountryName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setRegionalCountry(lovLineDTORegionalCountry.getLovValue());
                }
            }

            if (StringUtils.isNotEmpty(excelDTO.getHTraceName())) {
                LovLineDTO lovLineDTORegionalHTrace = LovUtils.getByName(LovHeaderCodeConstant.H_TRACE, excelDTO.getHTraceName());
                if (lovLineDTORegionalHTrace == null) {
                    String message = MessageHelper.getMessage("special.cell.match.rule.h_trace.not.exists",new Object[]{i[0], excelDTO.getHTraceName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setHTrace(lovLineDTORegionalHTrace.getLovValue());
                }
            }

            if (StringUtils.isNotEmpty(excelDTO.getCellSourceName())) {
                LovLineDTO lovLineDTOCellSource = LovUtils.getByName(LovHeaderCodeConstant.PCS_SOURCE_TYPE, excelDTO.getCellSourceName());
                if (lovLineDTOCellSource == null) {
                    String message = MessageHelper.getMessage("special.cell.match.rule.cellsource.not.exists",new Object[]{i[0], excelDTO.getCellSourceName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setCellSource(lovLineDTOCellSource.getLovValue());
                }
            }

            if (StringUtils.isNotEmpty(excelDTO.getAestheticsName())) {
                LovLineDTO lovLineDTOAesthetics = LovUtils.getByName(LovHeaderCodeConstant.AESTHETICS, excelDTO.getAestheticsName());
                if (lovLineDTOAesthetics == null) {
                    String message = MessageHelper.getMessage("special.cell.match.rule.aesthetics.not.exists",new Object[]{i[0], excelDTO.getAestheticsName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setAesthetics(lovLineDTOAesthetics.getLovValue());
                }
            }

            if (StringUtils.isNotEmpty(excelDTO.getTransparentDoubleGlassName())) {
                LovLineDTO lovLineDTOTransparentDoubleGlass = LovUtils.getByName(LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, excelDTO.getTransparentDoubleGlassName());
                if (lovLineDTOTransparentDoubleGlass == null) {
                    String message = MessageHelper.getMessage("special.cell.match.rule.transparent_double_glass.not.exists",new Object[]{i[0], excelDTO.getTransparentDoubleGlassName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setTransparentDoubleGlass(lovLineDTOTransparentDoubleGlass.getLovValue());
                }
            }

            if (StringUtils.isNotEmpty(excelDTO.getProductionGradeName())) {
                LovLineDTO lovLineDTOProductionGrade = LovUtils.getByName(LovHeaderCodeConstant.PRODUCT_GRADE, excelDTO.getProductionGradeName());
                if (lovLineDTOProductionGrade == null) {
                    String message = MessageHelper.getMessage("special.cell.match.rule.productiongrade.not.exists",new Object[]{i[0], excelDTO.getProductionGradeName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setProductionGrade(lovLineDTOProductionGrade.getLovValue());
                }
            }

            LovLineDTO lovLineDTOCommonCellsType = LovUtils.getByName(LovHeaderCodeConstant.APS_BATTERY_TYPE, excelDTO.getCommonCellsTypeName());
            if (lovLineDTOCommonCellsType == null) {
                String message = MessageHelper.getMessage("special.cell.match.rule.commoncellstype.not.exists",new Object[]{i[0], excelDTO.getCommonCellsTypeName()}).getDesc();
                errors.add(message);
            } else {
                excelDTO.setCommonCellsType(lovLineDTOCommonCellsType.getLovValue());
            }

            String groupKey = StringUtils.join(excelDTO.getCellsType(), excelDTO.getRegionalCountry(), excelDTO.getHTrace(),
                    excelDTO.getCellSource(), excelDTO.getAesthetics(), excelDTO.getTransparentDoubleGlass(),
                    excelDTO.getProductionGrade(), excelDTO.getCommonCellsType());
            if (groupKeyList.contains(groupKey)) {
                String message = MessageHelper.getMessage("special.cell.match.rule.groupKey.repeat",new Object[]{i[0]}).getDesc();
                errors.add(message);
            } else {
                groupKeyList.add(groupKey);
            }
            i[0]++;
        }

        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }
    }

    private void checkNullField(SpecialCellMatchRuleExcelDTO excelDTO, int[] i) {
        if (StringUtils.isEmpty(excelDTO.getCellsTypeName()) || StringUtils.isEmpty(excelDTO.getCommonCellsTypeName())) {
            String message = MessageHelper.getMessage("special.cell.match.rule.import.row.not.null",new Object[]{i[0]}).getDesc();
            throw new BizException(message);
        }
    }

    @Override
    public void export(SpecialCellMatchRuleQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<SpecialCellMatchRuleDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(dtos, excelPara);
        //导出调用excelUtils
        String fileName = MessageHelper.getMessage("bbom_special_cell_match_rule").getDesc();
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }
}
