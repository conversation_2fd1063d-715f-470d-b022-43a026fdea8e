package com.trinasolar.scp.bbom.service.service;

import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;

import java.util.List;
import java.util.Locale;

public interface MailService {
    static final String FREEMARKER_VERSION = "2.3.0";
    static final String TEMPLATE_PATH = "/templates";


    default Template getTemplate(String templateName) throws Exception {
        Configuration configuration = new Configuration(new Version(FREEMARKER_VERSION));
        configuration.setTemplateLoader(new ClassTemplateLoader(this.getClass(), TEMPLATE_PATH));
        configuration.setEncoding(Locale.getDefault(), "UTF-8");
        configuration.setDateFormat("yyyy-MM-dd HH:mm:ss");
        Template template = configuration.getTemplate(templateName);
        return template;
    }

    boolean send(List<String> toList, String mailTemplate, String subject, Object content, String fileJson) throws Exception;
}
