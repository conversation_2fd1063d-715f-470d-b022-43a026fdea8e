package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.MainGridSpacingRuleDEConvert;
import com.trinasolar.scp.bbom.domain.dto.MainGridSpacingRuleDTO;
import com.trinasolar.scp.bbom.domain.entity.MainGridSpacingRule;
import com.trinasolar.scp.bbom.domain.entity.QMainGridSpacingRule;
import com.trinasolar.scp.bbom.domain.excel.MainGridSpacingRuleExcelDTO;
import com.trinasolar.scp.bbom.domain.query.MainGridSpacingRuleQuery;
import com.trinasolar.scp.bbom.domain.save.MainGridSpacingRuleSaveDTO;
import com.trinasolar.scp.bbom.service.repository.MainGridSpacingRuleRepository;
import com.trinasolar.scp.bbom.service.service.MainGridSpacingRuleService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电池主栅间距规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@Slf4j
@Service("mainGridSpacingRuleService")
@RequiredArgsConstructor
public class MainGridSpacingRuleServiceImpl implements MainGridSpacingRuleService {
    private static final QMainGridSpacingRule qMainGridSpacingRule = QMainGridSpacingRule.mainGridSpacingRule;

    private final MainGridSpacingRuleDEConvert convert;

    private final MainGridSpacingRuleRepository repository;

    @Override
    public Page<MainGridSpacingRuleDTO> queryByPage(MainGridSpacingRuleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<MainGridSpacingRule> page = repository.findAll(booleanBuilder, pageable);
        List<MainGridSpacingRuleDTO> dto = convert.toDto(page.getContent());
        dto.forEach(item -> {
            convert(item);
        });
        return new PageImpl(dto, page.getPageable(), page.getTotalElements());
    }

    private void convert(MainGridSpacingRuleDTO item) {
        // 电池类型、组件车间的主栅间距，单个value
        //电池车间  多个 value 由逗号分割
        // 电池类型 BATTERY_TYPE
        LovLineDTO batteryType = LovUtils.get("BATTERY_TYPE", item.getBatteryType());
        item.setBatteryTypeName(batteryType.getLovName());
        // 组件车间和电池车间 work_shop
        Map<String, LovLineDTO> workShop = LovUtils.getAllByHeaderCode("work_shop");
        LovLineDTO itemWorkshop = workShop.get(item.getItemWorkshop());
        item.setItemWorkshopName(itemWorkshop.getLovName());

        if (StringUtils.isNotBlank(item.getBatteryWorkshop())) {
            String[] batteryWorkshops = item.getBatteryWorkshop().split(",");
            String batteryWorkshopName = "";
            for (String batteryWorkshop : batteryWorkshops) {
                if (StringUtils.isBlank(batteryWorkshop)) {
                    continue;
                }
                LovLineDTO lovLineDTO = workShop.get(batteryWorkshop);
                batteryWorkshopName = String.format("%s%s", batteryWorkshopName, lovLineDTO.getLovName() + ",");
            }
            item.setBatteryWorkshopName(batteryWorkshopName);
        }
        // 主栅间距 5A00100100133
        LovLineDTO mainGridSpacing = LovUtils.get("5A00100100133", item.getMainGridSpacing());
        if (mainGridSpacing != null) {
            item.setMainGridSpacing(mainGridSpacing.getLovName());
        }
    }

    private void buildWhere(BooleanBuilder booleanBuilder, MainGridSpacingRuleQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qMainGridSpacingRule.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qMainGridSpacingRule.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getItemWorkshop())) {
            booleanBuilder.and(qMainGridSpacingRule.itemWorkshop.eq(query.getItemWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryWorkshop())) {
            booleanBuilder.and(qMainGridSpacingRule.batteryWorkshop.eq(query.getBatteryWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getMainGridSpacing())) {
            booleanBuilder.and(qMainGridSpacingRule.mainGridSpacing.eq(query.getMainGridSpacing()));
        }
    }

    @Override
    public MainGridSpacingRuleDTO queryById(Long id) {
        MainGridSpacingRule queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public MainGridSpacingRuleDTO save(MainGridSpacingRuleSaveDTO saveDTO) {
        if (saveDTO.getEffectiveStartDate() == null) {
            saveDTO.setEffectiveStartDate(LocalDate.of(2000, 1, 1));
        }
        if (saveDTO.getEffectiveEndDate() == null) {
            saveDTO.setEffectiveEndDate(LocalDate.of(2099, 12, 31));
        }

        // 校验是否有一样的数据,有一样的设置Id
        MainGridSpacingRule mainGridSpacingRule = repository.findOne(
                qMainGridSpacingRule.batteryType.eq(saveDTO.getBatteryType())
                        .and(qMainGridSpacingRule.itemWorkshop.eq(saveDTO.getItemWorkshop()))
                        .and(qMainGridSpacingRule.effectiveStartDate.eq(saveDTO.getEffectiveStartDate()))
                        .and(qMainGridSpacingRule.effectiveEndDate.eq(saveDTO.getEffectiveEndDate()))
        ).orElse(null);
        if (mainGridSpacingRule == null) {
            // 校验是否有日期重叠的数据
            Iterable<MainGridSpacingRule> overlapData = repository.findAll(
                    qMainGridSpacingRule.batteryType.eq(saveDTO.getBatteryType())
                            .and(qMainGridSpacingRule.itemWorkshop.eq(saveDTO.getItemWorkshop()))
                            .and(qMainGridSpacingRule.effectiveStartDate.loe(saveDTO.getEffectiveEndDate()))
                            .and(qMainGridSpacingRule.effectiveEndDate.goe(saveDTO.getEffectiveStartDate()))
            );
            if (overlapData.iterator().hasNext()) {
                throw new BizException("bbom_valid_mainGridSpacingRule_repeat");
            }
            mainGridSpacingRule = new MainGridSpacingRule();
        }

        mainGridSpacingRule = convert.saveDTOtoEntity(saveDTO, mainGridSpacingRule);
        repository.save(mainGridSpacingRule);

        return this.queryById(mainGridSpacingRule.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(MainGridSpacingRuleQuery query, HttpServletResponse response) {
        List<MainGridSpacingRuleDTO> dtos = queryByPage(query).getContent();
        List<MainGridSpacingRuleExcelDTO> excelDTO = convert.toExcelDTO(dtos);

        ExcelUtils.setExportResponseHeader(response, "电池主栅间距规则_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "电池主栅间距规则").head(MainGridSpacingRuleExcelDTO.class).build();
            excelWriter.write(excelDTO, sheet);
        }
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile) {
        List<MainGridSpacingRuleExcelDTO> excelDtos = new LinkedList<>();
        EasyExcel.read(multipartFile.getInputStream(), MainGridSpacingRuleExcelDTO.class, new ReadListener<MainGridSpacingRuleExcelDTO>() {
                    @Override
                    public void invoke(MainGridSpacingRuleExcelDTO data, AnalysisContext context) {
                        excelDtos.add(data);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                    }
                }).sheet().registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new NumberToLocalDateConverter())
                .registerConverter(new LongStringConverter()).doRead();
        log.info("MainGridSpacingRuleExcelDTO:{}", excelDtos);
        verifyImport(excelDtos);
        doSaveExcelImport(excelDtos);
    }

    @Override
    public MainGridSpacingRuleDTO applyRule(MainGridSpacingRuleQuery query) {
        // 获取
        MainGridSpacingRule mainGridSpacingRule = repository.findOne(qMainGridSpacingRule.batteryType.eq(query.getBatteryType())
                .and(qMainGridSpacingRule.itemWorkshop.eq(query.getItemWorkshop()))
                .and(qMainGridSpacingRule.effectiveStartDate.loe(query.getEffectiveStartDate()))
                .and(qMainGridSpacingRule.effectiveEndDate.goe(query.getEffectiveStartDate()))
        ).orElse(null);
        if (mainGridSpacingRule == null) {
            return new MainGridSpacingRuleDTO();
        }

        return convert.toDto(mainGridSpacingRule);
    }

    @Override
    public List<MainGridSpacingRuleDTO> getAllRules(MainGridSpacingRuleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (query.getId() != null) {
            booleanBuilder.and(qMainGridSpacingRule.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qMainGridSpacingRule.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getItemWorkshop())) {
            booleanBuilder.and(qMainGridSpacingRule.itemWorkshop.eq(query.getItemWorkshop()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qMainGridSpacingRule.effectiveStartDate.loe(query.getEffectiveStartDate()));
            booleanBuilder.and(qMainGridSpacingRule.effectiveEndDate.goe(query.getEffectiveStartDate()));
        }
        Iterable<MainGridSpacingRule> all = repository.findAll(booleanBuilder);
        List<MainGridSpacingRuleDTO> dto = convert.toDto(Lists.newArrayList(all));
        dto.forEach(this::convert);
        return dto;
    }

    private void fillData(List<MainGridSpacingRuleExcelDTO> excelDtos) {
        Map<String, LovLineDTO> batteryTypeLovMap = LovUtils.getAllByHeaderCode("BATTERY_TYPE");
        excelDtos.forEach(i -> {
            i.setBatteryType(batteryTypeLovMap.get(i.getBatteryTypeName()).getLovValue());
            if (i.getEffectiveStartDate() == null) {
                i.setEffectiveStartDate(LocalDate.of(2000, 1, 1));
            }
            if (i.getEffectiveEndDate() == null) {
                i.setEffectiveEndDate(LocalDate.of(2099, 12, 31));
            }
        });
    }

    private void verifyImport(List<MainGridSpacingRuleExcelDTO> excelDtos) {
        // 校验
        List<String> errors = doVerifyImportData(excelDtos);
        if (CollectionUtils.isNotEmpty(errors)) {
            throw new BizException(String.valueOf(errors));
        }
        fillData(excelDtos);
        excelDtos.stream().collect(Collectors.groupingBy(i -> i.getBatteryTypeName() + "_" + i.getItemWorkshop()))
                .forEach((k, v) -> {
                    // 校验有效日期是否重叠
                    v.sort(Comparator.comparing(MainGridSpacingRuleExcelDTO::getEffectiveStartDate));
                    for (int i = 0; i < v.size() - 1; i++) {
                        if (v.get(i).getEffectiveEndDate().isAfter(v.get(i + 1).getEffectiveStartDate())) {
                            errors.add("电池类型[" + v.get(i).getBatteryTypeName() + "]和组件车间[" + v.get(i).getItemWorkshop() + "]有效日期重叠\n");
                        }
                    }
                    // 校验和数据库是否有数据重叠
                    for (MainGridSpacingRuleExcelDTO mainGridSpacingRuleExcelDTO : v) {
                        // 校验是否有一样的数据,有一样的设置Id
                        MainGridSpacingRule mainGridSpacingRule = repository.findOne(
                                qMainGridSpacingRule.batteryType.eq(mainGridSpacingRuleExcelDTO.getBatteryType())
                                        .and(qMainGridSpacingRule.itemWorkshop.eq(mainGridSpacingRuleExcelDTO.getItemWorkshop()))
                                        .and(qMainGridSpacingRule.effectiveStartDate.eq(mainGridSpacingRuleExcelDTO.getEffectiveStartDate()))
                                        .and(qMainGridSpacingRule.effectiveEndDate.eq(mainGridSpacingRuleExcelDTO.getEffectiveEndDate()))
                        ).orElse(null);
                        if (mainGridSpacingRule == null) {
                            // 校验是否有日期重叠的数据
                            Iterable<MainGridSpacingRule> overlapData = repository.findAll(
                                    qMainGridSpacingRule.batteryType.eq(mainGridSpacingRuleExcelDTO.getBatteryType())
                                            .and(qMainGridSpacingRule.itemWorkshop.eq(mainGridSpacingRuleExcelDTO.getItemWorkshop()))
                                            .and(qMainGridSpacingRule.effectiveStartDate.loe(mainGridSpacingRuleExcelDTO.getEffectiveEndDate()))
                                            .and(qMainGridSpacingRule.effectiveEndDate.goe(mainGridSpacingRuleExcelDTO.getEffectiveStartDate()))
                            );
                            if (overlapData.iterator().hasNext()) {
                                errors.add("电池类型[" + mainGridSpacingRuleExcelDTO.getBatteryTypeName() + "]和组件车间[" + mainGridSpacingRuleExcelDTO.getItemWorkshop() + "]有效日期重叠\n");
                            }
                        }
                    }
                });
        if (CollectionUtils.isNotEmpty(errors)) {
            throw new BizException(String.valueOf(errors));
        }
    }

    private List<String> doVerifyImportData(List<MainGridSpacingRuleExcelDTO> excelDtos) {
        List<String> errors = new ArrayList<>();
        boolean errorFlag;
        String errorMsg;
        int lineNumber = 1;
        Map<String, LovLineDTO> batteryTypeLovMap = LovUtils.getAllByHeaderCode("BATTERY_TYPE");
        Set<String> batteryTypeNameSet = batteryTypeLovMap.values().stream().map(LovLineDTO::getLovName).collect(Collectors.toSet());
        Map<String, LovLineDTO> workshopLovMap = LovUtils.getAllByHeaderCode("work_shop");
        Set<String> workshopValueSet = workshopLovMap.values().stream().map(LovLineDTO::getLovValue).collect(Collectors.toSet());
        // 主栅间距 5A00100100133
        Map<String, LovLineDTO> mainGridSpacingLovMap = LovUtils.getAllByHeaderCode("5A00100100133");
        Set<String> mainGridSpacingValueSet = mainGridSpacingLovMap.values().stream().map(LovLineDTO::getLovValue).collect(Collectors.toSet());

        for (MainGridSpacingRuleExcelDTO excelDto : excelDtos) {
            lineNumber++;
            errorFlag = false;
            errorMsg = "第 " + lineNumber + " 行: ";
            // 版本号不能为空
            if (!batteryTypeLovMap.containsKey(excelDto.getBatteryTypeName())) {
                errorFlag = true;
                errorMsg += "电池类型必须在指定的列表中[" + StringUtils.join(batteryTypeNameSet, ",") + "];";
            }
            if (!workshopLovMap.containsKey(excelDto.getItemWorkshop())) {
                errorFlag = true;
                errorMsg += "组件车间必须在指定的列表中[" + StringUtils.join(workshopValueSet, ",") + "];";
            }

            if (StringUtils.isNotBlank(excelDto.getBatteryWorkshop())) {
                String[] batteryWorkshops = excelDto.getBatteryWorkshop().split(",");
                for (String batteryWorkshop : batteryWorkshops) {
                    if (!workshopLovMap.containsKey(batteryWorkshop)) {
                        errorFlag = true;
                        errorMsg = new StringBuilder().append(errorMsg).append("电池车间必须在指定的列表中[" + StringUtils.join(workshopValueSet, ",") + "];").toString();
                    }
                }
            }

            if (!mainGridSpacingLovMap.containsKey(excelDto.getMainGridSpacing())) {
                errorFlag = true;
                errorMsg += "主栅间距必须在指定的列表中[" + StringUtils.join(mainGridSpacingValueSet, ",") + "];";
            }
            if (errorFlag) {
                errors.add(errorMsg + "\n");
            }
        }
        return errors;
    }

    private void doSaveExcelImport(List<MainGridSpacingRuleExcelDTO> excelDtos) {

        for (MainGridSpacingRuleExcelDTO excelDto : excelDtos) {
            save(convert.excelDTOtoSaveDTO(excelDto));
        }
    }
}
