package com.trinasolar.scp.bbom.service.context;

import com.trinasolar.scp.bbom.domain.dto.*;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 料号匹配上下文对象
 * 用于在doSingleMatch方法中传递共享数据，减少重复查询和对象创建
 * 
 * <AUTHOR> Assistant
 */
@Data
@Builder
public class MatchContext {
    
    /**
     * 匹配头信息
     */
    private MaterielMatchHeaderDTO header;
    
    /**
     * BOM替代项映射
     * Key: 组织ID, Value: 替代项列表
     */
    private Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap;
    
    /**
     * 表达式规则映射
     * Key: 规则ID, Value: 规则行列表
     */
    private Map<Long, List<ExpressRuleLineDTO>> expressRules;
    
    /**
     * DP属性和Item属性对应关系
     * Key: DP字段名, Value: Item字段名
     */
    private Map<String, String> dpMapping;
    
    /**
     * DP转换脚本映射
     * Key: DP字段名, Value: 转换脚本
     */
    private Map<String, String> dpTransScriptMapping;
    
    /**
     * 所有5A料号数据
     */
    private List<ItemsDTO> allItems;
    
    /**
     * 结构数据映射
     * Key: 组合键(替代项+组织ID+装配件ID), Value: 结构DTO
     */
    private Map<String, StructuresDTO> structures;
    
    /**
     * 电池类型主数据
     */
    private BatteryTypeMainDTO batteryTypeMain;
    
    /**
     * 工艺路线映射
     * Key: 路线标识, Value: 工艺路线DTO
     */
    private Map<String, ErpOperatRouteDTO> operatRouteMap;
    
    /**
     * 硅片属性类型映射
     * Key: 属性名, Value: 属性类型行DTO
     */
    private Map<String, AttrTypeLineDTO> siliconWaferAttrType;
    
    /**
     * 网版属性类型
     */
    private AttrTypeLineDTO screenPlateAttrType;
    
    /**
     * 继承匹配数据映射
     * Key: 匹配键, Value: 匹配行列表
     */
    private Map<String, List<MaterielMatchLine>> inheritMatchMap;
    
    /**
     * 创建匹配上下文的工厂方法
     */
    public static class MatchContextBuilder {
        
        /**
         * 构建完整的匹配上下文
         */
        public MatchContext buildComplete() {
            return this.build();
        }
        
        /**
         * 构建基础上下文（只包含必要数据）
         */
        public MatchContext buildBasic() {
            return MatchContext.builder()
                .header(this.header)
                .designatorMap(this.designatorMap)
                .expressRules(this.expressRules)
                .dpMapping(this.dpMapping)
                .allItems(this.allItems)
                .build();
        }
    }
    
    /**
     * 获取车间对应的库存组织ID
     */
    public Long getOrganizationId() {
        if (header == null || header.getWorkshop() == null) {
            return null;
        }
        
        // 这里可以添加车间到组织ID的转换逻辑
        // 暂时返回默认值
        return 82L;
    }
    
    /**
     * 检查上下文是否完整
     */
    public boolean isComplete() {
        return header != null 
            && designatorMap != null 
            && expressRules != null 
            && dpMapping != null 
            && allItems != null 
            && structures != null;
    }
    
    /**
     * 获取匹配ID
     */
    public Long getMatchId() {
        return header != null ? header.getId() : null;
    }
    
    /**
     * 获取车间名称
     */
    public String getWorkshop() {
        return header != null ? header.getWorkshop() : null;
    }
    
    /**
     * 获取电池类型
     */
    public String getBatteryType() {
        return header != null ? header.getBatteryType() : null;
    }
    
    /**
     * 验证上下文数据的有效性
     */
    public void validate() {
        if (header == null) {
            throw new IllegalStateException("匹配头信息不能为空");
        }
        
        if (allItems == null || allItems.isEmpty()) {
            throw new IllegalStateException("料号数据不能为空");
        }
        
        if (designatorMap == null || designatorMap.isEmpty()) {
            throw new IllegalStateException("BOM替代项数据不能为空");
        }
        
        if (expressRules == null || expressRules.isEmpty()) {
            throw new IllegalStateException("表达式规则数据不能为空");
        }
    }
}
