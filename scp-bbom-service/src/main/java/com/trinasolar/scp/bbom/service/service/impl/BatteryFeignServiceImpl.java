package com.trinasolar.scp.bbom.service.service.impl;

import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.query.ItemCodesQuery;
import com.trinasolar.scp.bbom.domain.query.ModuleBasePlaceQuery;
import com.trinasolar.scp.bbom.service.feign.ApsFeign;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.service.BatteryFeignService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryFeignServiceImpl
 * @Description
 * @Date 2023/12/13 14:36
 **/
@Slf4j
@Service("batteryFeignService")
@RequiredArgsConstructor
@CacheConfig(cacheNames = "BatteryFeignService", cacheManager = "caffeineCacheManager")
public class BatteryFeignServiceImpl implements BatteryFeignService {

    private final ApsFeign apsFeign;

    private final BomFeign bomFeign;

    /**
     * 查询基地车间生产单元信息
     *
     * @param basePlace
     * @param workshop
     * @param workunit
     * @return
     */
    @Override
    @Cacheable(cacheNames = "BatteryFeignService_findBasePlaceWorkshopWorkUnit", key = "#basePlace+'_'+#workshop+'_'+#workunit", unless = "#result == null")
    public List<ModuleBasePlaceDTO> findBasePlaceWorkshopWorkUnit(String basePlace, String workshop, String workunit) {
        // 校验合法性 生产基地 生产车间 api接口
        ModuleBasePlaceQuery moduleBasePlaceQuery = new ModuleBasePlaceQuery();
        moduleBasePlaceQuery.setBasePlace(basePlace);
        moduleBasePlaceQuery.setWorkshop(workshop);
        moduleBasePlaceQuery.setWorkunit(workunit);
        // zt 产品类型必传
        moduleBasePlaceQuery.setProductType("CELL");
        ResponseEntity<Results<List<ModuleBasePlaceDTO>>> listByBasePlace = apsFeign.findListByBasePlaceAndWorkshopAndWorkunit(moduleBasePlaceQuery);
        if (!listByBasePlace.getBody().isSuccess()) {
            throw new BizException("bbom_feign_findBasePlaceWorkshopWorkUnit_error");
        }
        return listByBasePlace.getBody().getData();
    }

    /**
     * 获取网版料号信息
     *
     * @param query
     * @return
     */
    @Override
    public Map<String, String> findItemDescByItemCodes(ItemCodesQuery query) {
        // 校验网版料号合法性 API接口 查询网版料号集合
        ResponseEntity<Results<Map<String, String>>> itemDescByItemCodes =
                bomFeign.findItemDescByItemCodes(query);
        if (!itemDescByItemCodes.getBody().isSuccess()) {
            throw new BizException("bbom_feign_findItemDescByItemCodes_error");
        }
        return itemDescByItemCodes.getBody().getData();
    }
}
