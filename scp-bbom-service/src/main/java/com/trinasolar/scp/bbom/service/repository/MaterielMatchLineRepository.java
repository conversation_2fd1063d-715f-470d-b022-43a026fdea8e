package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Repository
public interface MaterielMatchLineRepository extends JpaRepository<MaterielMatchLine, Long>, QuerydslPredicateExecutor<MaterielMatchLine> {
    @Query("select m from MaterielMatchLine m where m.finalVersion=?1 and m.month=?2 and m.isDeleted=1 and m.handWorkFlag='Y' and m.isOverseaId=?3 ")
    List<MaterielMatchLine> findLineByVersion(String version, String month, Long isOverseaId);

    @Query("select max(m.finalVersion) as finalVersion from MaterielMatchLine m where   m.month=?1 and m.isDeleted=1 and m.finalVersion is not null and m.isOverseaId=?2 ")
    String getMaxFinalVersion(String month, Long isOverseaId);

    @Query(value = "select * from bbom_materiel_match_line m where 1=1 and m.id in(1782589413323313152,1782356660052955136)",nativeQuery = true)
    List<MaterielMatchLine> findByAssemblySql(@Param("assemblySql") String assemblySql);
}
