package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.lang.Pair;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.QComponents;
import com.trinasolar.scp.bbom.domain.entity.QItems;
import com.trinasolar.scp.bbom.domain.entity.QStructures;
import com.trinasolar.scp.bbom.domain.enums.ItemCategoryEnum;
import com.trinasolar.scp.bbom.domain.excel.ExcelHead;
import com.trinasolar.scp.bbom.domain.excel.ExcelMain;
import com.trinasolar.scp.bbom.domain.query.MainReplacementQuery;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.domain.utils.PageUtil;
import com.trinasolar.scp.bbom.service.feign.ApsFeign;
import com.trinasolar.scp.bbom.service.feign.BAPSFeign;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.service.MainReplacementService;
import com.trinasolar.scp.bbom.service.util.excel.ExcelUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/10/1
 */
@Slf4j
@Service("mainReplacementService")
@RequiredArgsConstructor
public class MainReplacementServiceImpl implements MainReplacementService {
    private static final QComponents qComponents = QComponents.components;
    private static final QStructures qStructures = QStructures.structures;
    private static final QItems qItems1 = new QItems("qItems1");
    private static final QItems qItems2 = new QItems("qItems2");
    private final BAPSFeign bapsFeign;
    private final BomFeign bomFeign;
    private final ApsFeign apsFeign;
    private final JPAQueryFactory jpaQueryFactory;
    private static final String CELL_TYPE_PREFIX = "单晶_N型_210R_双面_16BB_二分";
    private static final Long COMMON_ORGANIZATION_ID = 82L;

    private Page<ScreenMainReplacementDTO> mockScreen() {
        List<ScreenMainReplacementDTO> screenMainReplacementDTOS = Lists.newLinkedList();
        List<MainReplacementDetailDTO> detailDTOS = Lists.newLinkedList();
        ScreenMainReplacementDTO replacementDTO = ScreenMainReplacementDTO
                .builder()
                .batteryType("210R-P型")
                .shardingMode("二分片")
                .regionalCountry("小区域")
                .mainGridSpace("10.8")
                .singleGlass("单玻")
                .workbench("1号机")
                .subFlag("主")
                .model("430-13")
                .itemCode("7A008290")
                .itemDescription("网版_普通_TOPCON_背电场网版_N型_210R_背面_细栅_网纱_3D_16BB_鱼叉头Pad1_二分片_无网结_500_9_13_3.5_15_15_192_2号机,宿迁C5,20240905")
                .build();

        Map<String, LovLineDTO> lovLineDTOMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, String> detailMap = lovLineDTOMap.values().stream().filter(ele -> StringUtils.isNotEmpty(ele.getAttribute3()))
                .collect(Collectors.toMap(LovLineDTO::getLovName, LovLineDTO::getAttribute3, (v1, v2) -> v1));

        detailMap.forEach((k, v) -> {
            MainReplacementDetailDTO detailDTO = MainReplacementDetailDTO
                    .builder()
                    .workShop(k)
                    .productionArea(v)
                    .build();
            detailDTOS.add(detailDTO);
        });
        detailDTOS.get(0).setExistFlagDesc("√");
        replacementDTO.setDetailDTOS(detailDTOS);
        screenMainReplacementDTOS.add(replacementDTO);
        return new PageImpl<>(screenMainReplacementDTOS);
    }

    @Override
    public Page<ScreenMainReplacementDTO> queryScreenByPage(MainReplacementQuery query) {
        // 步骤一：获取当前月及次月已排产的电池计划中涉及的电池料号
        List<String> allItemCodes = bapsFeign.queryTwoMonthAllItemCodes().getBody().getData();
        if (CollectionUtils.isEmpty(allItemCodes)) {
            return new PageImpl(Collections.emptyList());
        }

        // 步骤二：根据电池料号获取该料号在各个车间的BOM信息（电池BOM是根据替代项识别车间）
        // 步骤2.1：根据电池料号数据查询一批有效数据
        List<MainReplacementDBDTO> mainReplacementDBDTOS = this.getAllReplacementDBDTO(allItemCodes, ItemCategoryEnum.SCREEN_PLATE);
        if (CollectionUtils.isEmpty(mainReplacementDBDTOS)) {
            return new PageImpl(Collections.emptyList());
        }

        // 判断是否电池需求规则-10.8/10.9规则分类界面维护，用于主栅间距判断
        Map<String, String> cellRelationDTOMap = this.cellRelationDTOMap(allItemCodes);

        Map<String, Set<String>> allWorkbenchLovMap = this.getAllWorkbenchLovMap();
        List<String> countryCellNos = this.getAllCountryCellNos();
        Map<String, List<String>> workShopMap = this.getWorkShopMap(mainReplacementDBDTOS);
        Map<String, String> productionAreaMap = this.getAllProductionAreaMap();

        List<ScreenMainReplacementUnGroupDTO> unGroupDTOS = Lists.newLinkedList();
        for (MainReplacementDBDTO replacementDBDTO : mainReplacementDBDTOS) {
            String cellType = cellRelationDTOMap.getOrDefault(replacementDBDTO.getItemCode(), "");
            Set<String> workbenchs = allWorkbenchLovMap.getOrDefault(StringUtils.join(replacementDBDTO.getSegment13(), "-", replacementDBDTO.getSegment9()), Sets.newHashSet());
            if (CollectionUtils.isEmpty(workbenchs)) workbenchs.add(null);

            List<String> workShops = workShopMap.get(replacementDBDTO.getAlternateBomDesignator());
            workShops.forEach(workShop -> {
                workbenchs.forEach(workbench -> {
                    ScreenMainReplacementUnGroupDTO buildDTO = ScreenMainReplacementUnGroupDTO
                            .builder()
                            .batteryType(StringUtils.join(replacementDBDTO.getSegment3(), "-", replacementDBDTO.getSegment1()))
                            .shardingMode(replacementDBDTO.getSegment11())
                            .regionalCountry(countryCellNos.contains(replacementDBDTO.getItemCode()) ? "小区域" : null)
                            .mainGridSpace(cellType.startsWith(CELL_TYPE_PREFIX) ? replacementDBDTO.getSegment13() : null)
                            .singleGlass(replacementDBDTO.getSegment23().contains("单玻") ? replacementDBDTO.getSegment23() : null)
                            .workbench(workbench)
                            .subFlag(Objects.equals("N", replacementDBDTO.getSubstituteFlag()) ? "主" :
                                    (Objects.equals("Y", replacementDBDTO.getSubstituteFlag()) ? "替" : null))
                            .model(StringUtils.join(replacementDBDTO.getSegment6(), "-", replacementDBDTO.getSegment7()))
                            .itemCode(replacementDBDTO.getItemCode())
                            .itemDescription(replacementDBDTO.getItemDesc())
                            .workShop(workShop)
                            .productionArea(productionAreaMap.get(workShops))
                            .build();
                    unGroupDTOS.add(buildDTO);
                });
            });
        }

        List<String> detailKeyList = unGroupDTOS.stream().map(ele -> StringUtils.join(ele.getWorkShop(), "-", ele.getProductionArea())).distinct().collect(Collectors.toList());
        Map<String, List<ScreenMainReplacementUnGroupDTO>> unGroupDTOMap = unGroupDTOS.stream().collect(Collectors.groupingBy(ScreenMainReplacementUnGroupDTO::group));


        List<ScreenMainReplacementDTO> resultList = Lists.newLinkedList();
        unGroupDTOMap.forEach((k, v) -> {
            ScreenMainReplacementDTO replacementDTO = new ScreenMainReplacementDTO();
            BeanUtils.copyProperties(v.get(0), replacementDTO);
            List<String> spliteDetailKeyList = v.stream().map(ele -> StringUtils.join(ele.getWorkShop(), "-", ele.getProductionArea())).distinct().collect(Collectors.toList());

            List<MainReplacementDetailDTO> detailDTOS = detailKeyList.stream().map(key -> {
                String[] split = key.split("-");
                return MainReplacementDetailDTO
                        .builder()
                        .workShop(split.length >= 1 ? split[0] : null)
                        .productionArea(split.length >= 2 ? split[1] : null)
                        .existFlagDesc(spliteDetailKeyList.contains(key) ? "√" : null)
                        .build();
            }).collect(Collectors.toList());
            replacementDTO.setDetailDTOS(detailDTOS);
            resultList.add(replacementDTO);
        });

        List<ScreenMainReplacementDTO> finalResultList = Lists.newLinkedList();
        finalResultList.addAll(resultList);
        if (StringUtils.isNotEmpty(query.getBatteryType()) || StringUtils.isNotEmpty(query.getWorkbench())
                || StringUtils.isNotEmpty(query.getSubFlag()) || StringUtils.isNotEmpty(query.getItemCode())
                || StringUtils.isNotEmpty(query.getWorkShop())) {
            finalResultList = finalResultList.stream().filter(ele -> {
                Boolean ignoreFlag = true;
                if (StringUtils.isNotEmpty(query.getBatteryType())) {
                    ignoreFlag = Objects.equals(ele.getBatteryType(), query.getBatteryType() + "型");
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getWorkbench())) {
                    ignoreFlag = Objects.equals(ele.getWorkbench(), query.getWorkbench());
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getSubFlag())) {
                    String subFlag = Objects.equals("main", query.getSubFlag()) ? "主" : "替";
                    ignoreFlag = Objects.equals(ele.getSubFlag(), subFlag);
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getItemCode())) {
                    ignoreFlag = Objects.equals(ele.getItemCode(), query.getItemCode());
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getWorkShop())) {
                    ignoreFlag = ele.getDetailDTOS().stream().filter(detailDTO -> Objects.equals(detailDTO.getWorkShop(), query.getWorkShop())
                            && StringUtils.isNotEmpty(detailDTO.getExistFlagDesc())).count() > 0;
                }
                return ignoreFlag;
            }).collect(Collectors.toList());
        }

        finalResultList.sort(Comparator.comparing(ScreenMainReplacementDTO::getBatteryType, Comparator.nullsLast(String::compareTo))
                .thenComparing(ScreenMainReplacementDTO::getShardingMode, Comparator.nullsLast(String::compareTo))
                .thenComparing(ScreenMainReplacementDTO::getWorkbench, Comparator.nullsLast(String::compareTo)));
        List<ScreenMainReplacementDTO> slurryMainReplacementDTOS = PageUtil.startPage(finalResultList, query.getPageNumber(), query.getPageSize());
        Sort sort = Sort.by(Sort.Direction.ASC, "batteryType", "shardingMode", "workbench");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        return new PageImpl(slurryMainReplacementDTOS, pageable, finalResultList.size());
    }

    private List<MainReplacementDBDTO> getAllReplacementDBDTO(List<String> allItemCodes, ItemCategoryEnum itemCategoryEnum) {
        return jpaQueryFactory.select(
                        Projections.fields(MainReplacementDBDTO.class,
                                qItems1.itemCode.as("materialNo"), qItems1.segment1, qItems2.segment2, qItems1.segment3,
                                qItems2.segment4, qItems2.segment6, qItems2.segment7, qItems2.segment8, qItems2.segment9,
                                qItems1.segment11, qItems2.segment13, qItems1.segment23,
                                qItems2.itemCode, qItems2.itemDesc, qStructures.alternateBomDesignator, qComponents.substituteFlag)
                )
                .from(qComponents, qStructures, qItems1, qItems2)
                .where(qComponents.disableDate.isNull())
                .where(qComponents.bomId.eq(qStructures.id))
                .where(qItems1.organizationId.eq(qStructures.organizationId))
                .where(qItems2.organizationId.eq(qStructures.organizationId))
                .where(qComponents.componentItemId.eq(qItems2.sourceItemId))
                .where(qStructures.assemblyItemId.eq(qItems1.sourceItemId))
                .where(qItems2.categorySegment5.eq(itemCategoryEnum.getDesc()))
                .where(qItems1.itemCode.in(allItemCodes))
                .where(qStructures.alternateBomDesignator.isNotEmpty())
                .where(qItems1.lifecycleState.eq("量产").or(qItems1.isTemporaryOutput.eq("是")))
                .where(qItems2.lifecycleState.eq("量产").or(qItems2.isTemporaryOutput.eq("是")))
                .fetch();
    }

    /**
     * 获取LOV关联的所有小区域编码
     * @return
     */
    private List<String> getAllCountryCellNos() {
        return LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BDM_MATERIAL_AREA).values().stream()
                .distinct()
                .map(e -> e.getLovValue())
                .map(e -> Arrays.asList(e.split("、")))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 电池需求规则-10.8/10.9规则分类 判断逻辑
     * @param allItemCodes
     * @return
     */
    private Map<String, String> cellRelationDTOMap(List<String> allItemCodes) {
        List<CellRelationDTO> cellRelationDTOList = apsFeign.queryByMaterialNoList(allItemCodes).getBody().getData();
        return cellRelationDTOList.stream().collect(Collectors.toMap(CellRelationDTO::getMaterialNo,
                ele -> {
                    String[] split = ele.getCellType().split("_");
                    if (split.length <= 6) return ele.getCellType();
                    return ele.getCellType().substring(0, findNthCommaIndex(ele.getCellType(), 6));
                }, (v1, v2) -> v1));
    }

    public static int findNthCommaIndex(String str, int n) {
        int commaIndex = -1;
        for (int i = 0; i < n; i++) {
            commaIndex = str.indexOf('_', commaIndex + 1);
            if (commaIndex == -1) {
                break;
            }
        }
        return commaIndex;
    }

    /**
     * 获取所有有效的机台KEY
     * @return
     */
    private Map<String, Set<String>> getAllWorkbenchLovMap() {
        Map<String, Set<String>> allWorkbenchMap = Maps.newHashMap();
        Map<String, LovLineDTO> allCellMachineMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.CELL_MACHINE);
        allCellMachineMap.values().forEach(ele -> {
            LovLineDTO segment7LovLineDTO = StringUtils.isNumeric(ele.getAttribute1()) ? LovUtils.get(Long.valueOf(ele.getAttribute1())) : null;
            List<String> attribute2List = Arrays.asList(ele.getAttribute2().split(","));
            if (CollectionUtils.isEmpty(attribute2List)) return;
            LovLineDTO firstSegment4LovLineDTO = StringUtils.isNumeric(attribute2List.get(0))
                    ? LovUtils.get(Long.valueOf(attribute2List.get(0))) : null;
            LovLineDTO lastSegment4LovLineDTO = StringUtils.isNumeric(attribute2List.get(attribute2List.size() - 1))
                    ? LovUtils.get(Long.valueOf(attribute2List.get(attribute2List.size() - 1))) : null;
            allWorkbenchMap.put(StringUtils.join(segment7LovLineDTO.getLovName(), "-", firstSegment4LovLineDTO.getLovName()), Sets.newHashSet(ele.getLovName()));
            allWorkbenchMap.put(StringUtils.join(segment7LovLineDTO.getLovName(), "-", lastSegment4LovLineDTO.getLovName()), Sets.newHashSet(ele.getLovName()));

            Set<String> firstSegment4Set = allWorkbenchMap.getOrDefault(firstSegment4LovLineDTO.getLovName(), Sets.newHashSet());
            firstSegment4Set.add(ele.getLovName());
            allWorkbenchMap.put(firstSegment4LovLineDTO.getLovName(), firstSegment4Set);

            Set<String> lastSegment4Set = allWorkbenchMap.getOrDefault(lastSegment4LovLineDTO.getLovName(), Sets.newHashSet());
            lastSegment4Set.add(ele.getLovName());
            allWorkbenchMap.put(lastSegment4LovLineDTO.getLovName(), lastSegment4Set);
        });
        return allWorkbenchMap;
    }

    private Map<String, String> getAllProductionAreaMap() {
        Map<String, LovLineDTO> allWorkShopMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        return allWorkShopMap.values().stream().filter(ele -> StringUtils.isNotEmpty(ele.getAttribute3()))
                .collect(Collectors.toMap(LovLineDTO::getLovName, LovLineDTO::getAttribute3, (v1, v2) -> v1));
    }

    private Map<String, List<String>> getWorkShopMap(List<MainReplacementDBDTO> mainReplacementDBDTOS) {
        List<String> alternateBomDesignators = mainReplacementDBDTOS.stream().map(MainReplacementDBDTO::getAlternateBomDesignator).distinct().collect(Collectors.toList());
        List<ErpAlternateDesignatorDTO> erpAlternateDesignatorDTOS = bomFeign.queryByAlternateDesignatorCodes(alternateBomDesignators).getBody().getData();
        return erpAlternateDesignatorDTOS.stream().collect(Collectors.groupingBy(ErpAlternateDesignatorDTO::getAlternateDesignatorCode,
                Collectors.mapping(ErpAlternateDesignatorDTO::getDescription, Collectors.toList())));
    }

    @Override
    @SneakyThrows
    public void exportScreen(MainReplacementQuery query, HttpServletResponse response) {
        Map<String, ExcelMain> sheetMap = Maps.newHashMap();

        Page<ScreenMainReplacementDTO> screenMainReplacementDTOS = this.queryScreenByPage(query);
        screenMainReplacementDTOS.getContent().forEach(replacementDTO -> {
            List<Pair<String, String>> subList = replacementDTO.getDetailDTOS().stream().map(ele -> Pair.of(ele.getWorkShop(), ele.getProductionArea())).collect(Collectors.toList());
            Map<String, String> subMap = new HashMap<>();
            replacementDTO.getDetailDTOS().forEach(ele -> {
                String key = StringUtils.join("subMap_", ele.getWorkShop(), "_", ele.getProductionArea());
                subMap.put(key, ele.getExistFlagDesc());
            });
            replacementDTO.setSubList(subList);
            replacementDTO.setSubMap(subMap);
        });

        ScreenMainReplacementDTO replacementDTO = CollectionUtils.isNotEmpty(screenMainReplacementDTOS.getContent()) ? screenMainReplacementDTOS.getContent().get(0) : new ScreenMainReplacementDTO();
        List<ExcelHead> excelHeads = buildScreenExcelHead(replacementDTO);
        List<Map<String, Object>> data = screenMainReplacementDTOS.getContent().stream().map(ScreenMainReplacementDTO::convertMap).collect(Collectors.toList());
        ExcelMain excelMain = ExcelMain.builder()
                .excelHeads(excelHeads)
                .data(data)
                .build();
        sheetMap.put("网版BOM查询导出", excelMain);

        ExcelUtil.setExportResponseHeader(response, "网版BOM查询导出");
        ExcelUtil.export(response, sheetMap);
    }

    private List<ExcelHead> buildScreenExcelHead(ScreenMainReplacementDTO dto) {
        List<ExcelHead> excelHeads = Lists.newArrayList(
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                            ImmutableList.of(
                                    ExcelHead.builder()
                                            .merge(false)
                                            .label("电池型号")
                                            .prop("batteryType")
                                            .build()
                            )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("分片方式")
                                                .prop("shardingMode")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("小区域")
                                                .prop("regionalCountry")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("主栅间距")
                                                .prop("mainGridSpace")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("单玻")
                                                .prop("singleGlass")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("机台")
                                                .prop("workbench")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("主替标识")
                                                .prop("subFlag")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("型号")
                                                .prop("model")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("网版料号")
                                                .prop("itemCode")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("料号描述")
                                                .prop("itemDescription")
                                                .build()
                                )
                        )
                        .build()
        );
        if (CollectionUtils.isNotEmpty(dto.getSubList())) {
            dto.getSubList().forEach(sub -> {
                String key = StringUtils.join("subMap_", sub.getKey(), "_", sub.getValue());
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label(Objects.nonNull(sub.getValue()) ? sub.getValue() : " ")
                                .children(ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label(Objects.nonNull(sub.getKey()) ? sub.getKey() : " ")
                                                .prop(key)
                                                .build()
                                )).build()
                );
            });
        }
        return excelHeads;
    }

    @Override
    public void sendScreenEmail(MainReplacementQuery query) {

    }

    private Page<SlurryMainReplacementDTO> mockSlurry() {
        List<SlurryMainReplacementDTO> slurryMainReplacementDTOS = Lists.newLinkedList();
        List<MainReplacementDetailDTO> detailDTOS = Lists.newLinkedList();
        SlurryMainReplacementDTO replacementDTO = SlurryMainReplacementDTO
                .builder()
                .batteryType("210R-P型")
                .shardingMode("二分片")
                .regionalCountry("小区域")
                .mainGridSpace("10.8")
                .singleGlass("单玻")
                .workbench("1号机")
                .subFlag("主")
                .vendor("帝科")
                .model("N型主栅银浆-DK81A")
                .itemCode("7A008290")
                .itemDescription("网版_普通_TOPCON_背电场网版_N型_210R_背面_细栅_网纱_3D_16BB_鱼叉头Pad1_二分片_无网结_500_9_13_3.5_15_15_192_2号机,宿迁C5,20240905")
                .build();

        Map<String, LovLineDTO> lovLineDTOMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, String> detailMap = lovLineDTOMap.values().stream().filter(ele -> StringUtils.isNotEmpty(ele.getAttribute3()))
                .collect(Collectors.toMap(LovLineDTO::getLovName, LovLineDTO::getAttribute3, (v1, v2) -> v1));

        detailMap.forEach((k, v) -> {
            MainReplacementDetailDTO detailDTO = MainReplacementDetailDTO
                    .builder()
                    .workShop(k)
                    .productionArea(v)
                    .build();
            detailDTOS.add(detailDTO);
        });
        detailDTOS.get(0).setExistFlagDesc("√");
        replacementDTO.setDetailDTOS(detailDTOS);
        slurryMainReplacementDTOS.add(replacementDTO);
        return new PageImpl<>(slurryMainReplacementDTOS);
    }
    @Override
    public Page<SlurryMainReplacementDTO> querySlurryByPage(MainReplacementQuery query) {
//        return mockSlurry();
        // 步骤一：获取当前月及次月已排产的电池计划中涉及的电池料号
        List<String> allItemCodes = bapsFeign.queryTwoMonthAllItemCodes().getBody().getData();
        if (CollectionUtils.isEmpty(allItemCodes)) {
            return new PageImpl(Collections.emptyList());
        }

        // 步骤二：根据电池料号获取该料号在各个车间的BOM信息（电池BOM是根据替代项识别车间）
        // 步骤2.1：根据电池料号数据查询一批有效数据
        List<MainReplacementDBDTO> mainReplacementDBDTOS = this.getAllReplacementDBDTO(allItemCodes, ItemCategoryEnum.SLURRY);
        if (CollectionUtils.isEmpty(mainReplacementDBDTOS)) {
            return new PageImpl(Collections.emptyList());
        }

        // 判断是否电池需求规则-10.8/10.9规则分类界面维护，用于主栅间距判断
        Map<String, String> cellRelationDTOMap = this.cellRelationDTOMap(allItemCodes);

        Map<String, Set<String>> allWorkbenchLovMap = this.getAllWorkbenchLovMap();
        List<String> countryCellNos = this.getAllCountryCellNos();
        Map<String, List<String>> workShopMap = this.getWorkShopMap(mainReplacementDBDTOS);
        Map<String, String> productionAreaMap = this.getAllProductionAreaMap();

        List<ScreenMainReplacementUnGroupDTO> unGroupDTOS = Lists.newLinkedList();
        for (MainReplacementDBDTO replacementDBDTO : mainReplacementDBDTOS) {
            String cellType = cellRelationDTOMap.getOrDefault(replacementDBDTO.getItemCode(), "");
            Set<String> workbenchs = Objects.equals("通用", replacementDBDTO.getSegment7()) ? allWorkbenchLovMap.get(replacementDBDTO.getSegment4()) : Sets.newHashSet();
            if (CollectionUtils.isEmpty(workbenchs)) {
                workbenchs = allWorkbenchLovMap.getOrDefault(StringUtils.join(replacementDBDTO.getSegment7(), "-", replacementDBDTO.getSegment4()), Sets.newHashSet());
            }
            if (CollectionUtils.isEmpty(workbenchs)) workbenchs.add(null);

            List<String> workShops = workShopMap.get(replacementDBDTO.getAlternateBomDesignator());
            Set<String> finalWorkbenchs = workbenchs;
            workShops.forEach(workShop -> {
                finalWorkbenchs.forEach(workbench -> {
                    ScreenMainReplacementUnGroupDTO buildDTO = ScreenMainReplacementUnGroupDTO
                            .builder()
                            .batteryType(StringUtils.join(replacementDBDTO.getSegment3(), "-", replacementDBDTO.getSegment1()))
                            .shardingMode(replacementDBDTO.getSegment11())
                            .regionalCountry(countryCellNos.contains(replacementDBDTO.getItemCode()) ? "小区域" : null)
                            .mainGridSpace(cellType.startsWith(CELL_TYPE_PREFIX) ? replacementDBDTO.getSegment13() : null)
                            .singleGlass(replacementDBDTO.getSegment23().contains("单玻") ? replacementDBDTO.getSegment23() : null)
                            .workbench(workbench)
                            .subFlag(Objects.equals("N", replacementDBDTO.getSubstituteFlag()) ? "主" :
                                    (Objects.equals("Y", replacementDBDTO.getSubstituteFlag()) ? "替" : null))
                            .model(replacementDBDTO.getSegment2())
                            .itemCode(replacementDBDTO.getItemCode())
                            .itemDescription(replacementDBDTO.getItemDesc())
                            .workShop(workShop)
                            .productionArea(productionAreaMap.get(workShops))
                            .vendor(replacementDBDTO.getSegment8())
                            .build();
                    unGroupDTOS.add(buildDTO);
                });
            });
        }

        List<String> detailKeyList = unGroupDTOS.stream().map(ele -> StringUtils.join(ele.getWorkShop(), "-", ele.getProductionArea())).distinct().collect(Collectors.toList());
        Map<String, List<ScreenMainReplacementUnGroupDTO>> unGroupDTOMap = unGroupDTOS.stream().collect(Collectors.groupingBy(ScreenMainReplacementUnGroupDTO::group));


        List<SlurryMainReplacementDTO> resultList = Lists.newLinkedList();
        unGroupDTOMap.forEach((k, v) -> {
            SlurryMainReplacementDTO replacementDTO = new SlurryMainReplacementDTO();
            BeanUtils.copyProperties(v.get(0), replacementDTO);
            List<String> spliteDetailKeyList = v.stream().map(ele -> StringUtils.join(ele.getWorkShop(), "-", ele.getProductionArea())).distinct().collect(Collectors.toList());

            List<MainReplacementDetailDTO> detailDTOS = detailKeyList.stream().map(key -> {
                String[] split = key.split("-");
                return MainReplacementDetailDTO
                        .builder()
                        .workShop(split.length >= 1 ? split[0] : null)
                        .productionArea(split.length >= 2 ? split[1] : null)
                        .existFlagDesc(spliteDetailKeyList.contains(key) ? "√" : null)
                        .build();
            }).collect(Collectors.toList());
            replacementDTO.setDetailDTOS(detailDTOS);
            resultList.add(replacementDTO);
        });


        List<SlurryMainReplacementDTO> finalResultList = Lists.newLinkedList();
        finalResultList.addAll(resultList);
        if (StringUtils.isNotEmpty(query.getBatteryType()) || StringUtils.isNotEmpty(query.getWorkbench())
                || StringUtils.isNotEmpty(query.getSubFlag()) || StringUtils.isNotEmpty(query.getItemCode())
                || StringUtils.isNotEmpty(query.getWorkShop())) {
            finalResultList = finalResultList.stream().filter(ele -> {
                Boolean ignoreFlag = true;
                if (StringUtils.isNotEmpty(query.getBatteryType())) {
                    ignoreFlag = Objects.equals(ele.getBatteryType(), query.getBatteryType() + "型");
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getWorkbench())) {
                    ignoreFlag = Objects.equals(ele.getWorkbench(), query.getWorkbench());
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getSubFlag())) {
                    String subFlag = Objects.equals("main", query.getSubFlag()) ? "主" : "替";
                    ignoreFlag = Objects.equals(ele.getSubFlag(), subFlag);
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getItemCode())) {
                    ignoreFlag = Objects.equals(ele.getItemCode(), query.getItemCode());
                }
                if (!ignoreFlag) return ignoreFlag;

                if (StringUtils.isNotEmpty(query.getWorkShop())) {
                    ignoreFlag = ele.getDetailDTOS().stream().filter(detailDTO -> Objects.equals(detailDTO.getWorkShop(), query.getWorkShop())
                            && StringUtils.isNotEmpty(detailDTO.getExistFlagDesc())).count() > 0;
                }
                return ignoreFlag;
            }).collect(Collectors.toList());
        }

        finalResultList.sort(Comparator.comparing(SlurryMainReplacementDTO::getBatteryType, Comparator.nullsLast(String::compareTo))
                .thenComparing(SlurryMainReplacementDTO::getShardingMode, Comparator.nullsLast(String::compareTo))
                .thenComparing(SlurryMainReplacementDTO::getWorkbench, Comparator.nullsLast(String::compareTo)));
        List<SlurryMainReplacementDTO> slurryMainReplacementDTOS = PageUtil.startPage(finalResultList, query.getPageNumber(), query.getPageSize());
        Sort sort = Sort.by(Sort.Direction.ASC, "batteryType", "shardingMode", "workbench");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        return new PageImpl(slurryMainReplacementDTOS, pageable, finalResultList.size());
    }

    @Override
    @SneakyThrows
    public void exportSlurry(MainReplacementQuery query, HttpServletResponse response) {
        Map<String, ExcelMain> sheetMap = Maps.newHashMap();

        Page<SlurryMainReplacementDTO> slurryMainReplacementDTOS = this.querySlurryByPage(query);
        slurryMainReplacementDTOS.getContent().forEach(replacementDTO -> {
            List<Pair<String, String>> subList = replacementDTO.getDetailDTOS().stream().map(ele -> Pair.of(ele.getWorkShop(), ele.getProductionArea())).collect(Collectors.toList());
            Map<String, String> subMap = new HashMap<>();
            replacementDTO.getDetailDTOS().forEach(ele -> {
                String key = StringUtils.join("subMap_", ele.getWorkShop(), "_", ele.getProductionArea());
                subMap.put(key, ele.getExistFlagDesc());
            });
            replacementDTO.setSubList(subList);
            replacementDTO.setSubMap(subMap);
        });

        SlurryMainReplacementDTO replacementDTO = CollectionUtils.isNotEmpty(slurryMainReplacementDTOS.getContent()) ? slurryMainReplacementDTOS.getContent().get(0) : new SlurryMainReplacementDTO();
        List<ExcelHead> excelHeads = buildSlurryExcelHead(replacementDTO);
        List<Map<String, Object>> data = slurryMainReplacementDTOS.getContent().stream().map(SlurryMainReplacementDTO::convertMap).collect(Collectors.toList());
        ExcelMain excelMain = ExcelMain.builder()
                .excelHeads(excelHeads)
                .data(data)
                .build();
        sheetMap.put("浆料BOM查询导出", excelMain);

        ExcelUtil.setExportResponseHeader(response, "浆料BOM查询导出");
        ExcelUtil.export(response, sheetMap);
    }


    private List<ExcelHead> buildSlurryExcelHead(SlurryMainReplacementDTO dto) {
        List<ExcelHead> excelHeads = Lists.newArrayList(
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("电池型号")
                                                .prop("batteryType")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("分片方式")
                                                .prop("shardingMode")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("小区域")
                                                .prop("regionalCountry")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("主栅间距")
                                                .prop("mainGridSpace")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("单玻")
                                                .prop("singleGlass")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("机台")
                                                .prop("workbench")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("主替标识")
                                                .prop("subFlag")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("厂商")
                                                .prop("vendor")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("型号")
                                                .prop("model")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("浆料料号")
                                                .prop("itemCode")
                                                .build()
                                )
                        )
                        .build(),
                ExcelHead.builder()
                        .merge(false)
                        .label(" ")
                        .prop(" ")
                        .children(
                                ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label("料号描述")
                                                .prop("itemDescription")
                                                .build()
                                )
                        )
                        .build()
        );
        if (CollectionUtils.isNotEmpty(dto.getSubList())) {
            dto.getSubList().forEach(sub -> {
                String key = StringUtils.join("subMap_", sub.getKey(), "_", sub.getValue());
                excelHeads.add(
                        ExcelHead.builder()
                                .merge(false)
                                .label(Objects.nonNull(sub.getValue()) ? sub.getValue() : " ")
                                .children(ImmutableList.of(
                                        ExcelHead.builder()
                                                .merge(false)
                                                .label(Objects.nonNull(sub.getKey()) ? sub.getKey() : " ")
                                                .prop(key)
                                                .build()
                                )).build()
                );
            });
        }
        return excelHeads;
    }

    @Override
    public void sendSlurryEmail(MainReplacementQuery query) {

    }
}