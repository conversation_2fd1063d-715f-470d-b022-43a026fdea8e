package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateWorkshopDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateWorkshopQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateWorkshopSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
public interface BatteryScreenPlateWorkshopService {
    /**
     * 分页获取电池类型动态属性-网版
     *
     * @param query 查询对象
     * @return 电池类型动态属性-网版分页对象
     */
    Page<BatteryScreenPlateWorkshopDTO> queryByPage(BatteryScreenPlateWorkshopQuery query);

    BatteryScreenPlateWorkshopDTO getScreenWorkshopByHead(MaterielMatchHeaderDTO materielMatchHeaderDTO);


    /**
     * 根据主键获取电池类型动态属性-网版详情
     *
     * @param id 主键
     * @return 电池类型动态属性-网版详情
     */
    BatteryScreenPlateWorkshopDTO queryById(Long id);

    /**
     * 保存或更新电池类型动态属性-网版
     *
     * @param saveDTO 电池类型动态属性-网版保存对象
     * @return 电池类型动态属性-网版对象
     */
    BatteryScreenPlateWorkshopDTO save(BatteryScreenPlateWorkshopSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型动态属性-网版
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatteryScreenPlateWorkshopQuery query, HttpServletResponse response);

    void batchUpdate(BatteryScreenPlateWorkshopQuery query);


    List<BatteryScreenPlateWorkshopDTO> queryAllByCache();
}

