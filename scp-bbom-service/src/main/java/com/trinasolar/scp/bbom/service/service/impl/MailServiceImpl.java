package com.trinasolar.scp.bbom.service.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.trinasolar.scp.bbom.domain.dto.message.MsgReceiverDto;
import com.trinasolar.scp.bbom.service.feign.MessageFeign;
import com.trinasolar.scp.bbom.service.service.MailService;
import freemarker.template.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.StringWriter;
import java.util.List;

@Service("mailService")
public class MailServiceImpl implements MailService {
    @Autowired
    private MessageFeign messageFeign;

    /**
     * @param toList       邮箱列表
     * @param mailTemplate 邮件模版
     * @param subject      标题
     * @param content      数据
     * @param fileJson     附件传参 jsonArray.toJSONString()
     *                     JSONArray jsonArray = new JSONArray();
     *                     JSONObject jsonObject = new JSONObject();
     *                     jsonObject.put("fileName",scheduleEmailTaskLine.getFileName());
     *                     jsonObject.put("fileUrl",scheduleEmailTaskLine.getFileUrl());
     *                     jsonArray.add(jsonObject);
     * @return
     * @throws Exception
     */
    @Override
    public boolean send(List<String> toList, String mailTemplate, String subject, Object content, String fileJson) throws Exception {
        MsgReceiverDto msgReceiverDto = new MsgReceiverDto();
        Template template = getTemplate(mailTemplate);
        StringWriter writer = new StringWriter();
        template.process(content, writer);

        msgReceiverDto.setAppCode("20");
        msgReceiverDto.setTunnelCode("1001");
        msgReceiverDto.setSendMethod("EMAIL");
        msgReceiverDto.setTitle(subject);
        msgReceiverDto.setContent(writer.toString());
        msgReceiverDto.setRecipientNo(String.join(",", toList));
        msgReceiverDto.setFileJson(fileJson);
        Object sendMsg = messageFeign.sendMsg(msgReceiverDto);
        Gson gson = new Gson();
        String code = gson.fromJson(sendMsg.toString(), JsonObject.class).getAsJsonObject("RESPONSE").getAsJsonObject("RETURN_DATA").get("statusCodeValue").toString();
        if ("200".equals(code)) {
            return true;
        }
        return false;
    }

}
