package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ConversionCoefficientMwDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpOperatRouteDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalAttrMapViewDTO;

import java.util.List;
import java.util.Map;

/**
 * BomFeign 调用 服务
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
public interface BomService {
    List<ExternalAttrMapViewDTO> findBySrcCategorySegment4(String srcCategorySegment4);

    /**
     * 电池MW系数 调用接口 MOCK数据
     *
     * @return
     */
    List<ConversionCoefficientMwDTO> getBatteryMWQty();

    /**
     * 电池目标良率 调用接口 MOCK数据
     *
     * @return
     */
    List<ConversionCoefficientMwDTO> getBatteryEfficiencyQty();

    ErpAlternateDesignatorDTO findErpAlternateDesignator(Long parseLong, String designator);

    ErpAlternateDesignatorDTO getErpAlternateDesignatorDTOByWorkshop(String workshop);

    ErpOperatRouteDTO findBy5AAndAlternateRoutingDesignator(String itemCode, String alternateBomDesignator);

    Map<String, ErpOperatRouteDTO> getErpOperatRouteList();
}
