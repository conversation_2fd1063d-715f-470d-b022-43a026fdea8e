package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ComparisonChain;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.*;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.query.*;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchHeaderSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.DateUtil;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.BAPSFeign;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.repository.ComponentsRepository;
import com.trinasolar.scp.bbom.service.repository.ItemsRepository;
import com.trinasolar.scp.bbom.service.repository.MaterielMatchHeaderRepository;
import com.trinasolar.scp.bbom.service.repository.MaterielMatchLineRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.util.AttrUtil;
import com.trinasolar.scp.bbom.service.util.ExpressUtils;
import com.trinasolar.scp.bbom.service.util.StringTools;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.*;
import jodd.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.data.redis.core.RedisKeyValueTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Slf4j
@Service("materielMatchHeaderService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class MaterielMatchHeaderServiceImpl implements MaterielMatchHeaderService {
    public static final String none = "无";

    public static final String BBOM_PRODUCTION_ATTR = "BBOM_PRODUCTION_ATTR";

    public static final String BATTR_TYPE_011 = "BATTR_TYPE_011";

    public static final String ATTR_TYPE_043 = "ATTR_TYPE_043";

    public static final String ATTR_120007 = "ATTR_120007";

    private static final QMaterielMatchHeader qMaterielMatchHeader = QMaterielMatchHeader.materielMatchHeader;

    private static final QComponents qComponents = QComponents.components;

    private static final QStructures qStructures = QStructures.structures;

    private static final Map<String, String> itemFixedAttributes;

    private static final QItems qItems = QItems.items;

    private final static String SEGMENT2 = "正电极网版";

    private final static String ZDJWB = "正电极网版";

    private final static String ZDCWB = "正电场网版";
    private final static String BDJWB = "背电极网版";

    private final static String BDCWB = "背电场网版";

    private static final String ENG_MFG = "ENG/MFG";

    private static final String CATEGORY = "category";

    //specialArea
    private static final String SPECIAL_AREA = "specialArea";

    private static final String YF = "研发";

    private static final String LC = "量产";

    static {
        itemFixedAttributes = new HashMap<>();
        itemFixedAttributes.put("segment2", "Q1");
        itemFixedAttributes.put("segment22", "A");
        itemFixedAttributes.put("segment27", none);
        itemFixedAttributes.put("segment26", none);
        itemFixedAttributes.put("segment25", none);
    }

    private final AttrUtil attrUtil;

    private final MaterielMatchHeaderDEConvert convert;

    private final MaterielMatchLineDEConvert materielMatchLineDEConvert;

    private final MaterielMatchHeaderRepository repository;

    private final MaterielMatchLineRepository repositoryLine;

    private final ItemsService itemsService;

    private final BatteryTypeMainService batteryTypeMainService;

    private final RuleHeaderService ruleHeaderService;

    private final BatterySiliconWaferService batterySiliconWaferService;

    private final BatteryScreenPlateService batteryScreenPlateService;

    private final StructuresService structuresService;

    private final BomFeign bomFeign;

    private final JPAQueryFactory jpaQueryFactory;

    private final ComponentsService componentsService;

    private final BAPSFeign bapsFeign;

    private final ComponentsRepository componentsRepository;

    private final ComponentsDEConvert componentsDEConvert;

    private final BatteryTypeMainDEConvert batteryTypeMainDEConvert;

    private final MailService mailService;

    private final BatteryScreenPlateWorkshopService batteryScreenPlateWorkshopService;

    private final AMaterielMatchRuleService aMaterielMatchRuleService;

    private final SpecialCellMatchRuleService specialCellMatchRuleService;

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    @Lazy
    private MaterielMatchLineService materielMatchLineService;

    @Autowired
    @Lazy
    private MaterielMatchHeaderService materielMatchHeaderService;

    @Autowired
    BomService bomService;

    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private ItemsRepository itemsRepository;

    @Autowired
    private BatteryTypeProduceService batteryTypeProduceService;

    private final MaterielMatchQueryDEConvert materielMatchQueryDEConvert;

    @Autowired
    @Qualifier("asyncTaskExecutor")
    ExecutorService asyncTaskExecutor;

    @Value("${matchItem.dynamicKey}")
    public String matchItemDynamicKey;

    //itemSource：5A和itemNew：7A属性比对过滤
    private static Boolean checkByScreenItem(ItemsDTO itemSource, ItemsDTO screenItem) {
        Boolean flag1 = true;
        //
        if (StringUtils.isEmpty(screenItem.getSegment17()) || "无".equals(screenItem.getSegment17())) {
            flag1 = true;
        } else {
            flag1 = noneVerify2(flag1, itemSource.getSegment16(), screenItem.getSegment17());
        }
        // 如果是正电极
        if (SEGMENT2.equals(screenItem.getSegment2())) {
            if (StringUtils.isEmpty(screenItem.getSegment22()) || "无".equals(screenItem.getSegment22())) {
                flag1 = true;
            } else {
                flag1 = noneVerify2(flag1, itemSource.getSegment15(), screenItem.getSegment22());
            }
        }
        return flag1;
    }

    private static Boolean checkBackElectrodeItemsDTO(ItemsDTO itemSource, ItemsDTO itemNew) {
        Boolean flag1 = true;
        if (LovHeaderCodeConstant.N_MODEL.equals(itemSource.getSegment1())) {
            flag1 = noneVerifyBackElectrode(flag1, itemSource.getSegment17(), itemNew.getSegment22());
        }
        return flag1;
    }

    //数据库DTO的实体值、query对象的值
    private static boolean noneVerify2(Boolean flag, String value, String segment) {
        if (!flag) return false;
        return StringUtils.isNotEmpty(value) && value.equals(segment);
    }

    //背电极网版 网版副栅数：segment22与电池片料号segment17对应
    private static boolean noneVerifyBackElectrode(Boolean flag, String value, String segment) {
        if (!flag) return false;
        return StringUtils.isNotEmpty(value) && value.contains(segment);
    }

    private static boolean isFlag(MaterielMatchHeaderDTO materielMatchHeaderDTO, List<ExpressRuleLineDTO> value, boolean flag) {
        if (null != value.get(0).getEffectiveStartDate() && null != value.get(0).getEffectiveEndDate()) {
            if (value.get(0).getEffectiveStartDate().isBefore(materielMatchHeaderDTO.getCreatedTime().toLocalDate())
                    && value.get(0).getEffectiveEndDate().isAfter(materielMatchHeaderDTO.getCreatedTime().toLocalDate())) {
                flag = true;
            }
        } else if (null != value.get(0).getEffectiveStartDate() && value.get(0).getEffectiveStartDate().isBefore(materielMatchHeaderDTO.getCreatedTime().toLocalDate())) {
            flag = true;
        } else if (null != value.get(0).getEffectiveEndDate() && value.get(0).getEffectiveEndDate().isAfter(materielMatchHeaderDTO.getCreatedTime().toLocalDate())) {
            flag = true;
        } else if (null == value.get(0).getEffectiveStartDate() && null == value.get(0).getEffectiveEndDate()) {
            flag = true;
        }
        return flag;
    }

    private static MaterielMatchHeaderQuery getMaterielMatchHeaderQuery(Map.Entry<String, List<CellPlanLineDTO>> entry) {
        CellPlanLineDTO firstDto = entry.getValue().get(0);
        //根据维度查询bbom_materiel_match_header 是否存在
        MaterielMatchHeaderQuery headerQuery = new MaterielMatchHeaderQuery();
        headerQuery.setBasePlace(StringUtils.isNotEmpty(firstDto.getBasePlace()) ? firstDto.getBasePlace() : "无");
        headerQuery.setWorkshop(StringUtils.isNotEmpty(firstDto.getWorkshop()) ? firstDto.getWorkshop() : "无");
        headerQuery.setWorkunit(StringUtils.isNotEmpty(firstDto.getWorkunit()) ? firstDto.getWorkunit() : "无");
        headerQuery.setBatteryType(StringUtils.isNotEmpty(firstDto.getCellsType()) ? firstDto.getCellsType() : "无");
        headerQuery.setBatteryTypeId(firstDto.getCellsTypeId());
        headerQuery.setHTrace(StringUtils.isNotEmpty(firstDto.getHTrace()) ? firstDto.getHTrace() : "无");
        headerQuery.setAesthetics(StringUtils.isNotEmpty(firstDto.getAesthetics()) ? firstDto.getAesthetics() : "无");
        headerQuery.setTransparentDoubleGlass(StringUtils.isNotEmpty(firstDto.getTransparentDoubleGlass()) ? firstDto.getTransparentDoubleGlass() : "无");
        headerQuery.setSpecialArea(StringUtils.isNotEmpty(firstDto.getRegionalCountry()) ? firstDto.getRegionalCountry() : "无");
        headerQuery.setSpecialAreaId(firstDto.getRegionalCountryId());
        headerQuery.setPcsSourceType(StringUtils.isNotEmpty(firstDto.getCellSource()) ? firstDto.getCellSource() : "无");
        headerQuery.setPcsSourceLevel(StringUtils.isNotEmpty(firstDto.getWaferGrade()) ? firstDto.getWaferGrade() : "无");
        headerQuery.setBatteryManufacturer(StringUtils.isNotEmpty(firstDto.getCellMfrs()) ? firstDto.getCellMfrs() : "无");
        headerQuery.setIsSpecialRequirements(StringUtils.isNotEmpty(firstDto.getIsSpecialRequirement()) ? firstDto.getIsSpecialRequirement() : "无");
        headerQuery.setSiliconMaterialManufacturer(StringUtils.isNotEmpty(firstDto.getSiMfrs()) ? firstDto.getSiMfrs() : "无");
        headerQuery.setScreenManufacturer(StringUtils.isNotEmpty(firstDto.getScreenPlateMfrs()) ? firstDto.getScreenPlateMfrs() : "无");
        headerQuery.setSilverSlurryManufacturer(StringUtils.isNotEmpty(firstDto.getSilverPulpMfrs()) ? firstDto.getSilverPulpMfrs() : "无");
        headerQuery.setLowResistance(StringUtils.isNotEmpty(firstDto.getLowResistance()) ? firstDto.getLowResistance() : "无");
        headerQuery.setDemandPlace(StringUtils.isNotEmpty(firstDto.getDemandBasePlace()) ? firstDto.getDemandBasePlace() : "无");
        headerQuery.setProcessCategory(StringUtils.isNotEmpty(firstDto.getProcessCategory()) ? firstDto.getProcessCategory() : "无");
        headerQuery.setMonth(firstDto.getMonth());
        headerQuery.setProductionGrade(StringUtils.isNotEmpty(firstDto.getProductionGrade()) ? firstDto.getProductionGrade() : "无");
        headerQuery.setLine(firstDto.getNumberLine());
        headerQuery.setIsOversea(firstDto.getIsOversea());
        headerQuery.setIsOverseaId(firstDto.getIsOverseaId());
        headerQuery.setVersion(firstDto.getFinalVersion());
        headerQuery.setOldMonth(firstDto.getOldMonth());
        headerQuery.setMainGridSpace(firstDto.getMainGridSpace());
        headerQuery.setSpecialOrder(firstDto.getSpecialOrder());
        headerQuery.setManufactureProcess(firstDto.getManufactureProcess());
        return headerQuery;
    }

    @Override
    public Page<MaterielMatchHeaderDTO> queryByPage(MaterielMatchHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<MaterielMatchHeader> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<MaterielMatchHeaderDTO> queryByAll(MaterielMatchHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    private void buildWhere(BooleanBuilder booleanBuilder, MaterielMatchHeaderQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qMaterielMatchHeader.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qMaterielMatchHeader.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qMaterielMatchHeader.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qMaterielMatchHeader.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getSpecialArea())) {
            booleanBuilder.and(qMaterielMatchHeader.specialArea.eq(query.getSpecialArea()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qMaterielMatchHeader.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getPcsSourceType())) {
            booleanBuilder.and(qMaterielMatchHeader.pcsSourceType.eq(query.getPcsSourceType()));
        }
        if (StringUtils.isNotEmpty(query.getPcsSourceLevel())) {
            booleanBuilder.and(qMaterielMatchHeader.pcsSourceLevel.eq(query.getPcsSourceLevel()));
        }
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirements())) {
            booleanBuilder.and(qMaterielMatchHeader.isSpecialRequirements.eq(query.getIsSpecialRequirements()));
        }
        if (StringUtils.isNotEmpty(query.getScreenManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.screenManufacturer.eq(query.getScreenManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconMaterialManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.siliconMaterialManufacturer.eq(query.getSiliconMaterialManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.batteryManufacturer.eq(query.getBatteryManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getSilverSlurryManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.silverSlurryManufacturer.eq(query.getSilverSlurryManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qMaterielMatchHeader.lowResistance.eq(query.getLowResistance()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferPurchaseMethod())) {
            booleanBuilder.and(qMaterielMatchHeader.siliconWaferPurchaseMethod.eq(query.getSiliconWaferPurchaseMethod()));
        }
        if (StringUtils.isNotEmpty(query.getDemandPlace())) {
            booleanBuilder.and(qMaterielMatchHeader.demandPlace.like("%" + query.getDemandPlace() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qMaterielMatchHeader.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qMaterielMatchHeader.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qMaterielMatchHeader.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qMaterielMatchHeader.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getProcessCategory())) {
            booleanBuilder.and(qMaterielMatchHeader.processCategory.eq(query.getProcessCategory()));
        }
        //产品等级
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qMaterielMatchHeader.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qMaterielMatchHeader.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getItemDesc())) {
            booleanBuilder.and(qMaterielMatchHeader.itemDesc.eq(query.getItemDesc()));
        }
        if (query.getMonth() != null) {
            booleanBuilder.and(qMaterielMatchHeader.month.eq(query.getMonth()));
        }
        if (query.getLine() != null) {
            booleanBuilder.and(qMaterielMatchHeader.line.eq(query.getLine()));
        }
    }

    @Override
    public MaterielMatchHeaderDTO queryById(Long id) {
        MaterielMatchHeader queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    /**
     * 料号匹配
     *
     * @param matchId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void matchItem(Long matchId, Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        doSingleMatch(matchId, matchLineaAllMap);
    }

    /**
     * https://bizdevops.trinasolar.com/console/vteam/z82bfd/twDemand?vmode=table&id=3f5ca66c23544fd695d25b87af61ee8a&
     * p2_906 料号匹配考虑兼容特殊属性电池兼容
     * @param itemsDTOS
     */
    private List<ItemsDTO> filterInComponents(List<ItemsDTO> itemsDTOS) {
        if (CollectionUtils.isEmpty(itemsDTOS)) {
            return Collections.emptyList();
        }
        List<Long> assemblyItemIds = itemsDTOS.stream().map(ItemsDTO::getSourceItemId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(assemblyItemIds)) {
            return Collections.emptyList();
        }
        List<Long> effectAssemblyItemIds = jpaQueryFactory
                .selectDistinct(qStructures.assemblyItemId)
                .from(qComponents)
                .innerJoin(qStructures).on(qComponents.bomId.eq(qStructures.id))
                .where(qStructures.assemblyItemId.in(assemblyItemIds))
                .where(qStructures.isDeleted.eq(0))
                .where(qComponents.isDeleted.eq(0))
                .distinct()
                .fetch();
        return itemsDTOS.stream().filter(ele -> effectAssemblyItemIds.contains(ele.getSourceItemId())).collect(Collectors.toList());
    }

    private void doSingleMatch(Long matchId, Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        log.info("二期料号匹配 单个匹配 ...start matchId：{}", matchId);
        /***************************************************数据准备START*************************************************************/
        //根据matchid获取匹配头信息
        MaterielMatchHeaderDTO materielMatchHeaderDTO = queryById(matchId);
        //获取bom替代项集合
        Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap = getBomDesigntorByMatchId(materielMatchHeaderDTO.getWorkshop());
        // 获取规则
        Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList = getExpressRuleLineList();
        if (log.isDebugEnabled()) {
            log.debug("expressRuleLineList:{}", expressRuleLineList);
        }
        //获取dp属性和item属性对应关系
        Map<String, String> dpColumnsMap = getDpColAndItemColMap();
        //获取规则 转换
        Map<String, String> dpColumnsTransScriptMap = getDpColAndTransScriptMap();
        //获取Active、82L、自产电池片的物料集合
        List<ItemsDTO> itemsDTOList = itemsService.queryByMatchingAll();
        //获取电池类型静态属性
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryBatteryCodeTypeAllByBatteryName(materielMatchHeaderDTO.getBatteryType());
        //匹配物料过滤集合
        List<ItemsDTO> itemsDTOS = matchItemDTOS(materielMatchHeaderDTO, expressRuleLineList, itemsDTOList, batteryTypeMainDTO, dpColumnsMap, dpColumnsTransScriptMap);
        //根据匹配头查询匹配行数据 查询范围itemCode is null 、拆分标识[splitFlag]不为N、料号指定标识[handWorkFlag]不为Y
        List<MaterielMatchLineDTO> matchLineDTOListAll = getNonMatchMaterielMatchLines(matchId, "");

        //获取工艺路线
        Map<String, ErpOperatRouteDTO> erpOperatRouteMap = getErpOperatRouteList();
        //获取Structures 所有数据
        Map<String, StructuresDTO> structuresDTOMap = queryListByStructures();
        //获取应用硅片属性数据
        Map<String, AttrTypeLineDTO> siliconWaferAttrType = attrUtil.queryAttrTypeLinesByHeaderCode("ATTR_TYPE_018")
                .stream().collect(Collectors.toMap(AttrTypeLineDTO::getAttrCode, Function.identity(), (k1, k2) -> k1));

        //获取所有需要继承的lov反射后数据 批量成批匹配优化了入参 这里为了单次匹配进行兼容
        if (MapUtil.isEmpty(matchLineaAllMap)) {
            matchLineaAllMap = getAllLineMap();
        }

        // 第一层进行料号过滤，第二层进行料号匹配行关联物料信息
        try {
            matchLineDTOListAll = this.convertMatchHandle(matchId, itemsDTOS, matchLineDTOListAll, structuresDTOMap, designatorMap);
        } catch (Exception ex) {
            log.info("二期料号匹配 第一次 doSingleMatch matchId：{} exception {}", matchId, ex.getMessage());
        }
        Integer itemsDTOSCount = matchLineDTOListAll.stream().filter(ele -> !CollectionUtils.isEmpty(ele.getItemsDTOS()))
                .map(ele -> ele.getItemsDTOS().size()).reduce(Integer::sum).orElse(0);
        Boolean inheritFlag = false;
        if (itemsDTOSCount == 0) {
            // 第一轮料号过滤后没有命中到料号 则用继承进行匹配，继承匹配后则不再执行后续拆分和不拆分对应的匹配逻辑（临时解决方案，目前bbom_materiel_match_line表的四个字段不参与继承，后续需要改造整体流程 先拆分 后续再匹配继承）
            inheritFlag = this.handleForInherit(matchLineDTOListAll, matchLineaAllMap, materielMatchHeaderDTO, itemsDTOS, designatorMap, erpOperatRouteMap, structuresDTOMap);
            if (!inheritFlag) {
                // 如果没有正常匹配到，则进行特殊片源匹配规则进行匹配 - 电池等级为Q1的电池料号，再走一次料号匹配行关联物料信息
                itemsDTOS = this.convertMatchWithoutItemDTOSForQ1(materielMatchHeaderDTO, expressRuleLineList, batteryTypeMainDTO, dpColumnsMap, dpColumnsTransScriptMap, itemsDTOList);
                matchLineDTOListAll = this.convertMatchHandle(matchId, itemsDTOS, matchLineDTOListAll, structuresDTOMap, designatorMap);
            }
        }
        if (!inheritFlag) {
            //过滤获取已拆分的
            List<MaterielMatchLineDTO> matchLineDTOListBySpilitIsY = matchLineDTOListAll.stream().filter(split -> "Y".equals(split.getSplitFlag())).collect(Collectors.toList());
            //过滤获取未拆分的
            List<MaterielMatchLineDTO> matchLineDTOListBySpilitIsNull = matchLineDTOListAll.stream().filter(split -> StringUtils.isEmpty(split.getSplitFlag())).collect(Collectors.toList());
            //2024.12.27料号匹配逻辑修改-校验5A料号是否在电池车间存在有效BOM
            // 2025/04/02 仲华要求BOM无数据不过滤 需特殊处理 bbom_components
            if (Objects.equals("plan", materielMatchHeaderDTO.getPlanType())) {
                itemsDTOS = this.filterByValidBom(itemsDTOS,matchLineDTOListAll);
            }

            /***************************************************数据准备END*************************************************************/
            //拆分标识为Y的或者为空的且5A料号未指定 进行料号匹配操作
            operationBySplit(materielMatchHeaderDTO, designatorMap, erpOperatRouteMap, structuresDTOMap, matchLineDTOListBySpilitIsY, matchLineDTOListBySpilitIsNull, siliconWaferAttrType, itemsDTOS, expressRuleLineList,matchLineaAllMap);
        } else {
            log.info("二期料号匹配 第二轮命中料号 matchId：{} inheritFlag：{}", matchId, inheritFlag);
        }

        //匹配头备注置空
        materielMatchHeaderDTO.setRemark("");
        //匹配头信息保存
        repository.save(convert.toEntity(materielMatchHeaderDTO));
        log.info("二期料号匹配 单个匹配 ...end：{}", matchId);
    }

    /**
     * 继承逻辑处理
     * @param matchLineDTOListAll
     * @param matchLineaAllMap
     * @param materielMatchHeaderDTO
     * @param itemsDTOS
     * @param designatorMap
     * @param erpOperatRouteMap
     * @param structuresDTOMap
     */
    private Boolean handleForInherit(List<MaterielMatchLineDTO> matchLineDTOListAll,
                                     Map<String,List<MaterielMatchLine>> matchLineaAllMap,
                                     MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                     List<ItemsDTO> itemsDTOS,
                                     Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                     Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                                     Map<String, StructuresDTO> structuresDTOMap) {
        AtomicBoolean inheritFlag = new AtomicBoolean(false);
        matchLineDTOListAll.forEach(dto -> {
            String inheritItemCode = materielMatchLineService.getInheritItemCode(dto,matchLineaAllMap);
            if(StringUtils.isNotBlank(inheritItemCode)){
                //继承并中止料号匹配
                dto.setItemCode(inheritItemCode);
                dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
                dto.setItemMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
                ItemsDTO itemsDTO = itemsService.findOneByItemCode(inheritItemCode);
                if(Objects.isNull(itemsDTO)){
                    itemsDTO = new ItemsDTO();
                }
                //BOM替代项
                String alternateDesignatorCode = getAlternateDesignatorCode(materielMatchHeaderDTO.getWorkshop(), itemsDTO.getSourceItemId(), designatorMap, structuresDTOMap);
                dto.setAlternateBomDesignator(alternateDesignatorCode);
                dto.setMatchCodes(getMatchCodes(materielMatchHeaderDTO, itemsDTOS, designatorMap, erpOperatRouteMap, structuresDTOMap));
                dto.setItemDesc(itemsDTO.getItemDesc());
                ErpOperatRouteDTO operatRouteDTO = bomService.findBy5AAndAlternateRoutingDesignator(inheritItemCode, dto.getAlternateBomDesignator());
                if (operatRouteDTO == null) {
                    dto.setRoute("否");
                } else {
                    dto.setRoute("是");
                }
                dto.setCertifiedModels("");
                inheritFlag.set(true);
            }
        });
        if (inheritFlag.get()) {
            // 删除原始数据并保存新数据
            materielMatchHeaderService.saveMatch(materielMatchHeaderDTO, matchLineDTOListAll);
        } else {
            log.info("二期料号匹配 第一轮命中料号 matchId：{} inheritFlag：{}", materielMatchHeaderDTO.getId(), inheritFlag);
        }
        return inheritFlag.get();
    }

    @Override
    public Map<String,List<MaterielMatchLine>> getAllLineMap(){
        Map<String,List<MaterielMatchLine>> matchLineaAllMap = new HashMap<>();
        String sql = "select * from bbom_materiel_match_line where match_status = 'APPOINT_MATCH' and item_code is not null " +
                "and old_month >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y%m')";
        List<MaterielMatchLine> matchLineaAllList =entityManager.createNativeQuery(sql, MaterielMatchLine.class).getResultList();
        Map<String, LovLineDTO> inheritLovHashMap = LovUtils.getAllByHeaderCode("BBOM_CELL_MATCH_RULE");
        TreeMap<String, LovLineDTO> inheritLovMap = new TreeMap<>(inheritLovHashMap);
        matchLineaAllList.stream().forEach(matchItem ->{
            StringBuilder matchKey = new StringBuilder();
            StringBuilder dynamicKey = new StringBuilder();
            for(String key : inheritLovMap.keySet()){
                LovLineDTO value = inheritLovMap.get(key);
                if("Y".equals(value.getEnableFlag())){
                    String upCase = StrUtil.toCamelCase(value.getLovName());
                    if(StringUtils.isNotBlank(value.getAttribute1())){
                        upCase = value.getAttribute1();
                    }
                    Object matchItemValue = StringTools.getValueByLineEntity(matchItem, upCase);
                    String matchItemValueString = "";
                    if(Objects.nonNull(matchItemValue)){
                        matchItemValueString = matchItemValue.toString();
                    }
                    matchKey.append(matchItemValueString);
                    // p2_2444 电池BOM-料号匹配继承逻辑优化 增加动态属性判断
                    if(StringUtils.isNotEmpty(value.getAttribute1()) && matchItemDynamicKey.equals(value.getAttribute1())){
                        dynamicKey.append(matchItemValueString);
                    }
                }
            }
            List<MaterielMatchLine> lineList = matchLineaAllMap.get(matchKey.toString());
            if(CollectionUtils.isEmpty(lineList)){
                lineList = Lists.newArrayList();
            }
            lineList.add(matchItem);
            matchLineaAllMap.put(matchKey.toString(),lineList);

            // p2_2444 电池BOM-料号匹配继承逻辑优化 增加动态属性判断
            if (StringUtils.isNotEmpty(dynamicKey)) {
                String replaceMatchKey = matchKey.toString().replace(dynamicKey, "");
                List<MaterielMatchLine> dynamicLineList = matchLineaAllMap.get(replaceMatchKey);
                if(CollectionUtils.isEmpty(dynamicLineList)){
                    dynamicLineList = Lists.newArrayList();
                }
                dynamicLineList.add(matchItem);
                matchLineaAllMap.put(replaceMatchKey, dynamicLineList);
            }
        });
        return matchLineaAllMap;
    }

    private List<MaterielMatchLineDTO> convertMatchHandle(Long matchId,
                                                          List<ItemsDTO> itemsDTOS,
                                                          List<MaterielMatchLineDTO> matchLineDTOListAll,
                                                          Map<String, StructuresDTO> structuresDTOMap,
                                                          Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap) {
        // 如果物料为空,则报错
        if (CollectionUtils.isEmpty(itemsDTOS)) {
            matchLineDTOListAll.forEach(i -> {
                i.setRemark("属性匹配物料失败,请检查物料是否存在");
                i.setMatchStatus(MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getCode());
            });
            materielMatchLineService.saveBatch(matchLineDTOListAll);
            throw new BizException("bbom_matchItem_notExist");
        }

        // 刷新BOM替代项
        materielMatchLineService.flushAlternateBomDesignator(matchId, "", structuresDTOMap, designatorMap);
        //匹配行数据备注置空
        matchLineDTOListAll.stream().forEach(p -> p.setRemark(""));
        // 料号匹配行关联物料信息
        return assembleMatchLineAndItemCodes(itemsDTOS, matchLineDTOListAll);
    }

    /**
     * 电池等级为Q1的电池料号处理
     * @param materielMatchHeaderDTO
     * @param expressRuleLineList
     * @param batteryTypeMainDTO
     * @param dpColumnsMap
     * @param dpColumnsTransScriptMap
     * @param itemsDTOList
     * @return
     */
    private List<ItemsDTO> convertMatchWithoutItemDTOSForQ1(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                                            Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                                            BatteryTypeMainDTO batteryTypeMainDTO,
                                                            Map<String, String> dpColumnsMap,
                                                            Map<String, String> dpColumnsTransScriptMap,
                                                            List<ItemsDTO> itemsDTOList) {
        // 产品等级 = 无 即为Q1
        if (!Objects.equals("无", materielMatchHeaderDTO.getProductionGrade())) return Collections.emptyList();
        // p2_905 特殊片源匹配规则进行匹配
        String groupKey = StringTools.joinWith("", materielMatchHeaderDTO.getBatteryType(), materielMatchHeaderDTO.getSpecialArea(),
                materielMatchHeaderDTO.getHTrace(), materielMatchHeaderDTO.getPcsSourceType(), materielMatchHeaderDTO.getAesthetics(), materielMatchHeaderDTO.getTransparentDoubleGlass());
        List<String> allSpecialMaterielMatchRule = this.getAllSpecialMaterielMatchRule();
        if (allSpecialMaterielMatchRule.contains(groupKey)) {
            MaterielMatchHeaderDTO convertMatchHeaderDTO = new MaterielMatchHeaderDTO();
            BeanUtils.copyProperties(materielMatchHeaderDTO, convertMatchHeaderDTO);
            convertMatchHeaderDTO.setHTrace("无");
            convertMatchHeaderDTO.setSpecialArea("无");
            convertMatchHeaderDTO.setSpecialAreaId(null);
            convertMatchHeaderDTO.setPcsSourceType("无");
            convertMatchHeaderDTO.setAesthetics("无");
            convertMatchHeaderDTO.setTransparentDoubleGlass("无");
            //匹配物料过滤集合
            return matchItemDTOS(convertMatchHeaderDTO, expressRuleLineList, itemsDTOList, batteryTypeMainDTO, dpColumnsMap, dpColumnsTransScriptMap);
        }
        return Collections.emptyList();
    }

    //数据操作：已拆分、未拆分
    private void operationBySplit(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                  Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                  Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                                  Map<String, StructuresDTO> structuresDTOMap,
                                  List<MaterielMatchLineDTO> matchLineDTOListBySpilitIsY,
                                  List<MaterielMatchLineDTO> matchLineDTOListBySpilitIsNull,
                                  Map<String, AttrTypeLineDTO> siliconWaferAttrType,
                                  List<ItemsDTO> itemsDTOS,
                                  Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                  Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        if (CollectionUtils.isNotEmpty(matchLineDTOListBySpilitIsY)) {
            matchLineDTOListBySpilitIsY = matchLineDTOListBySpilitIsY.stream().filter(i -> {
                if (MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getCode().equals(i.getMatchStatus())) {
                    matchLineDTOListBySpilitIsNull.add(i);
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        //未拆分
        if (CollectionUtils.isNotEmpty(matchLineDTOListBySpilitIsNull)) {
            doSplit(materielMatchHeaderDTO, designatorMap, erpOperatRouteMap, structuresDTOMap, matchLineDTOListBySpilitIsNull, siliconWaferAttrType, itemsDTOS, expressRuleLineList,matchLineaAllMap);
        }
        //已拆分
        if (CollectionUtils.isNotEmpty(matchLineDTOListBySpilitIsY)) {
            //5A料号和7A网版料号匹配，获取匹配状态、BOM替代项、获取匹配明细行、备注
            screenItemFilter(materielMatchHeaderDTO, matchLineDTOListBySpilitIsY, designatorMap, erpOperatRouteMap, structuresDTOMap, itemsDTOS, expressRuleLineList,matchLineaAllMap);
            // 删除原始数据并保存新数据
            materielMatchHeaderService.saveMatch(materielMatchHeaderDTO, matchLineDTOListBySpilitIsY);
        }
    }

    private void doSplit(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                         Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                         Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                         Map<String, StructuresDTO> structuresDTOMap,
                         List<MaterielMatchLineDTO> matchLineDTOList,
                         Map<String, AttrTypeLineDTO> siliconWaferAttrType,
                         List<ItemsDTO> itemsDTOS,
                         Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                         Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        //获取网版属性配置
        AttrTypeLineDTO screenPlateAttrType = attrUtil.queryAttrTypeLinesByHeaderCode(ATTR_TYPE_043)
                .stream().filter(i -> i.getAttrCode().equals(ATTR_120007)).findFirst().orElseThrow(() -> new BizException("bbom_matchItem_screenPlateAttr_notConfig"));
        // 准备硅片切换的配置和网版切换的配置
        matchLineDTOList.forEach(matchLineDTO -> {
            // 网版拆分信息
            List<BatteryScreenPlateDTO> batteryScreenPlateDTOList = getBatteryScreenPlateDTOS(materielMatchHeaderDTO, matchLineDTO.getScheduleDate().atStartOfDay());
            if (!StringUtils.isEmpty(matchLineDTO.getMainGridSpace()) && !"无".equals(matchLineDTO.getMainGridSpace())) {
                batteryScreenPlateDTOList = batteryScreenPlateDTOList.stream().filter(ele -> {
                    ItemsDTO item = itemsService.findOneByItemCode(ele.getItemCodeNew());
                    Boolean mainGridSpaceFlag, segment16Flag, singleFlag = true;
                    if (Objects.isNull(item) && StringUtils.isNotEmpty(item.getSegment5())) {
                        mainGridSpaceFlag = false;
                    } else {
                        if (StringUtils.isEmpty(item.getSegment5()) || "无".equals(item.getSegment5())) {
                            mainGridSpaceFlag = true;
                        } else {
                            mainGridSpaceFlag = item.getSegment5().contains(matchLineDTO.getMainGridSpace());
                        }
                    }
                    if (StringUtils.isEmpty(item.getSegment16()) || "无".equals(item.getSegment16())) {
                        segment16Flag = true;
                    } else {
                        segment16Flag = ele.getBatteryName().contains(item.getSegment16().replace("片", ""));
                    }

                    if(StringUtils.isNotEmpty(materielMatchHeaderDTO.getTransparentDoubleGlass()) && "单玻".equals(materielMatchHeaderDTO.getTransparentDoubleGlass()) && !"Y".equals(ele.getSingleGlassFlag())){
                        singleFlag = false;
                    }else if((StringUtils.isBlank(materielMatchHeaderDTO.getTransparentDoubleGlass()) || !"单玻".equals(materielMatchHeaderDTO.getTransparentDoubleGlass())) && "Y".equals(ele.getSingleGlassFlag())){
                        singleFlag = false;
                    }

                    return mainGridSpaceFlag && segment16Flag && singleFlag;
                }).collect(Collectors.toList());
            }
            matchLineDTO.setScreenPlateSwitch(batteryScreenPlateDTOList);

            // 硅片拆分信息
            List<BatterySiliconWaferDTO> siliconWaferList = getBatterySiliconWaferDTOS(materielMatchHeaderDTO, matchLineDTO.getScheduleDate().atStartOfDay());
            matchLineDTO.setSiliconWaferList(siliconWaferList);
        });

        // 先进行网版和硅片的拆分,之后再进行切换过滤料号
        matchLineDTOList = splitScreenPlateAndSiliconWafer(matchLineDTOList);

        // 开始硅片切换和网版切换
        matchLineDTOList = applySiliconWaferSwitchAndScreenSwitch(screenPlateAttrType, matchLineDTOList, materielMatchHeaderDTO, siliconWaferAttrType);

        Map<Boolean, List<MaterielMatchLineDTO>> itemDtosIsEmptyMap = matchLineDTOList.stream()
                .collect(Collectors.groupingBy(i -> CollectionUtils.isEmpty(i.getItemsDTOS())));
        List<MaterielMatchLineDTO> emptyItemDtos = itemDtosIsEmptyMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        List<MaterielMatchLineDTO> hasItemDtos = itemDtosIsEmptyMap.getOrDefault(Boolean.FALSE, new ArrayList<>());

        emptyItemDtos.forEach(i -> {
            if(StringUtils.isBlank(i.getItemCode())){
                //未被继承则标记失败
                i.setRemark("属性匹配物料失败,请检查物料是否存在");
                i.setMatchStatus(MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getCode());
            }
        });

        screenItemFilter(materielMatchHeaderDTO, hasItemDtos, designatorMap, erpOperatRouteMap, structuresDTOMap, itemsDTOS, expressRuleLineList,matchLineaAllMap);
        //获取已删除的最大版本号数据 且已指定料号的旧版本 新旧比对
        getAppointItemByMaxVsrsion(materielMatchHeaderDTO.getId(), materielMatchHeaderDTO, hasItemDtos, designatorMap, erpOperatRouteMap, structuresDTOMap);
        matchLineDTOList.forEach(item->{
            hasItemDtos.forEach(hasItem->{
                if(item.getId().equals(hasItem.getId())){
                    BeanUtils.copyProperties(hasItem, item);
                }
            });
        });
        // 删除原始数据并保存新数据
        materielMatchHeaderService.saveMatch(materielMatchHeaderDTO, matchLineDTOList);
    }

    private static Long workshopConvrterOrgId(String workshop) {
        Long orgId = null;
        LovLineDTO lovLineDTO = LovUtils.get("work_shop", workshop);
        if (null != lovLineDTO) {
            String orgCode = lovLineDTO.getAttribute10();
            LovLineDTO orgLov = LovUtils.get("inventory_organization", orgCode);
            if (null != orgLov) {
                String orgIdStr = orgLov.getAttribute1();
                orgId = Long.valueOf(orgIdStr);
            }
        }
        return orgId;
    }

    //获取bom替代项集合
    private Map<Long, List<ErpAlternateDesignatorDTO>> getBomDesigntorByMatchId(String workshop) {
        Set<Long> set = new HashSet<>();
        //获取车间对应的库存组织
        Long orgId = workshopConvrterOrgId(workshop);
        if (orgId != null) {
            set.add(orgId);
        }
        //获取bom替代项集合
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        designatorQuery.setOrganizationIds(set.stream().collect(Collectors.toList()));
        List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryByOrganizationIds(designatorQuery).getBody().getData();
        if (CollectionUtils.isEmpty(designatorDTOList)) {
            throw new BizException("bbom_workshopAndOrgNotFoundDesignator", workshop);
        }
        Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap = designatorDTOList.stream().collect(Collectors.groupingBy(ErpAlternateDesignatorDTO::getOrganizationId));
        return designatorMap;
    }

    /**
     * 料号匹配全量
     *
     * @param
     * @return
     */
    @Override
    public void allMatchItemPage(MaterielMatchHeaderQuery query) {
        //redis锁
        String redisKey = "MaterielMatchHeaderServiceImpl.allMatchItemPage";
        RLock lock = redissonClient.getLock(redisKey);
        if (!lock.isLocked()) {
            try {
//                String month = "";
//                if (StringUtils.isNotEmpty(query.getMonth())) {
//                    month = query.getMonth();
//                } else {
//                    month = DateUtil.getMonth(LocalDate.now());
//                }
//                /********************************************************数据准备START*************************************************************/
//                //获取Active、82L、自产电池片的物料集合
//                List<ItemsDTO> itemsDTOList = itemsService.queryByMatchingAll();
//                //根据匹配头查询匹配行数据 查询范围itemCode is null 、拆分标识[splitFlag]不为N、料号指定标识[handWorkFlag]不为Y
//                List<MaterielMatchLineDTO> matchLineDTOListAll = getNonMatchMaterielMatchLines(null, month);
//                List<MaterielMatchHeaderDTO> matchHeaderDTOList = getMaterielMatchHeaderDTOS(matchLineDTOListAll, month);
//                //获取匹配行 分组
//                Map<Long, List<MaterielMatchLineDTO>> matchLineGroup = matchLineDTOListAll.stream().collect(Collectors.groupingBy(MaterielMatchLineDTO::getHeaderId));
//
//                //获取bom替代项集合
//                Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap = getDesignatorListMapByAll(matchHeaderDTOList, matchLineGroup);
//                //获取工艺路线
//                Map<String, ErpOperatRouteDTO> erpOperatRouteMap = getErpOperatRouteList();
//
//                //获取Structures 所有数据
//                Map<String, StructuresDTO> structuresDTOMap = queryListByStructures();
//
//                // 刷新BOM替代项
//                materielMatchLineService.flushAlternateBomDesignator(null, month, structuresDTOMap, designatorMap);
//
//                //获取所有电池类型静态属性
//                List<BatteryTypeMainDTO> batteryTypeMainDTOList = batteryTypeMainService.queryBatteryCodeTypeAll();
//                //电池类型静态属性分组
//                Map<String, BatteryTypeMainDTO> batteryTypeMainMap = batteryTypeMainDTOList.stream().collect(Collectors.toMap(BatteryTypeMainDTO::getBatteryName, Function.identity(), (k1, k2) -> k1));
//
//                //获取dp属性和item属性对应关系
//                Map<String, String> dpColumnsMap = getDpColAndItemColMap();
//                //获取规则 转换
//                Map<String, String> dpColumnsTransScriptMap = getDpColAndTransScriptMap();
//                //获取应用硅片属性数据
//                Map<String, AttrTypeLineDTO> siliconWaferAttrType = attrUtil.queryAttrTypeLinesByHeaderCode("ATTR_TYPE_018")
//                        .stream().collect(Collectors.toMap(AttrTypeLineDTO::getAttrCode, Function.identity(), (k1, k2) -> k1));
//
//                //获取所有需要继承的lov反射后数据
//                Map<String,List<MaterielMatchLine>> matchLineaAllMap = getAllLineMap();
//                /********************************************************数据准备END*************************************************************/
//
//                // 获取锁
//                lock.lock();
//                try {
//
//                    List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
//                    matchHeaderDTOList = matchHeaderDTOList.stream().filter(item -> item.getId().toString().equals("1882014005007093760")).collect(Collectors.toList());
//                    for (MaterielMatchHeaderDTO obj : matchHeaderDTOList) {
//                        CompletableFuture<Void> completableFutureApplyMtlCatHeaderDTO = CompletableFuture.runAsync(() -> {
//                            try {
//                                //料号批量匹配
//                                doMatchItem(obj, itemsDTOList,
//                                        designatorMap,
//                                        matchLineGroup.get(obj.getId()), erpOperatRouteMap,
//                                        structuresDTOMap,
//                                        batteryTypeMainMap.get(obj.getBatteryType()),
//                                        dpColumnsMap,
//                                        dpColumnsTransScriptMap,
//                                        siliconWaferAttrType,
//                                        matchLineaAllMap);
//                            } catch (Exception e) {
//                                log.warn("MaterielMatchHeaderServiceImpl料号匹配异常：id{} error:{}", obj.getId(), e.getMessage(), e);
//                            }
//                        }, threadPoolExecutor);
//                        completableFutures.add(completableFutureApplyMtlCatHeaderDTO);
//                    }
//
//                    CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
//                } finally {
//                    lock.unlock();  // 释放锁
//                }
                List<String> matchStatusList = Arrays.asList(MaterielMatchLineDTO.MatchStatus.FAIL_MATCH.getCode(), MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getCode(),
                        MaterielMatchLineDTO.MatchStatus.NON_MATCH.getCode());
                MaterielMatchLineQuery lineQuery = materielMatchQueryDEConvert.toDto(query);
                lineQuery.setPageNumber(1);
                lineQuery.setPageSize(Integer.MAX_VALUE);
                lineQuery.setMatchStatusList(matchStatusList);
                Page<MaterielMatchLineDTO> materielMatchLineDTOS = materielMatchLineService.queryByPage(lineQuery);
                if (CollectionUtils.isEmpty(materielMatchLineDTOS.getContent())) {
                    return;
                }
                Map<String,List<MaterielMatchLine>> matchLineaAllMap = this.getAllLineMap();
                MaterielMatchHeaderServiceImpl beanHandle = SpringContextUtils.getBean(MaterielMatchHeaderServiceImpl.class);
                log.info("二期料号匹配 成批匹配总数量：{}", materielMatchLineDTOS.getContent().size());
                materielMatchLineDTOS.getContent().forEach(item -> {
                    asyncTaskExecutor.execute(() -> {
                        try {
                            beanHandle.matchItem(item.getHeaderId(), matchLineaAllMap);
                        } catch (Exception ex) {
                            log.info("二期料号匹配 matchItem error headerId：{} exception: {}", item.getHeaderId(), JSON.toJSON(ex));
                        }
                    });
                });
            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                // 确保释放锁
                if (lock != null && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            throw new RuntimeException("其他用户正在操作中，请稍后");
        }
    }

    private List<ItemsDTO> filterByValidBom(List<ItemsDTO> itemsDTOList,List<MaterielMatchLineDTO> matchLineDTOListAll) {
        if(CollectionUtils.isEmpty(itemsDTOList)){
            return itemsDTOList;
        }
        String workshop = CollectionUtils.isNotEmpty(matchLineDTOListAll)?matchLineDTOListAll.get(0).getWorkshop():"";
        Map<String, List<ItemsDTO>> itemsDTOListGroup = itemsDTOList.stream().collect(Collectors.groupingBy(ItemsDTO::getItemCode));
        List<String> itemCodeList = new ArrayList<>(itemsDTOListGroup.keySet());
        List<MaterielMatchHeaderDTO> descriptionList = Lists.newArrayList();
        List<Map> queryList = itemsRepository.getDescriptionByCodes(itemCodeList);
        queryList.forEach(item->{
            MaterielMatchHeaderDTO dto = new MaterielMatchHeaderDTO();
            dto.setItemCode(item.get("itemCode").toString());
            dto.setAlternateBomDesignator(item.get("alternateBomDesignator").toString());
            descriptionList.add(dto);
        });
        List<ErpAlternateDesignatorDTO> erpList = Lists.newArrayList();
        try{
            ErpAlternateDesignatorQuery query = new ErpAlternateDesignatorQuery();
            query.setPageSize(Integer.MAX_VALUE);
            JSONObject resultsResponseObject = bomFeign.queryByPage2(query);
            JSONArray resultsResponseArray = resultsResponseObject.getJSONObject("RESPONSE").getJSONObject("RETURN_DATA")
                    .getJSONObject("body").getJSONObject("obj").getJSONObject("data")
                    .getJSONArray("content");
            erpList.addAll(resultsResponseArray.toJavaList(ErpAlternateDesignatorDTO.class));
        }catch (Exception e){
            log.error(ExceptionUtil.exceptionChainToString(e));
        }
        erpList = erpList.stream().filter(item->workshop.equals(item.getDescription())).collect(Collectors.toList());
        List<String> alternateDesignatorCodeList = erpList.stream().map(ErpAlternateDesignatorDTO::getAlternateDesignatorCode).collect(Collectors.toList());
        List<MaterielMatchHeaderDTO> descriptionFilterList = descriptionList.stream().filter(item->alternateDesignatorCodeList.contains(item.getAlternateBomDesignator())).collect(Collectors.toList());
        List<String> filterItemCodeList = descriptionFilterList.stream().map(MaterielMatchHeaderDTO::getItemCode).distinct().collect(Collectors.toList());
        return itemsDTOList.stream().filter(item->filterItemCodeList.contains(item.getItemCode())).collect(Collectors.toList());
    }

    private List<MaterielMatchHeaderDTO> getMaterielMatchHeaderDTOS(List<MaterielMatchLineDTO> matchLineDTOListAll, String month) {
        //查询条件 月份、头id集合 查询匹配行数据集合
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        List<Long> matchHeaderIds = matchLineDTOListAll.stream().map(headerDto -> headerDto.getHeaderId()).collect(Collectors.toList());
        booleanBuilder.and(qMaterielMatchHeader.oldMonth.eq(month));
        booleanBuilder.and(qMaterielMatchHeader.id.in(matchHeaderIds));
        Iterable<MaterielMatchHeader> matchHeaderList = repository.findAll(booleanBuilder);
        List<MaterielMatchHeaderDTO> matchHeaderDTOList = convert.toDto(IterableUtils.toList(matchHeaderList));
        return matchHeaderDTOList;
    }

    /**
     * 获取不是指定的, 拆分标识为空或者为Y,
     * 并且匹配 matchId 和 oldMonth, 投产如oldMonth 入库用month
     * 并且未匹配到料号的数据
     *
     * @param matchId
     * @param month
     * @return
     */
    private List<MaterielMatchLineDTO> getNonMatchMaterielMatchLines(Long matchId, String month) {
        JPAQuery<MaterielMatchLine> where = jpaQueryFactory.select(QMaterielMatchLine.materielMatchLine).
                from(QMaterielMatchLine.materielMatchLine).
                where(
                        // handWorkFlag 指定料号标识 为空或者不为Y
                        QMaterielMatchLine.materielMatchLine.handWorkFlag.isNull()
                                    .or(QMaterielMatchLine.materielMatchLine.handWorkFlag.ne("Y").or(QMaterielMatchLine.materielMatchLine.handWorkFlag.isEmpty()))
                                // splitFlag 拆分标识 为空或者为Y
                                .and(QMaterielMatchLine.materielMatchLine.splitFlag.isNull()
                                        .or(QMaterielMatchLine.materielMatchLine.splitFlag.eq("Y").or(QMaterielMatchLine.materielMatchLine.splitFlag.isEmpty())))
                );
        if (null != matchId) {
            where.where(QMaterielMatchLine.materielMatchLine.headerId.eq(matchId));
        }
        if (StringUtils.isNotBlank(month)) {
            where.where(QMaterielMatchLine.materielMatchLine.oldMonth.eq(month));
        }
        where.where(QMaterielMatchLine.materielMatchLine.itemCode.isNull().or(QMaterielMatchLine.materielMatchLine.itemCode.isEmpty()));

        List<MaterielMatchLineDTO> dto = materielMatchLineDEConvert.toDto(where.fetch());

        return dto;
    }

    //获取Structures 所有数据
    @Override
    @Cacheable(cacheNames = "MaterielMatchHeaderService_queryListByStructures", key = "")
    public Map<String, StructuresDTO> queryListByStructures() {
        //bom替代项、组织id、5A料号id 唯一
        List<StructuresDTO> structuresDTOList = structuresService.queryList();
        Map<String, StructuresDTO> structuresDTOMap = structuresDTOList.stream().collect(Collectors.toMap(ele -> String.format("%s%s%s", ele.getAlternateBomDesignator(), ele.getOrganizationId(), ele.getAssemblyItemId()), Function.identity(), (v1, v2) -> v1));
        return structuresDTOMap;
    }

    //bom替代项方法
    private Map<Long, List<ErpAlternateDesignatorDTO>> getDesignatorListMapByAll(List<MaterielMatchHeaderDTO> matchHeaderDTOList, Map<Long, List<MaterielMatchLineDTO>> matchLineGroup) {
        Set<Long> set = new HashSet<>();
        //获取车间对应的库存组织
        for (MaterielMatchHeaderDTO obj : matchHeaderDTOList) {
            LovLineDTO lovLineDTO = LovUtils.get("work_shop", obj.getWorkshop());
            if (null != lovLineDTO) {
                String orgCode = lovLineDTO.getAttribute10();
                LovLineDTO orgLov = LovUtils.get("inventory_organization", orgCode);
                if (null != orgLov) {
                    String orgId = orgLov.getAttribute1();
                    set.add(Long.valueOf(orgId));
                } else {
                    matchLineGroup.get(obj.getId()).stream().forEach(line -> {
                        line.setMatchStatus(MaterielMatchLineDTO.MatchStatus.NON_MATCH.getCode());
                        line.setRemark("车间没有查询到对应库存组织信息");
                    });
                    repositoryLine.saveAll(materielMatchLineDEConvert.toEntity(matchLineGroup.get(obj.getId())));
                }
            }
        }
        if (CollectionUtils.isEmpty(set)) {
            return new HashMap<>();
        }
        //获取bom替代项集合
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        designatorQuery.setOrganizationIds(new ArrayList<>(set));
        List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryByOrganizationIds(designatorQuery).getBody().getData();
        Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap = designatorDTOList.stream().collect(Collectors.groupingBy(ErpAlternateDesignatorDTO::getOrganizationId));
        return designatorMap;
    }

    //获取工艺路线全量数据
    public Map<String, ErpOperatRouteDTO> getErpOperatRouteList() {
        return bomService.getErpOperatRouteList();
    }

    private void doMatchItem(MaterielMatchHeaderDTO materielMatchHeaderDTO, List<ItemsDTO> itemsDTOList,
                             Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                             List<MaterielMatchLineDTO> matchLineDTOList,
                             Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                             Map<String, StructuresDTO> structuresDTOMap,
                             BatteryTypeMainDTO batteryTypeMainDTO,
                             Map<String, String> dpColumnsMap,
                             Map<String, String> dpColumnsTransScriptMap,
                             Map<String, AttrTypeLineDTO> siliconWaferAttrType,
                             Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        // 获取规则
        Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList = getExpressRuleLineList();
        if (log.isDebugEnabled()) {
            log.debug("expressRuleLineList:{}", expressRuleLineList);
        }
        //物料筛选过滤
        List<ItemsDTO> itemsDTOS = matchItemDTOS(materielMatchHeaderDTO, expressRuleLineList, itemsDTOList, batteryTypeMainDTO, dpColumnsMap, dpColumnsTransScriptMap);

        //2024.12.27料号匹配逻辑修改-校验5A料号是否在电池车间存在有效BOM
        itemsDTOS = this.filterByValidBom(itemsDTOS,matchLineDTOList);

        // 如果物料为空,则报错
        if (CollectionUtils.isEmpty(itemsDTOS)) {
            matchLineDTOList.stream().parallel().forEach(p -> {
                p.setRemark("属性匹配物料失败,请检查物料是否存在");
                p.setMatchStatus(MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getCode());
            });
            materielMatchLineService.saveBatch(matchLineDTOList);
            materielMatchHeaderDTO.setRemark("属性匹配物料失败,请检查物料是否存在");
        } else {
            matchLineDTOList.stream().forEach(p -> p.setRemark(""));
            // 料号匹配行关联物料信息
            matchLineDTOList = assembleMatchLineAndItemCodes(itemsDTOS, matchLineDTOList);
            //过滤获取已拆分的
            List<MaterielMatchLineDTO> matchLineDTOListBySpilitIsY = matchLineDTOList.stream().filter(split -> "Y".equals(split.getSplitFlag())).collect(Collectors.toList());
            //过滤获取未拆分的
            List<MaterielMatchLineDTO> matchLineDTOListBySpilitIsNull = matchLineDTOList.stream().filter(split -> StringUtils.isEmpty(split.getSplitFlag())).collect(Collectors.toList());
            ////拆分标识为Y的或者为空的且5A料号未指定 进行料号匹配操作
            operationBySplit(materielMatchHeaderDTO, designatorMap, erpOperatRouteMap, structuresDTOMap, matchLineDTOListBySpilitIsY, matchLineDTOListBySpilitIsNull, siliconWaferAttrType, itemsDTOS, expressRuleLineList,matchLineaAllMap);

            materielMatchHeaderDTO.setRemark("");
            materielMatchHeaderDTO.setMatchHeaderStatus("true");
        }
        //更新头数据
        repository.save(convert.toEntity(materielMatchHeaderDTO));
    }

    /**
     * 获取已删除的最大版本号数据 且已指定料号的旧版本数据
     *
     * @param matchId
     * @param materielMatchHeaderDTO
     * @param matchLineDTOListAll
     */
    private void getAppointItemByMaxVsrsion(Long matchId, MaterielMatchHeaderDTO materielMatchHeaderDTO, List<MaterielMatchLineDTO> matchLineDTOListAll, Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap, Map<String, StructuresDTO> structuresDTOMap) {
        String version = materielMatchLineService.getMaxFinalVersion(materielMatchHeaderDTO.getMonth(), materielMatchHeaderDTO.getIsOverseaId());
        //旧版本数据
        List<MaterielMatchLineDTO> dtoList = materielMatchLineService.queryMatchLineByVersion(materielMatchHeaderDTO, materielMatchHeaderDTO.getMonth(), version, materielMatchHeaderDTO.getIsOverseaId());
        if (CollectionUtils.isNotEmpty(dtoList)) {
            Map<String, MaterielMatchLineDTO> listMap = dtoList.stream().collect(Collectors.toMap(MaterielMatchLineDTO::getGroupFiled, Function.identity(), (user1, user2) -> user2));
            matchLineDTOListAll.stream().forEach(x -> {
                String key = x.getBatteryType() + "/"
                        + x.getAesthetics() + "/"
                        + x.getTransparentDoubleGlass() + "/"
                        + x.getSpecialArea() + "/"
                        + x.getHTrace() + "/"
                        + x.getPcsSourceType() + "/"
                        + x.getPcsSourceLevel() + "/"
                        + x.getIsSpecialRequirements() + "/"
                        + x.getScreenManufacturer() + "/"
                        + x.getSiliconMaterialManufacturer() + "/"
                        + x.getBatteryManufacturer() + "/"
                        + x.getSilverSlurryManufacturer() + "/"
                        + x.getLowResistance() + "/"
                        + x.getSiliconWaferPurchaseMethod() + "/"
                        + x.getDemandPlace() + "/"
                        + x.getBasePlace() + "/"
                        + x.getWorkshop() + "/"
                        + x.getWorkunit() + "/"
                        + x.getProductionGrade() + "/"
                        + x.getProcessCategory() + "/"
                        + x.getScreenPlateItemCode() + "/"
                        + x.getCellProductionPlanId() + "/"
                        + x.getOldMonth() + "/"
                        + x.getSupplyModeName() + "/"
                        + x.getRatioCode() + "/"
                        + x.getEcsCode() + "/"
                        + x.getIsOversea();
                if (null != listMap.get(key)) {
                    List<ItemsDTO> items = new ArrayList<>();
                    ItemsDTO item = itemsService.findOneByItemCode(listMap.get(key).getItemCode());
                    items.add(item);
                    x.setItemCode(listMap.get(key).getItemCode());
                    x.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
                    String alternateDesignatorCode = getAlternateDesignatorCode(materielMatchHeaderDTO.getWorkshop(), items.get(0).getSourceItemId(), designatorMap, structuresDTOMap);
                    x.setAlternateBomDesignator(alternateDesignatorCode);
                    x.setMatchCodes(getMatchCodes(materielMatchHeaderDTO, items, designatorMap, erpOperatRouteMap, structuresDTOMap));
                }
            });
        }
    }

    /**
     * 网版料号过滤
     * @param materielMatchHeaderDTO
     * @param matchLineDTOList
     * @param designatorMap
     * @param erpOperatRouteMap
     * @param structuresDTOMap
     * @param itemsDTOS
     * @param expressRuleLineList
     */
    private void screenItemFilter(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                  List<MaterielMatchLineDTO> matchLineDTOList,
                                  Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                  Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                                  Map<String, StructuresDTO> structuresDTOMap,
                                  List<ItemsDTO> itemsDTOS,
                                  Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                  Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        Map<String, String> allMatchRuleMap = this.getAllAMaterielMatchRule();
        List<String> allSpecialMaterielMatchRule = this.getAllSpecialMaterielMatchRule();
        matchLineDTOList.stream().forEach(dto -> {
            //新正电极和新背电极的7Asegment属性和5A物料的segment属性比对dd
            doElectrodeItemFilter(materielMatchHeaderDTO, designatorMap, erpOperatRouteMap, structuresDTOMap, dto, itemsDTOS, expressRuleLineList, allMatchRuleMap, allSpecialMaterielMatchRule,matchLineaAllMap);
        });
    }

    private Map<String, String> getAllAMaterielMatchRule() {
        String lang = MyThreadLocal.get().getLang();
        MyThreadLocal.get().setLang(LovHeaderCodeConstant.LANGUAGE_CN);
        AMaterielMatchRuleQuery query = new AMaterielMatchRuleQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNumber(1);
        Page<AMaterielMatchRuleDTO> aMaterielMatchRuleDTOS = aMaterielMatchRuleService.queryByPage(query);
        if (CollectionUtils.isEmpty(aMaterielMatchRuleDTOS.getContent())) {
            return MapUtil.empty();
        }
        MyThreadLocal.get().setLang(lang);
        return aMaterielMatchRuleDTOS.getContent().stream().collect(Collectors.toMap(AMaterielMatchRuleDTO::filedGroup,
                AMaterielMatchRuleDTO::getAItemCode, (v1, v2) -> v1));
    }

    private List<String> getAllSpecialMaterielMatchRule() {
        String lang = MyThreadLocal.get().getLang();
        SpecialCellMatchRuleQuery query = new SpecialCellMatchRuleQuery();
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNumber(1);
        Page<SpecialCellMatchRuleDTO> specialCellMatchRuleDTOS = specialCellMatchRuleService.queryByPage(query);
        if (CollectionUtils.isEmpty(specialCellMatchRuleDTOS.getContent())) {
            return Collections.emptyList();
        }
        MyThreadLocal.get().setLang(lang);
        return specialCellMatchRuleDTOS.getContent().stream().map(SpecialCellMatchRuleDTO::filedGroup).collect(Collectors.toList());
    }

    private void doElectrodeItemFilter(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                       Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                       Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                                       Map<String, StructuresDTO> structuresDTOMap,
                                       MaterielMatchLineDTO dto,
                                       List<ItemsDTO> itemsDTOS,
                                       Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                       Map<String, String> allMatchRuleMap,
                                       List<String> allSpecialMaterielMatchRule,
                                       Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        this.doElectrodeItemFilterHadnle(dto,materielMatchHeaderDTO);
//        this.doElectrodeItemFilterSpecialHandle(dto, itemsDTOS, expressRuleLineList, allMatchRuleMap, allSpecialMaterielMatchRule);
        List<ItemsDTO> items = dto.getItemsDTOS();
        //获取7A的segment17
        boolean flag7A = CollectionUtils.isNotEmpty(items);

        propertyAssignment(materielMatchHeaderDTO, designatorMap, erpOperatRouteMap, structuresDTOMap, dto, items, flag7A,matchLineaAllMap);
    }



    private void doElectrodeItemFilterHadnle(MaterielMatchLineDTO dto
            ,MaterielMatchHeaderDTO materielMatchHeaderDTO) {
        List<ItemsDTO> items = dto.getItemsDTOS();
        String positiveElectrodeItemCode = null;
        String backElectrodeItemCode = null;
        if (StringUtils.isNotEmpty(dto.getScreenPlateItemCode()) && StringUtils.isNotEmpty(dto.getScreenPlateCodeFilter())) {
            // 新正电极网版和新背电极网版都不为空
            positiveElectrodeItemCode = dto.getScreenPlateItemCode();
            backElectrodeItemCode = dto.getScreenPlateCodeFilter();
        } else if (StringUtils.isNotEmpty(dto.getScreenPlateItemCode())) {
            // 只有正电极主栅
            positiveElectrodeItemCode = dto.getScreenPlateItemCode();
        } else if (StringUtils.isNotEmpty(dto.getScreenPlateCodeFilter())) {
            // 只有背电极主栅
            backElectrodeItemCode = dto.getScreenPlateCodeFilter();
        } else if (StringUtils.isNotEmpty(dto.getScreenPlateItemCodeFineGrid()) && StringUtils.isNotEmpty(dto.getScreenPlateCodeFilterFineGrid())) {
            // 只有背电极细栅和背电极细栅
            positiveElectrodeItemCode = dto.getScreenPlateItemCodeFineGrid();
            backElectrodeItemCode = dto.getScreenPlateCodeFilterFineGrid();
        } else if (StringUtils.isNotEmpty(dto.getScreenPlateItemCodeFineGrid())) {
            // 只有正电极细栅
            positiveElectrodeItemCode = dto.getScreenPlateItemCodeFineGrid();
        } else if (StringUtils.isNotEmpty(dto.getScreenPlateCodeFilter())) {
            // 只有背电极细栅
            backElectrodeItemCode = dto.getScreenPlateCodeFilterFineGrid();
        }

        // 新正电极网版和新背电极网版都不为空
        if (StringUtils.isNotEmpty(positiveElectrodeItemCode) && StringUtils.isNotEmpty(backElectrodeItemCode)) {
            // 正电极过滤
            items = filterPositiveElectrodeItems(items, positiveElectrodeItemCode);
            // 背电极电池料号过滤
            items = filterBackElectrodeItems(items, backElectrodeItemCode);
        } else if (StringUtils.isNotEmpty(positiveElectrodeItemCode)) { // 只有正电极
            // 正电极过滤
            items = filterPositiveElectrodeItems(items, positiveElectrodeItemCode);
        } else if (StringUtils.isNotEmpty(backElectrodeItemCode)) { // 只有背电极
            items = dto.getItemsDTOS();
            // 背电极电池料号过滤
            items = filterBackElectrodeItems(items, backElectrodeItemCode);
        }


        //网板没匹配上,匹配车间级网板
        if (StringUtils.isEmpty(positiveElectrodeItemCode) && StringUtils.isEmpty(backElectrodeItemCode)) {
            materielMatchHeaderDTO.setScheduleDate(dto.getScheduleDate());
            BatteryScreenPlateWorkshopDTO workshopDto = batteryScreenPlateWorkshopService.getScreenWorkshopByHead(materielMatchHeaderDTO);
            if(Objects.nonNull(workshopDto)) {
                //防止空指针,初始化
                items.forEach(item->{
                    if(StringUtils.isBlank(item.getSegment17())){
                        item.setSegment17("");
                    }
                    if(StringUtils.isBlank(item.getSegment18())){
                        item.setSegment18("");
                    }
                });
                //正电极网版细栅与电池片属性segment17（精确匹配），背电极网版细栅与电池片属性segment18（模糊匹配
                items = items.stream().filter(item->workshopDto.getPositiveElectrodeScreenFineGrid().equals(item.getSegment15())
                        && item.getSegment17().contains(workshopDto.getNegativeElectrodeScreenFineGrid())
                        && (StringUtils.isEmpty(workshopDto.getMainGridInfo()) || Objects.equals(workshopDto.getMainGridInfo(), item.getSegment16()))).collect(Collectors.toList());
                dto.setNegativeElectrodeScreenFineGrid(workshopDto.getNegativeElectrodeScreenFineGrid());
                dto.setPositiveElectrodeScreenFineGrid(workshopDto.getPositiveElectrodeScreenFineGrid());
                dto.setMainGridInfo(workshopDto.getMainGridInfo());
            }
        }

        dto.setItemsDTOS(items);
    }

    /**
     * p2_906 料号匹配考虑兼容特殊属性电池兼容
     * https://bizdevops.trinasolar.com/console/vteam/z82bfd/twDemand?vmode=table&id=3f5ca66c23544fd695d25b87af61ee8a&
     * @param dto
     * @param itemsDTOS
     * @param expressRuleLineList
     * @param allMatchRuleMap
     * @param allSpecialMaterielMatchRule
     */
    private void doElectrodeItemFilterSpecialHandle(MaterielMatchLineDTO dto,
                                                    List<ItemsDTO> itemsDTOS,
                                                    Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                                    Map<String, String> allMatchRuleMap,
                                                    List<String> allSpecialMaterielMatchRule) {
        if (!CollectionUtils.isEmpty(dto.getItemsDTOS())) {
            return;
        }
        List<ItemsDTO> items = dto.getItemsDTOS();
        if (Objects.equals("A-", dto.getProductionGrade())) {
            // p2_905 A-料号匹配规则进行匹配
            // 实际片厚
            List<String> lineQtyList = dto.getSiliconWaferList().stream().map(BatterySiliconWaferDTO::getLineQty).distinct().map(String::valueOf).collect(Collectors.toList());
            // 主栅两端形状
            List<String> attrValueList = expressRuleLineList.values().stream().flatMap(List::stream)
                    .map(ExpressRuleLineDTO::getAttrValue).distinct().collect(Collectors.toList());
            List<String> allKeyList = Lists.newLinkedList();
            lineQtyList.forEach(lineQty -> {
                attrValueList.forEach(attrValue -> {
                    allKeyList.add(StringTools.joinWith("", dto.getIsOversea(), dto.getWorkshop(), dto.getBatteryType(),
                            lineQty, attrValue));
                });
            });
            List<String> effectItemCodes = allKeyList.stream().map(ele -> allMatchRuleMap.get(ele)).filter(Objects::nonNull).collect(Collectors.toList());
            items = itemsDTOS.stream().filter(ele -> effectItemCodes.contains(ele.getItemCode())).collect(Collectors.toList());
            dto.setItemsDTOS(items);
        } else if (Objects.equals("Q1", dto.getProductionGrade())) {
            // p2_905 特殊片源匹配规则进行匹配
            String groupKey = StringTools.joinWith("", dto.getBatteryType(), dto.getSpecialArea(), dto.getHTrace(),
                    dto.getPcsSourceType(), dto.getAesthetics(), dto.getTransparentDoubleGlass());
            if (allSpecialMaterielMatchRule.contains(groupKey)) {
                MaterielMatchLineDTO convertMatchDTO = new MaterielMatchLineDTO();
                BeanUtils.copyProperties(dto, convertMatchDTO);
                convertMatchDTO.setHTrace("无");
                convertMatchDTO.setSpecialArea("无");
                convertMatchDTO.setSpecialAreaId(null);
                convertMatchDTO.setPcsSourceType("无");
                convertMatchDTO.setAesthetics("无");
                convertMatchDTO.setTransparentDoubleGlass("无");
                List<MaterielMatchLineDTO> convertMatchDTOS = Collections.singletonList(convertMatchDTO);
                convertMatchDTOS = assembleMatchLineAndItemCodes(itemsDTOS, convertMatchDTOS);
                items = convertMatchDTOS.get(0).getItemsDTOS();
                dto.setItemsDTOS(items);
                //此方法未被使用,暂时不改造
//                this.doElectrodeItemFilterHadnle(dto,null,designatorMap, erpOperatRouteMap, structuresDTOMap);
            }
        }
    }

    private void propertyAssignment(MaterielMatchHeaderDTO materielMatchHeaderDTO, Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap, Map<String, StructuresDTO> structuresDTOMap, MaterielMatchLineDTO dto, List<ItemsDTO> items, boolean flag7A,Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        matchItemCode(materielMatchHeaderDTO, designatorMap, erpOperatRouteMap, structuresDTOMap, dto, items, flag7A,matchLineaAllMap);
    }

    //itemsDTOList::5A与7A料号属性过滤公共方法提取
    private List<ItemsDTO> filterPositiveElectrodeItems(List<ItemsDTO> itemsDTOList, String screenPlateItemCode) {
        List<ItemsDTO> items;
        // 获取正电极主栅的物料
        ItemsDTO screenPlateItemNew = itemsService.findOneByItemCode(screenPlateItemCode);
        items = filterByScreenPlateItem(itemsDTOList, screenPlateItemNew);
        return items;
    }

    private void matchItemCode(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                               Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                               Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                               Map<String, StructuresDTO> structuresDTOMap,
                               MaterielMatchLineDTO dto,
                               List<ItemsDTO> items,
                               boolean flag7A,
                               Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        String inheritItemCode = materielMatchLineService.getInheritItemCode(dto,matchLineaAllMap);
        if(StringUtils.isNotBlank(inheritItemCode)){
            //继承并中止料号匹配
            dto.setItemCode(inheritItemCode);
            dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
            dto.setItemMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
            ItemsDTO itemsDTO = itemsService.findOneByItemCode(inheritItemCode);
            if(Objects.isNull(itemsDTO)){
                itemsDTO = new ItemsDTO();
            }
            //BOM替代项
            String alternateDesignatorCode = getAlternateDesignatorCode(materielMatchHeaderDTO.getWorkshop(), itemsDTO.getSourceItemId(), designatorMap, structuresDTOMap);
            dto.setAlternateBomDesignator(alternateDesignatorCode);
            dto.setMatchCodes(getMatchCodes(materielMatchHeaderDTO, items, designatorMap, erpOperatRouteMap, structuresDTOMap));
            dto.setItemDesc(itemsDTO.getItemDesc());
            ErpOperatRouteDTO operatRouteDTO = bomService.findBy5AAndAlternateRoutingDesignator(inheritItemCode, dto.getAlternateBomDesignator());
            if (operatRouteDTO == null) {
                dto.setRoute("否");
            } else {
                dto.setRoute("是");
            }
            dto.setCertifiedModels("");

        }else{
            if(CollectionUtils.isNotEmpty(items) && StringUtils.isNotBlank(dto.getSiliconWaferValue())){
                items = items.stream().filter(item->dto.getSiliconWaferValue().equals(item.getSegment7())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(items)) {
                dto.setMatchStatus(flag7A ? MaterielMatchLineDTO.MatchStatus.FAIL_MATCH.getCode() : MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getCode());
                dto.setRemark(flag7A ? MaterielMatchLineDTO.MatchStatus.FAIL_MATCH.getName() : MaterielMatchLineDTO.MatchStatus.ATTR_NON_MATCH.getName());
            } else if (items.size() == 1) {
                // 匹配到一个
                ItemsDTO itemsDTO = items.get(0);
                dto.setItemCode(itemsDTO.getItemCode());
                dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
                dto.setItemMatchStatus(MaterielMatchLineDTO.MatchStatus.SINGLE_MATCH.getCode());
                //BOM替代项
                String alternateDesignatorCode = getAlternateDesignatorCode(materielMatchHeaderDTO.getWorkshop(), itemsDTO.getSourceItemId(), designatorMap, structuresDTOMap);
                dto.setAlternateBomDesignator(alternateDesignatorCode);
                dto.setMatchCodes(getMatchCodes(materielMatchHeaderDTO, items, designatorMap, erpOperatRouteMap, structuresDTOMap));
                dto.setItemDesc(itemsDTO.getItemDesc());
                ErpOperatRouteDTO operatRouteDTO = bomService.findBy5AAndAlternateRoutingDesignator(itemsDTO.getItemCode(), dto.getAlternateBomDesignator());
                if (operatRouteDTO == null) {
                    dto.setRoute("否");
                } else {
                    dto.setRoute("是");
                }
                dto.setCertifiedModels(itemsDTO.getSegment28());
            } else {
                // 匹配到多个
                dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
                dto.setItemMatchStatus(MaterielMatchLineDTO.MatchStatus.MULTI_MATCH.getCode());
                dto.setMatchCodes(getMatchCodes(materielMatchHeaderDTO, items, designatorMap, erpOperatRouteMap, structuresDTOMap));
                MaterielMatchLineMatchStatusDTO selectDTO = null;
                //继承逻辑
//            if (inheritItemCode != null) {
//                selectDTO = dto.getMatchCodes().stream().filter(i -> inheritItemCode.equals(i.getItemCode())).findFirst().orElse(null);
//            }

                if (selectDTO == null) {
                    //有bom替代项的优先 多个5A倒序排列 获取第一个
                    List<MaterielMatchLineMatchStatusDTO> matchLineListFilterBomAndSort5A = dto.getMatchCodes().stream().filter(x -> StringUtils.isNotEmpty(x.getAlternateBomDesignator())).sorted(Comparator.comparing(MaterielMatchLineMatchStatusDTO::getItemCode).reversed()).collect(Collectors.toList());
                    //多个5A倒序排列 获取第一个
                    List<MaterielMatchLineMatchStatusDTO> matchLineListSort5A = dto.getMatchCodes().stream().sorted(Comparator.comparing(MaterielMatchLineMatchStatusDTO::getItemCode).reversed()).collect(Collectors.toList());
                    selectDTO = CollectionUtils.isNotEmpty(matchLineListFilterBomAndSort5A) ? matchLineListFilterBomAndSort5A.get(0) : matchLineListSort5A.get(0);

                }

                dto.setItemCode(selectDTO.getItemCode());
                dto.setAlternateBomDesignator(selectDTO.getAlternateBomDesignator());

                dto.setItemDesc(selectDTO.getItemDesc());
                ErpOperatRouteDTO operatRouteDTO = bomService.findBy5AAndAlternateRoutingDesignator(selectDTO.getItemCode(), dto.getAlternateBomDesignator());
                if (operatRouteDTO == null) {
                    dto.setRoute("否");
                } else {
                    dto.setRoute("是");
                }
                dto.setCertifiedModels(selectDTO.getCertifiedModels());
            }
        }


    }

    /**
     * 通过网版物料过滤最终的料号
     *
     * @param itemSource
     * @param screenItem
     * @return
     */
    private List<ItemsDTO> filterByScreenPlateItem(List<ItemsDTO> itemSource, ItemsDTO screenItem) {
        List<ItemsDTO> filterList = itemSource.stream().filter(y -> checkByScreenItem(y, screenItem)).collect(Collectors.toList());
        return filterList;
    }

    //背电极属性过滤
    private List<ItemsDTO> filterBackElectrodeItems(List<ItemsDTO> itemSource, String backElectrodeItemCode) {
        ItemsDTO backElectrodeItems = itemsService.findOneByItemCode(backElectrodeItemCode);
        //获取7A属性
        List<ItemsDTO> filterList = itemSource.stream().filter(y -> checkBackElectrodeItemsDTO(y, backElectrodeItems)).collect(Collectors.toList());
        return filterList;
    }

    /**
     * 排产头查询5A数据
     *
     * @param headerDTOs
     * @return
     */
    public Map<Long, String> query5AByMatchHeadDto(List<MaterielMatchHeaderDTO> headerDTOs) {
        Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList = getExpressRuleLineList();
        Map<Long, String> itemCodeBy5A = new HashMap<>();
        //获取Active、82L、自产电池片的物料集合
        List<ItemsDTO> itemsDTOList = itemsService.queryByMatchingAll();
        if (log.isDebugEnabled()) {
            log.debug("expressRuleLineList:{}", expressRuleLineList);
        }
        //获取所有电池类型属性
        List<BatteryTypeMainDTO> batteryTypeMainDTOList = batteryTypeMainService.queryBatteryCodeTypeAll();
        Map<String, BatteryTypeMainDTO> batteryTypeMainMap = batteryTypeMainDTOList.stream().collect(Collectors.toMap(BatteryTypeMainDTO::getBatteryName, Function.identity(), (k1, k2) -> k1));

        Map<String, String> dpColumnsMap = getDpColAndItemColMap();
        Map<String, String> dpColumnsTransScriptMap = getDpColAndTransScriptMap();

        //获取网版属性配置
        AttrTypeLineDTO screenPlateAttrType = attrUtil.queryAttrTypeLinesByHeaderCode(ATTR_TYPE_043)
                .stream().filter(i -> i.getAttrCode().equals(ATTR_120007)).findFirst().orElseThrow(() -> new BizException("bbom_matchItem_screenPlateAttr_notConfig"));
        //获取应用硅片属性数据
        Map<String, AttrTypeLineDTO> siliconWaferAttrType = attrUtil.queryAttrTypeLinesByHeaderCode("ATTR_TYPE_018")
                .stream().collect(Collectors.toMap(AttrTypeLineDTO::getAttrCode, Function.identity(), (k1, k2) -> k1));

        // 获取规则
        headerDTOs.stream().forEach(headerDTO -> {
            //查询5A数据
            List<ItemsDTO> itemsDTOS = matchItemDTOS(headerDTO, expressRuleLineList, itemsDTOList, batteryTypeMainMap.get(headerDTO.getBatteryType()), dpColumnsMap, dpColumnsTransScriptMap);
            if (CollectionUtils.isNotEmpty(itemsDTOS)) {
                // 网版拆分信息
                MaterielMatchLineDTO firstLine = convert.toLineDTO(headerDTO);
                firstLine.setScheduleDate(headerDTO.getDemandDate());
                firstLine.setItemsDTOS(itemsDTOS);
                firstLine.setIsCatchProduction("量产");
                List<BatteryScreenPlateDTO> batteryScreenPlateDTOList = getBatteryScreenPlateDTOS(headerDTO, headerDTO.getDemandDate().atStartOfDay());
                firstLine.setScreenPlateSwitch(batteryScreenPlateDTOList);
                // 硅片拆分信息
                List<BatterySiliconWaferDTO> siliconWaferList = getBatterySiliconWaferDTOS(headerDTO, headerDTO.getDemandDate().atStartOfDay());
                firstLine.setSiliconWaferList(siliconWaferList);
                List<MaterielMatchLineDTO> matchLineDTOList = new ArrayList<>();
                matchLineDTOList.add(firstLine);
                matchLineDTOList = assembleMatchLineAndItemCodes(itemsDTOS, matchLineDTOList);

                // 使用硅片拆分和网版拆分来过滤料号
                // 先将本行拆成多行,然后再进行过滤料号,最后汇总料号?
                // 先进行网版和硅片的拆分,之后再进行切换过滤料号
                matchLineDTOList = splitScreenPlateAndSiliconWafer(matchLineDTOList);

                // 开始硅片切换和网版切换
                matchLineDTOList = applySiliconWaferSwitchAndScreenSwitch(screenPlateAttrType, matchLineDTOList, headerDTO, siliconWaferAttrType);

                List<ItemsDTO> selItems = matchLineDTOList.stream().map(MaterielMatchLineDTO::getItemsDTOS)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream).collect(Collectors.toList());
                // 选择一个料号
                // 获取替代项

                List<ItemsDTO> hasAltBomDesignator = new ArrayList<>();
                selItems.forEach(item -> {
                    String alternateBomDesignator = getAlternateDesignatorCode(headerDTO.getWorkshop(), item.getSourceItemId());
                    if (StringUtils.isNotEmpty(alternateBomDesignator)) {
                        hasAltBomDesignator.add(item);
                    }
                });
                if (CollectionUtils.isNotEmpty(hasAltBomDesignator)) {
                    selItems = hasAltBomDesignator;
                }
                String itemCode = selItems.stream()
                        .sorted((left, right) -> ComparisonChain
                                .start()
                                .compare(left.getItemCode(), right.getItemCode(), Ordering.usingToString().reverse().nullsLast())
                                .result())
                        .map(ItemsDTO::getItemCode).findFirst().orElse(null);

                itemCodeBy5A.put(headerDTO.getId(), itemCode);
            }
        });
        return itemCodeBy5A;
    }

    private String getAlternateDesignatorCode(String workshop, Long sourceItemId) {
        Map<String, StructuresDTO> structuresDTOMap = materielMatchHeaderService.queryListByStructures();

        LovLineDTO lovLineDTO = LovUtils.get("work_shop", workshop);
        if (null == lovLineDTO) {
            return "";
        }
        if (StringUtils.isEmpty(lovLineDTO.getAttribute10())) {
            return "";
        }
        String orgCode = lovLineDTO.getAttribute10();
        LovLineDTO orgLov = LovUtils.get("inventory_organization", orgCode);
        if (null == orgLov) {
            return "";
        }
        String orgId = orgLov.getAttribute1();
        // 去bom模块获取替代项
        Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap = materielMatchHeaderService.getErpAlternateDesignatorByOrgId(orgId);

        if (CollectionUtils.isNotEmpty(designatorMap.get(Long.valueOf(orgId)))) {
            List<ErpAlternateDesignatorDTO> designatorDTOList = designatorMap.get(Long.valueOf(orgId)).stream().filter(x -> x.getDescription().equals(workshop)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(designatorDTOList)) {
                return "";
            }
            ErpAlternateDesignatorDTO erpAlternateDesignatorDTO = designatorDTOList.get(0);
            StructuresDTO structuresDTO = structuresDTOMap.get(String.format("%s%s%s", erpAlternateDesignatorDTO.getAlternateDesignatorCode(), erpAlternateDesignatorDTO.getOrganizationId(), sourceItemId));
            String alternateDesignatorCode = "";
            if (null != structuresDTO) {
                alternateDesignatorCode = erpAlternateDesignatorDTO.getAlternateDesignatorCode();
            }
            return alternateDesignatorCode;
        } else {
            return "";
        }
    }

    @Override
    @Cacheable(cacheNames = "MaterielMatchHeaderService_getErpAlternateDesignatorByOrgId", key = "#orgId")
    public Map<Long, List<ErpAlternateDesignatorDTO>> getErpAlternateDesignatorByOrgId(String orgId) {
        Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap;
        //获取bom替代项集合
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        designatorQuery.setOrganizationIds(Collections.singletonList(Long.valueOf(orgId)));
        List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryByOrganizationIds(designatorQuery).getBody().getData();
        designatorMap = designatorDTOList.stream().collect(Collectors.groupingBy(ErpAlternateDesignatorDTO::getOrganizationId));
        return designatorMap;
    }

    private List<ComponentsDTO> getComponentsDTOS(StructuresDTO structuresDTO) {
        //根据5A料号+StructuresDTO主键查询行
        //componentsQuery.setBomId(structuresDTO.getId());
        List<ComponentsDTO> componentsDTOList = componentsService.queryList();
        Map<Long, List<ComponentsDTO>> compinentsMap = componentsDTOList.stream().collect(Collectors.groupingBy(ComponentsDTO::getBomId));
        return compinentsMap.get(structuresDTO.getId());
    }

    private List<ComponentsDTO> getComponentsList(List<Long> componentItemId) {
        return componentsService.getComponentsList(componentItemId);
    }

    //bom替代项取值
    private Map<String, ErpAlternateDesignatorDTO> getErpAlternateDesignatorDTO() {
        //获取bom替代项、库存组织根据车间
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        //designatorQuery.setDescription(headerDTO.getWorkshop());
        List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryList(designatorQuery).getBody().getData();
        Map<String, ErpAlternateDesignatorDTO> designatorDTOMap = designatorDTOList.stream().collect(Collectors.toMap(ErpAlternateDesignatorDTO::getDescription, Function.identity(), (v1, v2) -> v1));
        return designatorDTOMap;
    }

    /**
     * 网版拆分
     *
     * @param materielMatchLineDTOList
     * @return
     */
    private List<MaterielMatchLineDTO> splitScreenPlateAndSiliconWafer(
            List<MaterielMatchLineDTO> materielMatchLineDTOList) {

        // 这里变成一行一个数量
        // 将行按天进行分组
        Map<LocalDate, List<MaterielMatchLineDTO>> scheduleDateAndLineDTOS = materielMatchLineDTOList.stream().collect(Collectors.groupingBy(MaterielMatchLineDTO::getScheduleDate));
        List<MaterielMatchLineDTO> result = new LinkedList<>();
        scheduleDateAndLineDTOS.values().forEach(lines -> {
            MaterielMatchLineDTO firstLine = lines.get(0);
            List<MaterielMatchLineDTO> loopResult = null;
            if (CollectionUtils.isNotEmpty(firstLine.getScreenPlateSwitch())) {
                // 通过网版切换获取
                loopResult = getMatchLineByScreenPlateSwitch(lines);
            } else {
                loopResult = lines;
            }
            // 进行硅片切换
            if (CollectionUtils.isNotEmpty(firstLine.getSiliconWaferList())) {
                loopResult = getMatchLineBySiliconWaferSwitch(loopResult);
            }

            result.addAll(loopResult);
        });
        return result;
    }

    private List<MaterielMatchLineDTO> getMatchLineBySiliconWaferSwitch(List<MaterielMatchLineDTO> linesDTO) {
        MaterielMatchLineDTO firstLineDTO = linesDTO.get(0);
        List<MaterielMatchLineDTO> result = new ArrayList<>();
        //通过硅片品类与电池片料号属性segment4进行匹配
        Map<String, LovLineDTO> waferCategoryLovMap = LovUtils.getAllByHeaderCode("4A00100100109");
        waferCategoryLovMap.forEach((key,value)->{
            if(StringUtils.isNotBlank(value.getAttribute1())){
                LovLineDTO areaLov = LovUtils.getById(value.getAttribute1());
                if(Objects.nonNull(areaLov)){
                    value.setAttribute1(areaLov.getLovValue());
                }
            }
        });

        for (BatterySiliconWaferDTO batterySiliconWaferDTO : firstLineDTO.getSiliconWaferList()) {
            if (null == batterySiliconWaferDTO.getLineQty() || StringUtils.isBlank(batterySiliconWaferDTO.getOldBatteryValue())) {
                for (MaterielMatchLineDTO materielMatchLineDTO : linesDTO) {
                    //不拆分
                    materielMatchLineDTO.setSiliconWaferProperties(batterySiliconWaferDTO.getSiliconWaferProperties());
                    materielMatchLineDTO.setSiliconWaferCondition(batterySiliconWaferDTO.getConditionItem());
                    materielMatchLineDTO.setSiliconWaferValue(batterySiliconWaferDTO.getWaferThickness());
                    //硅片品类匹配
                    matchWaferCategoryByArea(materielMatchLineDTO,waferCategoryLovMap,batterySiliconWaferDTO.getWaferCategory());
                    result.add(materielMatchLineDTO);
                }
                continue;
            }
            // 当前线体数
            Integer curLine = batterySiliconWaferDTO.getLineQty();
            int switchLine = 1;

            for (MaterielMatchLineDTO dto : linesDTO) {
                // 线体数小的时候 全部给新网版
                if (switchLine <= curLine) {
                    dto.setSiliconWaferProperties(batterySiliconWaferDTO.getSiliconWaferProperties());
                    dto.setSiliconWaferCondition(batterySiliconWaferDTO.getConditionItem());
                    dto.setSiliconWaferValue(batterySiliconWaferDTO.getWaferThickness());
                    //硅片品类匹配
                    matchWaferCategoryByArea(dto,waferCategoryLovMap,batterySiliconWaferDTO.getWaferCategory());
                    result.add(dto);
                } else {
                    // 剩下的全部给旧网版
                    dto.setSiliconWaferProperties(batterySiliconWaferDTO.getOldSiliconWaferProperties());
                    dto.setSiliconWaferCondition(batterySiliconWaferDTO.getOldConditionItem());
                    dto.setSiliconWaferValue(batterySiliconWaferDTO.getOldBatteryValue());
                    //硅片品类匹配
                    matchWaferCategoryByArea(dto,waferCategoryLovMap,batterySiliconWaferDTO.getWaferCategory());
                    result.add(dto);
                }
                switchLine++;
            }
        }
        return result;
    }

    /**
     * 除获取硅片厚度外，还需要获取硅片品类字段，通过硅片品类与电池片料号属性segment4进行匹配。当通过硅片切换计划获取到硅片品类后，
     * 通过品类对应LOV：4A00100100109 获取attribute1，检查电池片料号属性segment4的值是否与LOV行的attribute1一致（若attribute1未维护，则不进行校验）
     * @param waferCategoryLovMap
     * @param waferCategory
     * @return
     */
    private void matchWaferCategoryByArea(MaterielMatchLineDTO materielMatchLineDTO,Map<String, LovLineDTO> waferCategoryLovMap,String waferCategory) {
        List<ItemsDTO> items = materielMatchLineDTO.getItemsDTOS();
        LovLineDTO lov = waferCategoryLovMap.get(waferCategory);
        if(Objects.nonNull(lov) && StringUtils.isNotBlank(lov.getAttribute1())
                && CollectionUtils.isNotEmpty(items)){
            items=items.stream().filter(item->item.getSegment4().equals(lov.getAttribute1())).collect(Collectors.toList());
            materielMatchLineDTO.setItemsDTOS(items);
        }else{
            List<String> attr1List = Lists.newArrayList();
            for(String key : waferCategoryLovMap.keySet()){
                if(StringUtils.isNotBlank(waferCategoryLovMap.get(key).getAttribute1())){
                    attr1List.add(waferCategoryLovMap.get(key).getAttribute1());
                }
            }
            items=items.stream().filter(item->!attr1List.contains(item.getSegment4())).collect(Collectors.toList());
            materielMatchLineDTO.setItemsDTOS(items);
        }
        materielMatchLineDTO.setWaferCategory(waferCategory);
    }

    private List<MaterielMatchLineDTO> getMatchLineByScreenPlateSwitch(
            List<MaterielMatchLineDTO> linesDTO
    ) {
        MaterielMatchLineDTO firstLineDTO = linesDTO.get(0);
        List<BatteryScreenPlateDTO> screenPlateSwitchList = firstLineDTO.getScreenPlateSwitch();
        BatteryScreenPlateDTO screenPlateSwitch = screenPlateSwitchList.stream().findFirst().orElse(null);

        // 正电极网板主栅
        BatteryScreenPlateDTO screenPlateSwitchZS = screenPlateSwitchList.stream().filter(screen -> StringUtils.equalsAny(screen.getScreenPlateVersionCategoryNew(), ZDJWB, ZDCWB) && screen.getItemDescNew().contains(LovHeaderCodeConstant.ZS)).findFirst().orElse(new BatteryScreenPlateDTO());
        // 正电极网板细栅
        BatteryScreenPlateDTO screenPlateSwitchXS = screenPlateSwitchList.stream().filter(screen -> StringUtils.equalsAny(screen.getScreenPlateVersionCategoryNew(), ZDJWB, ZDCWB) && screen.getItemDescNew().contains(LovHeaderCodeConstant.XS)).findFirst().orElse(new BatteryScreenPlateDTO());
        // 背电极网版主栅
        BatteryScreenPlateDTO backScreenPlateSwitchZS = screenPlateSwitchList.stream().filter(screen -> StringUtils.equalsAny(screen.getScreenPlateVersionCategoryNew(), BDJWB, BDCWB) && screen.getItemDescNew().contains(LovHeaderCodeConstant.ZS)).findFirst().orElse(null);
        // 背电极网版细栅
        BatteryScreenPlateDTO backScreenPlateSwitchXS = screenPlateSwitchList.stream().filter(screen -> StringUtils.equalsAny(screen.getScreenPlateVersionCategoryNew(), BDJWB, BDCWB) && screen.getItemDescNew().contains(LovHeaderCodeConstant.XS)).findFirst().orElse(null);

        String mainGridInfo = StringUtils.isNotEmpty(screenPlateSwitchZS.getMainGridInfo()) ? screenPlateSwitchZS.getMainGridInfo() : screenPlateSwitchXS.getMainGridInfo();
        List<MaterielMatchLineDTO> result = new ArrayList<>();
        //不切换
        if (null == screenPlateSwitch.getLine() || StringUtils.isBlank(screenPlateSwitch.getItemCodeOld())) {
            for (MaterielMatchLineDTO materielMatchLineDTO : linesDTO) {
                //不拆分
                materielMatchLineDTO.setScreenPlateItemCode(screenPlateSwitchZS.getItemCodeNew());
                materielMatchLineDTO.setScreenPlateItemCodeDesc(screenPlateSwitchZS.getItemDescNew());
                materielMatchLineDTO.setMainGridInfo(mainGridInfo);
                materielMatchLineDTO.setScreenPlateItemCodeFineGrid(screenPlateSwitchXS.getItemCodeNew());
                materielMatchLineDTO.setScreenPlateItemCodeDescFineGrid(screenPlateSwitchXS.getItemDescNew());
                materielMatchLineDTO.setPositiveElectrodeScreenFineGrid(StringUtils.isNotBlank(screenPlateSwitchZS.getGridsNumber()) ? screenPlateSwitchZS.getGridsNumber() : screenPlateSwitchXS.getGridsNumber());
                //新旧网版料号标识(N/O)
                materielMatchLineDTO.setNewOrOldItemFlag("N");
                //网版类别[正电极-Z、背电极-F]
                materielMatchLineDTO.setCategoryFlag(StringUtils.isNotEmpty(screenPlateSwitch.getScreenPlateVersionCategoryNew()) && screenPlateSwitch.getScreenPlateVersionCategoryNew().contains(LovHeaderCodeConstant.ZDJWB) ? "Z" : "F");
                materielMatchLineDTO.setItemsDTOS(materielMatchLineDTO.getItemsDTOS());
                if (backScreenPlateSwitchZS != null) {
                    materielMatchLineDTO.setScreenPlateCodeFilter(backScreenPlateSwitchZS.getItemCodeNew());
                    materielMatchLineDTO.setScreenPlateCodeDescFilter(backScreenPlateSwitchZS.getItemDescNew());
                    materielMatchLineDTO.setNegativeElectrodeScreenFineGrid(backScreenPlateSwitchZS.getGridsNumber());
                }
                if (backScreenPlateSwitchXS != null) {
                    materielMatchLineDTO.setScreenPlateCodeFilterFineGrid(backScreenPlateSwitchXS.getItemCodeNew());
                    materielMatchLineDTO.setScreenPlateCodeDescFilterFineGrid(backScreenPlateSwitchXS.getItemDescNew());
                    materielMatchLineDTO.setNegativeElectrodeScreenFineGrid(backScreenPlateSwitchXS.getGridsNumber());
                }
                result.add(materielMatchLineDTO);
            }
        }
        // 当前线体数
        Integer curLine = new BigDecimal(screenPlateSwitch.getLine()).intValue();
        Integer switchLine = 1;

        for (MaterielMatchLineDTO dto : linesDTO) {
            // 线体数小的时候 全部给新网版
            if (switchLine <= curLine) {
                dto.setScreenPlateItemCode(screenPlateSwitchZS.getItemCodeNew());
                dto.setScreenPlateItemCodeDesc(screenPlateSwitchZS.getItemDescNew());
                dto.setMainGridInfo(mainGridInfo);
                dto.setScreenPlateItemCodeFineGrid(screenPlateSwitchXS.getItemCodeNew());
                dto.setScreenPlateItemCodeDescFineGrid(screenPlateSwitchXS.getItemDescNew());
                dto.setPositiveElectrodeScreenFineGrid(StringUtils.isNotBlank(screenPlateSwitchZS.getGridsNumber()) ? screenPlateSwitchZS.getGridsNumber() : screenPlateSwitchXS.getGridsNumber());
                dto.setSwitchStartDate(screenPlateSwitch.getEffectiveStartDate());
                dto.setSwitchEndDate(screenPlateSwitch.getEffectiveEndDate());
                //拆分标识
                dto.setSplitFlag("Y");
                //新旧网版料号标识(N/O)
                dto.setNewOrOldItemFlag("N");
                //网版类别[正电极-Z、背电极-F]
                dto.setCategoryFlag(StringUtils.isNotEmpty(screenPlateSwitch.getScreenPlateVersionCategoryNew()) && screenPlateSwitch.getScreenPlateVersionCategoryNew().contains(LovHeaderCodeConstant.ZDJWB) ? "Z" : "F");
                dto.setTotalLine(new BigDecimal(screenPlateSwitch.getLine()));
                if (backScreenPlateSwitchZS != null) {
                    dto.setScreenPlateCodeFilter(backScreenPlateSwitchZS.getItemCodeNew());
                    dto.setScreenPlateCodeDescFilter(backScreenPlateSwitchZS.getItemDescNew());
                    dto.setNegativeElectrodeScreenFineGrid(backScreenPlateSwitchZS.getGridsNumber());
                }
                if (backScreenPlateSwitchXS != null) {
                    dto.setScreenPlateCodeFilterFineGrid(backScreenPlateSwitchXS.getItemCodeNew());
                    dto.setScreenPlateCodeDescFilterFineGrid(backScreenPlateSwitchXS.getItemDescNew());
                    dto.setNegativeElectrodeScreenFineGrid(backScreenPlateSwitchXS.getGridsNumber());
                }
                result.add(dto);
            } else {
                // 剩下的全部给旧网版
                // 如果小于1,则需要拆分
                dto.setScreenPlateItemCode(screenPlateSwitchZS.getItemCodeOld());
                dto.setScreenPlateItemCodeDesc(screenPlateSwitchZS.getItemDescOld());
                dto.setMainGridInfo(mainGridInfo);
                dto.setScreenPlateItemCodeFineGrid(screenPlateSwitchXS.getItemCodeOld());
                dto.setScreenPlateItemCodeDescFineGrid(screenPlateSwitchXS.getItemDescOld());
                dto.setSwitchStartDate(screenPlateSwitch.getEffectiveStartDate());
                dto.setSwitchEndDate(screenPlateSwitch.getEffectiveEndDate());
                dto.setPositiveElectrodeScreenFineGrid(StringUtils.isNotBlank(screenPlateSwitchZS.getGridsNumber()) ? screenPlateSwitchZS.getGridsNumber() : screenPlateSwitchXS.getGridsNumber());
                //拆分标识
                dto.setSplitFlag("Y");
                //新旧网版料号标识(N/O)
                // 此时为旧网版料号
                dto.setNewOrOldItemFlag("O");
                //网版类别[正电极-Z、背电极-F]
                dto.setCategoryFlag(StringUtils.isNotEmpty(screenPlateSwitch.getScreenPlateVersionCategoryOld())
                        && screenPlateSwitch.getScreenPlateVersionCategoryOld().contains(LovHeaderCodeConstant.ZDJWB) ? "Z" : "F");
                dto.setTotalLine(new BigDecimal(screenPlateSwitch.getLine()));
                if (backScreenPlateSwitchZS != null) {
                    dto.setScreenPlateCodeFilter(backScreenPlateSwitchZS.getItemCodeOld());
                    dto.setScreenPlateCodeDescFilter(backScreenPlateSwitchZS.getItemDescOld());
                    dto.setNegativeElectrodeScreenFineGrid(backScreenPlateSwitchZS.getGridsNumber());
                }
                if (backScreenPlateSwitchXS != null) {
                    dto.setScreenPlateCodeFilterFineGrid(backScreenPlateSwitchXS.getItemCodeOld());
                    dto.setScreenPlateCodeDescFilterFineGrid(backScreenPlateSwitchXS.getItemDescOld());
                    dto.setNegativeElectrodeScreenFineGrid(backScreenPlateSwitchXS.getGridsNumber());
                }
                result.add(dto);
            }
            switchLine++;
        }
        return result;
    }

    private List<MaterielMatchLineMatchStatusDTO> getMatchCodes(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                                                List<ItemsDTO> items,
                                                                Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                                                Map<String, ErpOperatRouteDTO> erpOperatRouteMap,
                                                                Map<String, StructuresDTO> structuresDTOMap) {
        List<MaterielMatchLineMatchStatusDTO> result = new ArrayList<>();
        for (ItemsDTO item : items) {
            MaterielMatchLineMatchStatusDTO dto = new MaterielMatchLineMatchStatusDTO();
            dto.setItemCode(item.getItemCode());
            dto.setItemDesc(item.getItemDesc());
            //这个方法适用于很多地方 是合理的
            if (items.size() == 1) {
                dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
            } else {
                dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MULTI_MATCH.getCode());
            }
            String alternateBomDesignator = getAlternateDesignatorCode(materielMatchHeaderDTO.getWorkshop(), item.getSourceItemId(), designatorMap, structuresDTOMap);
            dto.setAlternateBomDesignator(alternateBomDesignator);
            if (StringUtils.isEmpty(alternateBomDesignator)) {
                dto.setRemark("未找到BOM替代项");
            }
            //工艺路线
            if (isExistRoute(materielMatchHeaderDTO, item.getSourceItemId(), designatorMap, erpOperatRouteMap)) {
                dto.setRoute("yes");
            } else {
                dto.setRoute("no");
            }
            dto.setCertifiedModels(item.getSegment28());
            result.add(dto);
        }
        return result;
    }

    /**
     * 根据库存组织、5A料号的id、bom替代项查询工艺路线信息
     *
     * @param materielMatchHeaderDTO
     * @param sourceItemId
     * @return
     */
    private Boolean isExistRoute(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                 Long sourceItemId,
                                 Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                 Map<String, ErpOperatRouteDTO> erpOperatRouteMap) {
        LovLineDTO lovLineDTO = LovUtils.get("work_shop", materielMatchHeaderDTO.getWorkshop());
        if (null == lovLineDTO) {
            return false;
        }

        String orgCode = lovLineDTO.getAttribute10();
        LovLineDTO orgLov = LovUtils.get("inventory_organization", orgCode);
        if (null == orgLov) {
            return false;
        }
        String orgId = orgLov.getAttribute1();
        // 去bom模块获取替代项

        if (CollectionUtils.isNotEmpty(designatorMap.get(Long.valueOf(orgId)))) {
            List<ErpAlternateDesignatorDTO> designatorDTOList = designatorMap.get(Long.valueOf(orgId)).stream().filter(x -> x.getDescription().equals(materielMatchHeaderDTO.getWorkshop())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(designatorDTOList)) {
                return false;
            }
            ErpAlternateDesignatorDTO erpAlternateDesignatorDTO = designatorDTOList.get(0);
            ErpOperatRouteDTO erpOperatRouteDTO = erpOperatRouteMap.get(String.format("%s%s%s", erpAlternateDesignatorDTO.getAlternateDesignatorCode(), erpAlternateDesignatorDTO.getOrganizationId(), sourceItemId));
            if (null != erpOperatRouteDTO) {
                return true;
            }
        }
        return false;
    }

    @Override
    public String getAlternateDesignatorCode(String workshop, Long sourceItemId,
                                             Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                             Map<String, StructuresDTO> structuresDTOMap) {
        LovLineDTO lovLineDTO = LovUtils.get("work_shop", workshop);
        if (null == lovLineDTO) {
            return "";
        }
        if (StringUtils.isEmpty(lovLineDTO.getAttribute10())) {
            return "";
        }
        String orgCode = lovLineDTO.getAttribute10();
        LovLineDTO orgLov = LovUtils.get("inventory_organization", orgCode);
        if (null == orgLov) {
            return "";
        }
        String orgId = orgLov.getAttribute1();
        // 去bom模块获取替代项

        if (CollectionUtils.isNotEmpty(designatorMap.get(Long.valueOf(orgId)))) {
            List<ErpAlternateDesignatorDTO> designatorDTOList = designatorMap.get(Long.valueOf(orgId)).stream().filter(x -> x.getDescription().equals(workshop)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(designatorDTOList)) {
                return "";
            }
            ErpAlternateDesignatorDTO erpAlternateDesignatorDTO = designatorDTOList.get(0);
            StructuresDTO structuresDTO = structuresDTOMap.get(String.format("%s%s%s", erpAlternateDesignatorDTO.getAlternateDesignatorCode(), erpAlternateDesignatorDTO.getOrganizationId(), sourceItemId));
            String alternateDesignatorCode = "";
            if (null != structuresDTO) {
                alternateDesignatorCode = erpAlternateDesignatorDTO.getAlternateDesignatorCode();
            }
            return alternateDesignatorCode;
        } else {
            return "";
        }
    }

    //料号行组装物料信息
    private List<MaterielMatchLineDTO> assembleMatchLineAndItemCodes(List<ItemsDTO> itemsDTOS, List<MaterielMatchLineDTO> matchLineDTOList) {
        List<String> workshopList = matchLineDTOList.stream().map(MaterielMatchLineDTO::getWorkshop).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, List<ItemsDTO>> itemMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(workshopList)) {
            workshopList.forEach(workshop -> {
                // 将车间转换为库存组织
                Long orgId = workshopConvrterOrgId(workshop);
                // 将物料转换为当前车间的物料
                List<ItemsDTO> items = itemsDTOS.stream().map(item ->
                                itemsService.findOneByItemCodeAndOrganizationId(item.getItemCode(), orgId)).filter(ObjectUtil::isNotNull)
                        .collect(Collectors.toList());
                itemMap.put(workshop, items);
            });
        }

        matchLineDTOList.forEach(i -> {
            List<ItemsDTO> items = Collections.emptyList();
            if (StringUtils.isNotEmpty(i.getWorkshop())) {
                items = itemMap.getOrDefault(i.getWorkshop(), Collections.emptyList());
            }
            //在电池料号匹配时，若物料的生命周期为中大样，但是临时量产标识为是时，可将料号作为量产订单使用
            items.forEach(ele -> {
                if (Objects.equals("中大样", ele.getLifecycleState()) && Objects.equals("是", ele.getIsTemporaryOutput())) {
                    ele.setLifecycleState("量产");
                }
            });

            // #24373 处理研发量产标识
            // 料号匹配时，需要根据行表bbom_materiel_match_line中is_catch_production（研发/量产）字段，
            // 校验BBOM_ITEM表中lifecycle_state字段，
            // 行是研发时匹配料号时5A料号的lifecycle_state字段是中大样，行是量产时匹配料号时5A料号的lifecycle_state字段是量产。
            // 获取本行是研发还是量产
            String isCatchProduction = i.getIsCatchProduction();
            // 将研发转换为中大样
            if ("研发".equals(isCatchProduction)) {
                isCatchProduction = "中大样";
            }
            String finalIsCatchProduction = isCatchProduction;
            i.setItemsDTOS(items.stream().filter(a -> finalIsCatchProduction.equals(a.getLifecycleState())).collect(Collectors.toList()));
        });
        return matchLineDTOList;
    }

    private List<MaterielMatchLineDTO> applySiliconWaferSwitchAndScreenSwitch(
            AttrTypeLineDTO screenPlateAttrType,
            List<MaterielMatchLineDTO> matchLineDTOList,
            MaterielMatchHeaderDTO materielMatchHeaderDTO,
            Map<String, AttrTypeLineDTO> siliconWaferAttrType) {
        // 应用硅片切换,去过滤匹配到的5A 2025.1.14去除原逻辑需求1794
//        siliconWaferSwitch(matchLineDTOList, materielMatchHeaderDTO, siliconWaferAttrType);
        // 应用网版切换
        screenPlateSwitch(screenPlateAttrType, matchLineDTOList, materielMatchHeaderDTO);
        return matchLineDTOList;
    }

    /**
     * 应用硅片切换,去过滤匹配到的5A
     *
     * @param matchLineDTOList
     * @param materielMatchHeaderDTO
     * @param siliconWaferAttrType
     */
    private void siliconWaferSwitch(List<MaterielMatchLineDTO> matchLineDTOList, MaterielMatchHeaderDTO materielMatchHeaderDTO, Map<String, AttrTypeLineDTO> siliconWaferAttrType) {

        for (MaterielMatchLineDTO matchLineDTO : matchLineDTOList) {
            if (StringUtils.isNotBlank(matchLineDTO.getItemCode())) {
                continue;
            }
            if (CollectionUtils.isEmpty(matchLineDTO.getSiliconWaferList())) {
                continue;
            }

            List<ItemsDTO> filterItems = matchLineDTO.getItemsDTOS().stream().filter(itemsDTO -> {
                AttrTypeLineDTO attrTypeLineDTO = siliconWaferAttrType.get(matchLineDTO.getSiliconWaferProperties());
                if (attrTypeLineDTO == null) {
                    return false;
                }
                String fieldValue = (String) ReflectUtil.getFieldValue(itemsDTO, attrTypeLineDTO.getAttribute2());
                if (NumberUtil.isNumber(fieldValue) && NumberUtil.isNumber(matchLineDTO.getSiliconWaferValue())) {
                    return new BigDecimal(fieldValue).compareTo(new BigDecimal(matchLineDTO.getSiliconWaferValue())) == 0;
                }
                return false;
            }).collect(Collectors.toList());
            matchLineDTO.setItemsDTOS(filterItems);
        }
    }

    private List<BatterySiliconWaferDTO> getBatterySiliconWaferDTOS(MaterielMatchHeaderDTO materielMatchHeaderDTO, LocalDateTime verifyDate) {
        List<BatterySiliconWaferDTO> siliconWaferList = batterySiliconWaferService.listByMatchItem(materielMatchHeaderDTO.getBatteryType(), materielMatchHeaderDTO.getBasePlace(), materielMatchHeaderDTO.getWorkshop(), materielMatchHeaderDTO.getWorkunit(), verifyDate);
        if(materielMatchHeaderDTO.getPcsSourceType().contains("DT")){
            siliconWaferList = siliconWaferList.stream().filter(item->"Y".equals(item.getLowCarbonFlag())).collect(Collectors.toList());
        }else{
            siliconWaferList = siliconWaferList.stream().filter(item->"N".equals(item.getLowCarbonFlag())).collect(Collectors.toList());
        }
        return siliconWaferList;
    }

    /**
     * @param screenPlateAttrType
     * @param matchLineDTOList
     * @param materielMatchHeaderDTO
     */
    private void screenPlateSwitch(AttrTypeLineDTO screenPlateAttrType, List<MaterielMatchLineDTO> matchLineDTOList,
                                   MaterielMatchHeaderDTO materielMatchHeaderDTO) {
        for (MaterielMatchLineDTO matchLineDTO : matchLineDTOList) {
            if (StringUtils.isNotBlank(matchLineDTO.getItemCode())) {
                continue;
            }
            // 通过网版的配置去进行过滤
            // 通过7A的segment17 对应 5A的 segment16
            List<ItemsDTO> newItems = matchLineDTO.getItemsDTOS();
            if (StringUtils.isNotBlank(matchLineDTO.getScreenPlateItemCode())) {
                newItems = screenPlateItemVerify(screenPlateAttrType, matchLineDTO.getItemsDTOS(), matchLineDTO.getScreenPlateItemCode());
            }
            // 25.09.09 仲华要求进行注释 不针对背电极网版进行过滤
//            if (StringUtils.isNotBlank(matchLineDTO.getScreenPlateCodeFilter())) {
//                newItems = screenPlateItemVerify(screenPlateAttrType, matchLineDTO.getItemsDTOS(), matchLineDTO.getScreenPlateCodeFilter());
//            }
            matchLineDTO.setItemsDTOS(newItems);
        }
    }

    private List<BatteryScreenPlateDTO> getBatteryScreenPlateDTOS(MaterielMatchHeaderDTO materielMatchHeaderDTO, LocalDateTime verifyDate) {
        List<BatteryScreenPlateDTO> batteryScreenPlateDTOList = batteryScreenPlateService.getByMatchItem(
                materielMatchHeaderDTO.getBatteryType(), materielMatchHeaderDTO.getBasePlace(), materielMatchHeaderDTO.getWorkshop(), materielMatchHeaderDTO.getWorkunit(), verifyDate);
        return batteryScreenPlateDTOList;
    }

    /**
     * 正电极网版的数据遍历插入背电极网版料号
     *
     * @param matchLineDTO
     * @param materielMatchHeaderDTO
     * @return
     */
    public void screenPlateSwitchByCategoryFlag(MaterielMatchLineDTO matchLineDTO,
                                                MaterielMatchHeaderDTO materielMatchHeaderDTO) {

        if (StringUtils.isNotBlank(matchLineDTO.getItemCode())) {
            return;
        }

        // 查询补齐数据
        List<BatteryScreenPlateDTO> batteryScreenPlateDTOList = getBatteryScreenPlateDTOS(materielMatchHeaderDTO, matchLineDTO.getScheduleDate().atStartOfDay());
        if (CollectionUtils.isEmpty(batteryScreenPlateDTOList)) {
            return;
        }
        //网版料号限制新旧网版类别必须一致  获取背电极网版
        List<BatteryScreenPlateDTO> plateDTOS = batteryScreenPlateDTOList.stream().filter(screen -> LovHeaderCodeConstant.FDJWB.equals(screen.getScreenPlateVersionCategoryNew())).collect(Collectors.toList());
        //matchLineDTO.getNewOrOldItemFlag 为N的是新网版  新网版screenPlateCodeFilter存的也是新网版料号
        matchLineDTO.setScreenPlateCodeFilter("N".equals(matchLineDTO.getNewOrOldItemFlag()) ? plateDTOS.get(0).getItemCodeNew() : plateDTOS.get(0).getItemCodeOld());
    }

    private List<ItemsDTO> screenPlateItemVerify(AttrTypeLineDTO screenPlateAttrType, List<ItemsDTO> verifyItems, String itemCode) {
        ItemsDTO itemNew = itemsService.findOneByItemCode(itemCode);
        String itemNewVerifyVal = (String) ReflectUtil.getFieldValue(itemNew, screenPlateAttrType.getSourceColumn());
        // 如果新网版是无的话就不校验
        if (StringUtils.isBlank(itemNewVerifyVal) || itemNewVerifyVal.equals(none)) {
            return verifyItems;
        }

        verifyItems = verifyItems.stream().filter(i -> {
            String itemVerifyVal = (String) ReflectUtil.getFieldValue(i, screenPlateAttrType.getAttribute2());
            if (itemVerifyVal.equals(itemNewVerifyVal)) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        return verifyItems;
    }

    private List<ItemsDTO> matchItemDTOS(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                         Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                         List<ItemsDTO> itemsList,
                                         BatteryTypeMainDTO batteryTypeMainDTO,
                                         Map<String, String> dpColumnsMap,
                                         Map<String, String> dpColumnsTransScriptMap) {
        batteryTypeMainDTO = batteryTypeMainDEConvert.deepCopy(batteryTypeMainDTO);
        // 查询5A数据
        ItemsQuery itemsQuery = buildItemQuery(materielMatchHeaderDTO, batteryTypeMainDTO, dpColumnsMap, dpColumnsTransScriptMap);
        if (log.isDebugEnabled()) {
            log.debug("itemsQuery:{}", itemsQuery);
        }
        List<ItemsDTO> itemsDTOList = itemsService.filterList(itemsList, itemsQuery);
        if (log.isDebugEnabled()) {
            log.debug("itemsDTOList:{}", itemsDTOList);
        }
        // 通过规则筛选
        itemsDTOList = filterByRules(itemsDTOList, materielMatchHeaderDTO, dpColumnsMap, batteryTypeMainDTO, expressRuleLineList);
        //料号匹配考虑兼容特殊属性电池兼容
        // 2025/04/02 仲华要求入库计划BOM无数据不过滤 需特殊处理 bbom_components
        if (Objects.equals("plan", materielMatchHeaderDTO.getPlanType())) {
            itemsDTOList = this.filterInComponents(itemsDTOList);
        }
        return itemsDTOList;
    }

    private List<ItemsDTO> filterByRules(List<ItemsDTO> itemsDTOList, MaterielMatchHeaderDTO materielMatchHeaderDTO, Map<String, String> dpColumnsMap, BatteryTypeMainDTO batteryTypeMainDTO, Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList) {
        List<ExpressRuleLineDTO> expressRuleLineDTOList = getMeetDpRules(materielMatchHeaderDTO, batteryTypeMainDTO, expressRuleLineList);
        if (log.isDebugEnabled()) {
            log.debug("expressRuleLineDTOList:{}", expressRuleLineDTOList);
        }
        return itemsDTOList.stream().filter(itemsDTO -> {
            if (CollectionUtils.isEmpty(expressRuleLineDTOList)) {
                return true;
            }
            Map<String, Object> itemFieldValueMap = BeanUtil.beanToMap(itemsDTO, false, true);

            boolean result = true;
            String express = ExpressUtils.getControlDetailsExpress(expressRuleLineDTOList);
            Map<String, String> params = getParams(itemFieldValueMap, express);
            boolean b = ExpressUtils.execExpress(express, params);
            if (log.isDebugEnabled()) {
                log.debug("express:{},params:{},result:{}", express, params, b);
            }
            return b;
        }).collect(Collectors.toList());
    }

    /**
     * 获取符合Dp规则的行
     *
     * @param materielMatchHeaderDTO
     * @param batteryTypeMainDTO
     * @return
     */
    private List<ExpressRuleLineDTO> getMeetDpRules(MaterielMatchHeaderDTO materielMatchHeaderDTO, BatteryTypeMainDTO batteryTypeMainDTO, Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList) {
        // 使用dp筛选出相关的规则行
        Map<String, Object> dpMatchItemContext = getDpMatchItemContext(materielMatchHeaderDTO, batteryTypeMainDTO);

        List<ExpressRuleLineDTO> expressRuleLineDTOList = new ArrayList<>();
        expressRuleLineList.forEach((key, value) -> {
            value.forEach(item -> {
                item.getDetails().forEach(detail -> {
                    String sourceColumn = detail.getSourceColumn();
                    try {
                        LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, Long.parseLong(sourceColumn));
                        sourceColumn = lovLineDTO.getAttribute6();
                    } catch (Exception e) {
                        if (log.isDebugEnabled()) {
                            log.debug("字段不存在:{}", sourceColumn);
                        }
                    }
                    if (Objects.equals("battery_type", sourceColumn) && CollectionUtils.isNotEmpty(detail.getValues())) {
                        detail.getValues().forEach(detailValue -> {
                            String attrValue = LovUtils.getNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, detailValue.getAttrValue());
                            detailValue.setAttrValue(StringUtils.isNotEmpty(attrValue) ? attrValue : detailValue.getAttrValue());
                        });
                    }
                });
            });
            boolean flag = false;
            //校验是否存在规则有效期内
            flag = isFlag(materielMatchHeaderDTO, value, flag);
            String express = ExpressUtils.getDetailsExpress(value);
            Map<String, String> params = getParams(dpMatchItemContext, express);
            boolean b = ExpressUtils.execExpress(express, params);
            if (log.isDebugEnabled()) {
                log.debug("express:{},params:{},result:{}", express, params, b);
            }
            if (flag && b) {
                expressRuleLineDTOList.addAll(value);
            }
        });
        return expressRuleLineDTOList;
    }

    private Map<String, Object> getDpMatchItemContext(MaterielMatchHeaderDTO materielMatchHeaderDTO, BatteryTypeMainDTO batteryTypeMainDTO) {
        Map<String, Object> dpMatchItemMap = BeanUtil.beanToMap(materielMatchHeaderDTO, false, true);
        // 气候性策略转换
        Map<String, Object> batteryTypeMainContext = BeanUtil.beanToMap(batteryTypeMainDTO, false, true);

        batteryTypeMainContext.putAll(dpMatchItemMap);
        for (Map.Entry<String, Object> stringObjectEntry : batteryTypeMainContext.entrySet()) {
            try {
                LovLineDTO lineDTO = LovUtils.get(Long.parseLong(String.valueOf(stringObjectEntry.getValue())));
                if (lineDTO != null) {
                    batteryTypeMainContext.put(stringObjectEntry.getKey(), lineDTO.getLovValue());
                }
            } catch (Exception e) {
                // 出现问题则直接放入值
                if (Objects.equals("batteryName", stringObjectEntry.getKey())) {
                    LovLineDTO lineDTO = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, String.valueOf(stringObjectEntry.getValue()));
                    batteryTypeMainContext.put(stringObjectEntry.getKey(), Objects.nonNull(lineDTO) ? lineDTO.getLovValue() : stringObjectEntry.getValue());
                } else {
                    batteryTypeMainContext.put(stringObjectEntry.getKey(), stringObjectEntry.getValue());
                }
            }
        }

        return batteryTypeMainContext;
    }

    private Map<String, String> getParams(Map<String, Object> dpMatchItemContext, String express) {
        Map<String, String> params = new HashMap<>();
        final String regex = "\\{(.*?)\\}";

        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(express);

        while (matcher.find()) {
            for (int i = 1; i <= matcher.groupCount(); i++) {
                String field = matcher.group(i);
                if (StringUtils.isBlank(field)) {
                    continue;
                }
                String columnValue;
                try {
                    if ("specialArea".equals(field)) {
                        columnValue = LovUtils.getByName("country", dpMatchItemContext.get(field).toString()).getLovValue();
                    } else {
                        columnValue = LovUtils.get(Long.parseLong(dpMatchItemContext.get(field).toString())).getLovValue();
                    }
                } catch (Exception e) {
                    columnValue = Optional.ofNullable(dpMatchItemContext.get(field)).map(Object::toString).orElse(null);
                }
                params.put(field, columnValue);
            }
        }
        return params;
    }

    /**
     * 获取dp對象对象所相关的rule行的 attrId 和 现在值的关联
     *
     * @param demandLine
     * @param orderHeadFieldMap
     * @return
     */
    private Map<String, String> getRuleValuesMap(MaterielMatchHeaderDTO demandLine, Map<String, String> orderHeadFieldMap, Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList) {
        Map<String, String> allValues = new LinkedHashMap<>();
        for (List<ExpressRuleLineDTO> ruleLineDTOList : expressRuleLineList.values()) {
            for (ExpressRuleLineDTO lineDTO : ruleLineDTOList) {
                for (ExpressRuleDetailDTO detail : lineDTO.getDetails()) {
                    LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, Long.valueOf(detail.getSourceColumn()));
                    if (lovLineDTO != null && StringUtils.isNotEmpty(lovLineDTO.getAttribute6())) {
                        String sourceColumn = StrUtil.toCamelCase(lovLineDTO.getAttribute6());
                        String sourceColumnVal = null;
                        if (orderHeadFieldMap.containsKey(sourceColumn)) {
                            Object fieldValue = com.trinasolar.scp.common.api.util.BeanUtils.getFieldValue(demandLine, sourceColumn);
                            if (fieldValue == null || StringUtils.isBlank(fieldValue.toString())) {
                                fieldValue = com.trinasolar.scp.common.api.util.BeanUtils.getFieldValue(demandLine, orderHeadFieldMap.get(sourceColumn));
                            }
                            sourceColumnVal = Objects.toString(fieldValue);
                            if (StringUtils.isNotBlank(sourceColumnVal)) {
                                if (StringUtils.isNumeric(sourceColumnVal)) {
                                    try {
                                        LovLineDTO orderLineLov = LovUtils.get(Long.valueOf(sourceColumnVal));
                                        sourceColumnVal = orderLineLov.getLovValue();
                                    } catch (Exception e) {
                                        // 控制不报错
                                    }
                                }
                            }
                        }
                        allValues.put(detail.getSourceColumn(), StringUtils.isNotBlank(sourceColumnVal) ? sourceColumnVal : "");
                    }
                }
            }
        }
        return allValues;
    }

    /**
     * 获取需求分类下的所有规则
     *
     * @return
     */
    private List<RuleLineDTO> getCategoryRuleLine() {
        /**
         * 品类规则 category
         * 特殊区域规则 specialArea
         */
        LovLineDTO cellRuleClassification = Optional.ofNullable(LovUtils.get("CELL_Rule_CLASSIFICATION", CATEGORY))
                .orElseThrow(() -> new BizException("bbom_notFound_cellRuleClassification"));

        RuleHeaderQuery ruleHeaderQuery = new RuleHeaderQuery();
        ruleHeaderQuery.setRuleCategoryId(Long.valueOf(cellRuleClassification.getLovLineId().toString()));
        List<RuleLineDTO> ruleLineDTOS = ruleHeaderService.listLinesByLov(ruleHeaderQuery);
        return ruleLineDTOS;
    }

    /**
     * 查询 获取符合投产的数据的规则
     *
     * @return
     */
    @Override
    public Map<Long, List<ExpressRuleLineDTO>> getExpressRuleLineList() {
        //获取符合投产的数据的规则
        List<RuleLineDTO> ruleLines = getCategoryRuleLine();
        List<RuleLineDTO> ruleLineNew = getAreaRuleLine();
        ruleLines.addAll(ruleLineNew);
        return buildExpressRuleLineList(ruleLines);
    }

    /**
     * 复制集合 以及 每一条规则的详情和属性
     *
     * @param ruleLines
     * @return
     */
    private Map<Long, List<ExpressRuleLineDTO>> buildExpressRuleLineList(List<RuleLineDTO> ruleLines) {
        Map<Long, ExpressRuleLineDTO> lineMap = new HashMap<>();
        for (RuleLineDTO ruleLine : ruleLines) {
            ExpressRuleLineDTO expressRuleLineDTO = new ExpressRuleLineDTO();
            //复制主体
            BeanUtils.copyProperties(ruleLine, expressRuleLineDTO);
            expressRuleLineDTO.setGroupId(ruleLine.getRuleLineId());
            List<ExpressRuleDetailDTO> detailDTOList = new ArrayList<>();
            for (RuleDpDetailDTO ruleDpDetailDTO : ruleLine.getDetails()) {

                //获取属性字段名称
                String attrColumn = getAttrColumn(ruleDpDetailDTO.getDpFiledId(), false);
                ExpressRuleDetailDTO expressRuleDetailDTO = new ExpressRuleDetailDTO();
                BeanUtils.copyProperties(ruleDpDetailDTO, expressRuleDetailDTO);
                expressRuleDetailDTO.setSourceColumn(attrColumn);
                List<ExpressRuleValueDTO> expressRuleValueDTOS = new ArrayList<>();
                //复制详情值
                for (RuleDpValueDTO ruleDpValueDTO : ruleDpDetailDTO.getValues()) {
                    ExpressRuleValueDTO expressRuleValueDTO = new ExpressRuleValueDTO();
                    BeanUtils.copyProperties(ruleDpValueDTO, expressRuleValueDTO);
                    expressRuleValueDTOS.add(expressRuleValueDTO);
                }
                expressRuleDetailDTO.setValues(expressRuleValueDTOS);
                //复制详情
                detailDTOList.add(expressRuleDetailDTO);
            }
            expressRuleLineDTO.setDetails(detailDTOList);
            //H追溯 产品族 片源
            if (CollectionUtils.isEmpty(ruleLine.getControlObjectHeaders())) {
                continue;
            }
            RuleControlObjectDetailDTO ruleControlObjectDetailDTO = ruleLine.getControlObjectHeaders().get(0).getDetails().get(0);
            expressRuleLineDTO.setControlObjectId(ruleControlObjectDetailDTO.getMaterialsAttrFiledId());
            expressRuleLineDTO.setControlObject(ruleControlObjectDetailDTO.getMaterialsAttrFiled());
            RuleControlObjectValueDTO ruleControlObjectValueDTO = ruleControlObjectDetailDTO.getValues().get(0);
            expressRuleLineDTO.setAttrValue(ruleControlObjectValueDTO.getAttrValue());
            expressRuleLineDTO.setAttrValueId(ruleControlObjectValueDTO.getAttrValueId());
            expressRuleLineDTO.setAttrValueName(ruleControlObjectValueDTO.getAttrValueName());

            List<ExpressRuleDetailDTO> dtoList = new ArrayList<>();
            List<RuleControlObjectHeaderDTO> controlObjectHeaders = ruleLine.getControlObjectHeaders();
            for (RuleControlObjectHeaderDTO con : controlObjectHeaders) {
                List<RuleControlObjectDetailDTO> details = con.getDetails();
                for (RuleControlObjectDetailDTO des : details) {

                    String attrOperator = des.getAttrOperator();
                    Long materialsAttrFiledId = des.getMaterialsAttrFiledId();
                    //获取属性字段名称
                    String attrColumn = getAttrColumn(materialsAttrFiledId, false);
                    List<RuleControlObjectValueDTO> values = des.getValues();

                    List<ExpressRuleValueDTO> valueDTOList = new ArrayList<>();
                    for (RuleControlObjectValueDTO va : values) {
                        ExpressRuleValueDTO expressRuleValueDTO = new ExpressRuleValueDTO();
                        expressRuleValueDTO.setAttrValue(va.getAttrValue());
                        expressRuleValueDTO.setAttrValueTo(va.getAttrValueTo());
                        valueDTOList.add(expressRuleValueDTO);
                    }
                    ExpressRuleDetailDTO dto = new ExpressRuleDetailDTO();
                    dto.setAttrOperator(attrOperator);
                    dto.setSourceColumn(attrColumn);
                    dto.setValues(valueDTOList);
                    dtoList.add(dto);
                }

            }
            expressRuleLineDTO.setControlDetails(dtoList);
            lineMap.put(ruleLine.getRuleLineId(), expressRuleLineDTO);
        }
        return lineMap.values().stream().collect(Collectors.groupingBy(ExpressRuleLineDTO::getRuleLineId));
    }

    /**
     * 获取需求分类下的所有规则
     *
     * @return
     */
    private List<RuleLineDTO> getAreaRuleLine() {
        /**
         * 品类规则 category
         * 特殊区域规则 specialArea
         */
        LovLineDTO cellRuleClassification = Optional.ofNullable(LovUtils.get("CELL_Rule_CLASSIFICATION", SPECIAL_AREA))
                .orElseThrow(() -> new BizException("bbom_notFound_SpecialArea"));
        RuleHeaderQuery ruleHeaderQuery = new RuleHeaderQuery();
        ruleHeaderQuery.setRuleCategoryId(Long.valueOf(cellRuleClassification.getLovLineId().toString()));
        List<RuleLineDTO> ruleLineDTOS = ruleHeaderService.listLinesByLov(ruleHeaderQuery);
        return ruleLineDTOS;
    }

    private Map<String, String> getDpColAndTransScriptMap() {
        // 投产属性组
        List<AttrTypeLineDTO> productionAttr = attrUtil.queryAttrTypeLinesByHeaderCode(BBOM_PRODUCTION_ATTR);
        // 电池类型属性组
        List<AttrTypeLineDTO> attrType011 = attrUtil.queryAttrTypeLinesByHeaderCode(BATTR_TYPE_011);
        //获取规则 转换
        Map<String, String> dpColumnsMap = new HashMap<>();

        productionAttr.forEach(value -> {
            if (StringUtils.isNotBlank(value.getAttrRule())) {
                LovLineDTO lovLineDTO = LovUtils.get(Long.valueOf(value.getAttrRule()));
                if (lovLineDTO != null) {
                    dpColumnsMap.put(value.getSourceColumn(), lovLineDTO.getLovValue());
                }
            }

        });

        attrType011.forEach(value -> {
            if (StringUtils.isNotBlank(value.getAttrRule())) {
                LovLineDTO lovLineDTO = LovUtils.get(Long.valueOf(value.getAttrRule()));
                if (lovLineDTO != null) {
                    dpColumnsMap.put(value.getSourceColumn(), lovLineDTO.getAttribute1());
                }
            }
        });
        return dpColumnsMap;
    }

    /**
     * 获取dp属性和item属性对应关系
     *
     * @return
     */
    private Map<String, String> getDpColAndItemColMap() {
        // 投产属性组
        Map<String, LovLineDTO> productionAttr = LovUtils.getAllByHeaderCode(BBOM_PRODUCTION_ATTR);
        // 电池类型属性组
        Map<String, LovLineDTO> batteryType = LovUtils.getAllByHeaderCode(BATTR_TYPE_011);
        //获取规则 转换
        Map<String, String> dpColumnsMap = new HashMap<>();

        productionAttr.values().stream().filter(i -> StringUtils.isNotBlank(i.getAttribute6())).distinct().forEach(value -> {
            if (value.getAttribute5() != null) {
                LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, Long.valueOf(value.getAttribute5()));
                if (lovLineDTO != null) {
                    String fieldName = CharSequenceUtil.toCamelCase(value.getAttribute6());
                    dpColumnsMap.put(fieldName, lovLineDTO.getAttribute6());
                }
            }

        });

        batteryType.values().stream().filter(i -> StringUtils.isNotBlank(i.getAttribute6())).distinct().forEach(value -> {
            if (value.getAttribute5() != null) {
                LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, Long.valueOf(value.getAttribute5()));
                if (lovLineDTO != null) {
                    String fieldName = CharSequenceUtil.toCamelCase(value.getAttribute6());
                    dpColumnsMap.put(fieldName, lovLineDTO.getAttribute6());
                }
            }

        });
        return dpColumnsMap;
    }

    private ItemsQuery buildItemQuery(MaterielMatchHeaderDTO materielMatchHeaderDTO, BatteryTypeMainDTO batteryTypeMainDTO, Map<String, String> dpColumnsMap, Map<String, String> dpColumnsTransScriptMap) {
        ItemsQuery itemsQuery = new ItemsQuery();
        itemsQuery.setOrganizationId(82L);
        itemsQuery.setItemStatus("Active");

        applyTransRule(materielMatchHeaderDTO, dpColumnsTransScriptMap, batteryTypeMainDTO);

        buildItemQueryByBatteryType(materielMatchHeaderDTO, batteryTypeMainDTO, dpColumnsMap, itemsQuery);

        Field[] fields = ReflectUtil.getFields(materielMatchHeaderDTO.getClass());
        for (Field field : fields) {
            String fieldName = CharSequenceUtil.toCamelCase(field.getName());
            if (dpColumnsMap.containsKey(fieldName)) {
                String fieldValue = (String) ReflectUtil.getFieldValue(materielMatchHeaderDTO, fieldName);
                if (NumberUtil.isNumber(fieldValue)) {
                    try {
                        fieldValue = LovUtils.get(Long.parseLong(fieldValue)).getLovValue();
                    } catch (Exception e) {
                        if (log.isDebugEnabled()) {
                            log.debug("LovUtils.get({}) error", fieldValue, e);
                        }
                    }
                }
                String itemFieldName = dpColumnsMap.get(fieldName);
                String curFieldVal = (String) ReflectUtil.getFieldValue(itemsQuery, itemFieldName);
                if (StringUtils.isNotBlank(curFieldVal) && !"segment23".equals(dpColumnsMap.get(fieldName))) {
                    continue;
                } else if (StringUtils.isNotBlank(curFieldVal) && "segment23".equals(dpColumnsMap.get(fieldName))) {
                    //segment23 对应的字段有多个的 存在都是无或者一个为无一个不为无的情况
                    fieldValue = "无".equals(curFieldVal) ? fieldValue : curFieldVal;
                }
                if("segment60".equals(dpColumnsMap.get(fieldName))){
                    ReflectUtil.setFieldValue(itemsQuery, itemFieldName, batteryTypeMainDTO.getBatteryCode());
                    continue;
                }
                if (StringUtils.isNotBlank(fieldValue)) {
                    ReflectUtil.setFieldValue(itemsQuery, itemFieldName, fieldValue);
                } else {
                    ReflectUtil.setFieldValue(itemsQuery, itemFieldName, itemFixedAttributes.getOrDefault(itemFieldName, none));
                }
            }
        }

        // 校验固定属性
        for (Map.Entry<String, String> entry : itemFixedAttributes.entrySet()) {
            String colName = entry.getKey();
            String values = entry.getValue();
            String fieldValue = (String) ReflectUtil.getFieldValue(itemsQuery, colName);
            if (StringUtils.isBlank(fieldValue)) {
                ReflectUtil.setFieldValue(itemsQuery, colName, values);
            }
        }

        return itemsQuery;
    }

    private void applyTransRule(MaterielMatchHeaderDTO materielMatchHeaderDTO, Map<String, String> dpColumnsTransScriptMap, BatteryTypeMainDTO batteryTypeMainDTO) {
        for (Map.Entry<String, String> entry : dpColumnsTransScriptMap.entrySet()) {
            transMaterielMatchHeader(materielMatchHeaderDTO, entry);
            transBatteryTypeMainHeader(batteryTypeMainDTO, entry);
        }
    }

    private void transBatteryTypeMainHeader(BatteryTypeMainDTO batteryTypeMainDTO, Map.Entry<String, String> entry) {
        String colName = CharSequenceUtil.toCamelCase(entry.getKey());
        String transScript = entry.getValue();
        String fieldValue = (String) ReflectUtil.getFieldValue(batteryTypeMainDTO, colName);
        if (StringUtils.isNotBlank(fieldValue)) {
            // 准备脚本
            Map<String, String> transParam = new HashMap<>();
            transParam.put("input", fieldValue);
            String transFieldValue = execTransScript(fieldValue, transScript, transParam);
            ReflectUtil.setFieldValue(batteryTypeMainDTO, colName, transFieldValue);
            if (log.isDebugEnabled()) {
                log.debug("applyTransRule colName:{} fieldValue:{} transFieldValue:{}", colName, fieldValue, transFieldValue);
            }
        }
    }

    private void transMaterielMatchHeader(MaterielMatchHeaderDTO materielMatchHeaderDTO, Map.Entry<String, String> entry) {
        String colName = CharSequenceUtil.toCamelCase(entry.getKey());
        String transScript = entry.getValue();
        String fieldValue = (String) ReflectUtil.getFieldValue(materielMatchHeaderDTO, colName);
        if (StringUtils.isNotBlank(fieldValue)) {
            // 准备脚本
            Map<String, String> transParam = new HashMap<>();
            transParam.put("input", fieldValue);
            String transFieldValue = execTransScript(fieldValue, transScript, transParam);
            ReflectUtil.setFieldValue(materielMatchHeaderDTO, colName, transFieldValue);
            if (log.isDebugEnabled()) {
                log.debug("applyTransRule colName:{} fieldValue:{} transFieldValue:{}", colName, fieldValue, transFieldValue);
            }
        }
    }

    private String execTransScript(String columnValue, String transScript, Map<String, String> transParam) {
        if (StringUtils.isBlank(columnValue)) {
            return columnValue;
        }
        try {
            String transResult = ExpressUtils.execExpressForValue(transScript, transParam);
            columnValue = transResult;
        } catch (Exception e) {
            // 跳过错误
        }
        return columnValue;
    }

    private void buildItemQueryByBatteryType(MaterielMatchHeaderDTO materielMatchHeaderDTO, BatteryTypeMainDTO batteryTypeMainDTO, Map<String, String> dpColumnsMap, ItemsQuery itemsQuery) {
        itemsQuery.setCategorySegment3(batteryTypeMainDTO.getCrystalType() + "%");
        // 通过电池类型获取电池类型属性
        ReflectUtil.setFieldValue(itemsQuery, dpColumnsMap.get("pOrN"), batteryTypeMainDTO.getPOrN());
        ReflectUtil.setFieldValue(itemsQuery, dpColumnsMap.get("category"), batteryTypeMainDTO.getCategory());
        ReflectUtil.setFieldValue(itemsQuery, dpColumnsMap.get("singleDoubleFace"), batteryTypeMainDTO.getSingleDoubleFace());
        ReflectUtil.setFieldValue(itemsQuery, dpColumnsMap.get("numberMainGrids"), batteryTypeMainDTO.getNumberMainGrids());
        ReflectUtil.setFieldValue(itemsQuery, dpColumnsMap.get("shardingMode"), batteryTypeMainDTO.getShardingMode());
    }

    @Override
    public MaterielMatchHeaderDTO save(MaterielMatchHeaderSaveDTO saveDTO) {
        MaterielMatchHeader newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new MaterielMatchHeader());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(MaterielMatchHeaderQuery query, HttpServletResponse response) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
//        Iterable<MaterielMatchHeader> matchHeaderList = repository.findAll(booleanBuilder);
        //转换 集合

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMatch(MaterielMatchHeaderDTO materielMatchHeaderDTO, List<MaterielMatchLineDTO> newLineDTOS) {
        newLineDTOS.forEach(line -> {
            line.setHeaderId(materielMatchHeaderDTO.getId());
        });
        /** 删除旧行 保存新行**/
        materielMatchLineService.deleteMatchStatusByIds(newLineDTOS.stream().map(MaterielMatchLineDTO::getId).collect(Collectors.toList()));
        //修改or新增
        List<MaterielMatchLineDTO> lineDTOList = materielMatchLineService.saveBatch(newLineDTOS);
        //有5A料号的返回
        log.info("二期料号匹配 changeDataBy5AMatch headId：{} 行size：{}", materielMatchHeaderDTO.getId(), newLineDTOS.size());
        bapsFeign.changeDataBy5AMatch(materielMatchLineService.pushBapsCell5AItemCode(materielMatchLineDEConvert.toEntity(lineDTOList)));
    }

    /**
     * 获取源字段名
     *
     * @param id
     * @param hasMtl
     * @return
     */
    private String getAttrColumn(Long id, boolean hasMtl) {
        LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, id);
        if (lovLineDTO != null) {
            if (lovLineDTO.getAttribute5() != null && hasMtl) {
                return lovLineDTO.getAttribute5();
            }
            return lovLineDTO.getLovLineId().toString();
        }
        return null;
    }

    @Override
    public List<MaterielMatchHeaderDTO> queryList(MaterielMatchHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereByAps(booleanBuilder, query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    private void buildWhereByAps(BooleanBuilder booleanBuilder, MaterielMatchHeaderQuery query) {
        //基地
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qMaterielMatchHeader.basePlace.eq(query.getBasePlace()));
        }
        //车间
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qMaterielMatchHeader.workshop.eq(query.getWorkshop()));
        }
        //单元
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qMaterielMatchHeader.workunit.eq(query.getWorkunit()));
        }
        //电池类型
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qMaterielMatchHeader.batteryType.eq(query.getBatteryType()));
        }
        if (query.getMonth() != null) {
            booleanBuilder.and(qMaterielMatchHeader.month.eq(query.getMonth()));
        }
        if (query.getLine() != null) {
            booleanBuilder.and(qMaterielMatchHeader.line.eq(query.getLine()));
        }
        /************************以下数据存在为空的情况 使用isnull*******************************/
        //追溯
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qMaterielMatchHeader.hTrace.eq(query.getHTrace()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.hTrace.isNull());
        }
        //美学
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qMaterielMatchHeader.aesthetics.eq(query.getAesthetics()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.aesthetics.isNull());
        }
        //透明双玻
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qMaterielMatchHeader.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.transparentDoubleGlass.isNull());
        }
        //特殊区域==排产表的小区域国家
        if (StringUtils.isNotEmpty(query.getSpecialArea())) {
            booleanBuilder.and(qMaterielMatchHeader.specialArea.eq(query.getSpecialArea()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.specialArea.isNull());
        }
        //片源种类
        if (StringUtils.isNotEmpty(query.getPcsSourceType())) {
            booleanBuilder.and(qMaterielMatchHeader.pcsSourceType.eq(query.getPcsSourceType()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.pcsSourceType.isNull());
        }
        //硅片等级
        if (StringUtils.isNotEmpty(query.getPcsSourceLevel())) {
            booleanBuilder.and(qMaterielMatchHeader.pcsSourceLevel.eq(query.getPcsSourceLevel()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.pcsSourceLevel.isNull());
        }
        //电池厂家
        if (StringUtils.isNotEmpty(query.getBatteryManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.batteryManufacturer.eq(query.getBatteryManufacturer()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.batteryManufacturer.isNull());
        }
        //是否有特殊要求
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirements())) {
            booleanBuilder.and(qMaterielMatchHeader.isSpecialRequirements.eq(query.getIsSpecialRequirements()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.isSpecialRequirements.isNull());
        }
        //硅料厂家
        if (StringUtils.isNotEmpty(query.getSiliconMaterialManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.siliconMaterialManufacturer.eq(query.getSiliconMaterialManufacturer()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.siliconMaterialManufacturer.isNull());
        }
        //网版厂家
        if (StringUtils.isNotEmpty(query.getScreenManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.screenManufacturer.eq(query.getScreenManufacturer()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.screenManufacturer.isNull());
        }
        //银浆厂家
        if (StringUtils.isNotEmpty(query.getSilverSlurryManufacturer())) {
            booleanBuilder.and(qMaterielMatchHeader.silverSlurryManufacturer.eq(query.getSilverSlurryManufacturer()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.silverSlurryManufacturer.isNull());
        }
        //低阻
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qMaterielMatchHeader.lowResistance.eq(query.getLowResistance()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.lowResistance.isNull());
        }
        //需求地
        if (StringUtils.isNotEmpty(query.getDemandPlace())) {
            booleanBuilder.and(qMaterielMatchHeader.demandPlace.eq(query.getDemandPlace()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.demandPlace.isNull());
        }
        //加工类别
        if (StringUtils.isNotEmpty(query.getProcessCategory())) {
            booleanBuilder.and(qMaterielMatchHeader.processCategory.eq(query.getProcessCategory()));
        } else {
            booleanBuilder.and(qMaterielMatchHeader.processCategory.isNull());
        }
        //产品等级
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qMaterielMatchHeader.productionGrade.eq(query.getProductionGrade()));
        }
    }

    public void addMatchInfo(MaterielMatchHeaderDTO matchHeaderDTO) {
        if(CollectionUtils.isNotEmpty(matchHeaderDTO.getPlanDTOList())
                && StringUtils.isBlank(matchHeaderDTO.getPlanDTOList().get(0).getNoLastTime())){
            //删除头数据
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            if(StringUtils.isNotBlank(matchHeaderDTO.getPlanType())){
                booleanBuilder.and(qMaterielMatchHeader.planType.eq(matchHeaderDTO.getPlanType()));
            }else{
                booleanBuilder.and(qMaterielMatchHeader.planType.isNull());
            }
            booleanBuilder.and(qMaterielMatchHeader.oldMonth.eq(matchHeaderDTO.getMonthAndOversea().split("_")[0]));
            booleanBuilder.and(qMaterielMatchHeader.isOverseaId.eq(Long.valueOf(matchHeaderDTO.getMonthAndOversea().split("_")[1])));
            Iterable<MaterielMatchHeader> headerIterable = repository.findAll(booleanBuilder);
            List<MaterielMatchHeader> headerIterableList = Lists.newArrayList(headerIterable);
            List<Long> headerIds = headerIterableList.stream().map(MaterielMatchHeader::getId).collect(Collectors.toList());
            repository.deleteAll(headerIterable);

            //行删除
            materielMatchLineService.deleteByMonth(matchHeaderDTO.getMonthAndOversea().split("_")[0], Long.valueOf(matchHeaderDTO.getMonthAndOversea().split("_")[1]),headerIds);
        }

        Map<String, StructuresDTO> structuresDTOMap = queryListByStructures();
        //获取工艺路线
        Map<String, ErpOperatRouteDTO> erpOperatRouteMap = getErpOperatRouteList();
        //分组
        Map<String, List<CellPlanLineDTO>> listMap = matchHeaderDTO.getPlanDTOList().stream().collect(Collectors.groupingBy(CellPlanLineDTO::filedGroup));
        extractedByQuery(listMap, structuresDTOMap, erpOperatRouteMap);
    }

    private void extractedByQuery(Map<String, List<CellPlanLineDTO>> listMap, Map<String, StructuresDTO> structuresDTOMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap) {
        listMap.entrySet().stream().forEach(entry -> {
            MaterielMatchHeaderQuery headerQuery = getMaterielMatchHeaderQuery(entry);
            MaterielMatchHeaderDTO headerDTO = convertQuery(headerQuery);
            //新增头
            //新增行
            headerDTO.setPlanDTOList(entry.getValue());
            headerDTO.setMatchLineListMap(new ArrayList<>());

            if(CollectionUtils.isNotEmpty(entry.getValue())){
                CellPlanLineDTO cellPlanLineDTO = entry.getValue().get(0);
                headerDTO.setPlanType(cellPlanLineDTO.getPlanType());
                headerDTO.setRatioCode(cellPlanLineDTO.getRatioCode());
                headerDTO.setEcsCode(cellPlanLineDTO.getEcsCode());
                headerDTO.setSupplyModeName(cellPlanLineDTO.getSupplyMethod());
            }

            //头新增
            MaterielMatchHeader header = convert.toEntity(headerDTO);
            repository.save(header);
            // 进行行数据的保存
            materielMatchLineService.addMatchLineInfo(header, headerDTO, structuresDTOMap, erpOperatRouteMap);
        });
    }

    public MaterielMatchLineDTO getMaterielMatchLineDTOByLine(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                                              CellPlanLineDTO cellPlanLineDTO,
                                                              Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                                              Map<String, StructuresDTO> structuresDTOMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap) {
        MaterielMatchLineDTO dto = new MaterielMatchLineDTO();
        dto.setHeaderId(materielMatchHeaderDTO.getId());
        dto.setScheduleDate(cellPlanLineDTO.getStartTime().toLocalDate());
        dto.setScheduleQty(cellPlanLineDTO.getQtyPc());
        dto.setLine(cellPlanLineDTO.getNumberLine());
        dto.setCellQty(cellPlanLineDTO.getQtyPc());
        dto.setRemark("");
        dto.setRatioCode(cellPlanLineDTO.getRatioCode());
        dto.setEcsCode(cellPlanLineDTO.getEcsCode());
        dto.setSupplyModeName(cellPlanLineDTO.getSupplyMethod());
        dto.setCellProductionPlanId(cellPlanLineDTO.getId());
        if ("中大样".equals(cellPlanLineDTO.getSourceType())) {
            dto.setIsCatchProduction("研发");
            dto.setIsCatchProductionId(LovUtils.getByName(ENG_MFG, YF).getLovLineId());
        } else {
            dto.setIsCatchProduction("量产");
            dto.setIsCatchProductionId(LovUtils.getByName(ENG_MFG, LC).getLovLineId());
        }
        if (StringUtils.isNotBlank(cellPlanLineDTO.getItemCode())) {
            dto.setItemCode(cellPlanLineDTO.getItemCode());
            dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode());
            dto.setItemMatchStatus(MaterielMatchLineDTO.MatchStatus.APPOINT_BY_CELL_PLAN.getCode());
            ItemsDTO item = itemsService.findOneByItemCode(cellPlanLineDTO.getItemCode());
            dto.setItemDesc(item.getItemDesc());
            String alternateDesignatorCode = getAlternateDesignatorCode(materielMatchHeaderDTO.getWorkshop(), item.getSourceItemId(), designatorMap, structuresDTOMap);
            dto.setAlternateBomDesignator(alternateDesignatorCode);

            ErpOperatRouteDTO operatRouteDTO = bomService.findBy5AAndAlternateRoutingDesignator(item.getItemCode(), dto.getAlternateBomDesignator());
            if (operatRouteDTO == null) {
                dto.setRoute("否");
            } else {
                dto.setRoute("是");
            }

            dto.setCertifiedModels(item.getSegment28());

            List<ItemsDTO> items = new ArrayList<>();
            items.add(item);
            dto.setMatchCodes(getMatchCodes(materielMatchHeaderDTO, items, designatorMap, erpOperatRouteMap, structuresDTOMap));

        } else {
            dto.setMatchStatus(MaterielMatchLineDTO.MatchStatus.NON_MATCH.getCode());
        }
        dto.setFinalVersion(cellPlanLineDTO.getFinalVersion());
        dto.setMonth(cellPlanLineDTO.getMonth());
        //新增20个字段
        addFiled(materielMatchHeaderDTO, dto);
        dto.setSpecialOrder(cellPlanLineDTO.getSpecialOrder());
        dto.setManufactureProcess(cellPlanLineDTO.getManufactureProcess());
        return dto;
    }

    public void addFiled(MaterielMatchHeaderDTO materielMatchHeaderDTO, MaterielMatchLineDTO dto) {
        dto.setBatteryType(materielMatchHeaderDTO.getBatteryType());
        dto.setBatteryTypeId(materielMatchHeaderDTO.getBatteryTypeId());
        dto.setAesthetics(materielMatchHeaderDTO.getAesthetics());
        dto.setTransparentDoubleGlass(materielMatchHeaderDTO.getTransparentDoubleGlass());
        dto.setSpecialArea(materielMatchHeaderDTO.getSpecialArea());
        dto.setSpecialAreaId(materielMatchHeaderDTO.getSpecialAreaId());
        dto.setHTrace(materielMatchHeaderDTO.getHTrace());
        dto.setPcsSourceType(materielMatchHeaderDTO.getPcsSourceType());
        dto.setPcsSourceLevel(materielMatchHeaderDTO.getPcsSourceLevel());
        dto.setIsSpecialRequirements(materielMatchHeaderDTO.getIsSpecialRequirements());
        dto.setScreenManufacturer(materielMatchHeaderDTO.getScreenManufacturer());
        dto.setSiliconMaterialManufacturer(materielMatchHeaderDTO.getSiliconMaterialManufacturer());
        dto.setBatteryManufacturer(materielMatchHeaderDTO.getBatteryManufacturer());
        dto.setSilverSlurryManufacturer(materielMatchHeaderDTO.getSilverSlurryManufacturer());
        dto.setLowResistance(materielMatchHeaderDTO.getLowResistance());
        dto.setSiliconWaferPurchaseMethod(materielMatchHeaderDTO.getSiliconWaferPurchaseMethod());
        dto.setDemandPlace(materielMatchHeaderDTO.getDemandPlace());
        dto.setBasePlace(materielMatchHeaderDTO.getBasePlace());
        dto.setWorkshop(materielMatchHeaderDTO.getWorkshop());
        dto.setWorkunit(materielMatchHeaderDTO.getWorkunit());
        dto.setProcessCategory(materielMatchHeaderDTO.getProcessCategory());
        dto.setProductionGrade(materielMatchHeaderDTO.getProductionGrade());
        dto.setIsOversea(materielMatchHeaderDTO.getIsOversea());
        dto.setIsOverseaId(materielMatchHeaderDTO.getIsOverseaId());
        dto.setOldMonth(materielMatchHeaderDTO.getOldMonth());
        dto.setMainGridSpace(materielMatchHeaderDTO.getMainGridSpace());
    }

    @Override
    public void requestErpItemCodeSyncExternalItems() {
        DateTimeQuery dateTimeQuery = new DateTimeQuery();
        try {
            //dateTimeQuery.setFromDateTime(LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0).withNano(0));
            dateTimeQuery.setFromDateTime(LocalDateTime.now().minus(1, ChronoUnit.MONTHS).with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0).withSecond(0).withNano(0));

            bomFeign.dailySyncInterface(dateTimeQuery);
        } catch (Exception e) {
            log.error("ERP物料同步到bom_external_items", e.getMessage(), e);
            throw new BizException("ERP物料同步到bom_external_items,请联系管理员!");
        }

    }

    @Override
    public void externalItemsDistributeBomItem() {
        try {
            bomFeign.syncBbom();
        } catch (Exception e) {
            log.error("bom_external_items中间表数据下发到bom_item", e.getMessage(), e);
            throw new BizException("bom_external_items中间表数据下发到bom_item,请联系管理员!");
        }

    }

    /**
     * p2_570 电池料号匹配界面新增按钮，确认，点击确认时触发邮件提醒功能，表明电池排产料号匹配完成
     */
    @Override
    public void confirm() {
        EmailDataResultDTO emailDataResultDTO = new EmailDataResultDTO();
        //查询所有收件人
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.SEND_MAIL, LovHeaderCodeConstant.MATERIEL_MATCH_CONFIRM);
        //发送邮件
        List<String> emails = Arrays.asList(StringUtils.split(lovLineDTO.getAttribute1(), ","));
        try {
            mailService.send(new ArrayList<>(emails), "material_match_confirm.ftl", "电池料号匹配邮件提醒", MapUtil.of("Data", emailDataResultDTO), null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<MaterielMatchLineMatchStatusDTO> appointItemList(IdsDTO idsDTO) {
        //电池料号匹配明细行信息
        List<IdDTO> idDTOS = idsDTO.getIds();
        if(CollectionUtils.isEmpty(idDTOS)){
            return Collections.emptyList();
        }
        //获取Active、82L、自产电池片的物料集合
        QItems qItems = QItems.items;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ItemsDTO> jpaQuery = jpaQueryFactory.select(
                        Projections.fields(
                                ItemsDTO.class,
                                qItems.id.as("id"),
                                qItems.organizationId.as("organizationId"),
                                qItems.categorySegment2.as("categorySegment2"),
                                qItems.itemStatus.as("itemStatus"),
                                qItems.segment2.as("segment2"),
                                qItems.segment22.as("segment22"),
                                qItems.segment25.as("segment25"),
                                qItems.segment26.as("segment26"),
                                qItems.segment27.as("segment27"),
                                qItems.segment60.as("segment60"),
                                qItems.itemCode.as("itemCode"),
                                qItems.itemDesc.as("itemDesc"),
                                qItems.isDeleted.as("isDeleted")
                        )
                )
                .from(qItems)
                .where(qItems.organizationId.eq(82L))
                .where(qItems.categorySegment2.eq("自产电池片"))
                .where(qItems.itemCode.like("5A%"))
                .where(qItems.itemStatus.eq("Active"))
                .where(qItems.isDeleted.eq(DeleteEnum.NO.getCode()))
                .orderBy(qItems.itemCode.asc());

        List<MaterielMatchLineMatchStatusDTO> materielMatchLineMatchStatusDTOS = new ArrayList<>();
        IdDTO idDTO = idDTOS.get(0);
        MaterielMatchLineDTO materielMatchLineDTO = materielMatchLineService.queryById(Long.valueOf(idDTO.getId()));
        if(Objects.isNull(materielMatchLineDTO)){
            throw new BizException("电池料号匹配明细行信息不存在");
        }
        Long headerId = materielMatchLineDTO.getHeaderId();
        //根据matchid获取匹配头信息
        MaterielMatchHeaderDTO materielMatchHeaderDTO = queryById(headerId);
        if(Objects.isNull(materielMatchHeaderDTO)){
            throw new BizException("电池物料号匹配信息不存在");
        }
        //获取电池类型静态属性
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryBatteryCodeTypeAllByBatteryName(materielMatchHeaderDTO.getBatteryType());
        if(Objects.isNull(batteryTypeMainDTO)){
            throw new BizException("电池类型静态属性信息不存在");
        }
        //电池类型	battery_type	    segment60
        //产品等级	production_grade	segment2	为空时默认为：Q1
        //硅片等级	pcs_source_level	segment22	为空时默认为：A
        //返工片	 	                    segment27	默认为：无
        //EL图像	 	                    segment26	默认为：无或者空值
        //暗电流	 	                    segment25	默认为：无或者空值
        String productionGrade = materielMatchHeaderDTO.getProductionGrade();
        String pcsSourceLevel = materielMatchHeaderDTO.getPcsSourceLevel();
        jpaQuery.where(qItems.segment2.eq((StringUtils.isBlank(productionGrade) || none.equals(productionGrade)) ? "Q1":productionGrade));
        jpaQuery.where(qItems.segment22.eq((StringUtils.isBlank(pcsSourceLevel) || none.equals(pcsSourceLevel)) ? "A":pcsSourceLevel));
        jpaQuery.where(qItems.segment25.eq(none).or(qItems.segment25.isNull()));
        jpaQuery.where(qItems.segment26.eq(none).or(qItems.segment26.isNull()));
        jpaQuery.where(qItems.segment27.eq(none));
        jpaQuery.where(qItems.segment60.eq(batteryTypeMainDTO.getBatteryCode()));

        List<ItemsDTO> itemsDTOList = IterableUtils.toList(jpaQuery.fetch());

        //匹配物料信息为空
        if(CollectionUtils.isNotEmpty(itemsDTOList)){
            //组装料号信息
            for(ItemsDTO itemsDTO : itemsDTOList){
                String itemCode = itemsDTO.getItemCode();
                String itemDesc = itemsDTO.getItemDesc();
                if(StringUtils.isBlank(itemCode) || StringUtils.isBlank(itemDesc)){
                    continue;
                }
                MaterielMatchLineMatchStatusDTO materielMatchLineMatchStatusDTO = new MaterielMatchLineMatchStatusDTO();
                materielMatchLineMatchStatusDTO.setItemCode(itemCode);
                materielMatchLineMatchStatusDTO.setItemDesc(String.format("%s-%s",itemCode,itemDesc));
                materielMatchLineMatchStatusDTOS.add(materielMatchLineMatchStatusDTO);
            }
        }

        //整体排序
        return materielMatchLineMatchStatusDTOS;
    }

    //query转换dto
    public MaterielMatchHeaderDTO convertQuery(MaterielMatchHeaderQuery headerQuery) {
        MaterielMatchHeaderDTO headerDTO = new MaterielMatchHeaderDTO();
        headerDTO.setBasePlace(StringUtils.isNotEmpty(headerQuery.getBasePlace()) ? headerQuery.getBasePlace() : "无");
        headerDTO.setWorkshop(StringUtils.isNotEmpty(headerQuery.getWorkshop()) ? headerQuery.getWorkshop() : "无");
        headerDTO.setWorkunit(StringUtils.isNotEmpty(headerQuery.getWorkunit()) ? headerQuery.getWorkunit() : "无");
        headerDTO.setBatteryType(StringUtils.isNotEmpty(headerQuery.getBatteryType()) ? headerQuery.getBatteryType() : "无");
        headerDTO.setBatteryTypeId(headerQuery.getBatteryTypeId());
        headerDTO.setHTrace(StringUtils.isNotEmpty(headerQuery.getHTrace()) ? headerQuery.getHTrace() : "无");
        headerDTO.setAesthetics(StringUtils.isNotEmpty(headerQuery.getAesthetics()) ? headerQuery.getAesthetics() : "无");
        headerDTO.setTransparentDoubleGlass(StringUtils.isNotEmpty(headerQuery.getTransparentDoubleGlass()) ? headerQuery.getTransparentDoubleGlass() : "无");
        headerDTO.setSpecialArea(StringUtils.isNotEmpty(headerQuery.getSpecialArea()) ? headerQuery.getSpecialArea() : "无");
        headerDTO.setSpecialAreaId(headerQuery.getSpecialAreaId());
        headerDTO.setPcsSourceType(StringUtils.isNotEmpty(headerQuery.getPcsSourceType()) ? headerQuery.getPcsSourceType() : "无");
        headerDTO.setPcsSourceLevel(StringUtils.isNotEmpty(headerQuery.getPcsSourceLevel()) ? headerQuery.getPcsSourceLevel() : "无");
        headerDTO.setBatteryManufacturer(StringUtils.isNotEmpty(headerQuery.getBatteryManufacturer()) ? headerQuery.getBatteryManufacturer() : "无");
        headerDTO.setIsSpecialRequirements(StringUtils.isNotEmpty(headerQuery.getIsSpecialRequirements()) ? headerQuery.getIsSpecialRequirements() : "无");
        headerDTO.setSiliconMaterialManufacturer(StringUtils.isNotEmpty(headerQuery.getSiliconMaterialManufacturer()) ? headerQuery.getSiliconMaterialManufacturer() : "无");
        headerDTO.setScreenManufacturer(StringUtils.isNotEmpty(headerQuery.getScreenManufacturer()) ? headerQuery.getScreenManufacturer() : "无");
        headerDTO.setSilverSlurryManufacturer(StringUtils.isNotEmpty(headerQuery.getSilverSlurryManufacturer()) ? headerQuery.getSilverSlurryManufacturer() : "无");
        headerDTO.setLowResistance(StringUtils.isNotEmpty(headerQuery.getLowResistance()) ? headerQuery.getLowResistance() : "无");
        headerDTO.setDemandPlace(StringUtils.isNotEmpty(headerQuery.getDemandPlace()) ? headerQuery.getDemandPlace() : "无");
        headerDTO.setProcessCategory(StringUtils.isNotEmpty(headerQuery.getProcessCategory()) ? headerQuery.getProcessCategory() : "无");
        headerDTO.setMonth(headerQuery.getMonth());
        headerDTO.setLine(headerQuery.getLine());
        headerDTO.setProductionGrade(StringUtils.isNotEmpty(headerQuery.getProductionGrade()) ? headerQuery.getProductionGrade() : "无");
        headerDTO.setIsOversea(headerQuery.getIsOversea());
        headerDTO.setIsOverseaId(headerQuery.getIsOverseaId());
        headerDTO.setVersion(headerQuery.getVersion());
        headerDTO.setOldMonth(headerQuery.getOldMonth());
        headerDTO.setMainGridSpace(headerQuery.getMainGridSpace());
        headerDTO.setSpecialOrder(headerQuery.getSpecialOrder());
        headerDTO.setManufactureProcess(headerQuery.getManufactureProcess());
        return headerDTO;
    }
    //排产获取4A 优化方法

    /**
     * 排产头查询4A数据
     *
     * @param headerDTOs
     * @return
     */
    public Map<Long, List<String>> query4AByMatchHeadDto(List<MaterielMatchHeaderDTO> headerDTOs) {
        // 获取规则
        Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList = getExpressRuleLineList();
        if (log.isDebugEnabled()) {
            log.debug("expressRuleLineList:{}", expressRuleLineList);
        }
        //获取属性对应参数
        Map<String, String> dpColumnsMap = getDpColAndItemColMap();
        Map<String, String> dpColumnsTransScriptMap = getDpColAndTransScriptMap();
        //获取所有电池类型属性
        List<BatteryTypeMainDTO> batteryTypeMainDTOList = batteryTypeMainService.queryBatteryCodeTypeAll();
        Map<String, BatteryTypeMainDTO> batteryTypeMainMap = batteryTypeMainDTOList.stream().collect(Collectors.toMap(BatteryTypeMainDTO::getBatteryName, Function.identity(), (k1, k2) -> k1));
        ;
        Map<String, StructuresDTO> structuresDTOMap = queryListByStructures();
        Map<Long, List<String>> itemCodeBy4A = new HashMap<>();
        //现先获取所有4a有效的料号信息分组
        Map<Long, String> itemsMap;
        List<Items> itemAll = jpaQueryFactory.select(qItems).from(qItems)
                .where(qItems.itemCode.like("4A%").and(qItems.organizationId.eq(82L))
                        .and(qItems.isDeleted.eq(0))).fetch();
        itemsMap = itemAll.stream().collect(Collectors.toMap(Items::getSourceItemId, Items::getItemCode));
        //获取4A的sourceItemId
        List<Long> sourceItemIds = itemAll.stream().map(Items::getSourceItemId).collect(Collectors.toList());
        //获取对应的4AComponentsDTO
        List<ComponentsDTO> componentsList = getComponentsList(sourceItemIds);
        Map<Long, List<ComponentsDTO>> compinentsMap = componentsList.stream().collect(Collectors.groupingBy(ComponentsDTO::getBomId));
        //获取Active、82L、自产电池片的物料集合
        List<ItemsDTO> itemsDTOList = itemsService.queryByMatchingAll();
        Map<String, ErpAlternateDesignatorDTO> designatorDTOMap = getErpAlternateDesignatorDTO();
        for (MaterielMatchHeaderDTO headerDTO : headerDTOs) {

            ErpAlternateDesignatorDTO designatorDTO = designatorDTOMap.get(headerDTO.getWorkshop());
            if (designatorDTO == null) {
                continue;
            }
            BatteryTypeMainDTO batteryTypeMainDTO = Optional.ofNullable(batteryTypeMainMap.get(headerDTO.getBatteryType())).orElse(null);
            //查询5A数据 5A下面查询4A
            List<ItemsDTO> itemsDTOS = getMatchItemOptimize(headerDTO, expressRuleLineList,
                    itemsDTOList,
                    dpColumnsMap,
                    dpColumnsTransScriptMap,
                    batteryTypeMainDTO
            );
            List<Long> itemListFilter = itemsDTOS.stream().map(ItemsDTO::getSourceItemId).collect(Collectors.toList());
            for (Long sourceItemId : itemListFilter) {
                //bom替代项、组织id、5A料号id 唯一
                StructuresDTO structuresDTO = structuresDTOMap.get(String.format("%s%s%s", designatorDTO.getAlternateDesignatorCode(), designatorDTO.getOrganizationId(), sourceItemId));
                if (null != structuresDTO) {
                    List<ComponentsDTO> componentsDTOList = compinentsMap.get(structuresDTO.getId());
                    if (componentsDTOList != null) {
                        List<String> itemCodeList = new ArrayList<>();
                        componentsDTOList.stream().forEach(y -> {
                            if (StringUtils.isNotEmpty(itemsMap.get(y.getComponentItemId()))) {
                                itemCodeList.add(itemsMap.get(y.getComponentItemId()));
                            }
                        });
                        itemCodeBy4A.put(headerDTO.getId(), itemCodeList);
                    }
                }
            }
        }
        return itemCodeBy4A;
    }

    private List<ItemsDTO> getMatchItemOptimize(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                                Map<Long, List<ExpressRuleLineDTO>> expressRuleLineList,
                                                List<ItemsDTO> itemsList, Map<String, String> dpColumnsMap,
                                                Map<String, String> dpColumnsTransScriptMap,
                                                BatteryTypeMainDTO batteryTypeMainDTO) {
        //map获取的batteryTypeMainDTO数据存在数据值更新 所以不能直接引用getMatchItemOptimize方法的batteryTypeMainDTO
        batteryTypeMainDTO = batteryTypeMainDEConvert.deepCopy(batteryTypeMainDTO);

        // 查询5A数据
        ItemsQuery itemsQuery = buildItemQuery(materielMatchHeaderDTO, batteryTypeMainDTO, dpColumnsMap, dpColumnsTransScriptMap);
        if (log.isDebugEnabled()) {
            log.debug("itemsQuery:{}", itemsQuery);
        }
        List<ItemsDTO> itemsDTOList = itemsService.filterList(itemsList, itemsQuery);
        if (log.isDebugEnabled()) {
            log.debug("itemsDTOList:{}", itemsDTOList);
        }
        // 通过规则筛选
        itemsDTOList = filterByRules(itemsDTOList, materielMatchHeaderDTO, dpColumnsMap, batteryTypeMainDTO, expressRuleLineList);

        return itemsDTOList;
    }
}
