package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.RuleLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * BOM规则行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Repository
public interface RuleLineRepository extends JpaRepository<RuleLine, Long>, QuerydslPredicateExecutor<RuleLine> {

    @Query(value = "SELECT * FROM bbom_rule_line brl WHERE brl.rule_header_id = ?1 AND brl.is_deleted=0\n" +
            "and EXISTS(select 1 from bbom_rule_dp_detail dp_detail where dp_detail.rule_line_id=brl.rule_line_id and dp_detail.is_deleted=0\n" +
            "and EXISTS(select 1 from bbom_rule_dp_value brdv where brdv.rule_detail_id=dp_detail.rule_detail_id and dp_detail.is_deleted=0 and brdv.attr_value=?2)\n" +
            ")",
            nativeQuery = true)
    List<RuleLine> listByRuleHeaderIdAndItemCode(Long ruleHeaderId, String itemCode);

    @Query(value = "SELECT * FROM bbom_rule_line WHERE bbom_rule_line.rule_header_id = ?1 AND is_deleted=0",
            nativeQuery = true)
    List<RuleLine> listByRuleHeaderId(Long ruleHeaderId);

    @Query(value = "SELECT * FROM bbom_rule_line WHERE bbom_rule_line.rule_header_id in ?1 AND is_deleted=0",
            nativeQuery = true)
    List<RuleLine> listByRuleByHeaderIds(List<Long> ruleHeaderId);

    List<RuleLine> findByRuleHeaderIdIn(List<Long> collect);

    @Query(value = "SELECT count(1) FROM bbom_rule_line tb WHERE tb.no != 0", nativeQuery = true)
    Integer countNoNotEqualZore();

    @Query(value = "SELECT * FROM bbom_rule_line tb WHERE tb.no = 0", nativeQuery = true)
    List<RuleLine> findByNoEqualZore();

    @Query(value = "SELECT * FROM bbom_rule_line brl WHERE  brl.is_deleted=0 and exists(select 1 from bbom_rule_dp_detail dp_detail where dp_detail.rule_line_id=brl.rule_line_id and dp_detail.is_deleted=0" +
            " and EXISTS(select 1 from bbom_rule_dp_value brdv where brdv.rule_detail_id=dp_detail.rule_detail_id and dp_detail.is_deleted=0 and brdv.attr_value in ?1))",
            nativeQuery = true)
    List<RuleLine> queryRuleLinsByItemCodes(List<String> itemCodes);
}
