package com.trinasolar.scp.bbom.service.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.trinasolar.scp.bbom.domain.dto.ExpressRuleDetailDTO;
import com.trinasolar.scp.bbom.domain.dto.ExpressRuleLineDTO;
import com.trinasolar.scp.bbom.domain.dto.ExpressRuleResultDTO;
import com.trinasolar.scp.bbom.domain.dto.ExpressRuleValueDTO;
import com.trinasolar.scp.bbom.domain.enums.bom.AttrOperatorEnum;
import com.trinasolar.scp.bbom.domain.enums.bom.AttrValueTypeEnum;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/12
 */
@Component
@Slf4j
public class ExpressUtils {
    static final String NUMBER_VALUE_REGEX = "^[\\d\\.]*";

    static ScriptEngine scriptEngine;

    static {
        ScriptEngineManager manager = new ScriptEngineManager(null);
        scriptEngine = manager.getEngineByName("nashorn");
        try {
            scriptEngine.eval(initScript());
        } catch (ScriptException e) {
            e.printStackTrace();
        }
    }

    /**
     * 解析表达式
     *
     * @param express
     * @param params
     * @return
     */
    public static String analysisExpress(String express, Map<String, String> params) {
        if (params == null) {
            return null;
        }
        final String[] ex = {express};
        params.forEach((key,val)->{
            ex[0] = ex[0].replaceAll("\\{" + key + "\\}",
                    Optional.ofNullable(val).orElse(""));
        });
        return ex[0];
    }

    public static String execExpressForValue(String express, Map<String, String> params) {
        if (StringUtils.isBlank(express)) {
            return null;
        }

        log.info("执行表达式: {} ,params: {}", express, params);
        String currentExpress = ExpressUtils.analysisExpress(express, params);

        try {
            return (String) scriptEngine.eval(currentExpress);
        } catch (ScriptException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 执行表达式
     *
     * @param express
     * @param params
     * @return
     */
    public static boolean execExpress(String express, Map<String, String> params) {
        if (StringUtils.isBlank(express)) {
            return true;
        }

        // log.info("生成表达式: {} ,params: {}", express, params);
        String currentExpress = ExpressUtils.analysisExpress(express, params);
        log.info("执行表达式：{}", currentExpress);

        try {
            return (boolean) scriptEngine.eval(currentExpress);
        } catch (ScriptException e) {
            e.printStackTrace();
            return false;
        }
    }

    public static String getDetailsExpress(List<ExpressRuleLineDTO> ruleLines) {
        if (ruleLines.isEmpty()) {
            return "";
        }
        StringBuilder express = new StringBuilder();
        HashMap<Long, List<ExpressRuleLineDTO>> groupedRules = new HashMap<>();
        for (ExpressRuleLineDTO ruleLine : ruleLines) {
            Long groupId = ruleLine.getGroupId();
            groupedRules.computeIfAbsent(groupId, (k) -> new ArrayList<>());

            groupedRules.get(groupId).add(ruleLine);
        }

        boolean first = true;
        for (List<ExpressRuleLineDTO> expressRuleLineDTOS : groupedRules.values()) {
            String detailsExpress = expressRuleLineDTOS.stream().map(ExpressUtils::getDetailsExpress)
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(" || "));
            if (StringUtils.isBlank(detailsExpress)) {
                continue;
            }


            if (first) {
                express.append(" ( ");
                first = false;
            } else {
                express.append(" && ( ");
            }

            express.append(detailsExpress);
            express.append(")");
        }

        String expressStr = express.toString();
        // log.info("生成的表达式:{}", expressStr);
        return expressStr;
    }

    public static String getControlDetailsExpress(List<ExpressRuleLineDTO> ruleLines) {
        if (ruleLines.isEmpty()) {
            return "";
        }
        StringBuilder express = new StringBuilder();
        HashMap<Long, List<ExpressRuleLineDTO>> groupedRules = new HashMap<>();
        for (ExpressRuleLineDTO ruleLine : ruleLines) {
            Long groupId = ruleLine.getGroupId();
            groupedRules.computeIfAbsent(groupId, (k) -> new ArrayList<>());

            groupedRules.get(groupId).add(ruleLine);
        }

        boolean first = true;
        for (List<ExpressRuleLineDTO> expressRuleLineDTOS : groupedRules.values()) {
            String detailsExpress = expressRuleLineDTOS.stream().map(ExpressUtils::getControlDetailsExpress)
                    .filter(StringUtils::isNotBlank).collect(Collectors.joining(" || "));
            if (StringUtils.isBlank(detailsExpress)) {
                continue;
            }

            if (first) {
                express.append(" ( ");
                first = false;
            } else {
                express.append(" && ( ");
            }

            express.append(detailsExpress);
            express.append(")");
        }

        String expressStr = express.toString();
        // log.info("生成的表达式:{}", expressStr);
        return expressStr;
    }

    private static String getDetailsExpress(ExpressRuleLineDTO ruleLine) {
        if (ruleLine.getDetails().isEmpty()) {
            return "";
        }
        StringBuilder express = new StringBuilder();

        express.append(" (  ");
        boolean first = true;
        for (ExpressRuleDetailDTO detail : ruleLine.getDetails()) {
            String sourceColumn = detail.getSourceColumn();
            try {
                LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, Long.parseLong(sourceColumn));
                sourceColumn = lovLineDTO.getAttribute6();
            } catch (Exception e) {
                if (log.isDebugEnabled()) {
                    log.debug("字段不存在:{}", sourceColumn);
                }
            }
            Optional.ofNullable(sourceColumn).orElseThrow(() -> new BizException("字段不可为空"));
            detail.setSourceColumn(CharSequenceUtil.toCamelCase(sourceColumn));

            /* 如果是包含 则是 A == A1 || A==A2 || A==A3 */
            String delimiter = " || ";
            String attrOperator = null;
            if (!AttrOperatorEnum.getValues().contains(detail.getAttrOperator())) {
                LovLineDTO attrOperatorLov = LovUtils.get(Long.valueOf(detail.getAttrOperator()));
                attrOperator = attrOperatorLov.getLovValue();
            }
            if (attrOperator != null && Objects.equals(attrOperator, AttrOperatorEnum.EXCLUDE.getValue())) {
                /* 如果是排除 则是 A!= A1 && A!=A2 && A!=A3 */
                delimiter = " && ";
            }
            String detailValueExpress = detail.getValues().stream().map(value -> getValueExpressStr(value, detail))
                    .collect(Collectors.joining(delimiter));
            if (detail.getValues().size() > 1) {
                detailValueExpress = "( " + detailValueExpress + " )";
            }
            if (first) {
                express.append(detailValueExpress);
                first = false;
            } else {
                express.append(" && ").append(detailValueExpress);
            }
        }
        express.append(" ) ");

        return express.toString();
    }

    private static String getControlDetailsExpress(ExpressRuleLineDTO ruleLine) {
        if (ruleLine.getControlDetails().isEmpty()) {
            return "";
        }
        StringBuilder express = new StringBuilder();

        express.append(" (  ");
        boolean first = true;
        for (ExpressRuleDetailDTO detail : ruleLine.getControlDetails()) {
            String sourceColumn = detail.getSourceColumn();
            try {
                LovLineDTO lovLineDTO = LovUtils.get(LovUtils.ATTR, Long.parseLong(sourceColumn));
                sourceColumn = lovLineDTO.getAttribute6();
            } catch (Exception e) {
                if (log.isDebugEnabled()) {
                    log.debug("字段不存在:{}", sourceColumn);
                }
            }
            Optional.ofNullable(sourceColumn).orElseThrow(() -> new BizException("字段不可为空"));
            detail.setSourceColumn(CharSequenceUtil.toCamelCase(sourceColumn));

            /* 如果是包含 则是 A == A1 || A==A2 || A==A3 */
            String delimiter = " || ";
            String attrOperator = null;
            if (!AttrOperatorEnum.getValues().contains(detail.getAttrOperator())) {
                LovLineDTO attrOperatorLov = LovUtils.get(Long.valueOf(detail.getAttrOperator()));
                attrOperator = attrOperatorLov.getLovValue();
            }
            if (attrOperator != null && Objects.equals(attrOperator, AttrOperatorEnum.EXCLUDE.getValue())) {
                /* 如果是排除 则是 A!= A1 && A!=A2 && A!=A3 */
                delimiter = " && ";
            }
            String detailValueExpress = detail.getValues().stream().map(value -> getValueExpressStr(value, detail))
                    .collect(Collectors.joining(delimiter));
            if (detail.getValues().size() > 1) {
                detailValueExpress = "( " + detailValueExpress + " )";
            }
            if (first) {
                express.append(detailValueExpress);
                first = false;
            } else {
                express.append(" && ").append(detailValueExpress);
            }
        }
        express.append(" ) ");

        return express.toString();
    }

    private static String getValueExpressStr(ExpressRuleValueDTO expressRuleValueDTO, ExpressRuleDetailDTO detail) {
        StringBuilder express = new StringBuilder();
        String valPrefix = " ", attrOperator = null;
        if (!AttrOperatorEnum.getValues().contains(detail.getAttrOperator())) {
            LovLineDTO attrOperatorLov = LovUtils.get(Long.valueOf(detail.getAttrOperator()));
            attrOperator = attrOperatorLov.getLovValue();
        }

        // 分别处理数字和文本的情况
        if (AttrValueTypeEnum.NUMBER.getValue().equals(expressRuleValueDTO.getValueType())) {
            if (attrOperator != null && Objects.equals(attrOperator, AttrOperatorEnum.EXCLUDE.getValue())) {
                // 如果为排除,则对结果取反
                valPrefix = " !";
            }
            express.append(valPrefix).append("numRange({").append(detail.getSourceColumn()).append("},")
                    .append(expressRuleValueDTO.getAttrValue()).append(",")
                    .append(expressRuleValueDTO.getAttrValueTo()).append(") ");
        } else {
            if (attrOperator != null && Objects.equals(attrOperator, AttrOperatorEnum.EXCLUDE.getValue())) {
                // 如果为排除,则对结果取反
                valPrefix = " not_";
            }
            // 等于 和 包含,现在将等于包含看做一致
            express.append(valPrefix).append("ain('{").append(detail.getSourceColumn()).append("}','")
                    .append(expressRuleValueDTO.getAttrValue()).append("') ");
        }

        return express.toString();
    }

    public static BigDecimal getRuleNumberValue(String attrValue) {
        log.info("getRuleNumberValue: {}", attrValue);
//        匹配值的时候：
//        需要将其解析为BigDecimal，从第一个非数字字符截断，解析数值。
        final Pattern pattern = Pattern.compile(NUMBER_VALUE_REGEX, Pattern.MULTILINE);
        final Matcher matcher = pattern.matcher(attrValue.trim());
        if (matcher.find()) {
            String val = matcher.group(0);
            return new BigDecimal(val);
        }
        return null;
    }

    public static BigDecimal getItemNumberValue(String attrValue) {
        log.info("getItemNumberValue: {}", attrValue);
//        匹配值的时候：
//        （基础（物料表）表解析）
//        特例：1 范围值：≥610G/m2 解析为 610
//        2 误差值：610±20G/m2 解析成 610
        if (attrValue.contains("±")) {
            attrValue = attrValue.split("±")[0];
        }
        attrValue = attrValue.replace("≥", "");
        attrValue = attrValue.replace("≤", "");

        final Pattern pattern = Pattern.compile(NUMBER_VALUE_REGEX, Pattern.MULTILINE);
        final Matcher matcher = pattern.matcher(attrValue.trim());
        if (matcher.find()) {
            String val = matcher.group(0);
            return new BigDecimal(val);
        }
        return null;
    }

    public static ExpressRuleLineDTO copyRuleLineDTO(ExpressRuleLineDTO source) {
        ExpressRuleLineDTO expressRuleLineDTO = new ExpressRuleLineDTO();
        expressRuleLineDTO.setRuleLineId(source.getRuleLineId());
        expressRuleLineDTO.setGroupId(source.getGroupId());
        expressRuleLineDTO.setControlSubjectId(source.getControlSubjectId());
        expressRuleLineDTO.setControlSubject(source.getControlSubject());
        expressRuleLineDTO.setControlObjectId(source.getControlObjectId());
        expressRuleLineDTO.setControlObject(source.getControlObject());
        expressRuleLineDTO.setControlPurposeId(source.getControlPurposeId());
        expressRuleLineDTO.setControlPurpose(source.getControlPurpose());
        expressRuleLineDTO.setAttrValueId(source.getAttrValueId());
        expressRuleLineDTO.setAttrValue(source.getAttrValue());
        expressRuleLineDTO.setAttrValueName(source.getAttrValueName());
        expressRuleLineDTO.setDetails(new ArrayList<>(source.getDetails()));
        expressRuleLineDTO.setControlDetails(new ArrayList<>(source.getControlDetails()));
        return expressRuleLineDTO;
    }

    public static ExpressRuleResultDTO convertValue(List<ExpressRuleLineDTO> ruleLineDTOS, Map<String, String> values) {
        if (ruleLineDTOS == null || ruleLineDTOS.isEmpty()) {
            return null;
        }
        List<ExpressRuleLineDTO> newRuleLineDTOS = new ArrayList<>();
        ruleLineDTOS.forEach(p -> {
            boolean flag = true;
            for (ExpressRuleDetailDTO item : p.getDetails()) {
                String s = values.get(item.getSourceColumn());
                if (StringUtils.isBlank(s)) {
                    flag = false;
                }
            }
            if (flag) {
                newRuleLineDTOS.add(p);
            }
        });
        if (CollectionUtils.isNotEmpty(newRuleLineDTOS)) {
            for (ExpressRuleLineDTO lineDTO : newRuleLineDTOS) {
                String express = ExpressUtils.getDetailsExpress(Collections.singletonList(lineDTO));
                boolean newValue = ExpressUtils.execExpress(express, values);
                if (newValue) {
                    String value = null;
                    // LOVID为空，代表规则值是手输的
                    if (lineDTO.getAttrValueId() != null) {
                        value = lineDTO.getAttrValueId().toString();
                    } else if (StringUtils.isNotEmpty(lineDTO.getAttrValue())) {
                        value = lineDTO.getAttrValue();
                    }
                    if (value != null) {
                        return new ExpressRuleResultDTO(lineDTO.getRuleLineId(), value);
                    }
                }
            }
        }
        return null;
    }

    public static String initScript() {
        StringBuffer exp = new StringBuffer();
        exp.append("      function ain(fieldValue, value) {");
        exp.append("          return value != null && fieldValue != null && (';'+value+';').indexOf(';' + fieldValue +';') >= 0;");
        exp.append("      };");

        exp.append("      function eq(fieldValue, value) {");
        exp.append("          return value != null && fieldValue != null && ''+fieldValue == ''+value;");
        exp.append("      };");

//        exp.append("      function not_ain(fieldValue, value) {");
//        exp.append("          return value != null && fieldValue != null && !(fieldValue.indexOf('{') == 0 && fieldValue.indexOf('}') > 0) && (';'+value+';').indexOf(';' + fieldValue +';') < 0;");
//        exp.append("      };");

        exp.append("      function not_ain(fieldValue, value) {");
        exp.append("          return value != null && fieldValue != null &&  (';'+value+';').indexOf(';' + fieldValue +';') < 0;");
        exp.append("      };");

        exp.append("      function numRange(fieldValue, valueFrom,valueTo) {");
        exp.append("          return valueFrom != null && valueTo != null && fieldValue != null && " +
                " fieldValue >= valueFrom && fieldValue<=valueTo ;");
        exp.append("      };");

        return exp.toString();
        //结果
    }
}
