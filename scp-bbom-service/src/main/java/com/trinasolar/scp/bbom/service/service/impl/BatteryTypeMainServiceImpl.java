package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.*;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.BatteryTypeMain;
import com.trinasolar.scp.bbom.domain.entity.QBatteryTypeMain;
import com.trinasolar.scp.bbom.domain.excel.*;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateQuery;
import com.trinasolar.scp.bbom.domain.query.BatterySiliconWaferQuery;
import com.trinasolar.scp.bbom.domain.query.BatterySlurryQuery;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeMainQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryTypeMainSaveDTO;
import com.trinasolar.scp.bbom.service.repository.BatteryTypeMainRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.util.AttrUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电池类型静态属性
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Slf4j
@Service("batteryTypeMainService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class BatteryTypeMainServiceImpl implements BatteryTypeMainService {
    private static final QBatteryTypeMain qBatteryTypeMain = QBatteryTypeMain.batteryTypeMain;

    private final BatteryTypeMainDEConvert convert;

    private final BatteryTypeMainRepository repository;
    private final AttrUtil attrUtil;
    private final JPAQueryFactory jpaQueryFactory;
    @Autowired
    @Lazy
    private BatteryScreenPlateDEConvert batteryScreenPlateDEConvert;
    @Autowired
    @Lazy
    private BatterySiliconWaferDEConvert batterySiliconWaferDEConvert;
    @Autowired
    @Lazy
    private BatterySlurryDEConvert batterySlurryDEConvert;
    @Autowired
    @Lazy
    private BatteryTypeProduceDEConvert batteryTypeProduceDEConvert;
    @Autowired
    @Lazy
    private BatterySiliconWaferService batterySiliconWaferService;
    @Autowired
    @Lazy
    private BatteryScreenPlateService batteryScreenPlateService;
    @Autowired
    @Lazy
    private BatterySlurryService batterySlurryService;
    @Autowired
    @Lazy
    private BatteryTypeProduceService batteryTypeProduceService;

    @Override
    public Page<BatteryTypeMainDTO> queryByPage(BatteryTypeMainQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<BatteryTypeMain> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<BatteryTypeMainDTO> queryBatteryCodeType(BatteryTypeMainQuery query) {
        return convert.toDto(IterableUtils.toList(repository.findAll()));
    }

    @Override
    public List<BatteryTypeMainDTO> queryBatteryCodeTypeAll() {
        return convert.toDto(repository.findAll());
    }

    @Override
    public List<BatteryTypeMainDTO> queryMainByBatteryCode(BatteryTypeMainQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryTypeMain.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryTypeMain.batteryName.eq(query.getBatteryName()));
        }
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    @Override
    public BatteryTypeMainDTO queryByBatteryCode(String batteryCode) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qBatteryTypeMain.batteryCode.eq(batteryCode));
        Optional<BatteryTypeMain> one = repository.findOne(booleanBuilder);
        BatteryTypeMain batteryTypeMain = new BatteryTypeMain();
        if (one.isPresent()) {
            batteryTypeMain = one.get();
        }
        return convert.toDto(batteryTypeMain);
    }

    @Override
    @Cacheable(cacheNames = "BatteryTypeMainService_queryByBatteryName", key = "#batteryName", unless = "#result == null")
    public BatteryTypeMainDTO queryByBatteryName(String batteryName) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qBatteryTypeMain.batteryName.eq(batteryName));
        Optional<BatteryTypeMain> one = repository.findOne(booleanBuilder);
        BatteryTypeMain batteryTypeMain = new BatteryTypeMain();
        if (one.isPresent()) {
            batteryTypeMain = one.get();
        }
        return convert.toDto(batteryTypeMain);
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatteryTypeMainQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryTypeMain.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryTypeMain.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryTypeMain.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getCrystalType())) {
            booleanBuilder.and(qBatteryTypeMain.crystalType.eq(query.getCrystalType()));
        }
        if (StringUtils.isNotEmpty(query.getPOrN())) {
            booleanBuilder.and(qBatteryTypeMain.pOrN.eq(query.getPOrN()));
        }
        if (StringUtils.isNotEmpty(query.getCategory())) {
            booleanBuilder.and(qBatteryTypeMain.category.eq(query.getCategory()));
        }
        if (StringUtils.isNotEmpty(query.getSingleDoubleFace())) {
            booleanBuilder.and(qBatteryTypeMain.singleDoubleFace.eq(query.getSingleDoubleFace()));
        }
        if (StringUtils.isNotEmpty(query.getNumberMainGrids())) {
            booleanBuilder.and(qBatteryTypeMain.numberMainGrids.eq(query.getNumberMainGrids()));
        }
        if (StringUtils.isNotEmpty(query.getShardingMode())) {
            booleanBuilder.and(qBatteryTypeMain.shardingMode.eq(query.getShardingMode()));
        }
        if (StringUtils.isNotEmpty(query.getProductClassification())) {
            booleanBuilder.and(qBatteryTypeMain.productClassification.eq(query.getProductClassification()));
        }
    }

    @Override
    public BatteryTypeMainDTO queryById(Long id) {
        BatteryTypeMain queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public BatteryTypeMainDTO save(BatteryTypeMainSaveDTO saveDTO) {
        BatteryTypeMain newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new BatteryTypeMain());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public List<BatteryTypeMain> batchSaveUpdate(List<BatteryTypeMainDTO> saveDTO) {
        // 业务要求：数据库表中存在电池类型编码不做修改
        List<BatteryTypeMain> batteryTypeMains = repository.saveAll(convert.toEntity(saveDTO));
        return batteryTypeMains;
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(BatteryTypeMainQuery query, HttpServletResponse response) {
        List<BatteryTypeMainDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<BatteryTypeMainExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "电池类型静态属性导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "电池类型静态属性").head(BatteryTypeMainExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);

            // 查询硅片信息
            BatterySiliconWaferQuery siliconWaferQuery = new BatterySiliconWaferQuery();
            if (StringUtils.isNotBlank(query.getBatteryCode())) {
                siliconWaferQuery.setBatteryCode(query.getBatteryCode());
            }
            if (StringUtils.isNotBlank(query.getBatteryName())) {
                siliconWaferQuery.setBatteryName(query.getBatteryName());
            }
            List<BatterySiliconWaferDTO> batterySiliconWaferDTOList = batterySiliconWaferService.queryByBatteryCode(siliconWaferQuery);
            List<BatterySiliconWaferExcelDTO> batterySiliconWaferExcelDTOS = batterySiliconWaferDEConvert.toExcelDTO(batterySiliconWaferDTOList);
            if (batterySiliconWaferExcelDTOS.size() > 0) {
                batterySiliconWaferExcelDTOS.stream().forEach(waferDto -> {
                    String ItemNam = LovUtils.getNameByValue("Item_condition", waferDto.getConditionItem());
                    waferDto.setConditionItemName(ItemNam);
                    waferDto.setBatteryValueName(waferDto.getBatteryValue());
                    Map<String, AttrTypeLineDTO> siliconWaferAttrType = attrUtil.queryAttrTypeLinesByHeaderCode("ATTR_TYPE_018")
                            .stream().collect(Collectors.toMap(AttrTypeLineDTO::getAttrCode, Function.identity(), (k1, k2) -> k1));
                    waferDto.setSiliconWaferPropertiesName(siliconWaferAttrType.get(waferDto.getSiliconWaferProperties()).getAttrCnName());
                });

            }
            WriteSheet writeSheetPage = EasyExcel.writerSheet(1, "电池类型-硅片信息管理").head(BatterySiliconWaferExcelDTO.class).build();
            excelWriter.write(batterySiliconWaferExcelDTOS, writeSheetPage);
            // 查询网版
            BatteryScreenPlateQuery batteryScreenPlateQuery = new BatteryScreenPlateQuery();
            if (StringUtils.isNotBlank(query.getBatteryCode())) {
                batteryScreenPlateQuery.setBatteryCode(query.getBatteryCode());
            }
            if (StringUtils.isNotBlank(query.getBatteryName())) {
                batteryScreenPlateQuery.setBatteryName(query.getBatteryName());
            }
            query.setPageSize(Integer.MAX_VALUE);
            List<BatteryScreenPlateDTO> batteryScreenPlateDTOList = batteryScreenPlateService.queryByPage(batteryScreenPlateQuery).getContent();
            List<BatteryTypeMainScreenPlateExcelDTO> batteryScreenPlateExcelDTOS = batteryScreenPlateDEConvert.toTypeMainExcelDTO(batteryScreenPlateDTOList);
            WriteSheet writeSheetPage2 = EasyExcel.writerSheet(2, "电池类型-网版信息管理").head(BatteryTypeMainScreenPlateExcelDTO.class).build();
            excelWriter.write(batteryScreenPlateExcelDTOS, writeSheetPage2);
            // 查询浆料
            BatterySlurryQuery batterySlurryQuery = new BatterySlurryQuery();
            if (StringUtils.isNotBlank(query.getBatteryCode())) {
                batterySlurryQuery.setBatteryCode(query.getBatteryCode());
            }
            if (StringUtils.isNotBlank(query.getBatteryName())) {
                batterySlurryQuery.setBatteryName(query.getBatteryName());
            }
            batterySlurryQuery.setPageSize(Integer.MAX_VALUE);
            List<BatterySlurryDTO> batterySlurryDTOS = batterySlurryService.queryByPage(UserUtil.getUser().getId(), batterySlurryQuery).getContent();
            List<BatteryTypeMainSlurryExcelDTO> batterySlurryExcelDTOS = batterySlurryDEConvert.toTypeMainExcelDTO(batterySlurryDTOS);
            WriteSheet writeSheetPage3 = EasyExcel.writerSheet(3, "电池类型-浆料信息管理").head(BatteryTypeMainSlurryExcelDTO.class).build();
            excelWriter.write(batterySlurryExcelDTOS, writeSheetPage3);

            List<BatteryTypeProduceExcelDTO> batteryTypeProduceExcelDTOS = new ArrayList<>();
            //产品族
            Map<String, LovLineDTO> lovLineDTOMap = LovUtils.getAllByHeaderCode("aps_power_cell_type");
            if (null != lovLineDTOMap) {
                lovLineDTOMap.keySet().stream().forEach(key -> {
                    if (key.contains(exportDTOS.get(0).getBatteryName())) {
                        BatteryTypeProduceExcelDTO produceExcelDTO = new BatteryTypeProduceExcelDTO();
                        produceExcelDTO.setBatteryCode(query.getBatteryCode());
                        produceExcelDTO.setBatteryName(exportDTOS.get(0).getBatteryName());
                        produceExcelDTO.setName(lovLineDTOMap.get(key).getLovName());
                        batteryTypeProduceExcelDTOS.add(produceExcelDTO);
                    }
                });
            }
            WriteSheet writeSheetPage4 = EasyExcel.writerSheet(4, "电池类型-产出电池类型管理").head(BatteryTypeProduceExcelDTO.class).build();
            excelWriter.write(batteryTypeProduceExcelDTOS, writeSheetPage4);
        }
    }

    @Override
    public BatteryTypeMainDTO queryBatteryCodeTypeAllByBatteryName(String batteryName) {
        BatteryTypeMain batteryTypeMain = repository.findOne(qBatteryTypeMain.batteryName.eq(batteryName)).orElse(null);
        return convert.toDto(batteryTypeMain);
    }
}
