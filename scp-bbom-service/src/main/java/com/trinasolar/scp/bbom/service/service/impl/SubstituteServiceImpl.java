/**
 * @Function: SubstituteServiceImpl.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 14:12:15
 */
package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.entity.QComponents;
import com.trinasolar.scp.bbom.domain.entity.QItems;
import com.trinasolar.scp.bbom.domain.entity.QStructures;
import com.trinasolar.scp.bbom.domain.query.StructuresMrpQuery;
import com.trinasolar.scp.bbom.domain.query.SubstituteQuery;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.bbom.service.service.StructuresService;
import com.trinasolar.scp.bbom.service.service.SubstituteService;
import com.trinasolar.scp.common.api.util.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 *
 * @Function: SubstituteServiceImpl.java
 * @Description: 替代项处理
 * @version: v1.0.0
 * @author: niuweiwei
 * @date: 2024年1月3日 14:12:15
 */
@Slf4j
@Service("substituteService")
@RequiredArgsConstructor
public class SubstituteServiceImpl implements SubstituteService {

    private static final QComponents qComponents = QComponents.components;

    private static final QItems qItems = QItems.items;

    private static final QStructures qStructures = QStructures.structures;

    private final JPAQueryFactory jpaQueryFactory;

    private final ItemsService itemsService;

    private final StructuresService structuresService;

    @Override
    public StructuresDTO getItemCodeSubs(SubstituteQuery query) {
        Long organizationId = query.getOrganizationId();
        if (organizationId == null) {
            throw new BizException("bbom_valid_organization_notBlank");
        }
        if (StringUtils.isBlank(query.getItemCode())) {
            return null;
        }
        ItemsDTO itemsDTO = itemsService.findOneByItemCodeAndOrganizationId(query.getItemCode(), query.getOrganizationId());
        if (itemsDTO == null) {
            return null;
        }
        StructuresMrpQuery structuresMrpQuery = new StructuresMrpQuery();

        structuresMrpQuery.setAssemblyItemId(itemsDTO.getSourceItemId());
        structuresMrpQuery.setAlternateBomDesignator(query.getAlternateBomDesignator());

        StructuresDTO oneByMrpQuery = structuresService.findOneByMrpQuery(structuresMrpQuery);

        return oneByMrpQuery;
    }
}
