package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.PdmItemLifecycle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * pdm物料生命周期原始表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Repository
public interface PdmItemLifecycleRepository extends JpaRepository<PdmItemLifecycle, Long>, QuerydslPredicateExecutor<PdmItemLifecycle> {
    PdmItemLifecycle queryFirstByItemCodeAndOrgIdOrderByCreatedTimeDesc(String itemCode, Long orgId);
}
