package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.SiliconCellGradeDEConvert;
import com.trinasolar.scp.bbom.domain.dto.BatteryWaferPropertyDTO;
import com.trinasolar.scp.bbom.domain.dto.SiliconCellGradeDTO;
import com.trinasolar.scp.bbom.domain.entity.QSiliconCellGrade;
import com.trinasolar.scp.bbom.domain.entity.SiliconCellGrade;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.SiliconCellGradeExcelDTO;
import com.trinasolar.scp.bbom.domain.query.SiliconCellGradeQuery;
import com.trinasolar.scp.bbom.domain.save.SiliconCellGradeSaveDTO;
import com.trinasolar.scp.bbom.service.repository.SiliconCellGradeRepository;
import com.trinasolar.scp.bbom.service.service.BatterySiliconWaferService;
import com.trinasolar.scp.bbom.service.service.SiliconCellGradeService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 硅片等级与电池等级映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Slf4j
@Service("siliconCellGradeService")
@RequiredArgsConstructor
public class SiliconCellGradeServiceImpl implements SiliconCellGradeService {
    private static final QSiliconCellGrade qSiliconCellGrade = QSiliconCellGrade.siliconCellGrade;

    private final SiliconCellGradeDEConvert convert;

    private final SiliconCellGradeRepository repository;

    @Autowired
    private BatterySiliconWaferService batterySiliconWaferService;

    private static void verify(SiliconCellGradeSaveDTO saveDTO) {
        // 判断校验
        if (StringUtils.isBlank(saveDTO.getSiliconWaferProperties())) {
            throw new BizException("bbom_valid_siliconWaferProperties_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getSiliconWaferConditionalItem())) {
            throw new BizException("bbom_valid_SiliconWaferConditionalItem_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getSiliconWaferValue())) {
            throw new BizException("bbom_valid_SiliconWaferValue_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBatteryProperties())) {
            throw new BizException("bbom_valid_batteryProperties_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBatteryConditionalItem())) {
            throw new BizException("bbom_valid_BatteryConditionalItem_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBatteryValue())) {
            throw new BizException("bbom_valid_BatteryValue_notBlank");
        }
    }

    private static void extracted(SiliconCellGradeExcelDTO dto, BooleanBuilder booleanBuilder) {
        if (StringUtils.isNotEmpty(dto.getSiliconWaferProperties())) {
            booleanBuilder.and(qSiliconCellGrade.siliconWaferProperties.eq(dto.getSiliconWaferProperties()));
        }
        if (StringUtils.isNotEmpty(dto.getSiliconWaferConditionalItem())) {
            booleanBuilder.and(qSiliconCellGrade.siliconWaferConditionalItem.eq(dto.getSiliconWaferConditionalItem()));
        }
        if (StringUtils.isNotEmpty(dto.getSiliconWaferValue())) {
            booleanBuilder.and(qSiliconCellGrade.siliconWaferValue.eq(dto.getSiliconWaferValue()));
        }
        if (StringUtils.isNotEmpty(dto.getBatteryProperties())) {
            booleanBuilder.and(qSiliconCellGrade.batteryProperties.eq(dto.getBatteryProperties()));
        }
        if (StringUtils.isNotEmpty(dto.getBatteryConditionalItem())) {
            booleanBuilder.and(qSiliconCellGrade.batteryConditionalItem.eq(dto.getBatteryConditionalItem()));
        }
        if (StringUtils.isNotEmpty(dto.getBatteryValue())) {
            booleanBuilder.and(qSiliconCellGrade.batteryValue.eq(dto.getBatteryValue()));
        }
    }

    @Override
    public Page<SiliconCellGradeDTO> queryByPage(SiliconCellGradeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<SiliconCellGrade> page = repository.findAll(booleanBuilder, pageable);
        List<SiliconCellGradeDTO> siliconCellGradeDTOS = convert.toDto(page.getContent());
        for (SiliconCellGradeDTO dto : siliconCellGradeDTOS) {
            List<BatteryWaferPropertyDTO> waferList = batterySiliconWaferService.queryWaferPropertyList();
            List<BatteryWaferPropertyDTO> batterylist = batterySiliconWaferService.queryBatteryPropertyList();
            for (BatteryWaferPropertyDTO wafer : waferList) {
                if (wafer.getId().equals(dto.getId())) {
                    dto.setSiliconWaferPropertiesName(wafer.getName());
                }
            }
            for (BatteryWaferPropertyDTO battery : batterylist) {
                if (battery.getId().equals(dto.getId())) {
                    dto.setBatteryPropertiesName(battery.getName());
                }
            }
        }
        return new PageImpl(siliconCellGradeDTOS, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, SiliconCellGradeQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qSiliconCellGrade.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferProperties())) {
            booleanBuilder.and(qSiliconCellGrade.siliconWaferProperties.eq(query.getSiliconWaferProperties()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferConditionalItem())) {
            booleanBuilder.and(qSiliconCellGrade.siliconWaferConditionalItem.eq(query.getSiliconWaferConditionalItem()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferValue())) {
            booleanBuilder.and(qSiliconCellGrade.siliconWaferValue.eq(query.getSiliconWaferValue()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryProperties())) {
            booleanBuilder.and(qSiliconCellGrade.batteryProperties.eq(query.getBatteryProperties()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryConditionalItem())) {
            booleanBuilder.and(qSiliconCellGrade.batteryConditionalItem.eq(query.getBatteryConditionalItem()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryValue())) {
            booleanBuilder.and(qSiliconCellGrade.batteryValue.eq(query.getBatteryValue()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qSiliconCellGrade.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qSiliconCellGrade.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }
    }

    @Override
    public SiliconCellGradeDTO queryById(Long id) {
        SiliconCellGrade queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public SiliconCellGradeDTO save(SiliconCellGradeSaveDTO saveDTO) {
        SiliconCellGrade newObj = new SiliconCellGrade();
        // 为空判断
        verify(saveDTO);
        // 校验合法性
        List<BatteryWaferPropertyDTO> batteryWaferPropertyDTOS = batterySiliconWaferService.queryWaferPropertyList();
        List<BatteryWaferPropertyDTO> batteryWaferPropertyDTOS1 = batterySiliconWaferService.queryBatteryPropertyList();
        for (BatteryWaferPropertyDTO dto : batteryWaferPropertyDTOS) {
            if (!saveDTO.getSiliconWaferProperties().equals(dto.getId().toString())) {
                throw new BizException("bbom_valid_SiliconWaferProperties_illegal");
            }
        }
        for (BatteryWaferPropertyDTO dto : batteryWaferPropertyDTOS1) {
            if (!saveDTO.getBatteryProperties().equals(dto.getId().toString())) {
                throw new BizException("bbom_valid_BatteryProperties_illegal");
            }
        }
        // 查询 区分新增 修改结果集合
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        SiliconCellGradeExcelDTO siliconCellGradeExcelDTO = new SiliconCellGradeExcelDTO();
        BeanUtils.copyProperties(saveDTO, siliconCellGradeExcelDTO);
        extracted(siliconCellGradeExcelDTO, booleanBuilder);
        Optional<SiliconCellGrade> one = repository.findOne(booleanBuilder);
        if (one.isPresent()) {
            SiliconCellGrade siliconCellGrade = one.get();
            BeanUtils.copyProperties(siliconCellGrade, newObj);
            // 只需更新部分字段
            newObj.setSiliconWaferValue(saveDTO.getSiliconWaferValue());
            newObj.setBatteryValue(saveDTO.getBatteryValue());
        } else {
            saveDTO.setId(null);
            BeanUtils.copyProperties(saveDTO, newObj);
        }
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(SiliconCellGradeQuery query, HttpServletResponse response) {
        List<SiliconCellGradeDTO> dto = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        List<SiliconCellGradeExcelDTO> exportDTOS = convert.toExcelDTO(dto);
        //  硅片属性、电池属性需要转码 lov id->name
        for (SiliconCellGradeExcelDTO excelDTO : exportDTOS) {
            if (StringUtils.isNotBlank(excelDTO.getSiliconWaferProperties())) {
                List<BatteryWaferPropertyDTO> waferList = batterySiliconWaferService.queryWaferPropertyList();
                List<BatteryWaferPropertyDTO> batterylist = batterySiliconWaferService.queryBatteryPropertyList();
                for (BatteryWaferPropertyDTO wafer : waferList) {
                    if (wafer.getId().equals(excelDTO.getId())) {
                        excelDTO.setSiliconWaferProperties(wafer.getName());
                    }
                }
                for (BatteryWaferPropertyDTO battery : batterylist) {
                    if (battery.getId().equals(excelDTO.getId())) {
                        excelDTO.setBatteryProperties(battery.getName());
                    }
                }
            }
        }
        ExcelUtils.setExportResponseHeader(response, "硅片等级与电池等级映射-导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "硅片等级与电池等级映射").head(BatteryScreenPlateExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }
}
