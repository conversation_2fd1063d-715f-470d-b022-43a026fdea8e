package com.trinasolar.scp.bbom.service.util;

import com.trinasolar.scp.bbom.domain.dto.AttrTypeHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.bbom.domain.query.AttrTypeLineQuery;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Component
@Slf4j
@CacheConfig(cacheManager = "caffeineCacheManager")
public class AttrUtil {
    @Autowired
    public SystemFeign systemFeign;

    @Autowired
    @Lazy
    public AttrUtil attrUtil;

    @Cacheable(cacheNames = "getAttrLineById", condition = "#p0!=null",
            key = "#p0", unless = "#result == null")
    public AttrTypeLineDTO getAttrLineById(Long attrLineId) {
        try {
            return systemFeign.queryAttrTypeLineById(new IdDTO().setId(attrLineId.toString())).getData();
        } catch (Exception e) {
            return null;
        }
    }

    @Cacheable(cacheNames = "getAttrLineById",
            key = "#headerCode+'_'+#value", unless = "#result == null")
    public AttrTypeLineDTO getAttrLineByHeaderCodeAndValue(String headerCode, String value) {
        List<AttrTypeLineDTO> attrTypeLineDTOS = Optional.ofNullable(attrUtil.queryAttrTypeLinesByHeaderCode(headerCode))
                .orElse(new ArrayList<>());
        return attrTypeLineDTOS.stream().filter(attrTypeLineDTO -> value.equals(attrTypeLineDTO.getAttrCode())).findFirst().orElse(null);
    }

    @Cacheable(cacheNames = "getAttrHeaderById", condition = "#p0!=null",
            key = "#p0", unless = "#result == null")
    public AttrTypeHeaderDTO getAttrHeaderById(Long attrHeaderId) {
        try {
            AttrTypeHeaderDTO data = systemFeign.queryAttrTypeHeaderById(new IdDTO().setId(attrHeaderId.toString())).getData();
            return data;
        } catch (Exception e) {
            return null;
        }
    }

    @Cacheable(cacheNames = "queryAttrTypeLinesByHeaderCode", condition = "#p0!=null",
            key = "#p0", unless = "#result == null")
    public List<AttrTypeLineDTO> queryAttrTypeLinesByHeaderCode(String headerCode) {
        AttrTypeLineQuery attrTypeLineQuery = new AttrTypeLineQuery();
        attrTypeLineQuery.setCode(headerCode);
        return systemFeign.queryAttrTypeLinesByHeaderCode(attrTypeLineQuery).getBody().getData();
    }

    public List<AttrTypeLineDTO> queryAttrTypeLinesByHeaderCodeNoCache(String headerCode) {
        AttrTypeLineQuery attrTypeLineQuery = new AttrTypeLineQuery();
        attrTypeLineQuery.setCode(headerCode);
        return systemFeign.queryAttrTypeLinesByHeaderCode(attrTypeLineQuery).getBody().getData();
    }

    @Cacheable(cacheNames = "getAttrTypeHeaderByCategorySegment5", condition = "#p0!=null",
            key = "#p0", unless = "#result == null")
    public AttrTypeHeaderDTO getAttrTypeHeaderByCategorySegment5(String categorySegment5) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(categorySegment5);
        ResponseEntity<Results<AttrTypeHeaderDTO>> attrTypeHeaderByCategorySegment5 = systemFeign.getAttrTypeHeaderByCategorySegment5(idDTO);
        return attrTypeHeaderByCategorySegment5.getBody().getData();
    }

    @Cacheable(cacheNames = "findItemTransToLovAttrLines", condition = "#p0!=null",
            key = "#p0", unless = "#result == null")
    public List<AttrTypeLineDTO> findItemTransToLovAttrLines(String categorySegment5) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(categorySegment5);
        ResponseEntity<Results<List<AttrTypeLineDTO>>> attrTypeHeaderByCategorySegment5 = systemFeign.findItemTransToLovAttrLines(idDTO);
        return attrTypeHeaderByCategorySegment5.getBody().getData();
    }
}

