package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.entity.Items;
import com.trinasolar.scp.bbom.domain.entity.RuleLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 物料基础数据表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
@Repository
public interface ItemsRepository extends JpaRepository<Items, Long>, QuerydslPredicateExecutor<Items> {
    @Query("select i from Items i where i.sourceItemId = ?1 and i.organizationId = ?2 and i.language = ?3 and i.isDeleted = 0")
    Items findBySourceItemIdAndOrganizationIdAndLanguage(Long sourceItemId, Long organizationId, String language);

    @Query(value = "SELECT bi.item_code AS itemCode,bs.alternate_bom_designator AS alternateBomDesignator FROM scp_bbom.bbom_items bi,scp_bbom.bbom_structures bs " +
            "WHERE bi.source_item_id = bs.assembly_item_id " +
            " AND bi.organization_id = '82' " +
            " and bs.is_deleted = '0' " +
            " and bi.is_deleted = '0' " +
            " AND bi.item_code in(:itemCodeList)  " +
            " AND bs.alternate_bom_designator IS NOT NULL ",
            nativeQuery = true)
    List<Map> getDescriptionByCodes(@Param("itemCodeList") List<String> itemCodeList);
}
