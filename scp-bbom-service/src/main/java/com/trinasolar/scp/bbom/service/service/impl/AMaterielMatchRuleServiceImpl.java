package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.AMaterielMatchRuleDEConvert;
import com.trinasolar.scp.bbom.domain.dto.AMaterielMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.bbom.domain.entity.AMaterielMatchRule;
import com.trinasolar.scp.bbom.domain.entity.QAMaterielMatchRule;
import com.trinasolar.scp.bbom.domain.excel.AMaterielMatchRuleExcelDTO;
import com.trinasolar.scp.bbom.domain.query.AMaterielMatchRuleQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsQuery;
import com.trinasolar.scp.bbom.domain.save.AMaterielMatchRuleSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.ApsFeign;
import com.trinasolar.scp.bbom.service.repository.AMaterielMatchRuleRepository;
import com.trinasolar.scp.bbom.service.service.AMaterielMatchRuleService;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Slf4j
@Service("AMaterielMatchRuleService")
@RequiredArgsConstructor
public class AMaterielMatchRuleServiceImpl implements AMaterielMatchRuleService {
    private final AMaterielMatchRuleRepository repository;
    private final AMaterielMatchRuleDEConvert convert;
    private final ItemsService itemsService;
    private final ApsFeign apsFeign;
    private static final QAMaterielMatchRule aMaterielMatchRule = QAMaterielMatchRule.aMaterielMatchRule;

    @Override
    public Page<AMaterielMatchRuleDTO> queryByPage(AMaterielMatchRuleQuery query) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Page<AMaterielMatchRule> page = repository.findAll(booleanBuilder, pageable);
        List<AMaterielMatchRuleDTO> aMaterielMatchRuleDTOS = convert.toDto(page.getContent());
        return new PageImpl(aMaterielMatchRuleDTOS, page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, AMaterielMatchRuleQuery query) {
        if (StringUtils.isNotEmpty(query.getIsOversea())) {
            booleanBuilder.and(aMaterielMatchRule.isOversea.eq(query.getIsOversea()));
        }
        if (StringUtils.isNotEmpty(query.getCellsType())) {
            booleanBuilder.and(aMaterielMatchRule.cellsType.eq(query.getCellsType()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(aMaterielMatchRule.workshop.eq(query.getWorkshop()));
        }
    }

    @Override
    public AMaterielMatchRuleDTO queryById(Long id) {
        AMaterielMatchRule queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public AMaterielMatchRuleDTO save(AMaterielMatchRuleSaveDTO saveDTO) {
        AMaterielMatchRule newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new AMaterielMatchRule());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<AMaterielMatchRuleExcelDTO> excelDtos = ExcelUtils.readExcel(multipartFile.getInputStream(), null, AMaterielMatchRuleExcelDTO.class, excelPara);
        if (CollectionUtils.isEmpty(excelDtos)) {
            throw new BizException("import.data.empty");
        }
        this.checkInput(excelDtos);
        List<AMaterielMatchRule> saveList = convert.excelDtoToSaveDto(excelDtos);
        // 先删除之前的数据,再保存信息的数据
        repository.deleteAll();
        repository.saveAll(saveList);
    }

    private void checkInput(List<AMaterielMatchRuleExcelDTO> excelDTOS) {
        final int[] i = {2};
        List<String> errors = new ArrayList<>();
        List<String> groupKeyList = Lists.newLinkedList();
        List<String> cellBasePlaceList = this.getCellBasePlace();
        List<String> aItemCodeList = this.getAItemCode();
        for (AMaterielMatchRuleExcelDTO excelDTO : excelDTOS) {
            checkNullField(excelDTO, i);

            LovLineDTO lovLineDTOIsOversea = LovUtils.getByName(LovHeaderCodeConstant.DOMESTIC_OVERSEA, excelDTO.getIsOverseaName());
            if (lovLineDTOIsOversea == null) {
                String message = MessageHelper.getMessage("a.materiel.match.rule.import.isoversea.not.exists",new Object[]{i[0], excelDTO.getIsOverseaName()}).getDesc();
                errors.add(message);
            } else {
                excelDTO.setIsOversea(lovLineDTOIsOversea.getLovValue());
            }

            if (!cellBasePlaceList.contains(excelDTO.getWorkshop())) {
                String message = MessageHelper.getMessage("a.materiel.match.rule.import.workshop.not.exists",new Object[]{i[0], excelDTO.getWorkshop()}).getDesc();
                errors.add(message);
            }

            LovLineDTO lovLineDTOCellsType = LovUtils.getByName(LovHeaderCodeConstant.APS_BATTERY_TYPE, excelDTO.getCellsTypeName());
            if (lovLineDTOCellsType == null) {
                String message = MessageHelper.getMessage("a.materiel.match.rule.import.cellstype.not.exists",new Object[]{i[0], excelDTO.getCellsTypeName()}).getDesc();
                errors.add(message);
            } else {
                excelDTO.setCellsType(lovLineDTOCellsType.getLovValue());
            }

            if (StringUtils.isNotEmpty(excelDTO.getActualPieceThicknessName())) {
                LovLineDTO lovLineDTOActualPieceThickness = LovUtils.getByName(LovHeaderCodeConstant.ACTUAL_PIECE_THICKNESS, excelDTO.getActualPieceThicknessName());
                if (lovLineDTOActualPieceThickness == null) {
                    String message = MessageHelper.getMessage("a.materiel.match.rule.import.actualpiecethickness.not.exists",new Object[]{i[0], excelDTO.getActualPieceThicknessName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setActualPieceThickness(lovLineDTOActualPieceThickness.getLovValue());
                }
            }

            if (StringUtils.isNotEmpty(excelDTO.getMainGridBothShapeName())) {
                LovLineDTO lovLineDTOActualMainGridBothShape = LovUtils.getByName(LovHeaderCodeConstant.MAIN_GRID_BOTH_SHAPE, excelDTO.getMainGridBothShapeName());
                if (lovLineDTOActualMainGridBothShape == null) {
                    String message = MessageHelper.getMessage("a.materiel.match.rule.import.maingridbothshape.not.exists",new Object[]{i[0], excelDTO.getMainGridBothShapeName()}).getDesc();
                    errors.add(message);
                } else {
                    excelDTO.setMainGridBothShape(lovLineDTOActualMainGridBothShape.getLovValue());
                }
            }

            if (!aItemCodeList.contains(excelDTO.getAItemCode())) {
                String message = MessageHelper.getMessage("a.materiel.match.rule.import.aItemCode.not.exists",new Object[]{i[0], excelDTO.getAItemCode()}).getDesc();
                errors.add(message);
            }

            String groupKey = StringUtils.join(excelDTO.getIsOversea(), excelDTO.getWorkshop(), excelDTO.getCellsType(), excelDTO.getActualPieceThickness(), excelDTO.getMainGridBothShape(), excelDTO.getAItemCode());
            if (groupKeyList.contains(groupKey)) {
                String message = MessageHelper.getMessage("a.materiel.match.rule.import.maingridbothshape.groupKey.repeat",new Object[]{i[0]}).getDesc();
                errors.add(message);
            } else {
                groupKeyList.add(groupKey);
            }
            i[0]++;
        }
        if (errors.size() > 0) {
            String errorString = errors.stream()
                    .collect(Collectors.joining(";"));
            throw new BizException(errorString);
        }
    }

    private void checkNullField(AMaterielMatchRuleExcelDTO excelDTO, int[] i) {
        if (StringUtils.isEmpty(excelDTO.getIsOverseaName()) || StringUtils.isEmpty(excelDTO.getWorkshop())
                || Objects.isNull(excelDTO.getCellsTypeName()) || Objects.isNull(excelDTO.getAItemCode())) {
            String message = MessageHelper.getMessage("a.materiel.match.rule.import.row.not.null",new Object[]{i[0]}).getDesc();
            throw new BizException(message);
        }
    }

    private List<String> getAItemCode() {
        ItemsQuery query = new ItemsQuery();
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setItemStatus("Active");
        query.setCategorySegment5("电池片");
        Page<ItemsDTO> itemsDTOS = itemsService.queryByItemCode(query);
        return itemsDTOS.getContent().stream().map(ItemsDTO::getItemCode).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    private List<String> getCellBasePlace() {
        ResponseEntity<Results<List<String>>> response = apsFeign.getCellBasePlace();
        if (response == null || response.getBody() == null || !response.getBody().isSuccess()) {
            throw new BizException("获取电池基地数据失败");
        }
        return response.getBody().getData();
    }

    @Override
    @SneakyThrows
    public void export(AMaterielMatchRuleQuery query, HttpServletResponse response) {
        //获取数据库中数据
        List<AMaterielMatchRuleDTO> dtos = queryByPage(query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        //数据转换
        List<List<Object>> datas = ExcelUtils.getList(dtos, excelPara);
        //导出调用excelUtils
        String fileName = MessageHelper.getMessage("bbom_a_materiel_match_rule").getDesc();
        ExcelUtils.exportExWithLocalDate(response, fileName,fileName,excelPara.getSimpleHeader(),datas);
    }
}
