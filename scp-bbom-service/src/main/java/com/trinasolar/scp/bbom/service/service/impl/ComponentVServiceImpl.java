package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.ComponentVDEConvert;
import com.trinasolar.scp.bbom.domain.dto.BillOfMaterialVO;
import com.trinasolar.scp.bbom.domain.dto.ComponentVDTO;
import com.trinasolar.scp.bbom.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.OrganizationDefinitionsDTO;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.enums.YNEnum;
import com.trinasolar.scp.bbom.domain.query.ComponentVQuery;
import com.trinasolar.scp.bbom.domain.save.ComponentVSaveDTO;
import com.trinasolar.scp.bbom.service.repository.ComponentVRepository;
import com.trinasolar.scp.bbom.service.repository.ComponentsRepository;
import com.trinasolar.scp.bbom.service.repository.StructuresRepository;
import com.trinasolar.scp.bbom.service.service.ComponentVService;
import com.trinasolar.scp.bbom.service.service.ErpInterfaceService;
import com.trinasolar.scp.bbom.service.service.SystemService;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 同步cux3_bbom_component_v
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Slf4j
@Service("componentVService")
@RequiredArgsConstructor
public class ComponentVServiceImpl implements ComponentVService {

    private static final int LIMIT = 1000;

    private static final int PAGE_SIZE = 500;

    private static final QComponentV qComponentV = QComponentV.componentV;

    private static final QStructures qStructures = QStructures.structures;

    private static final QComponents qComponents = QComponents.components;

    private static final ReentrantLock saveStructureLock = new ReentrantLock();

    private static final ReentrantLock saveComponentLock = new ReentrantLock();

    public static final String COMPONENT_VSYNC_BY_INTERFACE = "componentVsyncByInterface";

    final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final ComponentVDEConvert convert;

    private final ComponentVRepository repository;

    private final SystemService systemService;

    private final ErpInterfaceService erpInterfaceService;

    private final JPAQueryFactory jpaQueryFactory;

    private final StructuresRepository structuresRepository;

    private final ComponentsRepository componentsRepository;

    private final RedissonClient redissonClient;

    @Autowired
    @Qualifier("dataSyncThreadPool")
    ExecutorService threadPoolExecutor;

    @Value("${erp.interface.platform.url}")
    private String erpUrl;

    @Value("${erp.interface.ea.clientid}")
    private String eaClientId;

    @Value("${erp.interface.ea.clientsecret}")
    private String eaClientSecret;

    @Override
    public Page<ComponentVDTO> queryByPage(ComponentVQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ComponentV> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ComponentVQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qComponentV.id.eq(query.getId()));
        }
        if (query.getLastUpdateDate() != null) {
            booleanBuilder.and(qComponentV.lastUpdateDate.eq(query.getLastUpdateDate()));
        }
        if (StringUtils.isNotEmpty(query.getBillSequenceId())) {
            booleanBuilder.and(qComponentV.billSequenceId.eq(query.getBillSequenceId()));
        }
        if (StringUtils.isNotEmpty(query.getAssemblyItemId())) {
            booleanBuilder.and(qComponentV.assemblyItemId.eq(query.getAssemblyItemId()));
        }
        if (StringUtils.isNotEmpty(query.getOrganizationId())) {
            booleanBuilder.and(qComponentV.organizationId.eq(query.getOrganizationId()));
        }
        if (StringUtils.isNotEmpty(query.getAssemblyType())) {
            booleanBuilder.and(qComponentV.assemblyType.eq(query.getAssemblyType()));
        }
        if (StringUtils.isNotEmpty(query.getAssItemNum())) {
            booleanBuilder.and(qComponentV.assItemNum.eq(query.getAssItemNum()));
        }
        if (StringUtils.isNotEmpty(query.getAssItemDes())) {
            booleanBuilder.and(qComponentV.assItemDes.eq(query.getAssItemDes()));
        }
        if (StringUtils.isNotEmpty(query.getAssItemUom())) {
            booleanBuilder.and(qComponentV.assItemUom.eq(query.getAssItemUom()));
        }
        if (StringUtils.isNotEmpty(query.getAlternateBomDesignator())) {
            booleanBuilder.and(qComponentV.alternateBomDesignator.eq(query.getAlternateBomDesignator()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttributeCategory())) {
            booleanBuilder.and(qComponentV.bmAttributeCategory.eq(query.getBmAttributeCategory()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute1())) {
            booleanBuilder.and(qComponentV.bmAttribute1.eq(query.getBmAttribute1()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute2())) {
            booleanBuilder.and(qComponentV.bmAttribute2.eq(query.getBmAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute3())) {
            booleanBuilder.and(qComponentV.bmAttribute3.eq(query.getBmAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute4())) {
            booleanBuilder.and(qComponentV.bmAttribute4.eq(query.getBmAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute5())) {
            booleanBuilder.and(qComponentV.bmAttribute5.eq(query.getBmAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute6())) {
            booleanBuilder.and(qComponentV.bmAttribute6.eq(query.getBmAttribute6()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute7())) {
            booleanBuilder.and(qComponentV.bmAttribute7.eq(query.getBmAttribute7()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute8())) {
            booleanBuilder.and(qComponentV.bmAttribute8.eq(query.getBmAttribute8()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute9())) {
            booleanBuilder.and(qComponentV.bmAttribute9.eq(query.getBmAttribute9()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute10())) {
            booleanBuilder.and(qComponentV.bmAttribute10.eq(query.getBmAttribute10()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute11())) {
            booleanBuilder.and(qComponentV.bmAttribute11.eq(query.getBmAttribute11()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute12())) {
            booleanBuilder.and(qComponentV.bmAttribute12.eq(query.getBmAttribute12()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute13())) {
            booleanBuilder.and(qComponentV.bmAttribute13.eq(query.getBmAttribute13()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute14())) {
            booleanBuilder.and(qComponentV.bmAttribute14.eq(query.getBmAttribute14()));
        }
        if (StringUtils.isNotEmpty(query.getBmAttribute15())) {
            booleanBuilder.and(qComponentV.bmAttribute15.eq(query.getBmAttribute15()));
        }
        if (StringUtils.isNotEmpty(query.getComponentSequenceId())) {
            booleanBuilder.and(qComponentV.componentSequenceId.eq(query.getComponentSequenceId()));
        }
        if (StringUtils.isNotEmpty(query.getSubstituteComponentId())) {
            booleanBuilder.and(qComponentV.substituteComponentId.eq(query.getSubstituteComponentId()));
        }
        if (StringUtils.isNotEmpty(query.getComponentItemId())) {
            booleanBuilder.and(qComponentV.componentItemId.eq(query.getComponentItemId()));
        }
        if (StringUtils.isNotEmpty(query.getItemNum())) {
            booleanBuilder.and(qComponentV.itemNum.eq(query.getItemNum()));
        }
        if (StringUtils.isNotEmpty(query.getOperationSeqNum())) {
            booleanBuilder.and(qComponentV.operationSeqNum.eq(query.getOperationSeqNum()));
        }
        if (StringUtils.isNotEmpty(query.getBicItemNum())) {
            booleanBuilder.and(qComponentV.bicItemNum.eq(query.getBicItemNum()));
        }
        if (StringUtils.isNotEmpty(query.getBicItemDes())) {
            booleanBuilder.and(qComponentV.bicItemDes.eq(query.getBicItemDes()));
        }
        if (StringUtils.isNotEmpty(query.getBicItemUom())) {
            booleanBuilder.and(qComponentV.bicItemUom.eq(query.getBicItemUom()));
        }
        if (StringUtils.isNotEmpty(query.getBasisType())) {
            booleanBuilder.and(qComponentV.basisType.eq(query.getBasisType()));
        }
        if (StringUtils.isNotEmpty(query.getComponentQuantity())) {
            booleanBuilder.and(qComponentV.componentQuantity.eq(query.getComponentQuantity()));
        }
        if (StringUtils.isNotEmpty(query.getAutoRequestMaterial())) {
            booleanBuilder.and(qComponentV.autoRequestMaterial.eq(query.getAutoRequestMaterial()));
        }
        if (StringUtils.isNotEmpty(query.getEffectivityDate())) {
            booleanBuilder.and(qComponentV.effectivityDate.eq(query.getEffectivityDate()));
        }
        if (StringUtils.isNotEmpty(query.getDisableDate())) {
            booleanBuilder.and(qComponentV.disableDate.eq(query.getDisableDate()));
        }
        if (StringUtils.isNotEmpty(query.getChangeNotice())) {
            booleanBuilder.and(qComponentV.changeNotice.eq(query.getChangeNotice()));
        }
        if (StringUtils.isNotEmpty(query.getPlanningFactor())) {
            booleanBuilder.and(qComponentV.planningFactor.eq(query.getPlanningFactor()));
        }
        if (StringUtils.isNotEmpty(query.getComponentYieldFactor())) {
            booleanBuilder.and(qComponentV.componentYieldFactor.eq(query.getComponentYieldFactor()));
        }
        if (StringUtils.isNotEmpty(query.getEnforceIntRequirements())) {
            booleanBuilder.and(qComponentV.enforceIntRequirements.eq(query.getEnforceIntRequirements()));
        }
        if (StringUtils.isNotEmpty(query.getIncludeInCostRollup())) {
            booleanBuilder.and(qComponentV.includeInCostRollup.eq(query.getIncludeInCostRollup()));
        }
        if (StringUtils.isNotEmpty(query.getBicItemType())) {
            booleanBuilder.and(qComponentV.bicItemType.eq(query.getBicItemType()));
        }
        if (StringUtils.isNotEmpty(query.getBicItemStatus())) {
            booleanBuilder.and(qComponentV.bicItemStatus.eq(query.getBicItemStatus()));
        }
        if (StringUtils.isNotEmpty(query.getWipSupplyType())) {
            booleanBuilder.and(qComponentV.wipSupplyType.eq(query.getWipSupplyType()));
        }
        if (StringUtils.isNotEmpty(query.getSupplyType())) {
            booleanBuilder.and(qComponentV.supplyType.eq(query.getSupplyType()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttributeCategory())) {
            booleanBuilder.and(qComponentV.bcAttributeCategory.eq(query.getBcAttributeCategory()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute1())) {
            booleanBuilder.and(qComponentV.bcAttribute1.eq(query.getBcAttribute1()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute2())) {
            booleanBuilder.and(qComponentV.bcAttribute2.eq(query.getBcAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute3())) {
            booleanBuilder.and(qComponentV.bcAttribute3.eq(query.getBcAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute4())) {
            booleanBuilder.and(qComponentV.bcAttribute4.eq(query.getBcAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute5())) {
            booleanBuilder.and(qComponentV.bcAttribute5.eq(query.getBcAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute6())) {
            booleanBuilder.and(qComponentV.bcAttribute6.eq(query.getBcAttribute6()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute7())) {
            booleanBuilder.and(qComponentV.bcAttribute7.eq(query.getBcAttribute7()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute8())) {
            booleanBuilder.and(qComponentV.bcAttribute8.eq(query.getBcAttribute8()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute9())) {
            booleanBuilder.and(qComponentV.bcAttribute9.eq(query.getBcAttribute9()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute10())) {
            booleanBuilder.and(qComponentV.bcAttribute10.eq(query.getBcAttribute10()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute11())) {
            booleanBuilder.and(qComponentV.bcAttribute11.eq(query.getBcAttribute11()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute12())) {
            booleanBuilder.and(qComponentV.bcAttribute12.eq(query.getBcAttribute12()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute13())) {
            booleanBuilder.and(qComponentV.bcAttribute13.eq(query.getBcAttribute13()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute14())) {
            booleanBuilder.and(qComponentV.bcAttribute14.eq(query.getBcAttribute14()));
        }
        if (StringUtils.isNotEmpty(query.getBcAttribute15())) {
            booleanBuilder.and(qComponentV.bcAttribute15.eq(query.getBcAttribute15()));
        }
        if (StringUtils.isNotEmpty(query.getBscFlag())) {
            booleanBuilder.and(qComponentV.bscFlag.eq(query.getBscFlag()));
        }
        if (query.getIsProcess() != null) {
            booleanBuilder.and(qComponentV.isProcess.eq(query.getIsProcess()));
        }
    }

    @Override
    public ComponentVDTO queryById(Long id) {
        ComponentV queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ComponentVDTO save(ComponentVSaveDTO saveDTO) {
        ComponentV newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ComponentV());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ComponentVQuery query, HttpServletResponse response) {
        List<ComponentVDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "同步cux3_bbom_component_v", "同步cux3_bbom_component_v", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public void syncByInterface(LocalDateTime fromDateTime, Long[] organizations, int pageNum) {
        // 分页
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        // 加分布式锁
        RLock lock = redissonClient.getLock(COMPONENT_VSYNC_BY_INTERFACE);
        if (lock.tryLock()) {
            try {
                LocalDateTime now = LocalDateTime.now();
                organizations = loadOrganizations(organizations);
                for (int i = 0; i < organizations.length; i++) {
                    Long organizationId = organizations[i];
                    ComponentV lastRecord = repository.findByLastRecordByOrganizationid(organizationId);
                    LocalDateTime from = LocalDateTime.parse("2001-01-01T00:00:00");
                    if (fromDateTime != null) {
                        from = fromDateTime;
                    } else if (lastRecord != null && lastRecord.getLastUpdateDate() != null) {
                        from = lastRecord.getLastUpdateDate();
                    }
                    for (; ; ) {
                        // 按月提交给线程池
                        String startTime = LocalDateTimeUtil.formatNormal(from);
                        from = from.plusMonths(1);
                        String endTime = LocalDateTimeUtil.formatNormal(from);
                        CompletableFuture<Void> saveFuture = CompletableFuture.runAsync(() -> {
                            doSync(pageNum, organizationId, startTime, endTime, now);
                        }, threadPoolExecutor);
                        completableFutures.add(saveFuture);
                        if (from.isAfter(now)) {
                            break;
                        }
                    }
                }
                CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
            } finally {
                lock.unlock();
            }
        }

    }

    private Long[] loadOrganizations(Long[] organizations) {
        if (organizations == null) {
            List<OrganizationDefinitionsDTO> organizationDefinitionsDTOS = systemService.getOrganizationCellsScpFlagIsY();
            organizations = organizationDefinitionsDTOS.stream().map(OrganizationDefinitionsDTO::getOrganizationId)
                    .distinct().toArray(Long[]::new);
        }
        return organizations;
    }

    private void doSync(int pageNum, Long organizationId, String startTime, String endTime, LocalDateTime now) {
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        // 提交给线程池
        int currentPage = pageNum;

        JSONObject param = new JSONObject();
        param.put("organization_id", Collections.singletonList(organizationId));
        param.put("pageSize", PAGE_SIZE);
        param.put("last_update_date_from", startTime);
        param.put("last_update_date_to", endTime);
        for (; ; ) {
            param.put("pageNum", currentPage++);
            JSONObject outJson = invockBomListInterface(param);
            JSONArray data = outJson.getJSONArray("data");
            if (data == null) {
                return;
            }
            List<BillOfMaterialVO> componentVS = data.toJavaList(BillOfMaterialVO.class);
            componentVS = filterItemNumber(now, componentVS);
            for (BillOfMaterialVO componentV : componentVS) {
                //  跳过disable的数据
                if (StringUtils.isNotBlank(componentV.getDisableDate())) {
                    LocalDateTime disableDate = LocalDateTime.parse(componentV.getDisableDate(), dateTimeFormatter);
                    if (now.minusMonths(1L).isAfter(disableDate)) {
                        continue;
                    }
                }
                ComponentV componentv = new ComponentV(componentV);
                if(StringUtils.isNotBlank(componentv.getComponentSequenceId())){
                    createComponentV(componentv);
                }
            }
        }
    }

    @Override
    public void transformation() {
        // 加分布式锁
        RLock lock = redissonClient.getLock(COMPONENT_VSYNC_BY_INTERFACE);
        if (lock.tryLock()) {
            // 分页
            List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
            try {
                List<String> componentSequenceIds = jpaQueryFactory.select(qComponentV.componentSequenceId)
                        .distinct()
                        .from(qComponentV)
                        .where(qComponentV.isProcess.eq(0))
                        .fetch();
                // 按组处理删除数据并且转换component 和 Structure
                for (String componentSequenceId : componentSequenceIds) {
                    CompletableFuture<Void> saveFuture = CompletableFuture.runAsync(() -> {
                        processByComponentSequenceId(componentSequenceId);
                    }, threadPoolExecutor);
                    completableFutures.add(saveFuture);
                }
                CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
            } finally {
                lock.unlock();
            }
        }
    }

    private void processByComponentSequenceId(String componentSequenceId) {
        // 获取批量
        List<ComponentV> componentVS = jpaQueryFactory.selectFrom(qComponentV)
                .where(qComponentV.componentSequenceId.eq(componentSequenceId))
                .where(qComponentV.isProcess.eq(0)).fetch();
        // 比较并保存差异
        if (CollectionUtils.isNotEmpty(componentVS)) {
            ComponentV componentV = componentVS.get(0);
            delByBillSequenceIds(componentV);
        }
        deleteComponentsV(componentSequenceId);
        // 用于剔除重复的处理逻辑
        for (ComponentV v : componentVS) {
            processComponentV(v);
        }
    }

    private void delByBillSequenceIds(ComponentV componentV) {
        // 查询有没有其他的BillSeqId
        JPAQuery<String> where = jpaQueryFactory
                .select(qComponentV.billSequenceId).from(qComponentV)
                .where(qComponentV.organizationId.eq(componentV.getOrganizationId()))
                .where(qComponentV.assemblyItemId.eq(componentV.getAssemblyItemId()))
                .where(qComponentV.billSequenceId.ne(componentV.getBillSequenceId()));

        // 这里需要考虑替代项
        if (StringUtils.isNotBlank(componentV.getAlternateBomDesignator())) {
            where.where(qComponentV.alternateBomDesignator.eq(componentV.getAlternateBomDesignator()));
        } else {
            where.where(qComponentV.alternateBomDesignator.isNull());
        }
        List<String> delBillSequenceIds = where
                .groupBy(qComponentV.billSequenceId)
                .fetch();
        //  如果不为空则删除所有BillSequenceIds下关联的数据
        for (String delBillSequenceId : delBillSequenceIds) {
            List<String> delComponentSequenceIds = jpaQueryFactory.select(qComponentV.componentSequenceId).from(qComponentV)
                    .distinct()
                    .where(qComponentV.billSequenceId.eq(delBillSequenceId))
                    .fetch();
            for (String delComponentSequenceId : delComponentSequenceIds) {
                deleteComponentsV(delComponentSequenceId);
            }
        }
    }

    private void deleteComponentsV(String componentSequenceId) {
        List<ComponentV> deleteList = jpaQueryFactory.selectFrom(qComponentV)
                .where(qComponentV.componentSequenceId.eq(componentSequenceId))
                .where(qComponentV.isProcess.eq(1)).fetch();
        log.warn("BOM同步ComponentV删除componentSequenceId--{}--{}", componentSequenceId, JSON.toJSONString(deleteList));
        repository.deleteAll(deleteList);

        // 删除所有关联的components数据,下一步再次生成
        List<Components> components = jpaQueryFactory
                .selectFrom(qComponents)
                .where(qComponents.componentSequenceId.eq(Long.valueOf(componentSequenceId))).fetch();
        log.warn("BOM同步components删除componentSequenceId--{}--{}", componentSequenceId, JSON.toJSONString(components));
        componentsRepository.deleteAll(components);
    }

    private void processComponentV(ComponentV componentV) {
        try {

            // 转换structure
            Structures structures = transStructure(componentV);
            // 转换component
            List<Components> componentsList = transComponent(componentV, structures);
            componentV.setIsProcess(1);
            repository.save(componentV);
        } catch (Exception e) {
            componentV.setErrorMsg(e.getClass() + " : " + e.getMessage());
            componentV.setIsProcess(3);
            repository.save(componentV);
            // 跳过错误
        }
    }

    private List<Components> transComponent(ComponentV componentV, Structures structure) {
        List<Components> result = new ArrayList<>();

        BigDecimal componentQuantity = new BigDecimal(componentV.getComponentQuantity());
        // 先查询是否幂等
        saveComponentLock.lock();
        try {
            Components component = findComponentsByComponentV(componentV);
            if (component == null) {
                component = saveComponent(componentV, structure, componentQuantity);
            } else {
                // 增加更新逻辑
                component = updateComponent(component, componentV, structure, componentQuantity);
            }
            result.add(component);
        } finally {
            saveComponentLock.unlock();
        }
        return result;
    }

    private Components updateComponent(Components component, ComponentV componentV, Structures structure, BigDecimal componentQuantity) {
        component.setBomId(structure.getId());
        if (StringUtils.isNotBlank(componentV.getOperationSeqNum())) {
            component.setOperationSeqNum(Integer.parseInt(componentV.getOperationSeqNum()));
        }
        if (StringUtils.isNotBlank(componentV.getComponentItemId())) {
            component.setComponentItemId(Long.parseLong(componentV.getComponentItemId()));
        }
        component.setSubstituteFlag(isSubstituteComponent(componentV) ? YNEnum.Y.getValue() : YNEnum.N.getValue());
        component.setLastUpdateDate(componentV.getLastUpdateDate());
        if (StringUtils.isNotBlank(componentV.getItemNum())) {
            component.setItemNum(Integer.valueOf(componentV.getItemNum()));
        }
        component.setComponentQuantity(componentQuantity.toString());
        if (StringUtils.isNotBlank(componentV.getComponentYieldFactor())) {
            component.setComponentYieldFactor(Integer.valueOf(componentV.getComponentYieldFactor()));
        }
        component.setComponentRemarks("");
        if (StringUtils.isNotBlank(componentV.getEffectivityDate())) {
            component.setEffectivityDate(LocalDateTimeUtil.parse(componentV.getEffectivityDate().substring(0, 19), DatePattern.NORM_DATETIME_PATTERN));
        }
        component.setChangeNotice(componentV.getChangeNotice());
        if (StringUtils.isNotBlank(componentV.getDisableDate())) {
            component.setDisableDate(LocalDateTimeUtil.parse(componentV.getDisableDate(), DatePattern.NORM_DATETIME_PATTERN));
        }
        component.setAttributeCategory(componentV.getBcAttributeCategory());
        component.setAttribute1(componentV.getBcAttribute1());
        component.setAttribute2(componentV.getBcAttribute2());
        component.setAttribute3(componentV.getBcAttribute3());
        component.setAttribute4(componentV.getBcAttribute4());
        component.setAttribute5(componentV.getBcAttribute5());
        component.setAttribute6(componentV.getBcAttribute6());
        component.setAttribute7(componentV.getBcAttribute7());
        component.setAttribute8(componentV.getBcAttribute8());
        component.setAttribute9(componentV.getBcAttribute9());
        component.setAttribute10(componentV.getBcAttribute10());
        component.setAttribute11(componentV.getBcAttribute11());
        component.setAttribute12(componentV.getBcAttribute12());
        component.setAttribute13(componentV.getBcAttribute13());
        component.setAttribute14(componentV.getBcAttribute14());
        component.setAttribute15(componentV.getBcAttribute15());
        if (StringUtils.isNotBlank(componentV.getPlanningFactor())) {
            component.setPlanningFactor(Integer.valueOf(componentV.getPlanningFactor()));
        }
        if (StringUtils.isNotBlank(componentV.getComponentSequenceId())) {
            component.setComponentSequenceId(Long.valueOf(componentV.getComponentSequenceId()));
        }
        if (StringUtils.isNotBlank(componentV.getSubstituteComponentId())) {
            component.setSubstituteComponentId(Long.valueOf(componentV.getSubstituteComponentId()));
        }
        if (StringUtils.isNotBlank(componentV.getBillSequenceId())) {
            component.setBillSequenceId(Long.valueOf(componentV.getBillSequenceId()));
        }
        if (StringUtils.isNotBlank(componentV.getWipSupplyType())) {
            component.setWipSupplyType(Integer.valueOf(componentV.getWipSupplyType()));
        }
//            component.setSupplySubinventory();
//            component.setSupplyLocatorId();
//            component.setBomItemType();
        if (StringUtils.isNotBlank(componentV.getBasisType())) {
            component.setBasisType(Integer.valueOf(componentV.getBasisType()));
        }
        componentsRepository.save(component);
        return component;
    }

    private Components saveComponent(ComponentV componentV, Structures structure, BigDecimal componentQuantity) {
        Components component;
        component = new Components();
        component.setBomId(structure.getId());
        if (StringUtils.isNotBlank(componentV.getOperationSeqNum())) {
            component.setOperationSeqNum(Integer.parseInt(componentV.getOperationSeqNum()));
        }
        if (StringUtils.isNotBlank(componentV.getComponentItemId())) {
            component.setComponentItemId(Long.parseLong(componentV.getComponentItemId()));
        }

        component.setSubstituteFlag(isSubstituteComponent(componentV) ? YNEnum.Y.getValue() : YNEnum.N.getValue());
        component.setLastUpdateDate(componentV.getLastUpdateDate());
        if (StringUtils.isNotBlank(componentV.getItemNum())) {
            component.setItemNum(Integer.valueOf(componentV.getItemNum()));
        }
        component.setComponentQuantity(componentQuantity.toString());
        if (StringUtils.isNotBlank(componentV.getComponentYieldFactor())) {
            component.setComponentYieldFactor(Integer.valueOf(componentV.getComponentYieldFactor()));
        }
        component.setComponentRemarks("");
        if (StringUtils.isNotBlank(componentV.getEffectivityDate())) {
            component.setEffectivityDate(LocalDateTimeUtil.parse(componentV.getEffectivityDate().substring(0, 19), DatePattern.NORM_DATETIME_PATTERN));
        }
        component.setChangeNotice(componentV.getChangeNotice());
        if (StringUtils.isNotBlank(componentV.getDisableDate())) {
            component.setDisableDate(LocalDateTimeUtil.parse(componentV.getDisableDate(), DatePattern.NORM_DATETIME_PATTERN));
        }
        component.setAttributeCategory(componentV.getBcAttributeCategory());
        component.setAttribute1(componentV.getBcAttribute1());
        component.setAttribute2(componentV.getBcAttribute2());
        component.setAttribute3(componentV.getBcAttribute3());
        component.setAttribute4(componentV.getBcAttribute4());
        component.setAttribute5(componentV.getBcAttribute5());
        component.setAttribute6(componentV.getBcAttribute6());
        component.setAttribute7(componentV.getBcAttribute7());
        component.setAttribute8(componentV.getBcAttribute8());
        component.setAttribute9(componentV.getBcAttribute9());
        component.setAttribute10(componentV.getBcAttribute10());
        component.setAttribute11(componentV.getBcAttribute11());
        component.setAttribute12(componentV.getBcAttribute12());
        component.setAttribute13(componentV.getBcAttribute13());
        component.setAttribute14(componentV.getBcAttribute14());
        component.setAttribute15(componentV.getBcAttribute15());
        component.setSlurrySubstituteFlag("N");
        if (StringUtils.isNotBlank(componentV.getPlanningFactor())) {
            component.setPlanningFactor(Integer.valueOf(componentV.getPlanningFactor()));
        }
        if (StringUtils.isNotBlank(componentV.getComponentSequenceId())) {
            component.setComponentSequenceId(Long.valueOf(componentV.getComponentSequenceId()));
        }
        if (StringUtils.isNotBlank(componentV.getSubstituteComponentId())) {
            component.setSubstituteComponentId(Long.valueOf(componentV.getSubstituteComponentId()));
        }
        if (StringUtils.isNotBlank(componentV.getBillSequenceId())) {
            component.setBillSequenceId(Long.valueOf(componentV.getBillSequenceId()));
        }
        if (StringUtils.isNotBlank(componentV.getWipSupplyType())) {
            component.setWipSupplyType(Integer.valueOf(componentV.getWipSupplyType()));
        }
//            component.setSupplySubinventory();
//            component.setSupplyLocatorId();
//            component.setBomItemType();
        if (StringUtils.isNotBlank(componentV.getBasisType())) {
            component.setBasisType(Integer.valueOf(componentV.getBasisType()));
        }
        componentsRepository.save(component);
        return component;
    }

    private boolean isSubstituteComponent(ComponentV componentV) {
        return StringUtils.isNotBlank(componentV.getSubstituteComponentId());
    }

    private Components findComponentsByComponentV(ComponentV componentV) {
        JPAQuery<Components> jpaQuery = jpaQueryFactory.selectFrom(qComponents)
                .where(qComponents.componentSequenceId.eq(Long.valueOf(componentV.getComponentSequenceId())));
        if (StringUtils.isBlank(componentV.getSubstituteComponentId())) {
            jpaQuery.where(qComponents.substituteComponentId.isNull());
        } else {
            jpaQuery.where(qComponents.substituteComponentId.eq(Long.valueOf(componentV.getSubstituteComponentId())));
        }
        return jpaQuery.fetchOne();
    }

    private Structures transStructure(ComponentV componentV) {
        Long billSequenceId = Long.parseLong(componentV.getBillSequenceId());
        Structures structure = jpaQueryFactory.selectFrom(qStructures)
                .where(qStructures.billSequenceId.eq(billSequenceId)).fetchOne();
        if (structure != null) {
            return structure;
        }
        saveStructureLock.lock();
        try {
            structure = new Structures();
            structure.setAssemblyItemId(Long.parseLong(componentV.getAssemblyItemId()));
            structure.setOrganizationId(Long.parseLong(componentV.getOrganizationId()));
            structure.setAlternateBomDesignator(componentV.getAlternateBomDesignator());
            structure.setLastUpdateDate(componentV.getLastUpdateDate());
            structure.setAttributeCategory(componentV.getBmAttributeCategory());
            structure.setAttribute1(componentV.getBmAttribute1());
            structure.setAttribute2(componentV.getBmAttribute2());
            structure.setAttribute3(componentV.getBmAttribute3());
            structure.setAttribute4(componentV.getBmAttribute4());
            structure.setAttribute5(componentV.getBmAttribute5());
            structure.setAttribute6(componentV.getBmAttribute6());
            structure.setAttribute7(componentV.getBmAttribute7());
            structure.setAttribute8(componentV.getBmAttribute8());
            structure.setAttribute9(componentV.getBmAttribute9());
            structure.setAttribute10(componentV.getBmAttribute10());
            structure.setAttribute11(componentV.getBmAttribute11());
            structure.setAttribute12(componentV.getBmAttribute12());
            structure.setAttribute13(componentV.getBmAttribute13());
            structure.setAttribute14(componentV.getBmAttribute14());
            structure.setAttribute15(componentV.getBmAttribute15());
            structure.setAssemblyType(Long.parseLong(componentV.getAssemblyType()));
            structure.setCommonBillSequenceId(billSequenceId);
            structure.setBillSequenceId(billSequenceId);
            saveStructureLock.lock();
            try {
                Structures comfirmedNon = jpaQueryFactory.selectFrom(qStructures)
                        .where(qStructures.billSequenceId.eq(billSequenceId)).fetchOne();
                if (comfirmedNon == null) {
                    structuresRepository.saveAndFlush(structure);
                } else {
                    structure = comfirmedNon;
                }
            } finally {
                saveStructureLock.unlock();
            }
        } finally {
            saveStructureLock.unlock();
        }
        return structure;
    }

    private void createComponentV(ComponentV componentV) {
        synchronized (this) {
            ComponentV findComponentV = findByComponent(componentV);
            if (findComponentV == null) {
                componentV.setIsProcess(0);
                repository.save(componentV);
            } else {
                // 同步最新修改
                BeanUtil.copyProperties(componentV, findComponentV, "id");
                findComponentV.setIsProcess(0);
                repository.save(findComponentV);
            }
        }
    }

    private ComponentV findByComponent(ComponentV componentV) {
        try {
            JPAQuery<ComponentV> jpaQuery = jpaQueryFactory.selectFrom(qComponentV)
                    .where(qComponentV.organizationId.eq(componentV.getOrganizationId()))
                    .where(qComponentV.assItemNum.eq(componentV.getAssItemNum()))
                    .where(qComponentV.componentSequenceId.eq(componentV.getComponentSequenceId()));
            if (StringUtils.isBlank(componentV.getSubstituteComponentId())) {
                jpaQuery.where(qComponentV.substituteComponentId.isNull());
            } else {
                jpaQuery.where(qComponentV.substituteComponentId.eq(componentV.getSubstituteComponentId()));
            }
            jpaQuery.orderBy(qComponentV.id.desc());
            return jpaQuery.fetchFirst();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private List<BillOfMaterialVO> filterItemNumber(LocalDateTime now, List<BillOfMaterialVO> componentVS) {
        componentVS = componentVS.stream().filter(item -> {
            return item.getItemNumber().startsWith("5A");
        }).collect(Collectors.toList());
        return componentVS;
    }

    private JSONObject invockBomListInterface(JSONObject param) {
        ExternalInterfaceDTO prams = createPrams("/ea006/v1/bom/list", param.toJSONString());
        String json = erpInterfaceService.postForString(prams);
        return JSON.parseObject(json);
    }

    private ExternalInterfaceDTO createPrams(String path, String requestBody) {
        ExternalInterfaceDTO dto = new ExternalInterfaceDTO();
        dto.setUrl(erpUrl);
        dto.setPath(path);
        dto.setClientId(eaClientId);
        dto.setSecret(eaClientSecret);
        dto.setRequestBody(requestBody);

        return dto;
    }

    @Override
    public void syncByInterfaceByPhaseII(LocalDateTime fromDateTime, Long[] organizations, int pageNum) {
        LocalDateTime now = LocalDateTime.now();
        organizations = loadOrganizations(organizations);
        for (int i = 0; i < organizations.length; i++) {
            ComponentV lastRecord = repository.findByLastRecordByOrganizationid(organizations[i]);
            LocalDateTime from = LocalDateTime.parse("2001-01-01T00:00:00");
            if (fromDateTime != null) {
                from = fromDateTime;
            } else if (lastRecord != null && lastRecord.getLastUpdateDate() != null) {
                from = lastRecord.getLastUpdateDate();
            }
            for (; ; ) {
                // 按月提交给线程池
                JSONObject param = new JSONObject();
                param.put("organization_id", Collections.singletonList(organizations[i]));
                param.put("pageSize", PAGE_SIZE);
                param.put("last_update_date_from", LocalDateTimeUtil.formatNormal(from));
                from = from.plusMonths(1);
                param.put("last_update_date_to", LocalDateTimeUtil.formatNormal(from));
                threadPoolExecutor.execute(() -> {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // 提交给线程池
                    int currentPage = pageNum;
                    for (; ; ) {
                        param.put("pageNum", currentPage++);
                        JSONObject outJson = invockBomListInterface(param);
                        JSONArray data = outJson.getJSONArray("data");
                        if (data == null) {
                            return;
                        }
                        List<BillOfMaterialVO> componentVS = data.toJavaList(BillOfMaterialVO.class);
                        componentVS = filterItemNumber(now, componentVS);
                        for (BillOfMaterialVO componentV : componentVS) {
                            //  跳过disable的数据
                            if (StringUtils.isNotBlank(componentV.getDisableDate())) {
                                LocalDateTime disableDate = LocalDateTime.parse(componentV.getDisableDate(), dateTimeFormatter);
                                if (now.isAfter(disableDate)) {
                                    continue;
                                }
                            }
                            ComponentV componentv = new ComponentV(componentV);
                            createComponentV(componentv);
                        }
                    }
                });
                if (from.isAfter(now)) {
                    break;
                }
            }

        }
    }
}
