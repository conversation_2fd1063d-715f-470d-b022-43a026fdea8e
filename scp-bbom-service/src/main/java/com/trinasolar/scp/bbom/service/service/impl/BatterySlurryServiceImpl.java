package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.BatterySlurryDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.*;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.entity.BatterySlurry;
import com.trinasolar.scp.bbom.domain.entity.Items;
import com.trinasolar.scp.bbom.domain.entity.QBatterySlurry;
import com.trinasolar.scp.bbom.domain.entity.QItems;
import com.trinasolar.scp.bbom.domain.excel.BatterySlurryExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.BatterySlurryExcelMailDTO;
import com.trinasolar.scp.bbom.domain.excel.ScreenLifeExcelDTO;
import com.trinasolar.scp.bbom.domain.query.BatterySlurryQuery;
import com.trinasolar.scp.bbom.domain.query.ModuleBasePlaceQuery;
import com.trinasolar.scp.bbom.domain.save.BatterySlurrySaveDTO;
import com.trinasolar.scp.bbom.domain.utils.FileUtil;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.ApsFeign;
import com.trinasolar.scp.bbom.service.feign.BmrpFeign;
import com.trinasolar.scp.bbom.service.repository.BatterySlurryRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.service.impl.scheduleEmail.SCPFileService;
import com.trinasolar.scp.bbom.service.util.LocalDateTimeUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-浆料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Slf4j
@Service("batterySlurryService")
@RequiredArgsConstructor
public class BatterySlurryServiceImpl implements BatterySlurryService {

    private static final QBatterySlurry qBatterySlurry = QBatterySlurry.batterySlurry;

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final BatterySlurryDEConvert convert;

    private final BatterySlurryRepository repository;

    private final BatteryTypeMainService batteryTypeMainService;

    private final BatteryFeignService batteryFeignService;

    private final ItemsService itemsService;

    private final ApsFeign apsFeign;

    private final MailService mailService;

    private final SlurryInformationService informationService;

    private final BmrpFeign bmrpFeign;

    private final BomService bomService;

    private final JPAQueryFactory jpaQueryFactory;

    private final StructuresService structuresService;

    private final ComponentsService componentsService;

    private final BMrpService bMrpService;

    private static final QItems qItems = QItems.items;

    @Autowired
    @Lazy
    private BatterySlurryService batterySlurryService;

    @Autowired
    private SCPFileService scpFileService;

    private static void queryConvert(BatterySlurryDTO excelDTO) {
        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        excelDTO.setWorkunitName(excelDTO.getWorkunit());
        if (StringUtils.isNumeric(excelDTO.getSwitchingType())) {
            LovLineDTO lovLineDTO = LovUtils.get("SWITCHING_TYPES", excelDTO.getSwitchingType());
            if (lovLineDTO != null) {
                excelDTO.setSwitchingTypeName(lovLineDTO.getLovName());
            }
        }
        if (StringUtils.isNumeric(excelDTO.getWorkBench())) {
            LovLineDTO lovLineDTO = LovUtils.get("CELL_MACHINE", Long.parseLong(excelDTO.getWorkBench()));
            if (lovLineDTO != null) {
                excelDTO.setWorkBenchName(lovLineDTO.getLovName());
            }
        }
        if (StringUtils.isNotEmpty(excelDTO.getLeadType())) {
            LovLineDTO lovLineDTO = LovUtils.get("LEAD_TYPE", excelDTO.getLeadType());
            if (lovLineDTO != null) {
                excelDTO.setLeadTypeName(lovLineDTO.getLovName());
            }
        }
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));

        LovLineDTO yesOrNoLov = LovUtils.get("yes_or_no", excelDTO.getSingleGlassFlag());
        if(Objects.nonNull(yesOrNoLov)){
            excelDTO.setSingleGlassFlagName(yesOrNoLov.getLovName());
        }

        Map<String, LovLineDTO> mainLov = LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
        LovLineDTO mainGridSpaceLov = mainLov.get(excelDTO.getMainGridSpace());
        if(Objects.nonNull(mainGridSpaceLov) && "main_grid_space".equals(mainGridSpaceLov.getAttribute1())){
            excelDTO.setMainGridSpace(mainGridSpaceLov.getLovValue());
        }
    }

    private void convertExcelToKey(BatterySlurrySaveDTO excelDTO) {
        StringBuffer sb = new StringBuffer();

        if(org.springframework.util.StringUtils.hasText(excelDTO.getMainGridSpace())){
            Map<String, LovLineDTO> mainLov = LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
            LovLineDTO mainGridSpaceLov = mainLov.get(excelDTO.getMainGridSpace());
            if(Objects.nonNull(mainGridSpaceLov)  && "main_grid_space".equals(mainGridSpaceLov.getAttribute1())){
                excelDTO.setMainGridSpace(mainGridSpaceLov.getLovName());
            }else {
                sb.append("请填写正确的主栅间距!");
            }
        }

        if(org.springframework.util.StringUtils.hasText(excelDTO.getSingleGlassFlagName())){
            Map<String, LovLineDTO> yesOrNoLov = LovUtils.getAllByHeaderCode("yes_or_no");
            if (Objects.nonNull(yesOrNoLov)) {
                LovLineDTO singleGlassFlagDtoLov = yesOrNoLov.get(excelDTO.getSingleGlassFlagName());
                if (Objects.nonNull(singleGlassFlagDtoLov)) {
                    excelDTO.setSingleGlassFlag(singleGlassFlagDtoLov.getLovValue());
                } else {
                    sb.append("请填写正确的单玻!");
                }
            }
        }


        if(org.springframework.util.StringUtils.hasText(excelDTO.getWorkBenchName())){
            Map<String, LovLineDTO> workbenchLov = LovUtils.getAllByHeaderCode("CELL_MACHINE");
            if (Objects.nonNull(workbenchLov) ) {
                LovLineDTO workbenchDtoLov = workbenchLov.get(excelDTO.getWorkBenchName());
                if (Objects.nonNull(workbenchDtoLov)) {
                    excelDTO.setWorkBench(String.valueOf(workbenchDtoLov.getLovLineId()));
                } else {
                    sb.append("请填写正确的机台!");
                }
            }
        }

        if (org.springframework.util.StringUtils.hasText(sb.toString())) {
            throw new BizException(sb.toString());
        }
    }

    private static void extracted(BatterySlurrySaveDTO dto, BooleanBuilder booleanBuilder) {
        if (null != dto.getId()) {
            booleanBuilder.and(qBatterySlurry.id.ne(dto.getId()));
        }
        // 唯一性校验改为:电池类型+生产基地+生产车间+物料料号-旧 唯一性校验 有效日期_起+有效日期_止 -> x
        // 20250325 update 电池类型+生产基地+生产车间+物料编码-新+机台+有效日期_起+有效日期 唯一性校验
        //电池类型
        if (StringUtils.isNotEmpty(dto.getBatteryCode())) {
            booleanBuilder.and(qBatterySlurry.batteryCode.eq(dto.getBatteryCode()));
        } else {
            booleanBuilder.and(qBatterySlurry.batteryCode.isNull().or(qBatterySlurry.batteryCode.isEmpty()));
        }
        //生产基地
        if (StringUtils.isNotEmpty(dto.getBasePlace())) {
            booleanBuilder.and(qBatterySlurry.basePlace.eq(dto.getBasePlace()));
        }
        //生产车间
        if (StringUtils.isNotEmpty(dto.getWorkshop())) {
            booleanBuilder.and(qBatterySlurry.workshop.eq(dto.getWorkshop()));
        }
//        //物料编码-旧
//        if (StringUtils.isNotEmpty(dto.getItemCodeOld())) {
//            booleanBuilder.and(qBatterySlurry.itemCodeOld.eq(dto.getItemCodeOld()));
//        } else {
//            booleanBuilder.and(qBatterySlurry.itemCodeOld.isNull().or(qBatterySlurry.itemCodeOld.isEmpty()));
//        }
        //物料编码-新
        if (StringUtils.isNotEmpty(dto.getItemCodeNew())) {
            booleanBuilder.and(qBatterySlurry.itemCodeNew.eq(dto.getItemCodeNew()));
        }
        // 机台
        if (StringUtils.isNotEmpty(dto.getWorkBench())) {
            booleanBuilder.and(qBatterySlurry.workBench.eq(dto.getWorkBench()));
        }
        if (dto.getEffectiveStartDate() != null && dto.getEffectiveEndDate() != null) {
            // 校验是否有日期重叠的数据
            booleanBuilder.and(qBatterySlurry.effectiveStartDate.loe(dto.getEffectiveEndDate()));
            booleanBuilder.and(qBatterySlurry.effectiveEndDate.goe(dto.getEffectiveStartDate()));
        } else if (dto.getEffectiveStartDate() == null && dto.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatterySlurry.effectiveStartDate.loe(dto.getEffectiveEndDate()));
        } else if (dto.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatterySlurry.effectiveEndDate.goe(dto.getEffectiveStartDate()));
        }
    }

    public static boolean isValidDateFormat(String dateStr, String dateFormat) {
        if (ObjectUtil.isEmpty(dateStr)) {
            return false;
        }
        try {
            DateUtil.parse(dateStr, dateFormat); // 将字符串解析为日期对象，如果解析成功，则说明字符串是有效的日期格式；否则说明字符串不是有效的日期格式。
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private static void verifySave(BatterySlurrySaveDTO saveDTO) {
        // 新增修改校验
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }
        // 维护类型是研发时允许电池类型不填写
        if (StringUtils.isBlank(saveDTO.getBatteryName()) && !LovHeaderCodeConstant.YF.equals(saveDTO.getLeadTypeName())) {
            throw new BizException("bbom_valid_batteryName_notBlank");
        }
        if (!LovHeaderCodeConstant.YF.equals(saveDTO.getLeadTypeName())) {
//            if (StringUtils.isBlank(saveDTO.getItemCodeOld())) {
//                throw new BizException("物料料号-旧不能为空");
//            }
//            if (StringUtils.isBlank(saveDTO.getLineOld())) {
//                throw new BizException("线体数量-旧不能为空");
//            }

        }
        //物料料号-新不能为空
        if (StringUtils.isBlank(saveDTO.getItemCodeNew())) {
            throw new BizException("bbom_valid_itemCodeNew_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getLineNew())) {
            throw new BizException("bbom_valid_line_new_not_blank");
        }
    }

    private static void verifyImport(BatterySlurryExcelDTO saveDTO) {
        // 导入校验
        if (StringUtils.isBlank(saveDTO.getLeadTypeName())) {
            throw new BizException("bbom_valid_switchType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlaceName())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshopName())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }

        if (StringUtils.isBlank(saveDTO.getItemCodeNew())) {
            throw new BizException("bbom_valid_itemCodeNew_notBlank");
        }

        if (StringUtils.isBlank(saveDTO.getLineNew())) {
            throw new BizException("bbom_valid_line_new_not_blank");
        }
        if (!LovHeaderCodeConstant.YF.equals(saveDTO.getLeadTypeName())) {
//            if (StringUtils.isBlank(saveDTO.getItemCodeOld())) {
//                throw new BizException("物料料号-旧不能为空");
//            }
//            if (StringUtils.isBlank(saveDTO.getLineOld())) {
//                throw new BizException("线体数量-旧不能为空");
//            }
        }
        if (StringUtils.isBlank(saveDTO.getBatteryName()) && !LovHeaderCodeConstant.YF.equals(saveDTO.getLeadTypeName())) {
            throw new BizException("bbom_valid_batteryName_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getVendorName()) && LovHeaderCodeConstant.YF.equals(saveDTO.getLeadTypeName())) {
            throw new BizException("bbom_valid_vendorName_notBlank");
        }
//        if (LovHeaderCodeConstant.YF.equals(saveDTO.getLeadTypeName()) && null == saveDTO.getQuantity()) {
//            throw new BizException("bbom_valid_YfAndQty_notBlank");
//        }

    }

    private static void extractedSave(BatterySlurrySaveDTO dto, BooleanBuilder booleanBuilder) {
        if (null != dto.getId()) {
            booleanBuilder.and(qBatterySlurry.id.eq(dto.getId()));
        }
    }

    private static void extractedImport(BatterySlurryExcelDTO dto, BooleanBuilder booleanBuilder) {
        //维护类型
        if (StringUtils.isNotEmpty(dto.getLeadType())) {
            booleanBuilder.and(qBatterySlurry.leadType.eq(dto.getLeadType()));
        }
        //电池类型
        if (StringUtils.isNotEmpty(dto.getBatteryCode())) {
            booleanBuilder.and(qBatterySlurry.batteryCode.eq(dto.getBatteryCode()));
        } else {
            booleanBuilder.and(qBatterySlurry.batteryCode.isNull().or(qBatterySlurry.batteryCode.isEmpty()));
        }
        //生产基地
        if (StringUtils.isNotEmpty(dto.getBasePlace())) {
            booleanBuilder.and(qBatterySlurry.basePlace.eq(dto.getBasePlace()));
        }
        //生产车间
        if (StringUtils.isNotEmpty(dto.getWorkshop())) {
            booleanBuilder.and(qBatterySlurry.workshop.eq(dto.getWorkshop()));
        }
        //物料编码-新
        if (StringUtils.isNotEmpty(dto.getItemCodeNew())) {
            booleanBuilder.and(qBatterySlurry.itemCodeNew.eq(dto.getItemCodeNew()));
        }
        //维护类型是研发时，维护类型+浆料料号-新+ 电池类型+生产基地+生产车间+供应商，供应商字段必填，
        if (LovHeaderCodeConstant.YF.equals(dto.getLeadTypeName())) {
            booleanBuilder.and(qBatterySlurry.vendorId.eq(dto.getVendorId()));
        } else {
//            //物料编码-旧
//            if (StringUtils.isNotEmpty(dto.getItemCodeOld())) {
//                booleanBuilder.and(qBatterySlurry.itemCodeOld.eq(dto.getItemCodeOld()));
//            } else {
//                booleanBuilder.and(qBatterySlurry.itemCodeOld.isNull().or(qBatterySlurry.itemCodeOld.isEmpty()));
//            }
        }
        LocalDate startDate = LocalDate.parse(dto.getEffectiveStartDates(), DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(dto.getEffectiveEndDates(), DATE_FORMATTER);
        booleanBuilder.and(qBatterySlurry.effectiveStartDate.eq(startDate.atStartOfDay()));
        booleanBuilder.and(qBatterySlurry.effectiveEndDate.eq(endDate.atStartOfDay()));
    }

    @Override
    public Page<BatterySlurryDTO> queryByPage(String userId, BatterySlurryQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.ASC, "batteryName","basePlace","workshop","effectiveStartDate","workBench","itemCodeNew");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //查询用户数据权限
        List<DataPrivilegeDTO> privilegeDTOList = informationService.getDataPrivilegeDTOS(userId, LovHeaderCodeConstant.LEAD_TYPE);
        if (CollectionUtils.isNotEmpty(privilegeDTOList)) {
            List<Long> ids = privilegeDTOList.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
            if (StringUtils.isEmpty(query.getLeadType())) {
                if (!ids.contains(-1L)) {
                    booleanBuilder.and(qBatterySlurry.leadTypeId.in(ids));
                }
            }
        } else {
            booleanBuilder.and(qBatterySlurry.leadTypeId.in(-1L));
        }
        Page<BatterySlurry> page = repository.findAll(booleanBuilder, pageable);
        // lov id->name 转码
        List<BatterySlurryDTO> batterySlurryDTOList = convert.toDto(page.getContent());
        for (BatterySlurryDTO bat : batterySlurryDTOList) {
            queryConvert(bat);
            judgeColorByLifecycleState(bat);
            dateConvert(bat);
        }
        return new PageImpl(batterySlurryDTOList, page.getPageable(), page.getTotalElements());
    }

    private void dateConvert(BatterySlurryDTO bat) {
        if (bat.getEffectiveStartDate() != null) {
            bat.setEffectiveStartDates(bat.getEffectiveStartDate().format(DATE_FORMATTER));
        }
        if (bat.getEffectiveEndDate() != null) {
            bat.setEffectiveEndDates(bat.getEffectiveEndDate().format(DATE_FORMATTER));
        }
    }

    /**
     * 判断颜色根据LifecycleState
     *
     * @param bat
     */
    private void judgeColorByLifecycleState(BatterySlurryDTO bat) {
        //获取库存组织
        LovLineDTO workShop = LovUtils.get("work_shop", bat.getWorkshop());
        //根据库存组织查询
        LovLineDTO organization = LovUtils.get("inventory_organization", workShop.getAttribute10());
        //网版料号新
        if (StringUtils.isNotBlank(bat.getItemCodeNew())) {
            ItemsDTO newItem = itemsService.findOneByItemCodeAndOrganizationId(bat.getItemCodeNew(), Long.parseLong(organization.getAttribute1()));
            if (newItem != null) {
                //如果状态为中大样的用前端淡橙色标注
                bat.setLifecycleStateNew(newItem.getLifecycleState());
            }
        }
        if (StringUtils.isNotBlank(bat.getItemCodeOld())) {
            ItemsDTO oldItem = itemsService.findOneByItemCodeAndOrganizationId(bat.getItemCodeOld(), Long.parseLong(organization.getAttribute1()));
            if (oldItem != null) {
                //如果状态为中大样的用前端淡橙色标注
                bat.setLifecycleStateNew(oldItem.getLifecycleState());
            }
        }
    }

    @Override
    public List<BatterySlurryDTO> queryByAll(BatterySlurryQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.LEAD_TYPE, LovHeaderCodeConstant.LC);
        booleanBuilder.and(qBatterySlurry.leadTypeId.eq(lovLineDTO.getLovLineId()));
        Iterable<BatterySlurry> slurryIterable = repository.findAll(booleanBuilder);
        return convert.toDto(IterableUtils.toList(slurryIterable));
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatterySlurryQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatterySlurry.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatterySlurry.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatterySlurry.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatterySlurry.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatterySlurry.workshop.eq(query.getWorkshop()));
        }
        if (CollectionUtils.isNotEmpty(query.getWorkshopList())) {
            booleanBuilder.and(qBatterySlurry.workshop.in(query.getWorkshopList()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qBatterySlurry.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeNew())) {
            booleanBuilder.and(qBatterySlurry.itemCodeNew.like("%" + query.getItemCodeNew() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getItemDescNew())) {
            booleanBuilder.and(qBatterySlurry.itemDescNew.eq(query.getItemDescNew()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeOld())) {
            booleanBuilder.and(qBatterySlurry.itemCodeOld.eq(query.getItemCodeOld()));
        }
        if (StringUtils.isNotEmpty(query.getItemDescOld())) {
            booleanBuilder.and(qBatterySlurry.itemDescOld.eq(query.getItemDescOld()));
        }
        if (StringUtils.isNotEmpty(query.getLineNew())) {
            booleanBuilder.and(qBatterySlurry.lineNew.eq(query.getLineNew()));
        }
        if (StringUtils.isNotEmpty(query.getLineOld())) {
            booleanBuilder.and(qBatterySlurry.lineOld.eq(query.getLineOld()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatterySlurry.effectiveStartDate.loe(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatterySlurry.effectiveEndDate.goe(query.getEffectiveEndDate()));
        }
    }

    @Override
    public BatterySlurryDTO queryById(Long id) {
        BatterySlurry queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public BatterySlurryDTO save(BatterySlurrySaveDTO saveDTO) {
        verifySave(saveDTO);
        // 查询 区分新增 修改结果集合
        BatterySlurry newObj = new BatterySlurry();
        //CELL +该基地+车间+单元下的数据 获取线体数量 AS A 和填写的(线体旧+线体新) AS B 比对数量 B<=A
        checkLine(saveDTO);
        checkRepeat(saveDTO);
        if (null != saveDTO.getId()) {
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            extractedSave(saveDTO, booleanBuilder);
            newObj = repository.findOne(booleanBuilder).orElse(new BatterySlurry());
        }
        saveDTO.setLeadTypeId(LovUtils.get(LovHeaderCodeConstant.LEAD_TYPE, saveDTO.getLeadType()).getLovLineId());
        convert.saveDTOtoEntity(saveDTO, newObj);
        //10.14新增字段是否bom中
        BomJudgment(newObj);
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    private void checkLine(BatterySlurrySaveDTO saveDTO) {
        //CELL +该基地+车间+单元下的数据 获取线体数量 AS A 和填写的(线体旧+线体新) AS B 比对数量 B<=A
        ModuleBasePlaceQuery placeDTO = new ModuleBasePlaceQuery();
        placeDTO.setProductType("CELL");
        placeDTO.setBasePlace(saveDTO.getBasePlace());
        placeDTO.setWorkshop(saveDTO.getWorkshop());
        placeDTO.setWorkunit(saveDTO.getWorkunit());
        ModuleBasePlaceDTO moduleBasePlaceDTO = apsFeign.findByBasePlaceAndWorkshopAndWorkunit(placeDTO).getBody().getData();
        //线体旧和线体新都不为空校验数量大小
        if (StringUtils.isNotEmpty(saveDTO.getLineNew())) {
            BigDecimal lineNew = new BigDecimal(saveDTO.getLineNew());
//            BigDecimal lineOld = new BigDecimal(saveDTO.getLineOld());
//            BigDecimal lineSum = lineNew.add(lineOld);
            Optional.ofNullable(moduleBasePlaceDTO).ifPresent(x -> {
                if (new BigDecimal(x.getTotalLine()).compareTo(lineNew) < 0) {
                    throw new BizException("bbom_valid_totalLineQty_illegal");
                }
            });
        }
    }

    //电池类型+生产基地+生产车间+物料编码-新+机台+有效日期_起+有效日期
    public void checkRepeat(BatterySlurrySaveDTO saveDTO) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        extracted(saveDTO, booleanBuilder);
        Iterable<BatterySlurry> newObj = repository.findAll(booleanBuilder);
        List<BatterySlurry> list = IterableUtils.toList(newObj);
        if (CollectionUtils.isNotEmpty(list)) {
            StringBuffer sb = new StringBuffer();
            sb.append(saveDTO.getBatteryName()).append("/")
                    .append(saveDTO.getBasePlaceName()).append("/")
                    .append(saveDTO.getWorkshop()).append("/");
            sb.append(saveDTO.getItemCodeNew()).append("/");
            sb.append(saveDTO.getWorkBench()).append("/");
            list.stream().map(i -> Optional.ofNullable(i.getEffectiveStartDate()).map(LocalDateTime::toLocalDate).map(LocalDate::toString).orElse("")
                            + " - " + Optional.ofNullable(i.getEffectiveEndDate()).map(LocalDateTime::toLocalDate).map(LocalDate::toString).orElse(""))
                    .forEach(i -> sb.append("(").append(i).append(")"));
            throw new BizException("bbom_valid_BatterySlurry_unique");

        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void importsEntity(MultipartFile file) {
        List<BatterySlurryExcelDTO> excelDto = new LinkedList<>();
        EasyExcel.read(file.getInputStream(), BatterySlurryExcelDTO.class, new ReadListener<BatterySlurryExcelDTO>() {
            @Override
            public void invoke(BatterySlurryExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        importDataSave(excelDto);
        log.info("电池类型动态属性-浆料 数据导入成功");

    }

    private void importDataSave(List<BatterySlurryExcelDTO> excelDto) {
        //获取供应商 供应商数据
        ApprovedVendorQuery query = new ApprovedVendorQuery();
        List<ApprovedVendorDTO> vendorList = bmrpFeign.vendorList(query).getBody().getData();
        Map<String, ApprovedVendorDTO> vendorMap = vendorList.stream().collect(Collectors.toMap(ApprovedVendorDTO::getVendorName, Function.identity(), (k1, k2) -> k1));
        // 保存或更新数据 查询数据库是否存在数据（存在既更新）
        // 查询切换类型
        // 校验合法性和赋值
        excelDto.forEach(p -> checkImport(p, vendorMap));
        // 校验同纬度下是否有日期重复的数据
        checkRepeatImport(excelDto);

        List<BatterySlurry> saves = new LinkedList<>();
        for (BatterySlurryExcelDTO excelDTO : excelDto) {
            if(StringUtils.isNotEmpty(excelDTO.getItemCodeNew()) && excelDTO.getItemCodeNew().contains("，")){
                excelDTO.setItemCodeNew(excelDTO.getItemCodeNew().replace("，",","));
            }
            // 查询 区分新增 修改结果集合
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            extractedImport(excelDTO, booleanBuilder);
            BatterySlurry newObj = repository.findOne(booleanBuilder).orElse(new BatterySlurry());

            if (newObj.getId() != null) {
                excelDTO.setId(newObj.getId());
            }
            BatterySlurrySaveDTO saveDTO = convert.toSaveDTO(excelDTO);
            saveDTO.setEffectiveStartDate(LocalDateTime.parse(excelDTO.getEffectiveStartDates() + " 00:00:00", DATE_TIME_FORMATTER));
            saveDTO.setEffectiveEndDate(LocalDateTime.parse(excelDTO.getEffectiveEndDates() + " 00:00:00", DATE_TIME_FORMATTER));

            LovLineDTO lovLineDTO = LovUtils.getByName("LEAD_TYPE", excelDTO.getLeadTypeName());
            if (lovLineDTO != null) {
                saveDTO.setLeadType(lovLineDTO.getLovValue());
            }
            this.convertExcelToKey(saveDTO);
            ItemsDTO itemsDTONew = itemsService.findOneByItemCode(saveDTO.getItemCodeNew());
            saveDTO.setItemDescNew(itemsDTONew.getItemDesc());
//            if (!LovHeaderCodeConstant.YF.equals(excelDTO.getLeadTypeName()) ) {
//                ItemsDTO itemsDTOOld = itemsService.findOneByItemCode(saveDTO.getItemCodeOld());
//                saveDTO.setItemDescOld(itemsDTOOld.getItemDesc());
//            }
            //CELL +该基地+车间+单元下的数据 获取线体数量 AS A 和填写的(线体旧+线体新) AS B 比对数量 B<=A
            checkLine(saveDTO);
            checkRepeat(saveDTO);
            saveDTO.setLeadTypeId(LovUtils.get(LovHeaderCodeConstant.LEAD_TYPE, saveDTO.getLeadType()).getLovLineId());
            convert.saveDTOtoEntity(saveDTO, newObj);
            BomJudgment(newObj);
            saves.add(newObj);
        }
        batterySlurryService.transcationSaves(saves);
    }

    private void checkRepeatImport(List<BatterySlurryExcelDTO> excelDto) {
        // 分组  电池类型+生产基地+生产车间+物料编码-新+机台+有效日期_起+有效日期止唯一性
        Map<String, List<BatterySlurryExcelDTO>> collect = excelDto.stream().collect(Collectors.groupingBy(
                i -> StringUtils.join(i.getBatteryName(),
                        "_", i.getBasePlace(), "_", i.getWorkshop(),
                        "_", i.getItemCodeNew(), "_", i.getWorkBenchName())
        ));
        List<String> errors = new LinkedList<>();
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                // 校验是否有开始日期结束日期重叠的数据
                List<BatterySlurryExcelDTO> dataRows = v.stream().sorted(Comparator.comparing(BatterySlurryExcelDTO::getEffectiveStartDates)).collect(Collectors.toList());
                for (int i = 0; i < dataRows.size() - 1; i++) {
                    DateTime end = DateUtil.parse(dataRows.get(i).getEffectiveEndDates(), "yyyy-MM-dd");
                    DateTime nextStart = DateUtil.parse(dataRows.get(i + 1).getEffectiveStartDates(), "yyyy-MM-dd");

                    // If the end date of the current row is after the start date of the next row, there is an overlap
                    if (end.isAfter(nextStart)) {
                        errors.add(k + " 维度下 日期存在重叠");
                        break;
                    }
                }
            }
        });
        if (CollectionUtils.isNotEmpty(errors)) {
            throw new BizException(MessageHelper.getMessage("bbom_valid_importData_repeat").getDesc() + "：" + errors);
        }
    }

    @Override
    @SneakyThrows
    public void export(BatterySlurryQuery query, HttpServletResponse response, String userId) {
        List<BatterySlurryDTO> dtos = queryByPage(userId, query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<BatterySlurryExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "浆料切换维护导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "浆料切换维护").head(BatterySlurryExcelDTO.class).build();
            //是否bom中转换是否
            exportDTOS.stream().forEach(item->{
                if("Y".equals(item.getIsExistBom())){
                    item.setIsExistBom("是");
                }else if("N".equals(item.getIsExistBom())){
                    item.setIsExistBom("否");
                }
            });
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    @Override
    public void queryListBySendMail() {
        exportEmail();
    }

    public void exportEmail() {
        EmailDataResultDTO emailDataResultDTO = new EmailDataResultDTO();
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //减三个月
        booleanBuilder.and(qBatterySlurry.createdTime.after(LocalDateTimeUtil.getDateTimePlusMinusMonths(3)));
        Iterable<BatterySlurry> batterySlurries = repository.findAll(booleanBuilder);
        //申请成功后发送邮件
        List<BatterySlurryDTO> dtos = convert.toDto(IterableUtils.toList(batterySlurries));
        //数据转换方法
        dtos.stream().forEach(p -> {
            queryConvert(p);
        });
        List<BatterySlurryExcelMailDTO> exportDTOS = convert.toMailExcelDTO(dtos);

        //网版切换
        String content = "浆料切换内容";
        JSONArray jsonArray = getObjects(emailDataResultDTO, exportDTOS, content);
        sendEmail(jsonArray, emailDataResultDTO);

    }

    public JSONArray getObjects(EmailDataResultDTO emailDataResultDTO, List<BatterySlurryExcelMailDTO> exportDTOS, String content) {
        File file = buildExcelFile(exportDTOS);
        String fileUrl = this.fileUpload(file);
        JSONArray jsonArray = FileUtil.getObjects(emailDataResultDTO, content, file, fileUrl, "浆料切换");
        return jsonArray;
    }

    @SneakyThrows
    private String fileUpload(File file) {
        MultipartFile multipartFile = FileUtil.getMultipartFile(file);
        return scpFileService.upload(multipartFile, false);
    }

    private File buildExcelFile(List<BatterySlurryExcelMailDTO> exportDTOS) {
        //创建目录
        File file = FileUtil.createLocalFile("浆料切换");
        // 文件输出位置
        ExcelWriter writer = EasyExcelFactory.write(file)
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build();


        WriteSheet sheet1 = EasyExcelFactory.writerSheet(0, "浆料切换导出信息").head(BatterySlurryExcelMailDTO.class).build();
        // 写数据
        writer.write(exportDTOS, sheet1);
        writer.finish();
        return file;
    }

    /**
     * 发送邮件
     */
    private void sendEmail(JSONArray jsonArray, EmailDataResultDTO emailDataResultDTO) {
        //查询所有收件人
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.SEND_MAIL, LovHeaderCodeConstant.SLURRY_MAIL);
        //发送邮件
        List<String> emails = Arrays.asList(StringUtils.split(lovLineDTO.getAttribute1(), ","));
        try {
            mailService.send(new ArrayList<>(emails), "battery_screen_plate.ftl", "浆料切换邮件提醒", MapUtil.of("Data", emailDataResultDTO), jsonArray.toJSONString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void checkImport(BatterySlurryExcelDTO excelDTO, Map<String, ApprovedVendorDTO> vendorMap) {
        verifyImport(excelDTO);
        // 校验合法性 生产基地 生产车间 api接口
        List<ModuleBasePlaceDTO> moduleBasePlaceDTOList = batteryFeignService.
                findBasePlaceWorkshopWorkUnit(excelDTO.getBasePlaceName(), excelDTO.getWorkshopName(), excelDTO.getWorkunitName());
        if (CollectionUtils.isEmpty(moduleBasePlaceDTOList)) {
            throw new BizException("bbom_valid_basePlaceWorkshop_illegal", excelDTO.getBasePlaceName(), excelDTO.getWorkshopName());
        }
        // 获取基地
        excelDTO.setBasePlace(excelDTO.getBasePlaceName());
        // 获取车间
        excelDTO.setWorkshop(excelDTO.getWorkshopName());
        // 补齐电池类型编码  研发的时候不校验电池类型名称
        if (StringUtils.isNotEmpty(excelDTO.getBatteryName())) {
            BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryName(excelDTO.getBatteryName());
            Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
                excelDTO.setBatteryCode(w.getBatteryCode());
            });
            if (StringUtils.isBlank(excelDTO.getBatteryCode())) {
                throw new BizException("bbom_valid_batteryCode_illegal", excelDTO.getBatteryCode());
            }
        }
        //供应商不为空
        if (StringUtils.isNotEmpty(excelDTO.getVendorName())) {
            if (null == vendorMap.get(excelDTO.getVendorName())) {
                throw new BizException("bbom_valid_vendorName_nonExist", excelDTO.getVendorName());
            } else {
                excelDTO.setVendorId(vendorMap.get(excelDTO.getVendorName()).getVendorId());
            }
        }
        boolean isEffectiveStartDateVaild = isValidDateFormat(excelDTO.getEffectiveStartDates(), "yyyy-MM-dd");
        if (!isEffectiveStartDateVaild) {
            throw new BizException("bbom_valid_effectiveStartDate_format_illegal", excelDTO.getEffectiveStartDates());
        }
        boolean isEffectiveEndDateVaild = isValidDateFormat(excelDTO.getEffectiveEndDates(), "yyyy-MM-dd");
        if (!isEffectiveEndDateVaild) {
            throw new BizException("bbom_valid_effectiveEndDate_format_illegal", excelDTO.getEffectiveEndDates());
        }
    }

    /**
     * 浆料切换
     * 入参：生产基地
     * 默认参数：维护类型为研发
     * 接口返回：生产基地、料号新、料号旧、数量、开始日期、供应商
     */
    public List<BatterySlurryDTO> queryBatterySlurryByBasePlace(BatterySlurryQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        //新产品导入
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.LEAD_TYPE, LovHeaderCodeConstant.YF);
        booleanBuilder.and(qBatterySlurry.leadTypeId.eq(lovLineDTO.getLovLineId()));
        Iterable<BatterySlurry> batterySlurries = repository.findAll(booleanBuilder);
        return convert.toDto(IterableUtils.toList(batterySlurries));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transcationSaves(List<BatterySlurry> saves) {
        repository.saveAll(saves);
    }

    //根据车间、单元、基地查询
    public void BomJudgment(BatterySlurry slurryEntity) {
        slurryEntity.setIsExistBom("N");
        //获取bom替代项、库存组织根据车间
        ErpAlternateDesignatorDTO designatorDTO = bomService.getErpAlternateDesignatorDTOByWorkshop(slurryEntity.getWorkshop());

        if (null != designatorDTO && designatorDTO.getId() != null) {
            //根据电池编号、库存组织id查询  使用物料getSourceItemId的默认使用过库存组织82L的
            //网版料号-新查询id 7A 唯一
            Items itemsDto = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.itemCode.eq(this.getItemCodeByStr(slurryEntity.getItemCodeNew())).and(qItems.organizationId.eq(82L)).and(qItems.isDeleted.eq(0))).fetchFirst();
            slurryEntity.setItemDescNew(itemsDto.getItemDesc());
            List<Long> structuresIdList = structuresService.findByAlternateDesignatorWithoutSourceItemId(designatorDTO.getOrganizationId().toString(), designatorDTO.getAlternateDesignatorCode());
            if (null != structuresIdList && null != itemsDto.getSourceItemId()) {
                Long componentsDTOCount = componentsService.findByStructureIdAndValidity(structuresIdList, itemsDto.getSourceItemId());
                if (ObjectUtil.isNotEmpty(componentsDTOCount)) {
                    slurryEntity.setIsExistBom("Y"); 
                }
            }
        }
    }
    private String getItemCodeByStr(String itemCodeNew){
        String[] itemCodeArray = itemCodeNew.split(",");
        return itemCodeArray[0];
    }

}
