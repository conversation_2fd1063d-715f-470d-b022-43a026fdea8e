package com.trinasolar.scp.bbom.service.feign;

import com.trinasolar.scp.bbom.domain.dto.CellFineDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.baps.CellProductionPlanDTO;
import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.bbom.domain.query.CellFineQuery;
import com.trinasolar.scp.bbom.domain.query.feign.baps.CellProductionPlanQuery;
import com.trinasolar.scp.bbom.service.feign.BapsDto.Cell5AItemCodeListDto;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * @author: darke
 * @create: 2022年4月26日09:21:02
 */
@FeignClient(value = FeignConstant.SCP_BATTERY_APS_API, path = "/scp-battery-aps-api")
public interface BAPSFeign {
    @PostMapping("/cell-production-plan/listForMatchItem")
    @ApiOperation(value = "提供给料号匹配,查询投产计划", notes = "获得投产计划列表")
    ResponseEntity<Results<List<CellProductionPlanDTO>>> listForMatchItem(@RequestBody CellProductionPlanQuery query);

    @PostMapping("/cell-fine/queryList")
    @ApiOperation(value = "电池良率表列表", notes = "电池良率表列表")
    ResponseEntity<Results<List<CellFineDTO>>> queryList(@RequestBody CellFineQuery query);

    @PostMapping("/cell-plan-line/changeDataBy5AMatch")
    @ApiOperation(value = "bom进行5A料号匹配后进行回改投产计划数据", notes = "bom进行5A料号匹配后进行回改投产计划数据")
    ResponseEntity<Results<Object>> changeDataBy5AMatch(@RequestBody Cell5AItemCodeListDto dto);

    @PostMapping("/cell-instock-plan-total/queryTwoMonthAllItemCodes")
    @ApiOperation(value = "获取当前月及次月已排产的电池计划中涉及的电池料号", notes = "获取当前月及次月已排产的电池计划中涉及的电池料号")
    ResponseEntity<Results<List<String>>> queryTwoMonthAllItemCodes();
}
