package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReflectUtil;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.ItemsDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.BatteryProductImport;
import com.trinasolar.scp.bbom.domain.entity.QItems;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.bbom.service.repository.ItemsRepository;
import com.trinasolar.scp.bbom.service.service.BatteryProductImportService;
import com.trinasolar.scp.bbom.service.service.BatteryTypeJobService;
import com.trinasolar.scp.bbom.service.service.BatteryTypeMainService;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.bbom.service.util.AttrUtil;
import com.trinasolar.scp.bbom.service.util.ExpressUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.util.BeanUtils;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryTypeJobServiceImpl
 * @Description
 * @Date 2023/12/18 20:03
 **/
@Slf4j
@Service("batteryTypeJobService")
@RequiredArgsConstructor
public class BatteryTypeJobServiceImpl implements BatteryTypeJobService {

    // 已处理-成功
    public static final String Y = "Y";
    public static final String BATTR_TYPE_011 = "BATTR_TYPE_011";
    private static final QItems qItems = QItems.items;
    private final static String BATTERY_TYPE = "BATTERY_TYPE";
    private final BatteryTypeMainService batteryTypeMainService;
    private final BatteryProductImportService batteryProductImportService;
    private final SystemFeign systemFeign;
    private final ItemsRepository repository;
    private final ItemsDEConvert convert;
    private final ItemsService itemsService;
    private final AttrUtil attrUtil;

    @Override
    public void batteryTypeJobHandler() {
        log.info("定时任务batteryTypeJobHandler-电池类型静态属性保存lov开始----->");
        try {
            LovLineQuery lovLineQuery = new LovLineQuery();
            lovLineQuery.setCode(BATTERY_TYPE);
            Results<LovHeaderDTO> lovHeaderDTOBody = systemFeign.queryLovHeaderByCode(lovLineQuery).getBody();
            LovHeaderDTO lovHeaderDTO = lovHeaderDTOBody.getData();
            List<LovLineDTO> lineDTOS = lovHeaderDTO.getLineDTOS();
            List<String> lovNameList = lineDTOS.stream().filter(item -> StringUtil.isNotBlank(item.getLovName())).map(LovLineDTO::getLovName).collect(Collectors.toList());
            List<BatteryTypeMainDTO> batteryTypeMainDTOS = batteryTypeMainService.queryBatteryCodeTypeAll();
            // 电池类型属性组
            List<AttrTypeLineDTO> attrType011 = attrUtil.queryAttrTypeLinesByHeaderCode(BATTR_TYPE_011);
            //获取规则 转换
            Map<String, String> dpColumnsMap = new HashMap<>();
            attrType011.forEach(value -> {
                if (StringUtils.isNotBlank(value.getAttrRule())) {
                    LovLineDTO lovLineDTO = LovUtils.get(Long.valueOf(value.getAttrRule()));
                    if (lovLineDTO != null) {
                        dpColumnsMap.put(value.getSourceColumn(), lovLineDTO.getAttribute1());
                    }
                }
            });

            int colNo = 0;
            List<LovLineDTO> lovLineDTOS = new ArrayList<>();

            for (BatteryTypeMainDTO dto : batteryTypeMainDTOS) {
                //规则转换
                extracted(dpColumnsMap, dto);
                if (lovNameList.contains(dto.getBatteryName())) {
                    continue;
                }

                LovLineDTO lovLineDTO = new LovLineDTO();
                lovLineDTO.setLovHeaderId(lovHeaderDTO.getLovHeaderId());
                lovLineDTO.setEnableFlag(Y);
                lovLineDTO.setEffectiveStartDate(LocalDate.now());
                lovLineDTO.setEffectiveEndDate(null);

                colNo += 1;
                lovLineDTO.setColNo(colNo);
                lovLineDTO.setLovValue(dto.getBatteryCode());
                lovLineDTO.setLovName(dto.getBatteryName());
                //单双晶
                lovLineDTO.setAttribute1(dto.getCrystalType());
                //PN型
                lovLineDTO.setAttribute2(null != LovUtils.getByName("5A00100100101", dto.getPOrN()) ? LovUtils.getByName("5A00100100101", dto.getPOrN()).getLovLineId().toString() : "");
                //品类
                lovLineDTO.setAttribute3(null != LovUtils.getByName("5A00200200129", dto.getCategory()) ? LovUtils.getByName("5A00200200129", dto.getCategory()).getLovLineId().toString() : "");
                //单双面
                lovLineDTO.setAttribute4(null != LovUtils.getByName("5A00100100129", dto.getSingleDoubleFace()) ? LovUtils.getByName("5A00100100129", dto.getSingleDoubleFace()).getLovLineId().toString() : "");
                //主栅类别
                lovLineDTO.setAttribute5(null != LovUtils.getByName("5A00100100105", dto.getNumberMainGrids()) ? LovUtils.getByName("5A00100100105", dto.getNumberMainGrids()).getLovLineId().toString() : "");
                //分片方式
                lovLineDTO.setAttribute6(null != LovUtils.getByName("5A00100100131", dto.getShardingMode()) ? LovUtils.getByName("5A00100100131", dto.getShardingMode()).getLovLineId().toString() : "");
                lovLineDTOS.add(lovLineDTO);
            }
            if (CollectionUtils.isEmpty(lovLineDTOS)) {
                return;
            }
            Results<Object> body = systemFeign.saveLovLineList(lovLineDTOS).getBody();
            if (body == null || !body.isSuccess()) {
                throw new BizException("定时任务batteryTypeJobHandler-电池类型静态属性保存lov出错");
            }
            log.info("定时任务batteryTypeJobHandler-电池类型静态属性保存lov完成----->");

        } catch (Exception e) {
            log.warn("定时任务batteryTypeJobHandler-电池类型静态属性保存lov-发生错误-----", e);
        }
    }

    private void extracted(Map<String, String> dpColumnsMap, BatteryTypeMainDTO dto) {
        for (Map.Entry<String, String> entry : dpColumnsMap.entrySet()) {
            String colName = CharSequenceUtil.toCamelCase(entry.getKey());
            String transScript = entry.getValue();
            String fieldValue = (String) ReflectUtil.getFieldValue(dto, colName);
            if (StringUtils.isNotBlank(fieldValue)) {
                // 准备脚本
                Map<String, String> transParam = new HashMap<>();
                transParam.put("input", fieldValue);
                String transFieldValue = execTransScript(fieldValue, transScript, transParam);
                ReflectUtil.setFieldValue(dto, colName, transFieldValue);
                if (log.isDebugEnabled()) {
                    log.debug("applyTransRule colName:{} fieldValue:{} transFieldValue:{}", colName, fieldValue, transFieldValue);
                }
            }
        }
    }

    private String execTransScript(String columnValue, String transScript, Map<String, String> transParam) {
        if (StringUtils.isBlank(columnValue)) {
            return columnValue;
        }
        try {
            String transResult = ExpressUtils.execExpressForValue(transScript, transParam);
            columnValue = transResult;
        } catch (Exception e) {
            // 跳过错误
        }
        return columnValue;
    }

    @Override
    public void batteryJobHandler() {
        log.info("定时任务batteryJobHandler-电池类型静态属性-解析开始----->");
        try {
            List<BatteryTypeMainDTO> batteryTypeMainSaveList = new ArrayList<>();
            // 全量查询 电池产品导入表数据 BATTERY_CELL_CRYSTAL_TYPE
            Map<String, LovLineDTO> batteryCellCrystalType = LovUtils.getAllByHeaderCode("BATTERY_CELL_CRYSTAL_TYPE");
            Map<String, LovLineDTO> productClassification = LovUtils.getAllByHeaderCode("BATTERY_CELL_CRYSTAL_TYPE");

            List<BatteryProductImportDTO> batteryProductImportDTOS = batteryProductImportService.queryAll();
            for (BatteryProductImportDTO bat : batteryProductImportDTOS) {
                BatteryProductImport batteryProductImport = new BatteryProductImport();
                BeanUtils.copyProperties(bat, batteryProductImport);
                // 按照解析规则 进行解析并入库
                // 电池产品名称 拼接
                BatteryTypeMainDTO dto = new BatteryTypeMainDTO();
                StringBuffer batteryNameBuff = new StringBuffer();
                if (StringUtils.isBlank(bat.getBatteryCode())) {
                    extracted(batteryProductImport, "电池类型编码不能为空");
                    continue;
                }
                if (StringUtils.isBlank(bat.getBatteryCrystalType())) {
                    extracted(batteryProductImport, "电池片晶体类型不能为空");
                    continue;
                }
                if (StringUtils.isBlank(bat.getBatteryDimensionCode())) {
                    extracted(batteryProductImport, "电池片尺寸编码不能为空");
                    continue;
                }
                if (StringUtils.isBlank(bat.getProductType())) {
                    extracted(batteryProductImport, "产品类型不能为空");
                    continue;
                }
                if (StringUtils.isBlank(bat.getNumberMainGrids())) {
                    extracted(batteryProductImport, "主栅数不能为空");
                    continue;
                }
                if (StringUtils.isBlank(bat.getShardingNumber())) {
                    extracted(batteryProductImport, "分片数不能为空");
                    continue;
                }

                // 电池产品编码
//                String batteryCodeStr = StringUtils.substringBefore(bat.getBatteryCode(), ".");
                // 放值
//                dto.setBatteryCode(batteryCodeStr);
                dto.setBatteryCode(bat.getBatteryCode());

                // 电池片晶体类型 参考页面数据晶体类型为 "N"
                LovLineDTO lovLineDTO = batteryCellCrystalType.get(bat.getBatteryCrystalType());
                String batteryCrystalType1 = lovLineDTO.getAttribute1();
                String batteryCrystalType2 = lovLineDTO.getAttribute2();
                dto.setCrystalType(batteryCrystalType1);
                dto.setPOrN(batteryCrystalType2);
                batteryNameBuff.append(batteryCrystalType1).append("_").append(batteryCrystalType2);

                // 电池片尺寸编码 G122 品类 ENCODING_OF_BATTERY_CELL_SIZE
                Map<String, LovLineDTO> encodingOfBatteryCellSize = LovUtils.getAllByHeaderCode("ENCODING_OF_BATTERY_CELL_SIZE");
                LovLineDTO lovLineDTO1 = encodingOfBatteryCellSize.get(bat.getBatteryDimensionCode());
                String batteryDimensionCode1 = lovLineDTO1.getAttribute1();
                if (StringUtils.isNumeric(batteryDimensionCode1)) {
                    LovLineDTO lovLineDTO2 = LovUtils.get(Long.valueOf(batteryDimensionCode1));
                    String lovName = lovLineDTO2.getLovName();
                    dto.setCategory(lovName);
                    batteryNameBuff.append("_").append(lovName);
                }

                // 产品类型 PRODUCT_TYPE 参考页面数据产品类型为 "B" 单双面
                Map<String, LovLineDTO> productType = LovUtils.getAllByHeaderCode("PRODUCT_TYPE");
                LovLineDTO lovLineDTO2 = productType.get(bat.getProductType());
                String productType1 = lovLineDTO2.getAttribute1();
                dto.setSingleDoubleFace(productType1);
                batteryNameBuff.append("_").append(productType1);

                // 产品分类 productClassification
                LovLineDTO productClassificationLovLineDTO = productClassification.get(bat.getBatteryCrystalType());
                if (Objects.nonNull(productClassificationLovLineDTO) && StringUtils.isNotEmpty(productClassificationLovLineDTO.getAttribute3())) {
                    dto.setProductClassification(productClassificationLovLineDTO.getAttribute3());
                    batteryNameBuff.append("_").append(productClassificationLovLineDTO.getAttribute3());
                } else {
                    batteryNameBuff.append("_");
                }

                // 主栅数 MAIN_GRID
                Map<String, LovLineDTO> mainGrid = LovUtils.getAllByHeaderCode("MAIN_GRID");
                LovLineDTO lovLineDTO3 = mainGrid.get(bat.getNumberMainGrids());
                String numberMainGrids1 = lovLineDTO3.getAttribute1();
                dto.setNumberMainGrids(numberMainGrids1);
                batteryNameBuff.append("_").append(numberMainGrids1);

                // 分片数 CELL_SHARDING
                Map<String, LovLineDTO> sharding = LovUtils.getAllByHeaderCode("CELL_SHARDING");
                LovLineDTO lovLineDTO4 = sharding.get(bat.getShardingNumber());
                String shardingNumber1 = lovLineDTO4.getAttribute1();
                dto.setShardingMode(shardingNumber1);
                batteryNameBuff.append("_").append(shardingNumber1);
                // 晶体类型_P/N型_品类_单双面_主栅类别_分片方式拼接而成
                dto.setBatteryName(batteryNameBuff.toString());

                // 校验是否能生成正确的电池类型
                if (
                        StringUtils.isBlank(dto.getCrystalType())
                ) {
                    extracted(batteryProductImport, "晶体类型转换错误");
                    continue;
                }
                if (
                        StringUtils.isBlank(dto.getPOrN())
                ) {
                    extracted(batteryProductImport, "PN型转换错误");
                    continue;
                }
                if (
                        StringUtils.isBlank(dto.getCategory())
                ) {
                    extracted(batteryProductImport, "品类转换错误");
                    continue;
                }
                if (
                        StringUtils.isBlank(dto.getSingleDoubleFace())
                ) {
                    extracted(batteryProductImport, "单双面转换错误");
                    continue;
                }
                if (
                        StringUtils.isBlank(dto.getProductClassification())
                ) {
                    extracted(batteryProductImport, "产品分类转换错误");
                    continue;
                }
                if (
                        StringUtils.isBlank(dto.getNumberMainGrids())
                ) {
                    extracted(batteryProductImport, "主栅数转换错误");
                    continue;
                }
                if (
                        StringUtils.isBlank(dto.getShardingMode())
                ) {
                    extracted(batteryProductImport, "分片方式转换错误");
                    continue;
                }

                // 业务要求：数据库表中存在电池类型编码不做修改
                BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryCode(dto.getBatteryCode());
                if (batteryTypeMainDTO == null || StringUtils.isBlank(batteryTypeMainDTO.getBatteryCode())) {
                    batteryTypeMainSaveList.add(dto);
                }
                extracted(batteryProductImport, "成功");
            }
            // 批量保存
            batteryTypeMainService.batchSaveUpdate(batteryTypeMainSaveList);
            log.info("定时任务batteryJobHandler-电池类型静态属性-解析完成----->");
        } catch (Exception e) {
            log.warn("定时任务batteryJobHandler-电池类型静态属性-解析数据时发生错误-----", e);
            throw e;
        }

    }

    private void extracted(BatteryProductImport batteryProductImport, String message) {
        batteryProductImport.setWarningReason(message);
        batteryProductImportService.updateById(batteryProductImport);
    }

    @Override
    public void batteryUpdateItmesJobHandler() {
        log.info("定时任务batteryUpdateItmesJobHandler-电池类型静态属性value字段更新物料表sigment60-更新开始----->");
        try {
            //查询lov BATTERY_CELL_CRYSTAL_TYPE
            Map<String, LovLineDTO> batteryUpdateItmes = LovUtils.getAllByHeaderCode("BATTERY_TYPE");
            Map<String, LovLineDTO> lovNameMap = batteryUpdateItmes.values().stream().collect(Collectors.toMap(LovLineDTO::getLovName, Function.identity(), (v1, v2) -> v1));
            lovNameMap.forEach((key, value) -> {
                /**
                 * 查询sql条件select * from bbom_items BI where
                 * BI.category_segment3 LIKE '单晶%' AND BI.segment1='P型' and bi.segment3='210' and bi.segment10
                 * ='双面' and bi.segment12='12BB' and bi.segment11 = '二分片' and bi.item_status='Active'
                 * and is_deleted=0
                 */
                String[] keyStr = value.getLovName().split("_");
                //防止测试数据
                if (keyStr.length > 5) {
                    value.setAttribute1(keyStr[0]);
                    value.setAttribute2(keyStr[1]);
                    value.setAttribute3(keyStr[2]);
                    value.setAttribute4(keyStr[3]);
                    // p2_3264 OR20250624026-料号对应的电池产品生成逻辑-TOPCON2.0  （这里LOV解析规则有调整）
                    value.setAttribute5(keyStr[5]);
                    value.setAttribute6(keyStr[6]);
                    value.setAttribute7(keyStr[4]);
                    List<ItemsDTO> itemsDTOList = queryByMatching(value);
                    if (CollectionUtils.isNotEmpty(itemsDTOList)) {
                        itemsDTOList.forEach(x -> {
                            x.setSegment60(value.getLovValue());
                        });
                        //更新
                        itemsService.batchSaveUpdate(itemsDTOList);
                    }
                }
            });
            log.info("定时任务batteryUpdateItmesJobHandler-电池类型静态属性value字段更新物料表sigment60-更新完成----->");
        } catch (Exception e) {
            log.warn("定时任务batteryUpdateItmesJobHandler-电池类型静态属性value字段更新物料表sigment60-更新发生错误-----", e);
            throw e;
        }

    }

    public List<ItemsDTO> queryByMatching(LovLineDTO query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    private void buildWhere(BooleanBuilder booleanBuilder, LovLineDTO query) {
        if (StringUtils.isNotEmpty(query.getAttribute1())) {
            booleanBuilder.and(qItems.categorySegment3.like(query.getAttribute1() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getAttribute2())) {
            booleanBuilder.and(qItems.segment1.eq(query.getAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute3())) {
            booleanBuilder.and(qItems.segment3.eq(query.getAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute4())) {
            booleanBuilder.and(qItems.segment10.eq(query.getAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute5())) {
            booleanBuilder.and(qItems.segment12.eq(query.getAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute6())) {
            if (query.getAttribute6().contains("二半")) {
                booleanBuilder.and(qItems.segment11.like("二半%"));
            } else if (query.getAttribute6().contains("二分")) {
                booleanBuilder.and((qItems.segment11.like("二分%")));
            } else {
                booleanBuilder.and(qItems.segment11.like(query.getAttribute6() + "%"));
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute7())) {
            booleanBuilder.and(qItems.segment9.eq(query.getAttribute7()));
        }
        booleanBuilder.and(qItems.isDeleted.eq(0));
    }
}
