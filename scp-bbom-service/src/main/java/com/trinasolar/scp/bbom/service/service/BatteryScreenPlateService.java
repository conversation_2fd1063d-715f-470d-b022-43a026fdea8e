package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电池类型动态属性-网版 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
public interface BatteryScreenPlateService {
    /**
     * 分页获取电池类型动态属性-网版
     *
     * @param query 查询对象
     * @return 电池类型动态属性-网版分页对象
     */
    Page<BatteryScreenPlateDTO> queryByPage(BatteryScreenPlateQuery query);

    /**
     * 查询网版信息
     *
     * @param query
     * @return
     */
    List<BatteryScreenPlateDTO> queryByAll(BatteryScreenPlateQuery query);

    /**
     * 查询网版信息 近三个月时间
     *
     * @return
     */
    void queryListBySendMail();

    /**
     * 根据主键获取电池类型动态属性-网版详情
     *
     * @param id 主键
     * @return 电池类型动态属性-网版详情
     */
    BatteryScreenPlateDTO queryById(Long id);

    /**
     * 保存或更新电池类型动态属性-网版
     *
     * @param saveDTO 电池类型动态属性-网版保存对象
     * @return 电池类型动态属性-网版对象
     */
    BatteryScreenPlateDTO save(BatteryScreenPlateSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型动态属性-网版
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatteryScreenPlateQuery query, HttpServletResponse response);

    void batchUpdate(BatteryScreenPlateQuery query);

    /**
     * 网版切换
     * 入参：生产基地
     * 接口返回：生产基地、料号新、料号旧、数量、开始日期、供应商
     */
    List<BatteryScreenPlateDTO> queryBatteryScreenByBasePlace(BatteryScreenPlateQuery query);

    List<BatteryScreenPlateDTO> getByMatchItem(String batteryType, String basePlace, String workshop, String workunit, LocalDateTime verifyDate);

    List<BatteryScreenPlateDTO> queryAllByCache();
}

