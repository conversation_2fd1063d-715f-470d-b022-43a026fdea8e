package com.trinasolar.scp.bbom.service.monitor;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 料号匹配性能监控
 * 用于监控doSingleMatch方法的性能改进效果
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Aspect
@Component
public class MatchPerformanceMonitor {

    private final ConcurrentHashMap<String, AtomicLong> executionCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> totalExecutionTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> maxExecutionTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> minExecutionTimes = new ConcurrentHashMap<>();

    /**
     * 监控doSingleMatch方法性能
     */
    @Around("execution(* com.trinasolar.scp.bbom.service.service.impl.MaterielMatchHeaderServiceImpl.doSingleMatch(..))")
    public Object monitorOriginalMatch(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorExecution(joinPoint, "doSingleMatch_original");
    }

    /**
     * 监控优化后的匹配方法性能
     */
    @Around("execution(* com.trinasolar.scp.bbom.service.service.OptimizedMaterielMatchService.doOptimizedSingleMatch(..))")
    public Object monitorOptimizedMatch(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorExecution(joinPoint, "doSingleMatch_optimized");
    }

    /**
     * 监控缓存服务性能
     */
    @Around("execution(* com.trinasolar.scp.bbom.service.service.MaterielMatchCacheService.*(..))")
    public Object monitorCacheService(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = "cache_" + joinPoint.getSignature().getName();
        return monitorExecution(joinPoint, methodName);
    }

    /**
     * 通用性能监控方法
     */
    private Object monitorExecution(ProceedingJoinPoint joinPoint, String methodKey) throws Throwable {
        StopWatch stopWatch = new StopWatch(methodKey);
        stopWatch.start();
        
        Object[] args = joinPoint.getArgs();
        Long matchId = null;
        if (args.length > 0 && args[0] instanceof Long) {
            matchId = (Long) args[0];
        }
        
        log.info("开始执行方法: {}, matchId: {}", methodKey, matchId);
        
        try {
            Object result = joinPoint.proceed();
            stopWatch.stop();
            
            long executionTime = stopWatch.getTotalTimeMillis();
            recordPerformanceMetrics(methodKey, executionTime);
            
            log.info("方法执行完成: {}, matchId: {}, 耗时: {}ms", methodKey, matchId, executionTime);
            
            return result;
            
        } catch (Exception e) {
            stopWatch.stop();
            log.error("方法执行失败: {}, matchId: {}, 耗时: {}ms, 错误: {}", 
                     methodKey, matchId, stopWatch.getTotalTimeMillis(), e.getMessage());
            throw e;
        }
    }

    /**
     * 记录性能指标
     */
    private void recordPerformanceMetrics(String methodKey, long executionTime) {
        // 执行次数
        executionCounts.computeIfAbsent(methodKey, k -> new AtomicLong(0)).incrementAndGet();
        
        // 总执行时间
        totalExecutionTimes.computeIfAbsent(methodKey, k -> new AtomicLong(0)).addAndGet(executionTime);
        
        // 最大执行时间
        maxExecutionTimes.computeIfAbsent(methodKey, k -> new AtomicLong(0))
            .updateAndGet(current -> Math.max(current, executionTime));
        
        // 最小执行时间
        minExecutionTimes.computeIfAbsent(methodKey, k -> new AtomicLong(Long.MAX_VALUE))
            .updateAndGet(current -> Math.min(current, executionTime));
    }

    /**
     * 获取性能统计报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("\n=== 料号匹配性能统计报告 ===\n");
        
        executionCounts.forEach((methodKey, count) -> {
            long totalTime = totalExecutionTimes.getOrDefault(methodKey, new AtomicLong(0)).get();
            long maxTime = maxExecutionTimes.getOrDefault(methodKey, new AtomicLong(0)).get();
            long minTime = minExecutionTimes.getOrDefault(methodKey, new AtomicLong(0)).get();
            long avgTime = count.get() > 0 ? totalTime / count.get() : 0;
            
            report.append(String.format(
                "方法: %s\n" +
                "  执行次数: %d\n" +
                "  总耗时: %dms\n" +
                "  平均耗时: %dms\n" +
                "  最大耗时: %dms\n" +
                "  最小耗时: %dms\n" +
                "---\n",
                methodKey, count.get(), totalTime, avgTime, maxTime, 
                minTime == Long.MAX_VALUE ? 0 : minTime
            ));
        });
        
        return report.toString();
    }

    /**
     * 比较原始方法和优化方法的性能
     */
    public String getPerformanceComparison() {
        StringBuilder comparison = new StringBuilder();
        comparison.append("\n=== 性能对比报告 ===\n");
        
        AtomicLong originalCount = executionCounts.get("doSingleMatch_original");
        AtomicLong optimizedCount = executionCounts.get("doSingleMatch_optimized");
        
        if (originalCount != null && optimizedCount != null) {
            long originalTotal = totalExecutionTimes.get("doSingleMatch_original").get();
            long optimizedTotal = totalExecutionTimes.get("doSingleMatch_optimized").get();
            
            long originalAvg = originalTotal / originalCount.get();
            long optimizedAvg = optimizedTotal / optimizedCount.get();
            
            double improvement = ((double)(originalAvg - optimizedAvg) / originalAvg) * 100;
            
            comparison.append(String.format(
                "原始方法平均耗时: %dms\n" +
                "优化方法平均耗时: %dms\n" +
                "性能提升: %.2f%%\n",
                originalAvg, optimizedAvg, improvement
            ));
        } else {
            comparison.append("暂无足够数据进行对比\n");
        }
        
        return comparison.toString();
    }

    /**
     * 重置性能统计
     */
    public void resetStatistics() {
        executionCounts.clear();
        totalExecutionTimes.clear();
        maxExecutionTimes.clear();
        minExecutionTimes.clear();
        log.info("性能统计已重置");
    }

    /**
     * 检查性能是否异常
     */
    public boolean isPerformanceAbnormal(String methodKey, long threshold) {
        AtomicLong maxTime = maxExecutionTimes.get(methodKey);
        return maxTime != null && maxTime.get() > threshold;
    }

    /**
     * 获取方法平均执行时间
     */
    public long getAverageExecutionTime(String methodKey) {
        AtomicLong count = executionCounts.get(methodKey);
        AtomicLong totalTime = totalExecutionTimes.get(methodKey);
        
        if (count != null && totalTime != null && count.get() > 0) {
            return totalTime.get() / count.get();
        }
        
        return 0;
    }
}
