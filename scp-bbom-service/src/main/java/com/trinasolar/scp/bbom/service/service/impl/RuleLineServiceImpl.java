package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.text.CharSequenceUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.system.LovLineSaveDTO;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.enums.AttrOperatorEnum;
import com.trinasolar.scp.bbom.domain.enums.CodeEnum;
import com.trinasolar.scp.bbom.domain.enums.YNEnum;
import com.trinasolar.scp.bbom.domain.event.CollocationRulesEvent;
import com.trinasolar.scp.bbom.domain.query.RuleHeaderQuery;
import com.trinasolar.scp.bbom.domain.query.RuleLineQuery;
import com.trinasolar.scp.bbom.domain.save.RuleLineSaveDTO;
import com.trinasolar.scp.bbom.domain.save.RuleLinesSaveDTO;
import com.trinasolar.scp.bbom.service.repository.RuleLineRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.util.AttrUtil;
import com.trinasolar.scp.bbom.service.util.CodeUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BOM规则行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 09:15:32
 */
@Slf4j
@Service("ruleLineService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "scpRedisCacheManager")
public class RuleLineServiceImpl implements RuleLineService {
    private final static QRuleLine qRuleLine = QRuleLine.ruleLine;

    private final RuleLineRepository repository;

    private final RuleDpDetailService ruleDpDetailService;

    private final RuleControlObjectHeaderService ruleControlObjectHeaderService;

    QRuleControlObjectHeader qRuleControlObjectHeader = QRuleControlObjectHeader.ruleControlObjectHeader;

    QRuleHeader qRuleHeader = QRuleHeader.ruleHeader;

    LRUCache<String, List<RuleLine>> ruleLineCache = CacheUtil.newLRUCache(512, 5 * 60 * 1000);

    LRUCache<Long, RuleLineDTO> queryIdCache = CacheUtil.newLRUCache(6000, 5 * 60 * 1000);

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private AttrUtil attrUtil;

    @Autowired
    @Lazy
    private RuleHeaderService ruleHeaderService;

    @Autowired
    @Lazy
    private RuleLineService ruleLineService;

    @Autowired
    SystemService systemService;

    @Override
    public Page<RuleLine> queryByPage(RuleLineQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public RuleLineDTO queryById(Long id) {
        RuleLineDTO result = queryIdCache.get(id);
        if (result == null) {
            RuleLine queryObj = repository.findById(id).orElse(null);
            if (queryObj == null) {
                return null;
            }

            result = new RuleLineDTO();
            BeanUtils.copyProperties(queryObj, result);
            setDtoValues(result);
            queryIdCache.put(id, result);
        }

        return result;
    }

    @Override
    public RuleLineDTO save(RuleLineSaveDTO saveDTO) {
        // 获取header
        RuleHeaderDTO ruleHeaderDTO = ruleHeaderService.queryById(saveDTO.getRuleHeaderId());

        RuleLine newObj;
        if (saveDTO.getRuleLineId() != null) {
            newObj = repository.findById(saveDTO.getRuleLineId()).orElse(new RuleLine());
        } else {
            newObj = new RuleLine();
            newObj.setCode(CodeUtil.getCode(CodeEnum.RULE_LINE));
            newObj.setNo(0);
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        // 保存详细信息
        ruleDpDetailService.saveDetails(newObj.getRuleLineId(), saveDTO.getDetails());

        // 保存排产材料对象因子
//        ruleMtlDetailService.saveDetails(newObj.getRuleLineId(), saveDTO.getMtlDetails());

        ruleControlObjectHeaderService.saveHeaders(newObj.getRuleLineId(), saveDTO.getControlObjectHeaders());

        // 生成序号
        genRuleLineNo();

        // 更新材料限制lov
        updateMaterialLimitationLov(ruleHeaderDTO);
        return this.queryById(newObj.getRuleLineId());
    }

    private void updateMaterialLimitationLov(RuleHeaderDTO ruleHeaderDTO) {
        // 校验是否为材料限制的header
        LovLineDTO ruleCategory = LovUtils.getById(ruleHeaderDTO.getRuleCategoryId());
        if (!"material".equals(ruleCategory.getLovValue())) {
            return;
        }
        // 获取此header下的所有规则行,然后对比lov中的信息如果有更新则更新lov
        List<RuleLine> ruleLines = listByRuleHeaderId(ruleHeaderDTO.getRuleHeaderId());
        Map<String, RuleLine> codeAndRuleLineMap = ruleLines.stream().collect(Collectors.toMap(RuleLine::getCode, Function.identity()));
        // 获取lov
        LovHeaderDTO lovHeaderDTO = systemService.getLovHeaderByCode("CELL_PRODUCTION_MATERIAL");
        Map<String, LovLineDTO> valueAndLovLineMap = LovUtils.getAllByHeaderCode("CELL_PRODUCTION_MATERIAL").values()
                .stream().collect(Collectors.toMap(LovLineDTO::getLovValue, Function.identity(), (a, b) -> a));
        // 如果不存在于lov中则增加
        HashSet<String> curCodes = new HashSet<>(codeAndRuleLineMap.keySet());
        HashSet<String> lovCodes = new HashSet<>(valueAndLovLineMap.keySet());
        curCodes.removeAll(lovCodes);
        // 现在的是不存在于lov中的d
        for (String code : curCodes) {
            RuleLine ruleLine = codeAndRuleLineMap.get(code);
            LovLineSaveDTO lovLineSaveDTO = new LovLineSaveDTO();
            lovLineSaveDTO.setLovHeaderId(lovHeaderDTO.getLovHeaderId());
            lovLineSaveDTO.setLovName(ruleLine.getName());
            lovLineSaveDTO.setLovValue(ruleLine.getCode());
            lovLineSaveDTO.setEnableFlag(YNEnum.Y.getValue());
            lovLineSaveDTO.setEffectiveStartDate(LocalDate.now());
            lovLineSaveDTO.setEffectiveEndDate(LocalDate.now().plusYears(20));
            systemService.saveOne(lovLineSaveDTO);
        }

        // 如果不存在于现在的行中,则标识为无效
        curCodes = new HashSet<>(codeAndRuleLineMap.keySet());
        lovCodes.removeAll(curCodes);
        for (String code : lovCodes) {
            LovLineDTO lovLineDTO = valueAndLovLineMap.get(code);
            LovLineSaveDTO lovLineSaveDTO = new LovLineSaveDTO();
            BeanUtils.copyProperties(lovLineDTO, lovLineSaveDTO);
            lovLineSaveDTO.setEnableFlag(YNEnum.N.getValue());
            systemService.saveOne(lovLineSaveDTO);
        }
    }

    @Override
    public void deleteById(Long id) {
        RuleLine ruleLine = ruleLineService.findRuleLineByLinesId(id);
        RuleHeaderDTO ruleHeaderDTO = ruleHeaderService.queryById(ruleLine.getRuleHeaderId());
        repository.deleteById(id);
        updateMaterialLimitationLov(ruleHeaderDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLines(RuleLinesSaveDTO ruleLinesSaveDTO) {
        verifyDefaultFlag(ruleLinesSaveDTO.getLines());
        verifyAndDelete(ruleLinesSaveDTO.getRuleHeaderId(), ruleLinesSaveDTO.getLines());

        RuleLine newObj = null;
        for (RuleLineSaveDTO line : ruleLinesSaveDTO.getLines()) {
            line.setRuleHeaderId(ruleLinesSaveDTO.getRuleHeaderId());
            RuleLineDTO ruleLineDTO = save(line);
            if (Objects.nonNull(line.getIsChange()) && line.getIsChange()) {
                newObj = new RuleLine();
                BeanUtils.copyProperties(ruleLineDTO, newObj);
            }
        }
        if (Objects.nonNull(newObj)) {
            applicationEventPublisher.publishEvent(new CollocationRulesEvent(newObj));
        }

    }

    private void verifyDefaultFlag(List<RuleLineSaveDTO> groupDTOList) {
        int defaultFlag = 0;
        for (RuleLineSaveDTO prodAttrGroupDTO : groupDTOList) {
            if (YNEnum.Y.getValue().equals(prodAttrGroupDTO.getDefaultFlag())) {
                defaultFlag++;
            }
        }
        if (defaultFlag > 1) {
            throw new BizException("bbom_only_default");
        }
    }

    @Override
    public List<RuleLineDTO> listLinesByHeaderId(Long ruleHeaderId) {
        List<RuleLine> lines = repository.listByRuleHeaderId(ruleHeaderId);
        return lines.stream().map(line -> {
            RuleLineDTO ruleLineDTO = new RuleLineDTO();
            BeanUtils.copyProperties(line, ruleLineDTO);
            if (StringUtils.isBlank(ruleLineDTO.getName())) {
                ruleLineDTO.setName(ruleLineDTO.getCode());
            }
            setLovAndAttrInfo(ruleLineDTO);
            setDtoValues(ruleLineDTO);
            return ruleLineDTO;
        }).collect(Collectors.toList());

    }

    @Override
    public List<RuleLineDTO> listLinesByHeaderIds(List<Long> ruleHeaderId) {
        List<RuleLine> lines = repository.listByRuleByHeaderIds(ruleHeaderId);
        return lines.stream().map(line -> {
            RuleLineDTO ruleLineDTO = new RuleLineDTO();
            BeanUtils.copyProperties(line, ruleLineDTO);
            setLovAndAttrInfo(ruleLineDTO);
            setDtoValues(ruleLineDTO);
            return ruleLineDTO;
        }).collect(Collectors.toList());

    }

    @Override
    public List<RuleLineDTO> listByRuleHeaderIdAndItemCode(Long ruleHeaderId, String itemCode) {
        List<RuleLine> lines = repository.listByRuleHeaderIdAndItemCode(ruleHeaderId, itemCode);
        return lines.stream().map(line -> {
            RuleLineDTO ruleLineDTO = new RuleLineDTO();
            BeanUtils.copyProperties(line, ruleLineDTO);
            if (StringUtils.isBlank(ruleLineDTO.getName())) {
                ruleLineDTO.setName(ruleLineDTO.getCode());
            }
            setLovAndAttrInfo(ruleLineDTO);
            setDtoValues(ruleLineDTO);
            return ruleLineDTO;
        }).collect(Collectors.toList());

    }

    private void setDtoValues(RuleLineDTO ruleLineDTO) {
        ruleLineDTO.setDetails(ruleDpDetailService.listByRuleLineId(ruleLineDTO.getRuleLineId()));
//        ruleLineDTO.setMtlDetails(ruleMtlDetailService.listByRuleLineId(ruleLineDTO.getRuleLineId()));
        ruleLineDTO.setControlObjectHeaders(ruleControlObjectHeaderService.listByRuleLineId(ruleLineDTO.getRuleLineId()));
//        ruleLineDTO.setMatchRules(matchRejectService.listMatchRulesByRuleLineId(ruleLineDTO.getRuleLineId()));
//        ruleLineDTO.setRejectRules(matchRejectService.listRejectRulesByRuleLineId(ruleLineDTO.getRuleLineId()));
//        ruleLineDTO.setBeMatchRules(matchRejectService.listBeMatchRulesByRuleLineId(ruleLineDTO.getRuleLineId()));
//        ruleLineDTO.setBeRejectRules(matchRejectService.listBeRejectRulesByRuleLineId(ruleLineDTO.getRuleLineId()));
        setLovAndAttrInfo(ruleLineDTO);
    }

    @Override
    public List<RuleLine> findByRuleCategoryId(Long lovLineId) {
        QRuleLine qRuleLine = QRuleLine.ruleLine;
        QRuleDpDetail qRuleDpDetail = QRuleDpDetail.ruleDpDetail;

        JPAQuery<RuleLine> where = jpaQueryFactory.select(qRuleLine)
                .from(qRuleLine)
                .leftJoin(qRuleDpDetail)
                .on(qRuleLine.ruleLineId.eq(qRuleDpDetail.ruleLineId))
                .groupBy(qRuleLine.ruleLineId);
        // 拼接查询条件
        where.where(qRuleDpDetail.dpFiledId.eq(lovLineId));

        return where.fetch();
    }

    @Override
    public String getControlObjectExpress(RuleLine ruleLine, String bomStructure) {
        List<RuleControlObjectHeaderDTO> ruleControlObjectHeaderDTOS =
                ruleControlObjectHeaderService.listByRuleLineId(ruleLine.getRuleLineId());

        List<RuleControlObjectHeaderDTO> objectHeaderDTOS = ruleControlObjectHeaderDTOS.stream()
                .filter(header -> header.getControlObject().equals(bomStructure)).collect(Collectors.toList());
        StringBuilder express = new StringBuilder();
        for (RuleControlObjectHeaderDTO objectHeaderDTO : objectHeaderDTOS) {
            express.append(" ( 1==1 ");
            for (RuleControlObjectDetailDTO detail : objectHeaderDTO.getDetails()) {
                AttrTypeLineDTO attrTypeLineDTO = attrUtil.getAttrLineById(detail.getMaterialsAttrFiledId());
                // 获取column
                if (attrTypeLineDTO == null || StringUtils.isBlank(attrTypeLineDTO.getSourceColumn())) {
                    continue;
                }

                String sourceColumn = CharSequenceUtil.toCamelCase(attrTypeLineDTO.getSourceColumn().trim());

                List<String> controlObjectValues = detail.getValues().stream().map(RuleControlObjectValueDTO::getAttrValue).collect(Collectors.toList());

                LovLineDTO attrOperatorLov = LovUtils.get(Long.parseLong(detail.getAttrOperator()));
                if (Objects.equals(attrOperatorLov.getLovValue(), AttrOperatorEnum.EXCLUDE.getValue())) {
                    // 排除
                    express.append(" && notin('{").append(sourceColumn).append("}','")
                            .append(StringUtils.join(controlObjectValues, ";")).append("') ");
                } else {
                    // 等于 和 包含,现在将等于包含看做一致
                    express.append(" && ain('{").append(sourceColumn).append("}','")
                            .append(StringUtils.join(controlObjectValues, ";")).append("') ");
                }
            }
            express.append(" ) ");
        }

        return express.toString();
    }

    @Override
    public List<RuleLine> findByControlObject(String controlObject) {
        QRuleLine qRuleLine = QRuleLine.ruleLine;
        QRuleControlObjectHeader qRuleControlObjectHeader = QRuleControlObjectHeader.ruleControlObjectHeader;

        JPAQuery<RuleLine> where = jpaQueryFactory.select(qRuleLine)
                .from(qRuleLine)
                .leftJoin(qRuleControlObjectHeader)
                .on(qRuleLine.ruleLineId.eq(qRuleControlObjectHeader.ruleLineId))
                .groupBy(qRuleLine.ruleLineId);
        // 拼接查询条件
        where.where(qRuleControlObjectHeader.controlObject.eq(controlObject));

        return where.fetch();
    }

    @Override
    public List<RuleLine> findByControlObjectAndRuleCategoryIds(String controlObject, List<Long> ruleCategoryIds) {
        String key = controlObject + "_" + ruleCategoryIds.stream().map(i -> String.valueOf(i)).collect(Collectors.joining(","));
        List<RuleLine> data = ruleLineCache.get(key);
        if (data == null) {
            JPAQuery<RuleLine> where = jpaQueryFactory.select(qRuleLine)
                    .from(qRuleLine)
                    .leftJoin(qRuleControlObjectHeader)
                    .on(qRuleLine.ruleLineId.eq(qRuleControlObjectHeader.ruleLineId).and(qRuleControlObjectHeader.isDeleted.eq(0)))
                    .leftJoin(qRuleHeader)
                    .on(qRuleLine.ruleHeaderId.eq(qRuleHeader.ruleHeaderId))
                    .groupBy(qRuleLine.ruleLineId);
            // 拼接查询条件
            where.where(qRuleControlObjectHeader.controlObject.eq(controlObject));
            where.where(qRuleHeader.ruleCategoryId.in(ruleCategoryIds));
            where.where(qRuleHeader.effectiveStartDate.isNull()
                    .or(qRuleHeader.effectiveStartDate.before(LocalDate.now())));
            where.where(qRuleHeader.effectiveEndDate.isNull()
                    .or(qRuleHeader.effectiveEndDate.after(LocalDate.now())));
            where.where(qRuleLine.effectiveStartDate.isNull()
                    .or(qRuleLine.effectiveStartDate.before(LocalDate.now())));
            where.where(qRuleLine.effectiveEndDate.isNull()
                    .or(qRuleLine.effectiveEndDate.after(LocalDate.now())));
            where.where(qRuleLine.isDeleted.eq(0));
            data = where.fetch();
            ruleLineCache.put(key, data);
        }
        return data;
    }

    public List<RuleLine> findByRuleCategoryIds(List<Long> ruleCategoryIds) {
        String key = ruleCategoryIds.stream().map(i -> String.valueOf(i)).collect(Collectors.joining(","));
        List<RuleLine> data = ruleLineCache.get(key);
        if (data == null) {
            JPAQuery<RuleLine> where = jpaQueryFactory.select(qRuleLine)
                    .from(qRuleLine)
                    .leftJoin(qRuleControlObjectHeader)
                    .on(qRuleLine.ruleLineId.eq(qRuleControlObjectHeader.ruleLineId).and(qRuleControlObjectHeader.isDeleted.eq(0)))
                    .leftJoin(qRuleHeader)
                    .on(qRuleLine.ruleHeaderId.eq(qRuleHeader.ruleHeaderId))
                    .groupBy(qRuleLine.ruleLineId);
            // 拼接查询条件
            where.where(qRuleHeader.ruleCategoryId.in(ruleCategoryIds));
            where.where(qRuleHeader.effectiveStartDate.isNull()
                    .or(qRuleHeader.effectiveStartDate.before(LocalDate.now())));
            where.where(qRuleHeader.effectiveEndDate.isNull()
                    .or(qRuleHeader.effectiveEndDate.after(LocalDate.now())));
            where.where(qRuleLine.effectiveStartDate.isNull()
                    .or(qRuleLine.effectiveStartDate.before(LocalDate.now())));
            where.where(qRuleLine.effectiveEndDate.isNull()
                    .or(qRuleLine.effectiveEndDate.after(LocalDate.now())));
            where.where(qRuleLine.isDeleted.eq(0));
            data = where.fetch();
            ruleLineCache.put(key, data);
        }
        return data;
    }

    @Override
    public List<RuleLine> listRulesByRuleCategoryId(Long ruleCategoryId) {
        return jpaQueryFactory.select(qRuleLine)
                .from(qRuleLine)
                .leftJoin(qRuleHeader)
                .on(qRuleHeader.ruleHeaderId.eq(qRuleLine.ruleHeaderId))
                .where(qRuleHeader.ruleCategoryId.eq(ruleCategoryId))
                .where(qRuleHeader.effectiveEndDate.isNull().or(qRuleHeader.effectiveEndDate.after(LocalDate.now())))
                .fetch();
    }

    @Override
    public RuleLineDTO convertDto(RuleLine item) {
        RuleLineDTO dto = new RuleLineDTO();
        BeanUtils.copyProperties(item, dto);
        setDtoValues(dto);
        return dto;
    }

    @Override
    public void genRuleLineNo() {
        Integer startValue = repository.countNoNotEqualZore();
        List<RuleLine> ruleLines = repository.findByNoEqualZore();
        for (RuleLine ruleLine : ruleLines) {
            ruleLine.setNo(++startValue);
            repository.save(ruleLine);
        }
    }

    private void setLovAndAttrInfo(RuleLineDTO item) {
        LovLineDTO controlSubject = LovUtils.get(item.getControlSubjectId());
        if (controlSubject != null) {
            item.setControlSubject(controlSubject.getLovName());
        }


        LovLineDTO controlPurpose = LovUtils.get(item.getControlPurposeId());
        if (controlPurpose != null) {
            item.setControlPurpose(controlPurpose.getLovName());
        }

        AttrTypeLineDTO attr = attrUtil.getAttrLineById(item.getControlObjectId());
        if (attr != null) {
            item.setControlObject(attr.getAttrCnName());
        }
    }

    private void verifyAndDelete(Long id, List<RuleLineSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<RuleLine> savedConfigs = repository.listByRuleHeaderId(id);

        if (lines == null || lines.isEmpty()) {
            if (!savedConfigs.isEmpty()) {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
            return;
        }
        List<Long> currentIds = lines.stream().filter(item -> item.getRuleLineId() != null).map(RuleLineSaveDTO::getRuleLineId)
                .collect(Collectors.toList());
        List<RuleLine> deleteConfigs = savedConfigs.stream().filter(item -> !currentIds.contains(item.getRuleLineId()))
                .collect(Collectors.toList());

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }
    }

    @Override
    public RuleLine findRuleLineByLinesId(Long ruleLinesId) {
        return repository.findById(ruleLinesId).orElse(null);
    }

    @Override
    public List<RuleLine> listByRuleHeaderId(Long ruleHeaderId) {
        return repository.listByRuleHeaderId(ruleHeaderId);
    }

    @Override
    public List<RuleLineInterfaceDTO> queryRuleLins(List<String> itemCodes) {

        Map<String, LovLineDTO> cellRuleHeaderClassifMap = LovUtils.getAllByHeaderCode("CELL_RULE_CLASSIFICATION");
        LovLineDTO materialRuleLov = cellRuleHeaderClassifMap.get("Production");
        if (materialRuleLov == null) {
            return Lists.newArrayList();
        }
        RuleHeaderQuery query = new RuleHeaderQuery();
        query.setRuleCategoryId(materialRuleLov.getLovLineId());
        List<RuleLineDTO> lineDTOList = ruleHeaderService.listLinesByLov(query);

        //需要识别的限制条件：监造，机台，认证，电池料号，批次，订单编号
        Map<Long, String> attrLineMap = attrUtil.queryAttrTypeLinesByHeaderCode("CELL_RESTRICTED").stream().filter(k -> StringUtils.equalsAnyIgnoreCase(k.getAttrCode(), "BATTR_TYPE_001", "BATTR_TYPE_004", "BATTR_TYPE_007", "BATTR_TYPE_009", "BATTR_TYPE_010", "BATTR_TYPE_011")).collect(Collectors.toMap(AttrTypeLineDTO::getAttrLineId, AttrTypeLineDTO::getAttrCode));
        return lineDTOList.stream().map(x -> {
            if (CollectionUtils.isNotEmpty(x.getDetails())) {
                RuleLineInterfaceDTO ruleLineInterfaceDTO = new RuleLineInterfaceDTO();
                x.getDetails().stream().filter(q -> attrLineMap.keySet().contains(q.getDpFiledId())).forEach(y -> {
                    String attrOperator = y.getAttrOperator();
                    List<RuleDpValueDTO> values = y.getValues();
                    String limitValues = getLimitValues(values);
                    String dpFieldCode = attrLineMap.get(y.getDpFiledId());
                    switch (dpFieldCode) {
                        case "BATTR_TYPE_001":
                            ruleLineInterfaceDTO.setSupervisionRestrictionOperator(attrOperator);
                            ruleLineInterfaceDTO.setSupervisionRestriction(limitValues);
                            break;
                        case "BATTR_TYPE_004":
                            ruleLineInterfaceDTO.setMachineLimitOperator(attrOperator);
                            ruleLineInterfaceDTO.setMachineLimit(limitValues);
                            break;
                        case "BATTR_TYPE_007":
                            ruleLineInterfaceDTO.setCertificationLimitationOperator(attrOperator);
                            ruleLineInterfaceDTO.setCertificationLimitation(limitValues);
                            break;
                        case "BATTR_TYPE_009":
                            ruleLineInterfaceDTO.setItemCodeLimitOperator(attrOperator);
                            ruleLineInterfaceDTO.setItemCodeLimit(limitValues);
                            break;
                        case "BATTR_TYPE_010":
                            ruleLineInterfaceDTO.setBatchLimitOperator(attrOperator);
                            ruleLineInterfaceDTO.setBatchLimit(limitValues);
                            break;
                        case "BATTR_TYPE_011":
                            ruleLineInterfaceDTO.setDpLimitOperator(attrOperator);
                            ruleLineInterfaceDTO.setDpLimit(limitValues);
                            break;
                        default:
                            break;
                    }
                });
                ruleLineInterfaceDTO.setEffectiveStartDate(x.getEffectiveStartDate());
                ruleLineInterfaceDTO.setEffectiveEndDate(x.getEffectiveEndDate());
                return ruleLineInterfaceDTO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取属性值
     *
     * @param values
     * @return
     */
    private String getLimitValues(List<RuleDpValueDTO> values) {
        if (CollectionUtils.isNotEmpty(values)) {
            return values.stream().map(RuleDpValueDTO::getAttrValue).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public Map<String, List<RuleLineDTO>> queryCellRestrictedByItemCode(List<String> itemCodes) {
        Map<String, LovLineDTO> cellRuleHeaderClassifMap = LovUtils.getAllByHeaderCode("CELL_RULE_CLASSIFICATION");
        LovLineDTO materialRuleLov = cellRuleHeaderClassifMap.get("Production");
        if (materialRuleLov == null) {
            return Collections.emptyMap();
        }
        List<RuleLineDTO> ruleLineDTOS = ruleHeaderService.listLineByRuleCategoryId(materialRuleLov.getLovLineId());

        // 获取电池片料号
        AttrTypeLineDTO itemCodeAttrDTO = attrUtil.getAttrLineByHeaderCodeAndValue("CELL_RESTRICTED", "BATTR_TYPE_009");
        // 过滤按物料过滤
        AttrTypeLineDTO materialLimit = attrUtil.getAttrLineByHeaderCodeAndValue("CELL_RESTRICTED", "BATTR_TYPE_008");
        return itemCodes.stream().collect(Collectors.toMap(i -> i, i ->
                ruleLineService.getRuleLineDTOSByItemCode(i, ruleLineDTOS, itemCodeAttrDTO, materialLimit)
        ));
    }

    @Override
    @Cacheable(cacheNames = "RuleLineService_getRuleLineDTOSByItemCode", key = "#p0", unless = "#result == null", condition = "#p0!=null")
    public List<RuleLineDTO> getRuleLineDTOSByItemCode(String i, List<RuleLineDTO> ruleLineDTOS, AttrTypeLineDTO itemCodeAttrDTO, AttrTypeLineDTO materialLimit) {
        List<RuleLineDTO> value = ruleLineDTOS.stream().filter(
                ruleLine -> ruleLine.getDetails().stream()
                        .filter(q -> q.getDpFiledId().equals(itemCodeAttrDTO.getAttrLineId())).findAny()
                        .map(y ->
                                y.getValues().stream().anyMatch(p -> p.getAttrValue().equals(i)))
                        .orElse(false)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(value)) {
            return value;
        }
        // 转换规则行
        List<RuleLineDTO> result = new LinkedList<>();
        for (RuleLineDTO ruleLineDTO : value) {
            RuleDpDetailDTO materialLimitDTO = ruleLineDTO.getDetails().stream()
                    .filter(q -> q.getDpFiledId().equals(materialLimit.getAttrLineId())).findAny().orElse(null);
            if (materialLimitDTO == null) {
                continue;
            }
            for (RuleDpValueDTO materialLimitDTOValue : materialLimitDTO.getValues()) {
                Long lovId = materialLimitDTOValue.getAttrValueId();
                if (lovId == null) {
                    continue;
                }
                LovLineDTO lovLineDTO = LovUtils.get(lovId);
                if (lovLineDTO == null) {
                    continue;
                }
                String ruleCode = lovLineDTO.getLovValue();
                if (StringUtils.isEmpty(ruleCode)) {
                    continue;
                }
                // 根据RuleCode获取规则行
                List<RuleLineDTO> ruleLineDTOList = ruleLineService.getRuleLineByRuleCode(ruleCode);
                result.addAll(ruleLineDTOList);
            }
        }
        return result;
    }

    @Override
    public List<RuleLineDTO> getRuleLineByRuleCode(String ruleCode) {
        Iterable<RuleLine> all = repository.findAll(qRuleLine.code.eq(ruleCode));
        return IterableUtils.toList(all).stream().map(this::convertDto)
                .collect(Collectors.toList());
    }
}
