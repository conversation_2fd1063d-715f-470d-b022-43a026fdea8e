package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ComponentVDTO;
import com.trinasolar.scp.bbom.domain.query.ComponentVQuery;
import com.trinasolar.scp.bbom.domain.save.ComponentVSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 同步cux3_bbom_component_v 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
public interface ComponentVService {
    /**
     * 分页获取同步cux3_bbom_component_v
     *
     * @param query 查询对象
     * @return 同步cux3_bbom_component_v分页对象
     */
    Page<ComponentVDTO> queryByPage(ComponentVQuery query);

    /**
     * 根据主键获取同步cux3_bbom_component_v详情
     *
     * @param id 主键
     * @return 同步cux3_bbom_component_v详情
     */
    ComponentVDTO queryById(Long id);

    /**
     * 保存或更新同步cux3_bbom_component_v
     *
     * @param saveDTO 同步cux3_bbom_component_v保存对象
     * @return 同步cux3_bbom_component_v对象
     */
    ComponentVDTO save(ComponentVSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除同步cux3_bbom_component_v
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ComponentVQuery query, HttpServletResponse response);

    void syncByInterface(LocalDateTime fromDateTime, Long[] organizations, int pageNum);

    void syncByInterfaceByPhaseII(LocalDateTime fromDateTime, Long[] organizations, int pageNum);

    void transformation();
}

