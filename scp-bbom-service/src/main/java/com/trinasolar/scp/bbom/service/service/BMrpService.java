package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ApprovedVendorDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ErpOpenPODTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ErpOpenPRDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.TjOnHandDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/2
 */
public interface BMrpService {
    List<TjOnHandDTO> getTjOnHandDTOSByItemCodeAndOrgId(String itemCode, Long organizationId);

    List<ErpOpenPODTO> getPoByItemCodeAndOrgId(String itemCode, Long organizationId);

    List<ErpOpenPRDTO> getPrByItemCodeAndOrgId(String itemCode, Long organizationId);

    Map<String, ApprovedVendorDTO> getApprovedVendorDTOByVendorNames(List<String> vendorNames);
}
