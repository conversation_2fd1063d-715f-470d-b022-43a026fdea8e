package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.RuleDpValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * BOM规则DP因子明细值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Repository
public interface RuleDpValueRepository extends JpaRepository<RuleDpValue, Long>, QuerydslPredicateExecutor<RuleDpValue> {

    @Query(value = "SELECT * FROM bbom_rule_dp_value WHERE bbom_rule_dp_value.rule_detail_id = ?1 AND is_deleted=0",
            nativeQuery = true)
    List<RuleDpValue> listByRuleDetailId(Long ruleDetailId);
}
