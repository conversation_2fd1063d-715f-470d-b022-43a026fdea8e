package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectValue;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectValueQuery;
import com.trinasolar.scp.bbom.domain.save.RuleControlObjectValueSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 规则管控对象值 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
public interface RuleControlObjectValueService {
    Page<RuleControlObjectValue> queryByPage(RuleControlObjectValueQuery query);

    RuleControlObjectValueDTO queryById(Long id);

    RuleControlObjectValueDTO save(RuleControlObjectValueSaveDTO saveDTO);

    void deleteById(Long id);

    void saveValues(Long id, List<RuleControlObjectValueSaveDTO> values);

    List<RuleControlObjectValueDTO> listByRuleControlObjectDetailId(Long id);
}

