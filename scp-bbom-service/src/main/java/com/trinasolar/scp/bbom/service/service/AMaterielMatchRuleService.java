package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.AMaterielMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.query.AMaterielMatchRuleQuery;
import com.trinasolar.scp.bbom.domain.save.AMaterielMatchRuleSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
public interface AMaterielMatchRuleService {

    Page<AMaterielMatchRuleDTO> queryByPage(AMaterielMatchRuleQuery query);

    AMaterielMatchRuleDTO queryById(Long id);

    AMaterielMatchRuleDTO save(AMaterielMatchRuleSaveDTO saveDTO);

    void logicDeleteByIds(List<Long> ids);

    void importData(MultipartFile multipartFile, ExcelPara excelPara);

    void export(AMaterielMatchRuleQuery query, HttpServletResponse response);
}
