package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.StructuresDEConvert;
import com.trinasolar.scp.bbom.domain.dto.ComponentsDTO;
import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.entity.QStructures;
import com.trinasolar.scp.bbom.domain.entity.Structures;
import com.trinasolar.scp.bbom.domain.query.StructuresMrpQuery;
import com.trinasolar.scp.bbom.domain.query.StructuresQuery;
import com.trinasolar.scp.bbom.domain.save.StructuresSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.StructureItemVO;
import com.trinasolar.scp.bbom.service.repository.StructuresRepository;
import com.trinasolar.scp.bbom.service.service.ComponentsService;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.bbom.service.service.StructuresService;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * BBOM结构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Slf4j
@Service("structuresService")
@RequiredArgsConstructor
@CacheConfig(cacheNames = "BBOM_Components", cacheManager = "scpRedisCacheManager")
public class StructuresServiceImpl implements StructuresService {
    private static final QStructures qStructures = QStructures.structures;

    private final StructuresDEConvert convert;

    private final StructuresRepository repository;

    private final ComponentsService componentsService;

    private final ItemsService itemsService;

    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public Page<StructuresDTO> queryByPage(StructuresQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<Structures> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<StructuresDTO> queryList() {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        List<StructuresDTO> structuresDTOList = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        return structuresDTOList;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, StructuresQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qStructures.id.eq(query.getId()));
        }
        if (query.getAssemblyItemId() != null) {
            booleanBuilder.and(qStructures.assemblyItemId.eq(query.getAssemblyItemId()));
        }
        if (query.getOrganizationId() != null) {
            booleanBuilder.and(qStructures.organizationId.eq(query.getOrganizationId()));
        }
        if (StringUtils.isNotEmpty(query.getAlternateBomDesignator())) {
            booleanBuilder.and(qStructures.alternateBomDesignator.eq(query.getAlternateBomDesignator()));
        }
        if (query.getLastUpdateDate() != null) {
            booleanBuilder.and(qStructures.lastUpdateDate.eq(query.getLastUpdateDate()));
        }
        if (query.getCommonAssemblyItemId() != null) {
            booleanBuilder.and(qStructures.commonAssemblyItemId.eq(query.getCommonAssemblyItemId()));
        }
        if (StringUtils.isNotEmpty(query.getSpecificAssemblyComment())) {
            booleanBuilder.and(qStructures.specificAssemblyComment.eq(query.getSpecificAssemblyComment()));
        }
        if (StringUtils.isNotEmpty(query.getPendingFromEcn())) {
            booleanBuilder.and(qStructures.pendingFromEcn.eq(query.getPendingFromEcn()));
        }
        if (StringUtils.isNotEmpty(query.getAttributeCategory())) {
            booleanBuilder.and(qStructures.attributeCategory.eq(query.getAttributeCategory()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute1())) {
            booleanBuilder.and(qStructures.attribute1.eq(query.getAttribute1()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute2())) {
            booleanBuilder.and(qStructures.attribute2.eq(query.getAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute3())) {
            booleanBuilder.and(qStructures.attribute3.eq(query.getAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute4())) {
            booleanBuilder.and(qStructures.attribute4.eq(query.getAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute5())) {
            booleanBuilder.and(qStructures.attribute5.eq(query.getAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute6())) {
            booleanBuilder.and(qStructures.attribute6.eq(query.getAttribute6()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute7())) {
            booleanBuilder.and(qStructures.attribute7.eq(query.getAttribute7()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute8())) {
            booleanBuilder.and(qStructures.attribute8.eq(query.getAttribute8()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute9())) {
            booleanBuilder.and(qStructures.attribute9.eq(query.getAttribute9()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute10())) {
            booleanBuilder.and(qStructures.attribute10.eq(query.getAttribute10()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute11())) {
            booleanBuilder.and(qStructures.attribute11.eq(query.getAttribute11()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute12())) {
            booleanBuilder.and(qStructures.attribute12.eq(query.getAttribute12()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute13())) {
            booleanBuilder.and(qStructures.attribute13.eq(query.getAttribute13()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute14())) {
            booleanBuilder.and(qStructures.attribute14.eq(query.getAttribute14()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute15())) {
            booleanBuilder.and(qStructures.attribute15.eq(query.getAttribute15()));
        }
        if (query.getAssemblyType() != null) {
            booleanBuilder.and(qStructures.assemblyType.eq(query.getAssemblyType()));
        }
        if (query.getCommonBillSequenceId() != null) {
            booleanBuilder.and(qStructures.commonBillSequenceId.eq(query.getCommonBillSequenceId()));
        }
        if (query.getBillSequenceId() != null) {
            booleanBuilder.and(qStructures.billSequenceId.eq(query.getBillSequenceId()));
        }
        if (query.getRequestId() != null) {
            booleanBuilder.and(qStructures.requestId.eq(query.getRequestId()));
        }
        if (query.getProgramApplicationId() != null) {
            booleanBuilder.and(qStructures.programApplicationId.eq(query.getProgramApplicationId()));
        }
        if (query.getProgramId() != null) {
            booleanBuilder.and(qStructures.programId.eq(query.getProgramId()));
        }
        if (query.getProgramUpdateDate() != null) {
            booleanBuilder.and(qStructures.programUpdateDate.eq(query.getProgramUpdateDate()));
        }
        if (query.getCommonOrganizationId() != null) {
            booleanBuilder.and(qStructures.commonOrganizationId.eq(query.getCommonOrganizationId()));
        }
        if (query.getNextExplodeDate() != null) {
            booleanBuilder.and(qStructures.nextExplodeDate.eq(query.getNextExplodeDate()));
        }
        if (query.getProjectId() != null) {
            booleanBuilder.and(qStructures.projectId.eq(query.getProjectId()));
        }
        if (query.getTaskId() != null) {
            booleanBuilder.and(qStructures.taskId.eq(query.getTaskId()));
        }
        if (StringUtils.isNotEmpty(query.getOriginalSystemReference())) {
            booleanBuilder.and(qStructures.originalSystemReference.eq(query.getOriginalSystemReference()));
        }
        if (query.getStructureTypeId() != null) {
            booleanBuilder.and(qStructures.structureTypeId.eq(query.getStructureTypeId()));
        }
        if (query.getImplementationDate() != null) {
            booleanBuilder.and(qStructures.implementationDate.eq(query.getImplementationDate()));
        }
        if (StringUtils.isNotEmpty(query.getObjName())) {
            booleanBuilder.and(qStructures.objName.eq(query.getObjName()));
        }
        if (StringUtils.isNotEmpty(query.getPk1Value())) {
            booleanBuilder.and(qStructures.pk1Value.eq(query.getPk1Value()));
        }
        if (StringUtils.isNotEmpty(query.getPk2Value())) {
            booleanBuilder.and(qStructures.pk2Value.eq(query.getPk2Value()));
        }
        if (StringUtils.isNotEmpty(query.getPk3Value())) {
            booleanBuilder.and(qStructures.pk3Value.eq(query.getPk3Value()));
        }
        if (StringUtils.isNotEmpty(query.getPk4Value())) {
            booleanBuilder.and(qStructures.pk4Value.eq(query.getPk4Value()));
        }
        if (StringUtils.isNotEmpty(query.getPk5Value())) {
            booleanBuilder.and(qStructures.pk5Value.eq(query.getPk5Value()));
        }
        if (query.getEffectivityControl() != null) {
            booleanBuilder.and(qStructures.effectivityControl.eq(query.getEffectivityControl()));
        }
        if (StringUtils.isNotEmpty(query.getIsPreferred())) {
            booleanBuilder.and(qStructures.isPreferred.eq(query.getIsPreferred()));
        }
        if (query.getSourceBillSequenceId() != null) {
            booleanBuilder.and(qStructures.sourceBillSequenceId.eq(query.getSourceBillSequenceId()));
        }
    }

    @Override
    public StructuresDTO queryById(Long id) {
        Structures queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public StructuresDTO save(StructuresSaveDTO saveDTO) {
        Structures newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new Structures());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(StructuresQuery query, HttpServletResponse response) {
        List<StructuresDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "BBOM结构", "BBOM结构", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @Cacheable(cacheNames = "BBOM_Structures_findOneByMrpQuery", key = "#query.getAssemblyItemId()+'_'+#query.getAlternateBomDesignator()", unless = "#result == null")
    public StructuresDTO findOneByMrpQuery(StructuresMrpQuery query) {
        Iterable<Structures> all = repository.findAll(
                qStructures.assemblyItemId.eq(query.getAssemblyItemId())
                        .and(qStructures.alternateBomDesignator.eq(query.getAlternateBomDesignator()))
        );
        ArrayList<Structures> structures = Lists.newArrayList(all);
        if (CollectionUtils.isEmpty(structures)) {
            return null;
        }
        StructuresDTO structuresDTO = convert.toDto(structures.get(0));
        StructureItemVO assemblyItemId = itemsService.getOneBySourceItemId(structuresDTO.getAssemblyItemId());
        if (assemblyItemId == null) {
            return null;
        }
        structuresDTO.setAssemblyItemCode(assemblyItemId.getItemCode());
        structuresDTO.setAssemblyItemDesc(assemblyItemId.getItemDesc());
        structuresDTO.setSegment3(assemblyItemId.getSegment3());

        // 查询子行
        List<ComponentsDTO> componentsDTOS = componentsService.findByStructure(structuresDTO);
        structuresDTO.setComponents(componentsDTOS);

        return structuresDTO;
    }


    @Override
    public List<StructuresDTO> findByComponentDTOAndOrganizationId(List<ComponentsDTO> componentsDTO, Long organizationId) {
        List<Structures> structures = jpaQueryFactory.selectFrom(qStructures)
                .where(qStructures.organizationId.eq(organizationId))
                .where(qStructures.billSequenceId.in(componentsDTO.stream().map(ComponentsDTO::getBillSequenceId).collect(Collectors.toList())))
                .fetch();
        return convert.toDto(structures);
    }

    @Override
    @Cacheable(cacheNames = "BBOM_Structures_findByAlternateDesignator", key = "#orgId+'_'+#sourceItemId+'_'+#alternateDesignatorCode", unless = "#result == null")
    public StructuresDTO findByAlternateDesignator(String orgId, Long sourceItemId, String alternateDesignatorCode) {
        Structures structures = jpaQueryFactory.selectFrom(qStructures)
                .where(qStructures.organizationId.eq(Long.parseLong(orgId))
                        .and(qStructures.assemblyItemId.eq(sourceItemId))
                        .and(qStructures.alternateBomDesignator.eq(alternateDesignatorCode)))
                .fetchFirst();
        return convert.toDto(structures);
    }

    @Override
    public List<Long> findByAlternateDesignatorWithoutSourceItemId(String orgId, String alternateDesignatorCode) {
        List<Structures> structures = jpaQueryFactory.selectFrom(qStructures)
                .where(qStructures.organizationId.eq(Long.parseLong(orgId))
                        .and(qStructures.alternateBomDesignator.eq(alternateDesignatorCode)))
                .fetch();
        List<Long> result =structures.stream().map(Structures::getId).collect(Collectors.toList());
        return result;
    }

    @Override
    public List<StructuresDTO> findAll() {
        List<Structures> all = repository.findAll();
        List<StructuresDTO> structuresDTOS = convert.toDto(all);
        
        for (StructuresDTO structuresDTO : structuresDTOS) {
            structuresDTO.setItem(itemsService.getOneBySourceItemId(structuresDTO.getAssemblyItemId()));
        }
        return structuresDTOS;
    }
}
