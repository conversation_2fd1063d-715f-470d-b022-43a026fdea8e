package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ScreenMainReplacementDTO;
import com.trinasolar.scp.bbom.domain.dto.SlurryMainReplacementDTO;
import com.trinasolar.scp.bbom.domain.query.MainReplacementQuery;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/10/1
 */
public interface MainReplacementService {
    /**
     * 网版查询
     * @param query
     * @return
     */
    Page<ScreenMainReplacementDTO> queryScreenByPage(MainReplacementQuery query);

    /**
     * 网版导出
     * @param query
     * @param response
     */
    void exportScreen(MainReplacementQuery query, HttpServletResponse response);

    /**
     * 网版邮件发送
     * @param query
     */
    void sendScreenEmail(MainReplacementQuery query);

    /**
     * 浆料查询
     * @param query
     * @return
     */
    Page<SlurryMainReplacementDTO> querySlurryByPage(MainReplacementQuery query);

    /**
     * 浆料导出
     * @param query
     * @param response
     */
    void exportSlurry(MainReplacementQuery query, HttpServletResponse response);

    /**
     * 浆料邮件发送
     * @param query
     */
    void sendSlurryEmail(MainReplacementQuery query);
}
