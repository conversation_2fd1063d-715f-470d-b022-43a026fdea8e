package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.ItemsDEConvert;
import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.bbom.domain.dto.ItemsLiftCycleDTO;
import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalAttrMapViewDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalItemsDTO;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.query.BatchItemQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsNewQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsQuery;
import com.trinasolar.scp.bbom.domain.save.ItemsSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.StructureItemVO;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.feign.ExternalFeign;
import com.trinasolar.scp.bbom.service.repository.ItemsRepository;
import com.trinasolar.scp.bbom.service.service.BomService;
import com.trinasolar.scp.bbom.service.service.ComponentsService;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.bbom.service.service.PdmItemLifecycleService;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 物料基础数据表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
@Slf4j
@Service("itemsService")
@RequiredArgsConstructor
@CacheConfig(cacheNames = "BBOM_items", cacheManager = "scpRedisCacheManager")
public class ItemsServiceImpl implements ItemsService {
    public static final String none = "无";

    private static final QItems qItems = QItems.items;

    private static final Long ORGANIZATION_ID = 82L;

    public static final int PAGE_SIZE = 500;

    private static String[] segments;

    private static String[] attributes;

    static {
        segments = new String[60];
        for (int j = 0; j < 60; j++) {
            segments[j] = "segment" + (j + 1);
        }
        attributes = new String[20];
        for (int j = 0; j < 20; j++) {
            attributes[j] = "attribute" + (j + 1);
        }
    }

    private final ItemsDEConvert convert;

    private final ItemsRepository repository;

    private final BomService bomService;

    private final JPAQueryFactory jpaQueryFactory;

    private final ExternalFeign externalFeign;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    @Lazy
    private ComponentsService componentsService;

    @Autowired
    @Lazy
    ItemsService itemsService;

    @Autowired
    PdmItemLifecycleService pdmItemLifecycleService;

    @Autowired
    BomFeign bomFeign;

    private BooleanBuilder buildWhereSourceItemId(List<Long> sourceItemId, List<String> categorySegment4) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (CollectionUtils.isNotEmpty(sourceItemId)) {
            booleanBuilder.and(qItems.sourceItemId.in(sourceItemId));
        }
        if (CollectionUtils.isNotEmpty(categorySegment4)) {
            booleanBuilder.and(qItems.categorySegment4.in(categorySegment4));
        }
        booleanBuilder.and(qItems.organizationId.eq(ORGANIZATION_ID));
        return booleanBuilder;
    }

    private BooleanBuilder buildWhereItemCodeNew(ItemsNewQuery itemsQuery) {
        List<String> itemCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemsQuery.getItemCodes())) {
            itemCodeList = itemsQuery.getItemCodes().stream().collect(Collectors.toList());
        }
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (CollectionUtils.isNotEmpty(itemCodeList)) {
            booleanBuilder.and(qItems.itemCode.in(itemCodeList));
        }
        if (itemsQuery.getOrganizationId() != null) {
            booleanBuilder.and(qItems.organizationId.in(itemsQuery.getOrganizationId()));
        } else {
            booleanBuilder.and(qItems.organizationId.eq(ORGANIZATION_ID));
        }
        return booleanBuilder;
    }

    private BooleanBuilder buildWhereItemCode(ItemsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        String itemCode = query.getItemCode();
        String itemDesc = query.getItemDesc();
        String categorySegment1 = query.getCategorySegment1();
        String categorySegment2 = query.getCategorySegment2();
        String categorySegment3 = query.getCategorySegment3();
        String categorySegment4 = query.getCategorySegment4();
        String categorySegment5 = query.getCategorySegment5();
        Long organizationId = query.getOrganizationId();

        if (StringUtil.isNotBlank(itemCode)) {
            booleanBuilder.and(qItems.itemCode.like("%" + itemCode + "%"));
        }
        if (StringUtils.isNotBlank(itemDesc)) {
            booleanBuilder.and(qItems.itemDesc.like("%" + itemDesc + "%"));
        }
        if (StringUtils.isNotBlank(categorySegment1)) {
            booleanBuilder.and(qItems.categorySegment1.eq(categorySegment1));
        }
        if (StringUtils.isNotBlank(categorySegment2)) {
            booleanBuilder.and(qItems.categorySegment2.eq(categorySegment2));
        }
        if (StringUtils.isNotBlank(categorySegment3)) {
            booleanBuilder.and(qItems.categorySegment3.eq(categorySegment3));
        }
        if (StringUtils.isNotBlank(categorySegment4)) {
            booleanBuilder.and(qItems.categorySegment4.eq(categorySegment4));
        }
        if (StringUtils.isNotBlank(categorySegment5)) {
            booleanBuilder.and(qItems.categorySegment5.eq(categorySegment5));
        }
        if (organizationId != null) {
            booleanBuilder.and(qItems.organizationId.eq(organizationId));
        } else {
            booleanBuilder.and(qItems.organizationId.eq(ORGANIZATION_ID));
        }
        if (StringUtils.isNotBlank(query.getItemStatus())) {
            booleanBuilder.and(qItems.itemStatus.eq(query.getItemStatus()));
        }
        return booleanBuilder;
    }

    @Override
    public Page<ItemsDTO> queryByPage(ItemsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<Items> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<ItemsDTO> queryByMatching(ItemsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereByMatch(booleanBuilder, query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    public List<ItemsDTO> queryByMatchingAll() {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereByMatchAll(booleanBuilder);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    public List<ItemsDTO> filterList(List<ItemsDTO> itemsDTOList, ItemsQuery query) {
        List<ItemsDTO> filterList = itemsDTOList.parallelStream().filter(y -> buildWhereByMatch2(y, query)).collect(Collectors.toList());
        return filterList;
    }

    private Boolean buildWhereByMatch2(ItemsDTO itemsDTO, ItemsQuery query) {
        Boolean flag1 = true;

        if (StringUtils.isNotEmpty(query.getCategorySegment1())) {
            flag1 = noneVerify2(flag1, itemsDTO.getCategorySegment1(), query.getCategorySegment1(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment2())) {
            flag1 = noneVerify2(flag1, itemsDTO.getCategorySegment2(), query.getCategorySegment2(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment3())) {
            if (StringUtils.isNotEmpty(itemsDTO.getCategorySegment3()) && itemsDTO.getCategorySegment3().indexOf(query.getCategorySegment3().replaceAll("%", "")) < 0) {
                flag1 = false;
            }
            //flag1=noneVerify2(flag1,itemsDTO.getCategorySegment3(), query.getCategorySegment3(),"");
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment4())) {
            flag1 = noneVerify2(flag1, itemsDTO.getCategorySegment4(), query.getCategorySegment4(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment5())) {
            flag1 = noneVerify2(flag1, itemsDTO.getCategorySegment5(), query.getCategorySegment5(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            flag1 = noneVerify2(flag1, itemsDTO.getItemCode(), query.getItemCode(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getItemDesc())) {
            flag1 = noneVerify2(flag1, itemsDTO.getItemDesc(), query.getItemDesc(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getItemStatus())) {
            flag1 = noneVerify2(flag1, itemsDTO.getItemStatus(), query.getItemStatus(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getPriUom())) {
            flag1 = noneVerify2(flag1, itemsDTO.getPriUom(), query.getPriUom(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment1())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment1(), query.getSegment1(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment2())) {
            if ("无".equals(query.getSegment2())) {
                flag1 = noneVerify2(flag1, itemsDTO.getSegment2(), query.getSegment2(), "Q1", itemsDTO);
            } else {
                flag1 = noneVerify2(flag1, itemsDTO.getSegment2(), query.getSegment2(), "", itemsDTO);
            }
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment3())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment3(), query.getSegment3(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment4())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment4(), query.getSegment4(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment5())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment5(), query.getSegment5(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment6())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment6(), query.getSegment6(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment7())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment7(), query.getSegment7(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment8())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment8(), query.getSegment8(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment9())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment9(), query.getSegment9(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment10())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment10(), query.getSegment10(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment11())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment11(), query.getSegment11(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment12())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment12(), query.getSegment12(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }

        // 主栅间距
        if (StringUtils.isNotEmpty(query.getSegment13())) {
            if (StringUtil.isBlank(query.getSegment13()) || "无".equals(query.getSegment13())) {
                // 当为无或者空时不校验主栅间距
            } else {
                flag1 = noneVerify2(flag1, itemsDTO.getSegment13(), query.getSegment13(), "", itemsDTO);
            }
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment14())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment14(), query.getSegment14(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment15())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment15(), query.getSegment15(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment16())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment16(), query.getSegment16(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment17())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment17(), query.getSegment17(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment18())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment18(), query.getSegment18(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment19())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment19(), query.getSegment19(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment20())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment20(), query.getSegment20(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment21())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment21(), query.getSegment21(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment22()) && !"无".equals(query.getSegment22())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment22(), query.getSegment22(), "", itemsDTO);
        } else {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment22(), query.getSegment22(), "A", itemsDTO);
        }
        if (!flag1) {
            return flag1;
        }
        // 恢复为精准校验
        // p2_836 电池料号匹配新增逻辑：当美学字段=美学时，不校验透明双玻字段；
        if (!"美学".equals(query.getSegment37()) && StringUtils.isNotEmpty(query.getSegment23())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment23(), query.getSegment23(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isBlank(query.getSegment24())) {
            query.setSegment24("无");
        }

        if (StringUtils.isNotEmpty(query.getSegment24()) && !"无".equals(query.getSegment24())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment24(), query.getSegment24(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }

        if (!flag1) {
            return flag1;
        }
        if (StringUtils.isNotEmpty(query.getSegment25())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment25(), query.getSegment25(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment26())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment26(), query.getSegment26(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment27())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment27(), query.getSegment27(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment28())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment28(), query.getSegment28(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment29()) && !"无".equals(query.getSegment29())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment29(), query.getSegment29(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment30()) && !"无".equals(query.getSegment30())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment30(), query.getSegment30(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment31())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment31(), query.getSegment31(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment32())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment32(), query.getSegment32(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment33())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment33(), query.getSegment33(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment34())) {
            flag1 = notHaveVerify(flag1, itemsDTO.getSegment34(), query.getSegment34(), "", itemsDTO);
            //特殊处理版本
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment35())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment35(), query.getSegment35(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment36())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment36(), query.getSegment36(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment37())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment37(), query.getSegment37(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment38())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment38(), query.getSegment38(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment39())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment39(), query.getSegment39(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment40())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment40(), query.getSegment40(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment41())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment41(), query.getSegment41(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment42())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment42(), query.getSegment42(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment43())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment43(), query.getSegment43(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment44())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment44(), query.getSegment44(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment45())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment45(), query.getSegment45(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment46())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment46(), query.getSegment46(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment47())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment47(), query.getSegment47(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment48())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment48(), query.getSegment48(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment49())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment49(), query.getSegment49(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment50())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment50(), query.getSegment50(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment51())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment51(), query.getSegment51(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment52())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment52(), query.getSegment52(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment53())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment53(), query.getSegment53(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment54())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment54(), query.getSegment54(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment55())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment55(), query.getSegment55(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment56())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment56(), query.getSegment56(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment57())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment57(), query.getSegment57(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment58())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment58(), query.getSegment58(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment59())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment59(), query.getSegment59(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getSegment60())) {
            flag1 = noneVerify2(flag1, itemsDTO.getSegment60(), query.getSegment60(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute1())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute1(), query.getAttribute1(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute2())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute2(), query.getAttribute2(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute3())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute3(), query.getAttribute3(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute4())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute4(), query.getAttribute4(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute5())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute5(), query.getAttribute5(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute6())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute6(), query.getAttribute6(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute7())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute7(), query.getAttribute7(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute8())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute8(), query.getAttribute8(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute9())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute9(), query.getAttribute9(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute10())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute10(), query.getAttribute10(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute11())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute11(), query.getAttribute11(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute12())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute12(), query.getAttribute12(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute13())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute13(), query.getAttribute13(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute14())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute14(), query.getAttribute14(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute15())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute15(), query.getAttribute15(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute16())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute16(), query.getAttribute16(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute17())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute17(), query.getAttribute17(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute18())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute18(), query.getAttribute18(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute19())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute19(), query.getAttribute19(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getAttribute20())) {
            flag1 = noneVerify2(flag1, itemsDTO.getAttribute20(), query.getAttribute20(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getItemType())) {
            flag1 = noneVerify2(flag1, itemsDTO.getItemType(), query.getItemType(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getCustomerOrderFlag())) {
            flag1 = noneVerify2(flag1, itemsDTO.getCustomerOrderFlag(), query.getCustomerOrderFlag(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getItemSubcategory())) {
            flag1 = noneVerify2(flag1, itemsDTO.getItemSubcategory(), query.getItemSubcategory(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }
        if (StringUtils.isNotEmpty(query.getLifecycleState())) {
            flag1 = noneVerify2(flag1, itemsDTO.getLifecycleState(), query.getLifecycleState(), "", itemsDTO);
            if (!flag1) {
                return flag1;
            }
        }

        /*if (StringUtils.isNotEmpty(query.getLanguage())) {
            booleanBuilder.and(qItems.language.eq(query.getLanguage()));
        }*/
        return flag1;
    }

    @Override
    public Page<ItemsDTO> queryByItemCode(ItemsQuery query) {
        BooleanBuilder booleanBuilder = buildWhereItemCode(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<Items> page = repository.findAll(booleanBuilder, pageable);
        List<ItemsDTO> itemsDTOS = convert.toDto(IterableUtils.toList(page));
        List<ItemsDTO> itemsDTOList = itemsDTOS.stream().collect(Collectors.
                collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.
                        comparing(p -> String.valueOf(p.getItemCode())))), ArrayList::new));
        return new PageImpl(itemsDTOList, page.getPageable(), page.getTotalElements());
    }

    @Override
    public Map<String, ItemsDTO> queryByItemCodeAll(ItemsNewQuery query) {
        if (query.getOrganizationId() == null) {
            query.setOrganizationId(ORGANIZATION_ID);
        }
        if (CollectionUtils.isEmpty(query.getItemCodes())) {
            BooleanBuilder booleanBuilder = buildWhereItemCodeNew(query);
            List<String> itemCodes = jpaQueryFactory.select(qItems.itemCode).from(qItems).where(booleanBuilder).fetch();
            query.setItemCodes(itemCodes);
        }
        Map<String, ItemsDTO> resultMap = Maps.newConcurrentMap();
        query.getItemCodes().parallelStream().forEach(itemCode -> {
            ItemsDTO itemsDTO = itemsService.getOneByItemCodeAndOrganizationId(itemCode, query.getOrganizationId());
            if (itemsDTO != null) {
                resultMap.put(itemCode, itemsDTO);
            }
        });
        return resultMap;
    }

    @Override
    public Map<String, ItemsDTO> queryByItemCodeAllNew(ItemsNewQuery query) {
        if (query.getOrganizationId() == null) {
            query.setOrganizationId(ORGANIZATION_ID);
        }
        if (CollectionUtils.isEmpty(query.getItemCodes())) {
            BooleanBuilder booleanBuilder = buildWhereItemCodeNew(query);
            List<String> itemCodes = jpaQueryFactory.select(qItems.itemCode).from(qItems).where(booleanBuilder).fetch();
            query.setItemCodes(itemCodes);
        }
        Map<String, ItemsDTO> resultMap = new ConcurrentHashMap<>();
        query.getItemCodes().parallelStream().forEach(itemCode -> {
            ItemsDTO itemsDTO = itemsService.getOneByItemCodeLifecycleState(itemCode);
            resultMap.put(itemCode, itemsDTO);
        });
        return resultMap;
    }

    @Override
    @Cacheable(cacheNames = "BBOM_items_getOneByItemCodeAndOrganizationId", key = "#itemCode+'_'+#organizationId", unless = "#result == null")
    public ItemsDTO getOneByItemCodeAndOrganizationId(String itemCode, Long organizationId) {
        if (StringUtils.isEmpty(itemCode)) {
            throw new BizException("VALID_ITEM_CODE_NOT_EXIST", itemCode);
        }
        List<String> allItemCodes = Arrays.asList(itemCode.split(","));
        ItemsDTO itemsDTO = convert.toDto(jpaQueryFactory.selectFrom(qItems)
                .where(qItems.itemCode.in(allItemCodes))
                .where(qItems.organizationId.eq(organizationId))
                .fetchFirst());
        return itemsDTO;
    }

    @Override
    public Map<Long, ItemsDTO> queryBySourceItemCode(List<Long> sourceItemId, List<String> categorySegment4) {
        BooleanBuilder booleanBuilder = buildWhereSourceItemId(sourceItemId, categorySegment4);
        List<ItemsDTO> itemsDTOList = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        Map<Long, ItemsDTO> resultMap = new HashMap();
        for (ItemsDTO item : itemsDTOList) {
            resultMap.put(item.getSourceItemId(), item);
        }
        return resultMap;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ItemsQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qItems.id.eq(query.getId()));
        }
        if (query.getItemId() != null) {
            booleanBuilder.and(qItems.itemId.eq(query.getItemId()));
        }
        if (query.getOrganizationId() != null) {
            booleanBuilder.and(qItems.organizationId.eq(query.getOrganizationId()));
        }
        if (StringUtils.isNotEmpty(query.getSourceSystemCode())) {
            booleanBuilder.and(qItems.sourceSystemCode.eq(query.getSourceSystemCode()));
        }
        if (query.getSourceItemId() != null) {
            booleanBuilder.and(qItems.sourceItemId.eq(query.getSourceItemId()));
        }
        if (query.getSourceInvOrgId() != null) {
            booleanBuilder.and(qItems.sourceInvOrgId.eq(query.getSourceInvOrgId()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment1())) {
            booleanBuilder.and(qItems.categorySegment1.eq(query.getCategorySegment1()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment2())) {
            booleanBuilder.and(qItems.categorySegment2.eq(query.getCategorySegment2()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment3())) {
            booleanBuilder.and(qItems.categorySegment3.like(query.getCategorySegment3()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment4())) {
            booleanBuilder.and(qItems.categorySegment4.eq(query.getCategorySegment4()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment5())) {
            booleanBuilder.and(qItems.categorySegment5.eq(query.getCategorySegment5()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qItems.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getItemDesc())) {
            booleanBuilder.and(qItems.itemDesc.eq(query.getItemDesc()));
        }
        if (StringUtils.isNotEmpty(query.getItemStatus())) {
            booleanBuilder.and(qItems.itemStatus.eq(query.getItemStatus()));
        }
        if (StringUtils.isNotEmpty(query.getPriUom())) {
            booleanBuilder.and(qItems.priUom.eq(query.getPriUom()));
        }
        if (StringUtils.isNotEmpty(query.getSegment1())) {
            noneVerify(booleanBuilder, qItems.segment1, query.getSegment1(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment2())) {
            noneVerify(booleanBuilder, qItems.segment2, query.getSegment2(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment3())) {
            noneVerify(booleanBuilder, qItems.segment3, query.getSegment3(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment4())) {
            noneVerify(booleanBuilder, qItems.segment4, query.getSegment4(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment5())) {
            noneVerify(booleanBuilder, qItems.segment5, query.getSegment5(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment6())) {
            noneVerify(booleanBuilder, qItems.segment6, query.getSegment6(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment7())) {
            noneVerify(booleanBuilder, qItems.segment7, query.getSegment7(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment8())) {
            noneVerify(booleanBuilder, qItems.segment8, query.getSegment8(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment9())) {
            noneVerify(booleanBuilder, qItems.segment9, query.getSegment9(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment10())) {
            noneVerify(booleanBuilder, qItems.segment10, query.getSegment10(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment11())) {
            noneVerify(booleanBuilder, qItems.segment11, query.getSegment11(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment12())) {
            noneVerify(booleanBuilder, qItems.segment12, query.getSegment12(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment13())) {
            noneVerify(booleanBuilder, qItems.segment13, query.getSegment13(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment14())) {
            noneVerify(booleanBuilder, qItems.segment14, query.getSegment14(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment15())) {
            noneVerify(booleanBuilder, qItems.segment15, query.getSegment15(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment16())) {
            noneVerify(booleanBuilder, qItems.segment16, query.getSegment16(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment17())) {
            noneVerify(booleanBuilder, qItems.segment17, query.getSegment17(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment18())) {
            noneVerify(booleanBuilder, qItems.segment18, query.getSegment18(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment19())) {
            noneVerify(booleanBuilder, qItems.segment19, query.getSegment19(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment20())) {
            noneVerify(booleanBuilder, qItems.segment20, query.getSegment20(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment21())) {
            noneVerify(booleanBuilder, qItems.segment21, query.getSegment21(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment22())) {
            noneVerify(booleanBuilder, qItems.segment22, query.getSegment22(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment23())) {
            noneVerify(booleanBuilder, qItems.segment23, query.getSegment23(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment24())) {
            noneVerify(booleanBuilder, qItems.segment24, query.getSegment24(), Lists.newArrayList("", "常规"));
        }
        if (StringUtils.isNotEmpty(query.getSegment25())) {
            noneVerify(booleanBuilder, qItems.segment25, query.getSegment25(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment26())) {
            noneVerify(booleanBuilder, qItems.segment26, query.getSegment26(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment27())) {
            noneVerify(booleanBuilder, qItems.segment27, query.getSegment27(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment28())) {
            noneVerify(booleanBuilder, qItems.segment28, query.getSegment28(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment29())) {
            noneVerify(booleanBuilder, qItems.segment29, query.getSegment29(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment30())) {
            noneVerify(booleanBuilder, qItems.segment30, query.getSegment30(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment31())) {
            booleanBuilder.and(qItems.segment31.eq(query.getSegment31()));
        }
        if (StringUtils.isNotEmpty(query.getSegment32())) {
            booleanBuilder.and(qItems.segment32.eq(query.getSegment32()));
        }
        if (StringUtils.isNotEmpty(query.getSegment33())) {
            booleanBuilder.and(qItems.segment33.eq(query.getSegment33()));
        }
        if (StringUtils.isNotEmpty(query.getSegment34())) {
            noneVerify(booleanBuilder, qItems.segment34, query.getSegment34(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment35())) {
            booleanBuilder.and(qItems.segment35.eq(query.getSegment35()));
        }
        if (StringUtils.isNotEmpty(query.getSegment36())) {
            booleanBuilder.and(qItems.segment36.eq(query.getSegment36()));
        }
        if (StringUtils.isNotEmpty(query.getSegment37())) {
            booleanBuilder.and(qItems.segment37.eq(query.getSegment37()));
        }
        if (StringUtils.isNotEmpty(query.getSegment38())) {
            booleanBuilder.and(qItems.segment38.eq(query.getSegment38()));
        }
        if (StringUtils.isNotEmpty(query.getSegment39())) {
            booleanBuilder.and(qItems.segment39.eq(query.getSegment39()));
        }
        if (StringUtils.isNotEmpty(query.getSegment40())) {
            booleanBuilder.and(qItems.segment40.eq(query.getSegment40()));
        }
        if (StringUtils.isNotEmpty(query.getSegment41())) {
            booleanBuilder.and(qItems.segment41.eq(query.getSegment41()));
        }
        if (StringUtils.isNotEmpty(query.getSegment42())) {
            booleanBuilder.and(qItems.segment42.eq(query.getSegment42()));
        }
        if (StringUtils.isNotEmpty(query.getSegment43())) {
            booleanBuilder.and(qItems.segment43.eq(query.getSegment43()));
        }
        if (StringUtils.isNotEmpty(query.getSegment44())) {
            booleanBuilder.and(qItems.segment44.eq(query.getSegment44()));
        }
        if (StringUtils.isNotEmpty(query.getSegment45())) {
            booleanBuilder.and(qItems.segment45.eq(query.getSegment45()));
        }
        if (StringUtils.isNotEmpty(query.getSegment46())) {
            booleanBuilder.and(qItems.segment46.eq(query.getSegment46()));
        }
        if (StringUtils.isNotEmpty(query.getSegment47())) {
            booleanBuilder.and(qItems.segment47.eq(query.getSegment47()));
        }
        if (StringUtils.isNotEmpty(query.getSegment48())) {
            booleanBuilder.and(qItems.segment48.eq(query.getSegment48()));
        }
        if (StringUtils.isNotEmpty(query.getSegment49())) {
            booleanBuilder.and(qItems.segment49.eq(query.getSegment49()));
        }
        if (StringUtils.isNotEmpty(query.getSegment50())) {
            booleanBuilder.and(qItems.segment50.eq(query.getSegment50()));
        }
        if (StringUtils.isNotEmpty(query.getSegment51())) {
            booleanBuilder.and(qItems.segment51.eq(query.getSegment51()));
        }
        if (StringUtils.isNotEmpty(query.getSegment52())) {
            booleanBuilder.and(qItems.segment52.eq(query.getSegment52()));
        }
        if (StringUtils.isNotEmpty(query.getSegment53())) {
            booleanBuilder.and(qItems.segment53.eq(query.getSegment53()));
        }
        if (StringUtils.isNotEmpty(query.getSegment54())) {
            booleanBuilder.and(qItems.segment54.eq(query.getSegment54()));
        }
        if (StringUtils.isNotEmpty(query.getSegment55())) {
            booleanBuilder.and(qItems.segment55.eq(query.getSegment55()));
        }
        if (StringUtils.isNotEmpty(query.getSegment56())) {
            booleanBuilder.and(qItems.segment56.eq(query.getSegment56()));
        }
        if (StringUtils.isNotEmpty(query.getSegment57())) {
            booleanBuilder.and(qItems.segment57.eq(query.getSegment57()));
        }
        if (StringUtils.isNotEmpty(query.getSegment58())) {
            booleanBuilder.and(qItems.segment58.eq(query.getSegment58()));
        }
        if (StringUtils.isNotEmpty(query.getSegment59())) {
            booleanBuilder.and(qItems.segment59.eq(query.getSegment59()));
        }
        if (StringUtils.isNotEmpty(query.getSegment60())) {
            booleanBuilder.and(qItems.segment60.eq(query.getSegment60()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute1())) {
            booleanBuilder.and(qItems.attribute1.eq(query.getAttribute1()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute2())) {
            booleanBuilder.and(qItems.attribute2.eq(query.getAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute3())) {
            booleanBuilder.and(qItems.attribute3.eq(query.getAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute4())) {
            booleanBuilder.and(qItems.attribute4.eq(query.getAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute5())) {
            booleanBuilder.and(qItems.attribute5.eq(query.getAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute6())) {
            booleanBuilder.and(qItems.attribute6.eq(query.getAttribute6()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute7())) {
            booleanBuilder.and(qItems.attribute7.eq(query.getAttribute7()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute8())) {
            booleanBuilder.and(qItems.attribute8.eq(query.getAttribute8()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute9())) {
            booleanBuilder.and(qItems.attribute9.eq(query.getAttribute9()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute10())) {
            booleanBuilder.and(qItems.attribute10.eq(query.getAttribute10()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute11())) {
            booleanBuilder.and(qItems.attribute11.eq(query.getAttribute11()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute12())) {
            booleanBuilder.and(qItems.attribute12.eq(query.getAttribute12()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute13())) {
            booleanBuilder.and(qItems.attribute13.eq(query.getAttribute13()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute14())) {
            booleanBuilder.and(qItems.attribute14.eq(query.getAttribute14()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute15())) {
            booleanBuilder.and(qItems.attribute15.eq(query.getAttribute15()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute16())) {
            booleanBuilder.and(qItems.attribute16.eq(query.getAttribute16()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute17())) {
            booleanBuilder.and(qItems.attribute17.eq(query.getAttribute17()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute18())) {
            booleanBuilder.and(qItems.attribute18.eq(query.getAttribute18()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute19())) {
            booleanBuilder.and(qItems.attribute19.eq(query.getAttribute19()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute20())) {
            booleanBuilder.and(qItems.attribute20.eq(query.getAttribute20()));
        }
        if (StringUtils.isNotEmpty(query.getLanguage())) {
            booleanBuilder.and(qItems.language.eq(query.getLanguage()));
        }
        if (StringUtils.isNotEmpty(query.getItemType())) {
            booleanBuilder.and(qItems.itemType.eq(query.getItemType()));
        }
        if (StringUtils.isNotEmpty(query.getCustomerOrderFlag())) {
            booleanBuilder.and(qItems.customerOrderFlag.eq(query.getCustomerOrderFlag()));
        }
        if (StringUtils.isNotEmpty(query.getItemSubcategory())) {
            booleanBuilder.and(qItems.itemSubcategory.eq(query.getItemSubcategory()));
        }
        if (StringUtils.isNotEmpty(query.getLifecycleState())) {
            booleanBuilder.and(qItems.lifecycleState.eq(query.getLifecycleState()));
        }
    }

    private void noneVerify(BooleanBuilder booleanBuilder, StringPath stringPath, String segment, List<String> specialMatch) {
        if (segment.equals(none)) {
            specialMatch.add(none);
            booleanBuilder.and(stringPath.in(specialMatch).or(stringPath.isNull()));
        } else {
            booleanBuilder.and(stringPath.eq(segment));
        }
    }

    //数据库DTO的实体值、query对象的值
    private boolean noneVerify2(Boolean flag, String value, String segment, String specialValue, ItemsDTO itemsDTO) {
        if (!flag) return false;
        boolean result;
        if (((none.equals(segment) || StringUtils.isEmpty(segment)) && (none.equals(value) || StringUtils.isEmpty(value))) && StringUtils.isEmpty(specialValue)) {
            result = true;
        } else {
            if (StringUtils.isNotEmpty(specialValue)) {
                result = StringUtils.isNotEmpty(value) && value.equals(specialValue);
            } else {
                result = StringUtils.isNotEmpty(value) && value.equals(segment);
            }
        }
        if (!result) {
            log.info("料号匹配失败 {} 原因 : value:{},segment:{},specialValue:{}", itemsDTO.getItemCode(), value, segment, specialValue);
        }
        return result;
    }

    private boolean notHaveVerify(Boolean flag, String value, String segment, String specialValue, ItemsDTO itemsDTO) {
        if (!flag) return false;
        if(StringUtils.isNotBlank(value) && StringUtils.isNotBlank(segment) && value.equals(segment)) {
            return true;
        }else if(StringUtils.isBlank(value) && StringUtils.isBlank(segment)) {
            return true;
        }else{
            log.info("料号匹配失败 {} 原因 : value:{},segment:{},specialValue:{}", itemsDTO.getItemCode(), value, segment, specialValue);
            return false;
        }
    }

    //数据库DTO的实体值、query对象的值
    private boolean noneVerify2(Boolean flag, String value, String segment, List<String> specialValues) {
        if (!flag) return false;
        if (((none.equals(segment) || StringUtils.isEmpty(segment)) && (none.equals(value) || StringUtils.isEmpty(value))) && CollectionUtils.isEmpty(specialValues)) {
            return true;
        } else {
            if (CollectionUtils.isNotEmpty(specialValues)) {
                Boolean containFlag = false;
                if (value.contains("LECO")) {
                    Long containCount = specialValues.stream().filter(ele -> value.contains(ele)).collect(Collectors.counting());
                    containFlag = containCount > 0;
                }
                return StringUtils.isNotEmpty(value) && (specialValues.contains(value) || containFlag);
            }
            return StringUtils.isNotEmpty(value) && value.equals(segment);
        }
    }

    @Override
    public ItemsDTO queryById(Long id) {
        Items queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ItemsDTO save(ItemsSaveDTO saveDTO) {
        Items newObj = Optional.ofNullable(saveDTO.getItemId())
                .map(id -> repository.getOne(saveDTO.getItemId()))
                .orElse(new Items());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getItemId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ItemsQuery query, HttpServletResponse response) {
        List<ItemsDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "物料基础数据表", "物料基础数据表", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public void importByExternalItems(ExternalItemsDTO externalItems) {
        String categorySegment4 = externalItems.getCategorySegment4();
        Items items = repository.findBySourceItemIdAndOrganizationIdAndLanguage(
                externalItems.getSourceItemId(),
                externalItems.getOrganizationId(),
                externalItems.getLanguage()
        );

        if (items == null) {
            items = new Items();
        }

        items.setOrganizationId(externalItems.getOrganizationId());
        items.setSourceSystemCode(externalItems.getSourceSystemCode());
        items.setSourceItemId(externalItems.getSourceItemId());
        items.setSourceInvOrgId(externalItems.getSourceInvOrgId());
        items.setCategorySegment1(externalItems.getCategorySegment1());
        items.setCategorySegment2(externalItems.getCategorySegment2());
        items.setCategorySegment3(externalItems.getCategorySegment3());
        items.setCategorySegment4(externalItems.getCategorySegment4());
        items.setItemCode(externalItems.getItemCode());
        items.setItemType(externalItems.getItemType());
        items.setLanguage(externalItems.getLanguage());
        // todo check 值是否为空的判断
        items.setLifecycleState(externalItems.getLifecycleState());
        items.setIsTemporaryOutput(externalItems.getIsTemporaryOutput());

        for (int i = 1; i < 21; i++) {
            String attr = getAttribute(i);
            try {
                String value = org.apache.commons.beanutils.BeanUtils.getProperty(externalItems, attr);
                if (StringUtils.isNotBlank(value)) {
                    BeanUtil.setFieldValue(items, attr, value);
                }
            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                e.printStackTrace();
                log.trace(e.getMessage());
            }
        }
        items.setItemDesc(externalItems.getItemDesc());
        items.setItemStatus(externalItems.getItemStatus());
        items.setPriUom(externalItems.getPriUom());

        List<ExternalAttrMapViewDTO> externalAttrMapViewList = bomService.findBySrcCategorySegment4(categorySegment4);

        if (CollectionUtils.isEmpty(externalAttrMapViewList)) {
            for (int i = 1; i < 61; i++) {
                String attr = getSegment(i);
                try {
                    String value = org.apache.commons.beanutils.BeanUtils.getProperty(externalItems, attr);
                    if (StringUtils.isNotBlank(value)) {
                        BeanUtil.setFieldValue(items, attr, value);
                    }
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                    e.printStackTrace();
                    log.trace(e.getMessage());
                }
            }
            ItemsSaveDTO itemsSaveDTO = new ItemsSaveDTO();
            synchronized (this) {
                Items findItems = repository.findBySourceItemIdAndOrganizationIdAndLanguage(
                        items.getSourceItemId(), items.getOrganizationId(), items.getLanguage()
                );
                if (findItems != null) {
                    itemsSaveDTO.setItemId(findItems.getItemId());
                }
                BeanUtils.copyProperties(items, itemsSaveDTO, "itemId", "lifecycleState");
                save(itemsSaveDTO);
            }
            return;
        }

        Set<String> attrTypeCnNameSet = new HashSet<>();
        boolean setFieldValueFlag = false;

        for (ExternalAttrMapViewDTO externalAttrMapView : externalAttrMapViewList) {
            // segment1
            String sourceColumn = externalAttrMapView.getSourceColumn();
            if (StringUtils.isEmpty(sourceColumn)) {
                log.info("错误: StringUtils.isEmpty(sourceColumn)");
                throw new BizException("错误: externalAttrMapView.getSourceColumn() is null : " + JSON.toJSONString(externalAttrMapView));
            }

            // segment8
            String srcAttrColumn = externalAttrMapView.getSrcAttrColumn();

            String srcAttrColumnValue = (String) com.trinasolar.scp.common.api.util.BeanUtils.getFieldValue(externalItems, srcAttrColumn);

            com.trinasolar.scp.common.api.util.BeanUtils.setFieldValue(items, sourceColumn, srcAttrColumnValue);


            setFieldValueFlag = true;
            attrTypeCnNameSet.add(externalAttrMapView.getSrcAttrValue());
        }

        // 同一个物料的多个属性对应的属性行肯定都属于同一个属性头，如不是，数据有问题
        if (setFieldValueFlag || attrTypeCnNameSet.size() == 1) {
            ItemsSaveDTO itemsSaveDTO = new ItemsSaveDTO();
            BeanUtils.copyProperties(items, itemsSaveDTO);
            itemsSaveDTO.setCategorySegment5(attrTypeCnNameSet.iterator().next());

            save(itemsSaveDTO);
            log.info("成功保存");
        } else {
            throw new BizException("错误: setFieldValueFlag || attrTypeCnNameSet.size() == 1");
        }
    }

    @NonNull
    @Override
    public ItemsDTO findOneByItemCode(String itemCode) {
        if (StringUtils.isBlank(itemCode)) {
            throw new IllegalArgumentException("物料Code不能为空");
        }
        return itemsService.getOneByItemCode(itemCode);
    }

    @Override
    @Cacheable(cacheNames = "BBOM_items_findOneByItemCode", key = "#itemCode", unless = "#result == null")
    public ItemsDTO getOneByItemCode(String itemCode) {
        if (StringUtils.isEmpty(itemCode)) {
            throw new BizException("VALID_ITEM_CODE_NOT_EXIST", itemCode);
        }
        List<String> allItemCodes = Arrays.asList(itemCode.split(","));
        ItemsDTO itemsDTO = convert.toDto(jpaQueryFactory.selectFrom(qItems)
                .where(qItems.itemCode.in(allItemCodes))
                .orderBy(qItems.id.desc())
                .fetchFirst());
        if (itemsDTO == null) {
            throw new BizException("VALID_ITEM_CODE_NOT_EXIST", itemCode);
        }
        return itemsDTO;
    }

    @Override
    @Cacheable(cacheNames = "BBOM_items_getOneByItemCodeLifecycleState", key = "#itemCode", unless = "#result == null")
    public ItemsDTO getOneByItemCodeLifecycleState(String itemCode) {
        ItemsDTO itemsDTO = convert.toDto(jpaQueryFactory.selectFrom(qItems)
                .where(qItems.itemCode.eq(itemCode))
                .where(qItems.lifecycleState.isNotNull())
                .orderBy(qItems.id.desc())
                .fetchFirst());
        if (itemsDTO == null) {
           return new ItemsDTO();
        }
        return itemsDTO;
    }

    @Override
    public StructureItemVO getOneBySourceItemId(Long sourceItemId) {
        Items items = jpaQueryFactory.selectFrom(qItems)
                .where(qItems.sourceItemId.eq(sourceItemId))
                .where(qItems.itemStatus.eq("Active"))
                .where(qItems.organizationId.eq(ORGANIZATION_ID)).fetchFirst();
        return convert.itemToStructureItemVO(items);
    }

    private String getAttribute(int i) {
        return attributes[i - 1];
    }

    private String getSegment(int i) {
        return segments[i - 1];
    }

    public List<Items> batchSaveUpdate(List<ItemsDTO> saveDTO) {
        // 业务要求：数据库表中存在电池类型编码不做修改
        List<Items> itemsDTOList = repository.saveAll(convert.toEntity(saveDTO));
        return itemsDTOList;
    }

    public void ItemLifecycleStateSync(LocalDate date) {
        log.info("ItemLifecycleStateSync date {}", date.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Map<String, Object> map = new HashMap<String, Object>();
        //获取当天日期 fmt.format(localDateTime)
        int pageNumber = 1;
        List<Items> itemsAll = jpaQueryFactory.selectFrom(qItems).fetch();

        while (true) {
            map.put("startUpdateTime", fmt.format(date));
            map.put("endUpdateTime", fmt.format(date.plusDays(1L)));
            map.put("pageSize", PAGE_SIZE);
            map.put("pageNumber", pageNumber);
            Map<String, Object> objectMap = externalFeign.queryItemService(map);
            log.info("物料生命周期同步开始 --->" + date + " - " + pageNumber + " 页");
            pageNumber++;
            try {
                Map<Long, Items> updItems = new HashMap<>();
                if (null != objectMap) {
                    if (objectMap.get("code") instanceof Integer) {
                        Integer value = (Integer) objectMap.get("code"); // cast obj to String
                        if (value.equals(0)) {
                            JSONObject jsonObject = (JSONObject) objectMap.get("data");
                            JSONArray partInfos = jsonObject.getJSONArray("partInfos");
                            if (partInfos == null) {
                                return;
                            }
                            List<ItemsLiftCycleDTO> filterList = JSON.parseArray(JSON.toJSONString(partInfos), ItemsLiftCycleDTO.class);
                            if (CollectionUtils.isEmpty(filterList)) {
                                return;
                            }
                            pdmItemLifecycleService.saveLifeCycle(date, filterList);

                            Map<String, Items> keyAndItemMap = itemsAll.stream().collect(Collectors.toMap(i -> i.getItemCode() + "_" + i.getOrganizationId(), i -> i, (k1, k2) -> k1));

                            Map<String, ItemsSaveDTO> lifecycleSaveDTOMap = Maps.newHashMap();
                            filterList.stream().forEach(x -> {
//                            Items items = jpaQueryFactory.selectFrom(qItems)
//                                    .where(qItems.itemCode.eq(x.getNumber())
//                                            .and(qItems.organizationId.eq(x.getView()))
//                                            .and(qItems.isDeleted.eq(0)))
//                                    .fetchFirst();
                                log.warn("料号{}---orgId{}", x.getNumber(), x.getView());
                                Items items = keyAndItemMap.get(x.getNumber() + "_" + x.getView());
                                if (null != items && (StringUtils.isBlank(items.getLifecycleState()) || !Objects.equals(items.getLifecycleState(), x.getStage())
                                        || StringUtils.isBlank(items.getIsTemporaryOutput()) || !Objects.equals(items.getIsTemporaryOutput(), x.getIsTemporaryOutput()))) {
                                    log.warn("items对象料号{}---orgId{}----LifecycleState{}", items.getItemCode(), items.getItemCode(), items.getLifecycleState());
                                    items.setLifecycleState(x.getStage());
                                    items.setIsTemporaryOutput(x.getIsTemporaryOutput());
                                    //updItems.put(items.getId(), items);

                                    repository.save(items);
                                    log.warn("更新结束!");
                                }
                                // 同步给一期
                                ItemsSaveDTO itemsSaveDTO = new ItemsSaveDTO();
                                itemsSaveDTO.setItemCode(x.getNumber());
                                itemsSaveDTO.setOrganizationId(x.getView());
                                itemsSaveDTO.setLifecycleState(x.getStage());
                                itemsSaveDTO.setIsTemporaryOutput(x.getIsTemporaryOutput());
                                String groupKey = StringUtils.join(itemsSaveDTO.getItemCode(), itemsSaveDTO.getOrganizationId());
                                lifecycleSaveDTOMap.put(groupKey, itemsSaveDTO);
                            });

                            List<List<ItemsSaveDTO>> splitList = CollUtil.split(lifecycleSaveDTOMap.values(), 50);
                            splitList.forEach(splitLifecycleSaveDTOS -> {
                                try {
                                    // 更新同步一期组件生命周期状态
                                    bomFeign.syncLifecycleBatchSave(splitLifecycleSaveDTOS);
                                } catch (Exception ex) {
                                    log.error("ItemLifecycleStateSync syncLifecycleBatchSave params {} error ", JSON.toJSONString(splitLifecycleSaveDTOS), ex);
                                    throw ex;
                                }
                            });
                           /*  if (!updItems.isEmpty()) {
                                log.warn("updItems{}", JSON.toJSONString(updItems));
                                repository.saveAll(updItems.values());
                            } */
                        }
                    }
                }

            } catch (Exception e) {
                log.error("定时任务ItemCodeSyncJobHandler-物料生命周期同步发生错误----- 参数;{}", map, e);
                return;
            }
        }
    }

    @Override
    public Map<String, List<ItemsDTO>> queryStructureItemByComponentItem(BatchItemQuery query) {
        return query.getItemQueryList().stream().collect(
                Collectors.toMap(i -> StringUtils.join(i.getItemCode(), "_", i.getOrganizationId()),
                        i -> {
                            // 查询7A物料
                            Items items = jpaQueryFactory.selectFrom(qItems)
                                    .where(qItems.itemCode.eq(i.getItemCode()))
                                    .where(qItems.organizationId.eq(i.getOrganizationId()))
                                    .where(qItems.itemStatus.eq("Active"))
                                    .fetchFirst();
                            // 然后去查询bomComponent
                            List<StructuresDTO> structuresDTO = componentsService.findBomStructureByComponentItemIdAndOrganizationId(items.getSourceItemId(), i.getOrganizationId());
                            if (CollectionUtils.isEmpty(structuresDTO)) {
                                return new ArrayList<>();
                            }
                            List<Items> itemsList = jpaQueryFactory.selectFrom(qItems)
                                    .where(qItems.sourceItemId.in(structuresDTO.stream().map(StructuresDTO::getAssemblyItemId).collect(Collectors.toList())))
                                    .where(qItems.itemStatus.eq("Active"))
                                    .where(qItems.organizationId.eq(i.getOrganizationId())).fetch();
                            return itemsList.stream().map(k -> {
                                ItemsDTO itemsDTO = new ItemsDTO();
                                itemsDTO.setId(k.getId());
                                itemsDTO.setSourceItemId(k.getSourceItemId());
                                itemsDTO.setItemCode(k.getItemCode());
                                itemsDTO.setItemDesc(k.getItemDesc());
                                return itemsDTO;
                            }).collect(Collectors.toList());
                        }, (k1, k2) -> k1));
    }

    @Override
    @Cacheable(cacheNames = "BBOM_items_findOneByItemCodeAndOrganizationId", key = "#itemCode + '_' + #organizationId", unless = "#result == null")
    public ItemsDTO findOneByItemCodeAndOrganizationId(String itemCode, Long organizationId) {
        Items items = jpaQueryFactory.selectFrom(qItems)
                .where(qItems.itemCode.eq(itemCode))
                .where(qItems.organizationId.eq(organizationId))
                .where(qItems.itemStatus.eq("Active"))
                .fetchFirst();
        return convert.toDto(items);
    }

    private void buildWhereByMatchAll(BooleanBuilder booleanBuilder) {
        booleanBuilder.and(qItems.categorySegment2.eq("自产电池片"));
        booleanBuilder.and(qItems.organizationId.eq(82L));
        booleanBuilder.and(qItems.itemStatus.eq("Active"));
        booleanBuilder.and(qItems.itemCode.like("5A%"));
    }

    private void buildWhereByMatch(BooleanBuilder booleanBuilder, ItemsQuery query) {
        booleanBuilder.and(qItems.categorySegment2.eq("自产电池片"));
        booleanBuilder.and(qItems.organizationId.eq(82L));
        booleanBuilder.and(qItems.itemStatus.eq("Active"));
        if (query.getId() != null) {
            booleanBuilder.and(qItems.id.eq(query.getId()));
        }
        if (query.getItemId() != null) {
            booleanBuilder.and(qItems.itemId.eq(query.getItemId()));
        }
        if (StringUtils.isNotEmpty(query.getSourceSystemCode())) {
            booleanBuilder.and(qItems.sourceSystemCode.eq(query.getSourceSystemCode()));
        }
        if (query.getSourceItemId() != null) {
            booleanBuilder.and(qItems.sourceItemId.eq(query.getSourceItemId()));
        }
        if (query.getSourceInvOrgId() != null) {
            booleanBuilder.and(qItems.sourceInvOrgId.eq(query.getSourceInvOrgId()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment1())) {
            booleanBuilder.and(qItems.categorySegment1.eq(query.getCategorySegment1()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment2())) {
            booleanBuilder.and(qItems.categorySegment2.eq(query.getCategorySegment2()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment3())) {
            booleanBuilder.and(qItems.categorySegment3.like(query.getCategorySegment3()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment4())) {
            booleanBuilder.and(qItems.categorySegment4.eq(query.getCategorySegment4()));
        }
        if (StringUtils.isNotEmpty(query.getCategorySegment5())) {
            booleanBuilder.and(qItems.categorySegment5.eq(query.getCategorySegment5()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qItems.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getItemDesc())) {
            booleanBuilder.and(qItems.itemDesc.eq(query.getItemDesc()));
        }
        if (StringUtils.isNotEmpty(query.getItemStatus())) {
            booleanBuilder.and(qItems.itemStatus.eq(query.getItemStatus()));
        }
        if (StringUtils.isNotEmpty(query.getPriUom())) {
            booleanBuilder.and(qItems.priUom.eq(query.getPriUom()));
        }
        if (StringUtils.isNotEmpty(query.getSegment1())) {
            noneVerify(booleanBuilder, qItems.segment1, query.getSegment1(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment1.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment2())) {
            noneVerify(booleanBuilder, qItems.segment2, query.getSegment2(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment3())) {
            noneVerify(booleanBuilder, qItems.segment3, query.getSegment3(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment3.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment4())) {
            noneVerify(booleanBuilder, qItems.segment4, query.getSegment4(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment5())) {
            noneVerify(booleanBuilder, qItems.segment5, query.getSegment5(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment6())) {
            noneVerify(booleanBuilder, qItems.segment6, query.getSegment6(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment7())) {
            noneVerify(booleanBuilder, qItems.segment7, query.getSegment7(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment8())) {
            noneVerify(booleanBuilder, qItems.segment8, query.getSegment8(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment9())) {
            noneVerify(booleanBuilder, qItems.segment9, query.getSegment9(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment10())) {
            noneVerify(booleanBuilder, qItems.segment10, query.getSegment10(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment10.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment11())) {
            noneVerify(booleanBuilder, qItems.segment11, query.getSegment11(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment11.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment12())) {
            noneVerify(booleanBuilder, qItems.segment12, query.getSegment12(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment12.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment13())) {
            noneVerify(booleanBuilder, qItems.segment13, query.getSegment13(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment14())) {
            noneVerify(booleanBuilder, qItems.segment14, query.getSegment14(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment15())) {
            noneVerify(booleanBuilder, qItems.segment15, query.getSegment15(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment16())) {
            noneVerify(booleanBuilder, qItems.segment16, query.getSegment16(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment17())) {
            noneVerify(booleanBuilder, qItems.segment17, query.getSegment17(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment18())) {
            noneVerify(booleanBuilder, qItems.segment18, query.getSegment18(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment19())) {
            noneVerify(booleanBuilder, qItems.segment19, query.getSegment19(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment20())) {
            noneVerify(booleanBuilder, qItems.segment20, query.getSegment20(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment21())) {
            noneVerify(booleanBuilder, qItems.segment21, query.getSegment21(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment21.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment22()) && !"无".equals(query.getSegment22())) {
            noneVerify(booleanBuilder, qItems.segment22, query.getSegment22(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment22.isNull().or(qItems.segment22.eq("A")));
        }
        if (StringUtils.isNotEmpty(query.getSegment23())) {
            noneVerify(booleanBuilder, qItems.segment23, query.getSegment23(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment23.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment24())) {
            noneVerify(booleanBuilder, qItems.segment24, query.getSegment24(), Lists.newArrayList("", "常规"));
        } else {
            booleanBuilder.and(qItems.segment24.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment25())) {
            noneVerify(booleanBuilder, qItems.segment25, query.getSegment25(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment26())) {
            noneVerify(booleanBuilder, qItems.segment26, query.getSegment26(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment27())) {
            noneVerify(booleanBuilder, qItems.segment27, query.getSegment27(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment28())) {
            noneVerify(booleanBuilder, qItems.segment28, query.getSegment28(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment29()) && !"无".equals(query.getSegment29())) {
            noneVerify(booleanBuilder, qItems.segment29, query.getSegment29(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment30()) && !"无".equals(query.getSegment30())) {
            noneVerify(booleanBuilder, qItems.segment30, query.getSegment30(), Lists.newArrayList(""));
        }
        if (StringUtils.isNotEmpty(query.getSegment31())) {
            booleanBuilder.and(qItems.segment31.eq(query.getSegment31()));
        }
        if (StringUtils.isNotEmpty(query.getSegment32())) {
            booleanBuilder.and(qItems.segment32.eq(query.getSegment32()));
        }
        if (StringUtils.isNotEmpty(query.getSegment33())) {
            booleanBuilder.and(qItems.segment33.eq(query.getSegment33()));
        }
        if (StringUtils.isNotEmpty(query.getSegment34())) {
            noneVerify(booleanBuilder, qItems.segment34, query.getSegment34(), Lists.newArrayList(""));
        } else {
            booleanBuilder.and(qItems.segment34.isNull());
        }
        if (StringUtils.isNotEmpty(query.getSegment35())) {
            booleanBuilder.and(qItems.segment35.eq(query.getSegment35()));
        }
        if (StringUtils.isNotEmpty(query.getSegment36())) {
            booleanBuilder.and(qItems.segment36.eq(query.getSegment36()));
        }
        if (StringUtils.isNotEmpty(query.getSegment37())) {
            booleanBuilder.and(qItems.segment37.eq(query.getSegment37()));
        }
        if (StringUtils.isNotEmpty(query.getSegment38())) {
            booleanBuilder.and(qItems.segment38.eq(query.getSegment38()));
        }
        if (StringUtils.isNotEmpty(query.getSegment39())) {
            booleanBuilder.and(qItems.segment39.eq(query.getSegment39()));
        }
        if (StringUtils.isNotEmpty(query.getSegment40())) {
            booleanBuilder.and(qItems.segment40.eq(query.getSegment40()));
        }
        if (StringUtils.isNotEmpty(query.getSegment41())) {
            booleanBuilder.and(qItems.segment41.eq(query.getSegment41()));
        }
        if (StringUtils.isNotEmpty(query.getSegment42())) {
            booleanBuilder.and(qItems.segment42.eq(query.getSegment42()));
        }
        if (StringUtils.isNotEmpty(query.getSegment43())) {
            booleanBuilder.and(qItems.segment43.eq(query.getSegment43()));
        }
        if (StringUtils.isNotEmpty(query.getSegment44())) {
            booleanBuilder.and(qItems.segment44.eq(query.getSegment44()));
        }
        if (StringUtils.isNotEmpty(query.getSegment45())) {
            booleanBuilder.and(qItems.segment45.eq(query.getSegment45()));
        }
        if (StringUtils.isNotEmpty(query.getSegment46())) {
            booleanBuilder.and(qItems.segment46.eq(query.getSegment46()));
        }
        if (StringUtils.isNotEmpty(query.getSegment47())) {
            booleanBuilder.and(qItems.segment47.eq(query.getSegment47()));
        }
        if (StringUtils.isNotEmpty(query.getSegment48())) {
            booleanBuilder.and(qItems.segment48.eq(query.getSegment48()));
        }
        if (StringUtils.isNotEmpty(query.getSegment49())) {
            booleanBuilder.and(qItems.segment49.eq(query.getSegment49()));
        }
        if (StringUtils.isNotEmpty(query.getSegment50())) {
            booleanBuilder.and(qItems.segment50.eq(query.getSegment50()));
        }
        if (StringUtils.isNotEmpty(query.getSegment51())) {
            booleanBuilder.and(qItems.segment51.eq(query.getSegment51()));
        }
        if (StringUtils.isNotEmpty(query.getSegment52())) {
            booleanBuilder.and(qItems.segment52.eq(query.getSegment52()));
        }
        if (StringUtils.isNotEmpty(query.getSegment53())) {
            booleanBuilder.and(qItems.segment53.eq(query.getSegment53()));
        }
        if (StringUtils.isNotEmpty(query.getSegment54())) {
            booleanBuilder.and(qItems.segment54.eq(query.getSegment54()));
        }
        if (StringUtils.isNotEmpty(query.getSegment55())) {
            booleanBuilder.and(qItems.segment55.eq(query.getSegment55()));
        }
        if (StringUtils.isNotEmpty(query.getSegment56())) {
            booleanBuilder.and(qItems.segment56.eq(query.getSegment56()));
        }
        if (StringUtils.isNotEmpty(query.getSegment57())) {
            booleanBuilder.and(qItems.segment57.eq(query.getSegment57()));
        }
        if (StringUtils.isNotEmpty(query.getSegment58())) {
            booleanBuilder.and(qItems.segment58.eq(query.getSegment58()));
        }
        if (StringUtils.isNotEmpty(query.getSegment59())) {
            booleanBuilder.and(qItems.segment59.eq(query.getSegment59()));
        }
        if (StringUtils.isNotEmpty(query.getSegment60())) {
            booleanBuilder.and(qItems.segment60.eq(query.getSegment60()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute1())) {
            booleanBuilder.and(qItems.attribute1.eq(query.getAttribute1()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute2())) {
            booleanBuilder.and(qItems.attribute2.eq(query.getAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute3())) {
            booleanBuilder.and(qItems.attribute3.eq(query.getAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute4())) {
            booleanBuilder.and(qItems.attribute4.eq(query.getAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute5())) {
            booleanBuilder.and(qItems.attribute5.eq(query.getAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute6())) {
            booleanBuilder.and(qItems.attribute6.eq(query.getAttribute6()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute7())) {
            booleanBuilder.and(qItems.attribute7.eq(query.getAttribute7()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute8())) {
            booleanBuilder.and(qItems.attribute8.eq(query.getAttribute8()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute9())) {
            booleanBuilder.and(qItems.attribute9.eq(query.getAttribute9()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute10())) {
            booleanBuilder.and(qItems.attribute10.eq(query.getAttribute10()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute11())) {
            booleanBuilder.and(qItems.attribute11.eq(query.getAttribute11()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute12())) {
            booleanBuilder.and(qItems.attribute12.eq(query.getAttribute12()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute13())) {
            booleanBuilder.and(qItems.attribute13.eq(query.getAttribute13()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute14())) {
            booleanBuilder.and(qItems.attribute14.eq(query.getAttribute14()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute15())) {
            booleanBuilder.and(qItems.attribute15.eq(query.getAttribute15()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute16())) {
            booleanBuilder.and(qItems.attribute16.eq(query.getAttribute16()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute17())) {
            booleanBuilder.and(qItems.attribute17.eq(query.getAttribute17()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute18())) {
            booleanBuilder.and(qItems.attribute18.eq(query.getAttribute18()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute19())) {
            booleanBuilder.and(qItems.attribute19.eq(query.getAttribute19()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute20())) {
            booleanBuilder.and(qItems.attribute20.eq(query.getAttribute20()));
        }
        if (StringUtils.isNotEmpty(query.getLanguage())) {
            booleanBuilder.and(qItems.language.eq(query.getLanguage()));
        }
        if (StringUtils.isNotEmpty(query.getItemType())) {
            booleanBuilder.and(qItems.itemType.eq(query.getItemType()));
        }
        if (StringUtils.isNotEmpty(query.getCustomerOrderFlag())) {
            booleanBuilder.and(qItems.customerOrderFlag.eq(query.getCustomerOrderFlag()));
        }
        if (StringUtils.isNotEmpty(query.getItemSubcategory())) {
            booleanBuilder.and(qItems.itemSubcategory.eq(query.getItemSubcategory()));
        }
        if (StringUtils.isNotEmpty(query.getLifecycleState())) {
            booleanBuilder.and(qItems.lifecycleState.eq(query.getLifecycleState()));
        }
    }

    public List<String> getCellsTypeBy7AItemCodes(List<String> itemCodes) {
        List<Items> itemsList = jpaQueryFactory.select(qItems).from(qItems).where(qItems.itemCode.in(itemCodes).and(qItems.isDeleted.eq(0)).and(qItems.organizationId.eq(82L))).fetch();
        List<Long> compentIds = itemsList.stream().map(ele -> ele.getSourceItemId()).distinct().collect(Collectors.toList());
        List<Components> componentsList = jpaQueryFactory.select(QComponents.components).from(QComponents.components).where(QComponents.components.componentItemId.in(compentIds).and(QComponents.components.isDeleted.eq(0))).fetch();
        List<Long> componentsId = componentsList.stream().map(ele -> ele.getBomId()).distinct().collect(Collectors.toList());
        List<Structures> structuresList = jpaQueryFactory.select(QStructures.structures).from(QStructures.structures).where(QStructures.structures.id.in(componentsId).and(QStructures.structures.isDeleted.eq(0))).fetch();
        List<Long> itemCodesBy5AId = structuresList.stream().map(ele -> ele.getAssemblyItemId()).distinct().collect(Collectors.toList());
        List<Items> queryItemsListBySourceItemId = jpaQueryFactory.select(qItems).from(qItems).where(qItems.sourceItemId.in(itemCodesBy5AId).and(qItems.isDeleted.eq(0)).and(qItems.organizationId.eq(82L))).fetch();
        List<String> cellsTypes = queryItemsListBySourceItemId.stream().map(ele -> ele.getSegment60()).distinct().collect(Collectors.toList());
        return cellsTypes;
    }

    /**
     * 查询返工的5A物料
     *
     * @param itemsDTO
     */
    public List<ItemsDTO> findReworkItemCode(ItemsDTO itemsDTO) {

        itemsDTO = findOneByItemCode(itemsDTO.getItemCode());
        List<ItemsDTO> itemsList = redissonClient.getList("filterList");
        List<ItemsDTO> getFilterList = getFilterItemList(itemsList, itemsDTO);
        if (CollectionUtils.isNotEmpty(getFilterList)) {
            return getFilterList;
        } else {
            //清除缓存数据
            redissonClient.getList("filterList").clear();
            //第一步 查询82  5A 自产电池片 返工的料号
            JPAQuery<Items> jpaQuery = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.organizationId.eq(82L)
                            .and(qItems.itemCode.like("5A%"))
                            .and(qItems.categorySegment2.eq("自产电池片"))
                            .and(qItems.itemStatus.eq("Active"))
                            .and(qItems.segment27.eq("返工片")));

            List<ItemsDTO> itemsDTOList = convert.toDto(jpaQuery.fetch());
            List<ItemsDTO> filterList = getFilterItemList(itemsDTOList, itemsDTO);
            RList<ItemsDTO> redisItems = redissonClient.getList("filterList");
            redisItems.addAll(filterList);
            return filterList;
        }
    }

    @Override
    @Cacheable(cacheNames = "BBOM_items_findOneByItemId", key = "#itemId", unless = "#result == null")
    public ItemsDTO findOneByItemId(Long itemId) {
        Items items = jpaQueryFactory.selectFrom(qItems)
                .where(qItems.sourceItemId.eq(itemId))
                .where(qItems.itemStatus.eq("Active"))
                .where(qItems.organizationId.eq(ORGANIZATION_ID)).fetchFirst();
        return convert.toDto(items);
    }

    @Override
    public Map<String, String> findLifecycleStateByItems(List<String> itemCodes) {
        List<Items> itemsList = jpaQueryFactory.selectFrom(qItems)
                .where(qItems.itemCode.in(itemCodes)).fetch();
        return itemsList.stream().filter(k -> StringUtils.isNotBlank(k.getLifecycleState())).collect(Collectors.toMap(Items::getItemCode, Items::getLifecycleState, (a, b) -> a));
    }

    /**
     * 根据料号获取物料详细（for baps）
     *
     * @param itemCodes
     * @param productGrades
     * @return
     */
    @Override
    public List<ItemsDTO> findItemsByItemCodeAndProductGrades(List<String> itemCodes, List<String> productGrades, Long organizationId) {
        if (CollectionUtils.isEmpty(itemCodes)) {
            return Lists.newArrayList();
        }
        JPAQuery<Items> where = jpaQueryFactory.select(qItems).from(qItems).where(qItems.itemCode.in(itemCodes));
        if (CollectionUtils.isNotEmpty(productGrades)) {
            where.where(qItems.segment2.in(productGrades));
        }
        if (Objects.nonNull(organizationId)) {
            where.where(qItems.organizationId.eq(organizationId));
        }
        List<Items> fetch = where.fetch();
        if (CollectionUtils.isNotEmpty(fetch)) {
            return convert.toDto(fetch);
        } else {
            return Lists.newArrayList();
        }

    }

    public List<ItemsDTO> getFilterItemList(List<ItemsDTO> itemsDTOList, ItemsDTO itemsDTO) {
        List<ItemsDTO> filterList = itemsDTOList.parallelStream().filter(y -> filterListByAttr(y, itemsDTO)).collect(Collectors.toList());
        return filterList;
    }

    //根据属性过滤
    private Boolean filterListByAttr(ItemsDTO items, ItemsDTO checkItems) {
        Boolean flag1 = true;
        //晶体类型
        flag1 = noneVerify2(flag1, items.getSegment1(), checkItems.getSegment1(), "", items);
        //产品等级
        flag1 = noneVerify2(flag1, items.getSegment2(), checkItems.getSegment2(), "", items);
        //品类
        flag1 = noneVerify2(flag1, items.getSegment3(), checkItems.getSegment3(), "", items);
        //面积
        flag1 = noneVerify2(flag1, items.getSegment4(), checkItems.getSegment4(), "", items);
        //长度
        flag1 = noneVerify2(flag1, items.getSegment5(), checkItems.getSegment5(), "", items);
        //宽度
        flag1 = noneVerify2(flag1, items.getSegment6(), checkItems.getSegment6(), "", items);
        //厚度
        flag1 = noneVerify2(flag1, items.getSegment7(), checkItems.getSegment7(), "", items);
        //切片后宽度
        flag1 = noneVerify2(flag1, items.getSegment8(), checkItems.getSegment8(), "", items);
        //产品分类
        flag1 = noneVerify2(flag1, items.getSegment9(), checkItems.getSegment9(), "", items);
        //单双面
        flag1 = noneVerify2(flag1, items.getSegment10(), checkItems.getSegment10(), "", items);
        //分片方式
        flag1 = noneVerify2(flag1, items.getSegment11(), checkItems.getSegment11(), "", items);
        //主栅类别
        flag1 = noneVerify2(flag1, items.getSegment12(), checkItems.getSegment12(), "", items);
        // 主栅间距
        if (StringUtils.isNotEmpty(checkItems.getSegment13())) {
            if (StringUtil.isBlank(checkItems.getSegment13()) || "无".equals(checkItems.getSegment13())) {
                // 当为无或者空时不校验主栅间距
            } else {
                flag1 = noneVerify2(flag1, items.getSegment13(), checkItems.getSegment13(), "", items);
            }
        }
        //主栅直径
        flag1 = noneVerify2(flag1, items.getSegment14(), checkItems.getSegment14(), "", items);
        //细栅数量
        flag1 = noneVerify2(flag1, items.getSegment15(), checkItems.getSegment15(), "", items);
        //主栅两端数量
        flag1 = noneVerify2(flag1, items.getSegment16(), checkItems.getSegment16(), "", items);
        //分段模式
        flag1 = noneVerify2(flag1, items.getSegment17(), checkItems.getSegment17(), "", items);
        //防断栅
        flag1 = noneVerify2(flag1, items.getSegment18(), checkItems.getSegment18(), "", items);
        //网版图形-正
        flag1 = noneVerify2(flag1, items.getSegment19(), checkItems.getSegment19(), "", items);
        //网版图形-背
        flag1 = noneVerify2(flag1, items.getSegment20(), checkItems.getSegment20(), "", items);
        //硅料厂商
        flag1 = noneVerify2(flag1, items.getSegment21(), checkItems.getSegment21(), "", items);
        //硅料等级
        flag1 = noneVerify2(flag1, items.getSegment22(), checkItems.getSegment22(), "", items);
        //特殊订单
        if (StringUtils.isNotEmpty(checkItems.getSegment23())) {
            // #24574 如果segment23有值时精准校验，如果segment23为无时时不校验
            if (!checkItems.getSegment23().equals("无")) {
                flag1 = noneVerify2(flag1, items.getSegment23(), checkItems.getSegment23(), "", items);
            }
        }
        //电池工艺
        flag1 = noneVerify2(flag1, items.getSegment24(), checkItems.getSegment24(), "", items);
        //暗电流
        flag1 = noneVerify2(flag1, items.getSegment25(), checkItems.getSegment25(), "", items);
        //EL图像
        flag1 = noneVerify2(flag1, items.getSegment26(), checkItems.getSegment26(), "", items);
        //返工片
        flag1 = noneVerify2(flag1, items.getSegment27(), "返工片", "", items);
        //认证型号
        flag1 = noneVerify2(flag1, items.getSegment28(), checkItems.getSegment28(), "", items);
        //原产地信息
        flag1 = noneVerify2(flag1, items.getSegment29(), checkItems.getSegment29(), "", items);
        //加工类别
        flag1 = noneVerify2(flag1, items.getSegment30(), checkItems.getSegment30(), "", items);
        //补充说明
        flag1 = noneVerify2(flag1, items.getSegment31(), checkItems.getSegment31(), "", items);
        //认证厚度
        flag1 = noneVerify2(flag1, items.getSegment33(), checkItems.getSegment33(), "", items);
        //H追溯
        flag1 = noneVerify2(flag1, items.getSegment34(), checkItems.getSegment34(), "", items);
        //限制数量
        flag1 = noneVerify2(flag1, items.getSegment36(), checkItems.getSegment36(), "", items);
        //膜层结构
        flag1 = noneVerify2(flag1, items.getSegment37(), checkItems.getSegment37(), "", items);
        return flag1;
    }

    @Override
    public Map<Long, ItemsDTO> getItemsDTOMapBySourceItemIds(List<Long> sourceItemIds) {
        JPAQuery<Items> jpaQuery = jpaQueryFactory.select(qItems).from(qItems)
                .where(qItems.sourceItemId.in(sourceItemIds));
        List<ItemsDTO> itemsDTOList = convert.toDto(jpaQuery.fetch());
        return itemsDTOList.stream().collect(Collectors.toMap(ItemsDTO::getSourceItemId, Function.identity(), (v1, v2) -> v1));
    }
}
