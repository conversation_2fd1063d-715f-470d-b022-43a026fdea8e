package com.trinasolar.scp.bbom.service.service;


import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectHeaderDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectHeader;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.RuleControlObjectHeaderSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 规则管控对象头 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:32
 */
public interface RuleControlObjectHeaderService {
    Page<RuleControlObjectHeader> queryByPage(RuleControlObjectHeaderQuery query);

    RuleControlObjectHeaderDTO queryById(Long id);

    RuleControlObjectHeaderDTO save(RuleControlObjectHeaderSaveDTO saveDTO);

    void deleteById(Long id);

    void saveHeaders(Long ruleLineId, List<RuleControlObjectHeaderSaveDTO> controlObjectHeaders);

    List<RuleControlObjectHeaderDTO> listByRuleLineId(Long ruleLineId);

    List<RuleControlObjectHeader> listByRuleLineIdAndControlObject(Long ruleLineId, String bomStructure);

    List<RuleControlObjectHeader> findByRuleLineIdInAndControlObject(List<Long> ruleLineIds, String bomStructure);
}

