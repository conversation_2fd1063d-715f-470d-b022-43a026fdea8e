package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryTypeMain;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeMainQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryTypeMainSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池类型静态属性 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
public interface BatteryTypeMainService {
    /**
     * 分页获取电池类型静态属性
     *
     * @param query 查询对象
     * @return 电池类型静态属性分页对象
     */
    Page<BatteryTypeMainDTO> queryByPage(BatteryTypeMainQuery query);

    /**
     * 查询电池编码和类型下拉列表
     *
     * @return 查询电池编码和类型下拉列表
     */
    List<BatteryTypeMainDTO> queryBatteryCodeType(BatteryTypeMainQuery query);

    /**
     * 查询电池编码和类型下拉列表
     *
     * @return 查询电池编码和类型下拉列表
     */
    List<BatteryTypeMainDTO> queryBatteryCodeTypeAll();

    /**
     * 根据编码和电池类型查询电池类型静态属性信息和硅片信息
     *
     * @param query
     * @return
     */
    List<BatteryTypeMainDTO> queryMainByBatteryCode(BatteryTypeMainQuery query);


    /**
     * 根据 batteryCode查询数据
     *
     * @param batteryCode
     * @return
     */
    BatteryTypeMainDTO queryByBatteryCode(String batteryCode);

    /**
     * 根据 batteryName查询数据
     *
     * @param batteryName
     * @return
     */
    BatteryTypeMainDTO queryByBatteryName(String batteryName);

    /**
     * 根据主键获取电池类型静态属性详情
     *
     * @param id 主键
     * @return 电池类型静态属性详情
     */
    BatteryTypeMainDTO queryById(Long id);

    /**
     * 保存或更新电池类型静态属性
     *
     * @param saveDTO 电池类型静态属性保存对象
     * @return 电池类型静态属性对象
     */
    BatteryTypeMainDTO save(BatteryTypeMainSaveDTO saveDTO);


    /**
     * 保存或更新电池类型静态属性
     *
     * @param saveDTO
     * @return
     */
    List<BatteryTypeMain> batchSaveUpdate(List<BatteryTypeMainDTO> saveDTO);

    /**
     * 根据主键逻辑删除电池类型静态属性
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatteryTypeMainQuery query, HttpServletResponse response);

    BatteryTypeMainDTO queryBatteryCodeTypeAllByBatteryName(String batteryType);
}

