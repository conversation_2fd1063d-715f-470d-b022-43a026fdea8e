package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.PdmItemLifecycleDEConvert;
import com.trinasolar.scp.bbom.domain.dto.ItemsLiftCycleDTO;
import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleDTO;
import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleSyncDTO;
import com.trinasolar.scp.bbom.domain.entity.PdmItemLifecycle;
import com.trinasolar.scp.bbom.domain.entity.QPdmItemLifecycle;
import com.trinasolar.scp.bbom.domain.query.PdmItemLifecycleQuery;
import com.trinasolar.scp.bbom.domain.query.PdmItemLifecycleSyncQuery;
import com.trinasolar.scp.bbom.domain.save.PdmItemLifecycleSaveDTO;
import com.trinasolar.scp.bbom.service.repository.ItemsRepository;
import com.trinasolar.scp.bbom.service.repository.PdmItemLifecycleRepository;
import com.trinasolar.scp.bbom.service.service.PdmItemLifecycleService;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * pdm物料生命周期原始表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Slf4j
@Service("pdmItemLifecycleService")
@RequiredArgsConstructor
public class PdmItemLifecycleServiceImpl implements PdmItemLifecycleService {
    private final ItemsRepository itemsRepository;

    private static final QPdmItemLifecycle qPdmItemLifecycle = QPdmItemLifecycle.pdmItemLifecycle;

    private final PdmItemLifecycleDEConvert convert;

    private final PdmItemLifecycleRepository repository;

    @Override
    public Page<PdmItemLifecycleDTO> queryByPage(PdmItemLifecycleQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<PdmItemLifecycle> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, PdmItemLifecycleQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qPdmItemLifecycle.id.eq(query.getId()));
        }
        if (query.getSyncDate() != null) {
            booleanBuilder.and(qPdmItemLifecycle.syncDate.eq(query.getSyncDate()));
        }
        if(StringUtils.isNotEmpty(query.getItemCode())){
            booleanBuilder.and(qPdmItemLifecycle.itemCode.eq(query.getItemCode()));
        }
        if(StringUtils.isNotEmpty(query.getName())){
            booleanBuilder.and(qPdmItemLifecycle.name.eq(query.getName()));
        }
        if(StringUtils.isNotEmpty(query.getLifecycle())){
            booleanBuilder.and(qPdmItemLifecycle.lifecycle.eq(query.getLifecycle()));
        }
        if(StringUtils.isNotEmpty(query.getStage())){
            booleanBuilder.and(qPdmItemLifecycle.stage.eq(query.getStage()));
        }
        if(StringUtils.isNotEmpty(query.getItemDesc())){
            booleanBuilder.and(qPdmItemLifecycle.itemDesc.eq(query.getItemDesc()));
        }
        if (query.getOrgId() != null) {
            booleanBuilder.and(qPdmItemLifecycle.orgId.eq(query.getOrgId()));
        }
        if(StringUtils.isNotEmpty(query.getVersion())){
            booleanBuilder.and(qPdmItemLifecycle.version.eq(query.getVersion()));
        }
    }

    @Override
    public PdmItemLifecycleDTO queryById(Long id) {
        PdmItemLifecycle queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public PdmItemLifecycleDTO save(PdmItemLifecycleSaveDTO saveDTO) {
        PdmItemLifecycle newObj = Optional.ofNullable(saveDTO.getId())
            .flatMap(repository::findById)
            .orElse(new PdmItemLifecycle());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(PdmItemLifecycleQuery query, HttpServletResponse response) {
       List<PdmItemLifecycleDTO> dtos = queryByPage(query).getContent();

       ExcelPara excelPara = query.getExcelPara();
       List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

       ExcelUtils.exportEx(response, "pdm物料生命周期原始表", "pdm物料生命周期原始表", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public void saveLifeCycle(LocalDate date, List<ItemsLiftCycleDTO> filterList) {
        List<PdmItemLifecycle> saveList = new LinkedList<>();
        for (ItemsLiftCycleDTO itemsLiftCycleDTO : filterList) {
            PdmItemLifecycle pdmItemLifecycle = new PdmItemLifecycle();
            pdmItemLifecycle.setSyncDate(date);
            pdmItemLifecycle.setItemCode(itemsLiftCycleDTO.getNumber());
            pdmItemLifecycle.setName(itemsLiftCycleDTO.getName());
            pdmItemLifecycle.setLifecycle(itemsLiftCycleDTO.getLifecycle());
            pdmItemLifecycle.setStage(itemsLiftCycleDTO.getStage());
            pdmItemLifecycle.setItemDesc(itemsLiftCycleDTO.getDesc());
            pdmItemLifecycle.setOrgId(itemsLiftCycleDTO.getView());
            pdmItemLifecycle.setVersion(itemsLiftCycleDTO.getVersion());
            pdmItemLifecycle.setIsTemporaryOutput(itemsLiftCycleDTO.getIsTemporaryOutput());
            saveList.add(pdmItemLifecycle);
        }
        repository.saveAll(saveList);
    }

    @Override
    public PdmItemLifecycleSyncDTO queryByItemCodeAndOrgId(PdmItemLifecycleSyncQuery syncQuery) {
        PdmItemLifecycle lifecycle = repository.queryFirstByItemCodeAndOrgIdOrderByCreatedTimeDesc(syncQuery.getItemCode(), syncQuery.getOrgId());
        if (Objects.isNull(lifecycle)) {
            return null;
        }
        return PdmItemLifecycleSyncDTO
                .builder()
                .itemCode(lifecycle.getItemCode())
                .orgId(lifecycle.getOrgId())
                .lifecycleState(lifecycle.getStage())
                .isTemporaryOutput(lifecycle.getIsTemporaryOutput())
                .build();
    }
}
