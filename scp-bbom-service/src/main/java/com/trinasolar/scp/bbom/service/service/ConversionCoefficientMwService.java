package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ConversionCoefficientMwDTO;
import com.trinasolar.scp.bbom.domain.query.ConversionCoefficientMwQuery;
import com.trinasolar.scp.bbom.domain.save.ConversionCoefficientMwSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 兆瓦转换系数 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
public interface ConversionCoefficientMwService {
    /**
     * 分页获取兆瓦转换系数
     *
     * @param query 查询对象
     * @return 兆瓦转换系数分页对象
     */
    Page<ConversionCoefficientMwDTO> queryByPage(ConversionCoefficientMwQuery query);

    /**
     * 根据主键获取兆瓦转换系数详情
     *
     * @param id 主键
     * @return 兆瓦转换系数详情
     */
    ConversionCoefficientMwDTO queryById(Long id);

    /**
     * 保存或更新兆瓦转换系数
     *
     * @param saveDTO 兆瓦转换系数保存对象
     * @return 兆瓦转换系数对象
     */
    ConversionCoefficientMwDTO save(ConversionCoefficientMwSaveDTO saveDTO);

    /**
     * 计算-保存
     *
     * @param saveDTO
     * @return
     */
    Page<ConversionCoefficientMwDTO> computeSave(ConversionCoefficientMwSaveDTO saveDTO);

    /**
     * job调用 计算-保存
     *
     * @return
     */
    void computeSaveWithJob();

    /**
     * 根据主键逻辑删除兆瓦转换系数
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ConversionCoefficientMwQuery query, HttpServletResponse response);

    /**
     * 查询电池属性兆瓦转换系数(用于物料库存趋势)，按 电池类型 + "_" + 基地 + "_" + 物料分类 获取物料MW系数
     *
     * @return
     */
    Map<String, BigDecimal> getBatteryTypeMWForInventoryTrends();

    /**
     * 查询电池属性兆瓦转换系数(用于物料库存趋势)，按 品类 + "_" + 基地 获取平均物料MW系数
     *
     * @return
     */
    Map<String, BigDecimal> getAverageMVForInventoryTrends();
}

