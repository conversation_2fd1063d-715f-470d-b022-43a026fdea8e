package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.bbom.domain.query.LowEfficiencyCellPercentQuery;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface LowEfficiencyCellPercentService {

    Page<LowEfficiencyCellPercentDTO> queryByPage(LowEfficiencyCellPercentQuery query);

    void importFromExcel(MultipartFile file);

    void batchDelete(List<Long> ids);

    void update(LowEfficiencyCellPercentDTO dto);

    void exportToExcel(LowEfficiencyCellPercentQuery query, HttpServletResponse response);

    List<LowEfficiencyCellPercentDTO> list(LowEfficiencyCellPercentQuery query);
}
