package com.trinasolar.scp.bbom.service.service;


import com.trinasolar.scp.bbom.domain.dto.RuleDpDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpDetail;
import com.trinasolar.scp.bbom.domain.query.RuleDpDetailQuery;
import com.trinasolar.scp.bbom.domain.save.RuleDpDetailSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * BOM规则DP因子 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:08
 */
public interface RuleDpDetailService {
    Page<RuleDpDetail> queryByPage(RuleDpDetailQuery query);

    RuleDpDetailDTO queryById(Long id);

    RuleDpDetailDTO save(RuleDpDetailSaveDTO saveDTO);

    void deleteById(Long id);

    void saveDetails(Long ruleLineId, List<RuleDpDetailSaveDTO> details);

    List<RuleDpDetailDTO> listByRuleLineId(Long ruleLineId);
}

