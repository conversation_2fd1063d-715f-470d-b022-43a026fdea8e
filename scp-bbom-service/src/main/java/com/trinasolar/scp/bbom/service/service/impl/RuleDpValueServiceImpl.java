package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.dto.CustomerDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleDpValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpValue;
import com.trinasolar.scp.bbom.domain.query.RuleDpValueQuery;
import com.trinasolar.scp.bbom.domain.save.RuleDpValueSaveDTO;
import com.trinasolar.scp.bbom.service.feign.DPFeign;
import com.trinasolar.scp.bbom.service.repository.RuleDpValueRepository;
import com.trinasolar.scp.bbom.service.service.RuleDpValueService;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * BOM规则DP因子明细值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:01
 */
@Slf4j
@Service("ruleDpValueService")
@RequiredArgsConstructor
public class RuleDpValueServiceImpl implements RuleDpValueService {
    private final RuleDpValueRepository repository;

    private final LovUtil lovUtil;

    @Autowired
    private DPFeign dpFeign;


    @Override
    public Page<RuleDpValue> queryByPage(RuleDpValueQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public RuleDpValueDTO queryById(Long id) {
        RuleDpValue queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        RuleDpValueDTO result = new RuleDpValueDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public RuleDpValueDTO save(RuleDpValueSaveDTO saveDTO) {
        RuleDpValue newObj;
        if (saveDTO.getId() != null) {
            newObj = repository.findById(saveDTO.getId()).orElse(new RuleDpValue());
        } else {
            newObj = new RuleDpValue();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    public void saveValues(Long ruleDetailId, List<RuleDpValueSaveDTO> values) {
        verifyAndDelete(ruleDetailId, values);
        if (values == null || values.isEmpty()) {
            return;
        }
        for (RuleDpValueSaveDTO value : values) {
            value.setRuleDetailId(ruleDetailId);
            save(value);
        }
    }

    @Override
    public List<RuleDpValueDTO> listByRuleDetailId(Long ruleDetailId, String dpFiledName) {
        List<RuleDpValue> ruleDpValues = repository.listByRuleDetailId(ruleDetailId);
        // p2_802 电池片投产限制界面型号字段调整
        Map<String, LovLineDTO> cellProdLovMap = LovUtils.getAllByHeaderCode("aps_power_cell_prod_map");
        return ruleDpValues.stream().map(value -> {
            RuleDpValueDTO ruleDpValueDTO = new RuleDpValueDTO();
            BeanUtils.copyProperties(value, ruleDpValueDTO);
            if (ruleDpValueDTO.getAttrValueId() != null && cellProdLovMap.containsKey(ruleDpValueDTO.getAttrValueId().toString())) {
                LovLineDTO lovLineDTO = cellProdLovMap.get(ruleDpValueDTO.getAttrValueId().toString());
                ruleDpValueDTO.setAttrValueName(lovLineDTO.getLovName() + "," + lovLineDTO.getAttribute2());
            } else {
                lovUtil.setLovLineName(ruleDpValueDTO, ruleDpValueDTO.getAttrValueId(), "attrValueName", ruleDpValueDTO.getAttrValue());
            }
            lovUtil.setLovLineName(ruleDpValueDTO, ruleDpValueDTO.getAttrValueToId(), "attrValueToName", ruleDpValueDTO.getAttrValueTo());
            if (dpFiledName.equals("客户名称")) {
                ResponseEntity<Results<CustomerDTO>> responseEntity = dpFeign.detail(new IdDTO(ruleDpValueDTO.getAttrValue()));
                CustomerDTO customerDTO = responseEntity.getBody().getData();
                ruleDpValueDTO.setAttrValueName(customerDTO.getCustomerName());
            }
            return ruleDpValueDTO;
        }).collect(Collectors.toList());
    }

    private void verifyAndDelete(Long id, List<RuleDpValueSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<RuleDpValue> savedConfigs = repository.listByRuleDetailId(id);

        if (lines == null || lines.isEmpty()) {
            if (!savedConfigs.isEmpty()) {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
            return;
        }
        List<Long> currentIds = lines.stream().filter(item -> item.getId() != null).map(RuleDpValueSaveDTO::getId)
                .collect(Collectors.toList());
        List<RuleDpValue> deleteConfigs = savedConfigs.stream().filter(item -> !currentIds.contains(item.getId()))
                .collect(Collectors.toList());

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }
    }
}
