package com.trinasolar.scp.bbom.service.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.trinasolar.scp.bbom.domain.excel.ExcelHead;
import com.trinasolar.scp.bbom.domain.excel.ExcelMain;
import com.trinasolar.scp.bbom.service.util.excel.MergeStrategy;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import org.apache.commons.collections4.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 动态表头Excel工具类
 *
 * <AUTHOR>
 * @date 2022年9月21日16:15:31
 */
public class ExcelTreandsUtil {
    /**
     * 导出
     *
     * @param response
     * @param excelMain
     * @throws IOException
     */
    public static void export(HttpServletResponse response, ExcelMain excelMain) throws IOException {
        EasyExcel.write(response.getOutputStream())
                .head(ExcelTreandsUtil.buildHead(excelMain.getExcelHeads()))
                .sheet("Sheet1")
                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .doWrite(ExcelTreandsUtil.buildData(excelMain));
    }

    public static void setExportResponseHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            String fileNameCode = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileNameCode + ".xlsx");
            response.setHeader("Access-Control-Allow-Origin", "Content-Disposition");
            response.setHeader("Access-Control-Allow-Origin", "Content-disposition");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        } catch (Throwable var3) {
            throw var3;
        }
    }

    /**
     * 导出
     *
     * @param response
     * @param sheetMap
     * @throws IOException
     */
    public static void export(HttpServletResponse response, Map<String, ExcelMain> sheetMap) throws IOException {
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        sheetMap.forEach((sheetName, excelMain) -> {
            WriteSheet sheet = EasyExcel
                    .writerSheet(sheetName)
                    .head(ExcelTreandsUtil.buildHead(excelMain.getExcelHeads()))
                    .registerWriteHandler(new MergeStrategy(excelMain))
                    .registerConverter(new LocalDateConverter())
                    .registerConverter(new LocalDateTimeConverter())
                    .build();
            excelWriter.write(ExcelTreandsUtil.buildData(excelMain), sheet);
        });
        excelWriter.finish();

//        EasyExcel.write(response.getOutputStream())
//                .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
//                .sheet("Sheet1")
//                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
//                .registerConverter(new LocalDateConverter())
//                .registerConverter(new LocalDateTimeConverter())
//                .doWrite(ExcelUtil.buildData(excelMain));
    }

    /**
     * 构建导出表头
     */
    public static List<List<String>> buildHead(List<ExcelHead> heads) {
        boolean needMerge = false;
        for (ExcelHead headVO : heads) {
            if (CollectionUtils.isNotEmpty(headVO.getChildren())) {
                needMerge = true;
            }
        }
        List<List<String>> headList = new ArrayList<>();
        for (ExcelHead excelHead : heads) {
            if (CollectionUtils.isNotEmpty(excelHead.getChildren())) {
                excelHead.getChildren().forEach(child -> {
                    List<String> head = new ArrayList<>();
                    head.add(excelHead.getLabel());
                    head.add(child.getLabel());
                    headList.add(head);
                });
            } else {
                List<String> head = new ArrayList<>();
                head.add(excelHead.getLabel());
                if (needMerge) {
                    head.add(excelHead.getLabel());
                }
                headList.add(head);
            }
        }
        return headList;
    }

    /**
     * 构建导出数据
     */
    public static List<List<Object>> buildData(ExcelMain excelMain) {
        int maxExcel = 32767;
        List<List<Object>> list = new ArrayList<>();
        if (Objects.isNull(excelMain) || CollectionUtils.isEmpty(excelMain.getData())) {
            return list;
        }
        List<ExcelHead> heads = excelMain.getExcelHeads();
        for (Map<String, Object> map : excelMain.getData()) {
            List<Object> data = new ArrayList<>();
            for (ExcelHead head : heads) {
                if (CollectionUtils.isNotEmpty(head.getChildren())) {
                    head.getChildren().forEach(child -> {
                        Object value = map.get(child.getProp());
                        if ((value + "").length() >= maxExcel) {
                            data.add((value + "").substring(0, 255) + "字符过长");
                        } else {
                            data.add(value);
                        }
                    });
                } else {
                    Object value = map.get(head.getProp());
                    if ((value + "").length() >= maxExcel) {
                        data.add((value + "").substring(0, 255) + "字符过长");
                    } else {
                        data.add(value);
                    }
                }
            }
            list.add(data);
        }
        return list;
    }

}
