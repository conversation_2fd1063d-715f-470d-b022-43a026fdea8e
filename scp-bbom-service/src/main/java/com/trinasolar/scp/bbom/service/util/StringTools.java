package com.trinasolar.scp.bbom.service.util;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Optional;
import java.util.regex.Matcher;

public class StringTools {
    public static String joinWith(String separator, String... values) {

        if (values == null || values.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        Arrays.stream(values).forEach(value -> {
            value = Optional.ofNullable(value).orElse("");
            sb.append(separator);
            sb.append(value);
        });
        return sb.substring(1);
    }

    public static String getValueByLineEntity(MaterielMatchLine entity, String fieldName) {
        try{
            Class<?> clazz = entity.getClass();
            // 获取Field对象 - 获取name字段
            Field nameField = clazz.getDeclaredField(fieldName);
            nameField.setAccessible(true); // 如果是私有字段，需要设置访问权限
            return nameField.get(entity).toString();
        }catch (Exception e){
            return "";
        }
    }

    public static String getValueByLineDTOEntity(MaterielMatchLineDTO entity, String fieldName) {
        try{
            Class<?> clazz = entity.getClass();
            // 获取Field对象 - 获取name字段
            Field nameField = clazz.getDeclaredField(fieldName);
            nameField.setAccessible(true); // 如果是私有字段，需要设置访问权限
            return nameField.get(entity).toString();
        }catch (Exception e){
            return "";
        }
    }

    public static String convertToCamelCase(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        // 使用正则表达式找到下划线及其后面的字符，并将其转换为大写
        String camelCaseOutput = input.replaceAll("_([a-z])", Matcher.quoteReplacement(input).toUpperCase());

        // 如果字符串以下划线开头，需要将其转换为小写
        if (camelCaseOutput.startsWith("_")) {
            camelCaseOutput = camelCaseOutput.substring(1).toLowerCase() + camelCaseOutput.substring(1);
        }

        return camelCaseOutput;
    }


}
