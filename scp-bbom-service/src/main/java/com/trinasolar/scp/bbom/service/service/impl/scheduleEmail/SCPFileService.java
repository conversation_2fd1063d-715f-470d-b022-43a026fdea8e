package com.trinasolar.scp.bbom.service.service.impl.scheduleEmail;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.trinasolar.scp.common.api.util.BizException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@RefreshScope
public class SCPFileService {

    public final static Cache<String, String> TOKENCACHE = CacheBuilder.newBuilder()
            //设置cache的初始大小为10， 要合理设置该值
            .initialCapacity(10)
            //设置并发数为5，即同一时间最多只能有5个线程往cache执行写入操作
            .concurrencyLevel(5)
            //设置cache中的数据在写入之后的存活时间为30mins
            .expireAfterWrite(30, TimeUnit.MINUTES)
            //构建cache实例
            .build();
    @Value("${trinasolar.uploader.clientId}")
    private String clientId;
    @Value("${trinasolar.uploader.clientSecret}")
    private String clientSecret;
    @Value("${trinasolar.uploader.fileURL}")
    private String cephUrl;
    @Resource(name = "restTemplate2")
    private RestTemplate restTemplate;

    public String upload(MultipartFile file, Boolean flag) throws IOException {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
            @Override
            public String getFilename() {
                //return UUID.randomUUID().toStrving();
                if (flag) {
                    return UUID.randomUUID() + "." + getExtensionName(file.getOriginalFilename().replace("\r\n", ""));
                } else {
                    return UUID.randomUUID() + "." + getExtensionName(file.getOriginalFilename());
                }
            }
        };
        params.add("file", resource);
        params.add("token", getAccessToken());
        return uploadUrl(params);
    }

    public String uploadUrl(MultiValueMap<String, Object> params) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, httpHeaders);
        String url = cephUrl + "/uploadFile";
        ResponseEntity<TrinaResponse> response = restTemplate.exchange(url, HttpMethod.POST, request, TrinaResponse.class);
        if (response.getStatusCode() == HttpStatus.OK) {
            TrinaResponse<HashMap<String, Object>> result = response.getBody();
            HashMap<String, Object> data = result.getData();
            if (data.get("fileUrl").toString().contains("https")) {
                return data.get("fileUrl").toString();
            } else {
                return data.get("fileUrl").toString().replace("http", "https");
            }

        } else {
            throw new BizException("上传发生错误");
        }
    }

    public String genToken() {
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<TrinaResponse> response = restTemplate.getForEntity(cephUrl + "/user?clientId=" + clientId + "&clientSecret=" + clientSecret, TrinaResponse.class);
        String token = "";
        if (response.getStatusCode() == HttpStatus.OK) {
            TrinaResponse<HashMap<String, Object>> result = response.getBody();
            HashMap<String, Object> data = result.getData();
            token = data.get("token").toString();
        }
        return token;
    }

    public void setMdmToken() {
        TOKENCACHE.put("ACCESS_TOKEN", genToken());
    }

    public String getAccessToken() {
        String result = TOKENCACHE.getIfPresent("ACCESS_TOKEN");
        if (result != null) {
            return result;
        } else {
            result = this.genToken();
            if (!StringUtils.isEmpty(result)) {
                setMdmToken();
            }
            return result;
        }
    }

    public String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

}
