package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ScreenLifeDTO;
import com.trinasolar.scp.bbom.domain.query.ScreenLifeQuery;
import com.trinasolar.scp.bbom.domain.save.ScreenLifeSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 网版寿命信息维护 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
public interface ScreenLifeService {
    /**
     * 分页获取网版寿命信息维护
     *
     * @param query 查询对象
     * @return 网版寿命信息维护分页对象
     */
    Page<ScreenLifeDTO> queryByPage(String userId, ScreenLifeQuery query);

    /**
     * 获取网版寿命信息维护
     *
     * @return 网版寿命信息维护对象
     */
    List<ScreenLifeDTO> queryList();

    /**
     * 获取网版寿命信息维护
     *
     * @return 网版寿命信息维护对象
     */
    ScreenLifeDTO queryList(ScreenLifeQuery query);

    /**
     * 根据主键获取网版寿命信息维护详情
     *
     * @param id 主键
     * @return 网版寿命信息维护详情
     */
    ScreenLifeDTO queryById(Long id);

    /**
     * 保存或更新网版寿命信息维护
     *
     * @param saveDTO 网版寿命信息维护保存对象
     * @return 网版寿命信息维护对象
     */
    ScreenLifeDTO save(ScreenLifeSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除网版寿命信息维护
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ScreenLifeQuery query, HttpServletResponse response, String userId);

    /**
     * 导出模版
     *
     * @param query
     * @param response
     */
    void queryByPageExport(ScreenLifeQuery query, HttpServletResponse response, String userId);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);

    /**
     * 查询网版寿命信息 近三个月时间
     *
     * @return
     */
    void queryListBySendMail();

    List<String> getVersionList();

    void batchUpdateBomLife();

    List<ScreenLifeDTO> queryListWithLifeTimeConvert();
}

