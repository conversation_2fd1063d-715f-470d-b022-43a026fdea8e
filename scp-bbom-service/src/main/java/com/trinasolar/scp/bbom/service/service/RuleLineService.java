package com.trinasolar.scp.bbom.service.service;


import com.trinasolar.scp.bbom.domain.dto.AttrTypeLineDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleLineDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleLineInterfaceDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleLine;
import com.trinasolar.scp.bbom.domain.query.RuleLineQuery;
import com.trinasolar.scp.bbom.domain.save.RuleLineSaveDTO;
import com.trinasolar.scp.bbom.domain.save.RuleLinesSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * BOM规则行表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 09:15:32
 */
public interface RuleLineService {
    Page<RuleLine> queryByPage(RuleLineQuery query);

    RuleLineDTO queryById(Long id);

    RuleLineDTO save(RuleLineSaveDTO saveDTO);

    void deleteById(Long id);

    void saveLines(RuleLinesSaveDTO ruleLinesSaveDTO);

    List<RuleLineDTO> listLinesByHeaderId(Long ruleHeaderId);

    List<RuleLineDTO> listLinesByHeaderIds(List<Long> ruleHeaderId);

    List<RuleLineDTO> listByRuleHeaderIdAndItemCode(Long ruleHeaderId, String itemCode);

    List<RuleLine> findByRuleCategoryId(Long lovLineId);

    String getControlObjectExpress(RuleLine ruleLine, String bomStructure);

    List<RuleLine> findByControlObject(String controlObject);

    List<RuleLine> findByControlObjectAndRuleCategoryIds(String bomStructure, List<Long> ruleCategoryIds);

    List<RuleLine> listRulesByRuleCategoryId(Long lovLineId);

    RuleLineDTO convertDto(RuleLine item);

    void genRuleLineNo();

    RuleLine findRuleLineByLinesId(Long ruleLinesId);

    List<RuleLine> listByRuleHeaderId(Long ruleHeaderId);

    List<RuleLine> findByRuleCategoryIds(List<Long> ruleCategoryIds);

    List<RuleLineInterfaceDTO> queryRuleLins(List<String> itemCodes);

    Map<String, List<RuleLineDTO>> queryCellRestrictedByItemCode(List<String> itemCodes);

    List<RuleLineDTO> getRuleLineDTOSByItemCode(String i, List<RuleLineDTO> ruleLineDTOS, AttrTypeLineDTO itemCodeAttrDTO, AttrTypeLineDTO materialLimit);

    List<RuleLineDTO> getRuleLineByRuleCode(String ruleCode);
}

