package com.trinasolar.scp.bbom.service.service;


import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectDetail;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectDetailQuery;
import com.trinasolar.scp.bbom.domain.save.RuleControlObjectDetailSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 规则管控对象详情 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
public interface RuleControlObjectDetailService {
    Page<RuleControlObjectDetail> queryByPage(RuleControlObjectDetailQuery query);

    RuleControlObjectDetailDTO queryById(Long id);

    RuleControlObjectDetailDTO save(RuleControlObjectDetailSaveDTO saveDTO);

    void deleteById(Long id);

    void saveDetails(Long id, List<RuleControlObjectDetailSaveDTO> details);

    List<RuleControlObjectDetailDTO> listByRuleControlObjectHeaderId(Long id);
}

