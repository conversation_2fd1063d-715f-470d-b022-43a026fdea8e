package com.trinasolar.scp.bbom.service.util;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import com.trinasolar.scp.bbom.domain.query.LovLineQuery;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Component
@Slf4j
public class LovUtil {
    @Autowired
    public SystemFeign systemFeign;

    // 可修改为Caffeine的缓存
    LRUCache<String, List<LovLineDTO>> lovCodeCache = CacheUtil.newLRUCache(1000, 5 * 60 * 1000);

    LRUCache<String, String> specialMarketCache = CacheUtil.newLRUCache(1000, 2 * 60 * 1000);

    public String convertClimateStrategy(String itemAttribute14) {
        if (StringUtils.isBlank(itemAttribute14)) {
            return itemAttribute14;
        }
        List<LovLineDTO> ClimateStrategyLovs = getLovLineByCode("6A00100100129");
        for (LovLineDTO climateStrategyLov : ClimateStrategyLovs) {
            if (Objects.equals(climateStrategyLov.getLovValue(), itemAttribute14)) {
                return climateStrategyLov.getAttribute1();
            } else if (Objects.equals(climateStrategyLov.getLovLineId().toString(), itemAttribute14)) {
                return climateStrategyLov.getAttribute1();
            }
        }

        return null;
    }

    /**
     * 设置Lov行的名称, 调用示例:
     * LovUtil.setLovLineName(ruleDpValueDTO,ruleDpValueDTO.getAttrValueId(),"attrValueName");
     *
     * @param obj       需要设置的对象
     * @param lovLineId lov行Id
     * @param nameField 需要保存的名称的字段
     */
    public void setLovLineName(Object obj, Long lovLineId, String nameField) {
        if (lovLineId == null) {
            return;
        }

        LovLineDTO lineDTO = null;
        try {
            lineDTO = LovUtils.get(lovLineId);
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("LovUtil lovLineId 查询错误: {}", lovLineId);
            return;
        }
        Class<?> clazz = obj.getClass();
        try {
            Method m = clazz.getDeclaredMethod(setMethodName(nameField), String.class);
            m.invoke(obj, lineDTO.getLovName());
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    public void setLovLineName(Object obj, Long lovLineId, String nameField, String defaultValue) {
        if (lovLineId == null) {
            return;
        }

        LovLineDTO lineDTO = LovUtils.get(lovLineId);
        String lovName;
        if (lineDTO == null) {
            lovName = defaultValue;
        } else {
            lovName = lineDTO.getLovName();
        }
        Class<?> clazz = obj.getClass();
        try {
            Method m = clazz.getDeclaredMethod(setMethodName(nameField), String.class);
            m.invoke(obj, lovName);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    private String setMethodName(String srcAttrColumn) {
        return "set" + srcAttrColumn.substring(0, 1).toUpperCase() + srcAttrColumn.substring(1);
    }

    public String getLovValue(String lovId) {
        try {
            return LovUtils.get(Long.parseLong(lovId)).getLovValue();
        } catch (Exception e) {
            return lovId;
        }
    }

    public synchronized Map<String,LovLineDTO> getLovMapByCode(String code) {
        List<LovLineDTO> lovLineDTOS = lovCodeCache.get(code);
        if (lovLineDTOS == null) {
            Results<List<LovLineDTO>> body = systemFeign.queryByCode(LovLineQuery.builder().code(code).build()).getBody();
            if (body == null) {
                lovLineDTOS = new ArrayList<>();
            } else {
                lovLineDTOS = body.getData();
            }
            lovCodeCache.put(code, lovLineDTOS);
        }
        Map<String,LovLineDTO> lovMap = new HashMap<>();
        lovLineDTOS.forEach(item->{
            if(StringUtils.isNotBlank(item.getLovName())){
                lovMap.put(item.getLovName(), item);
            }
            if(StringUtils.isNotBlank(item.getLovValue())){
                lovMap.put(item.getLovValue(), item);
            }
            if(ObjectUtils.isNotEmpty(item.getLovLineId())){
                lovMap.put(item.getLovLineId().toString(),item);
            }
        });
        return lovMap;
    }

    public synchronized List<LovLineDTO> getLovLineByCode(String code) {
        List<LovLineDTO> lovLineDTOS = lovCodeCache.get(code);
        if (lovLineDTOS == null) {
            Results<List<LovLineDTO>> body = systemFeign.queryByCode(LovLineQuery.builder().code(code).build()).getBody();
            if (body == null) {
                lovLineDTOS = new ArrayList<>();
            } else {
                lovLineDTOS = body.getData();
            }
            lovCodeCache.put(code, lovLineDTOS);
        }
        return lovLineDTOS;
    }


    public String convertCountryToSpecialMarket(String coverCountry) {
        String country = specialMarketCache.get(coverCountry);
        if (country == null) {
            List<LovLineDTO> countrys = getLovLineByCode("country");
            // 默认找不到
            country = "无";
            if (StringUtils.isNotBlank(coverCountry)) {
                for (LovLineDTO lovLineDTO : countrys) {
                    if ((coverCountry.equals(lovLineDTO.getLovValue()) || coverCountry.equals(lovLineDTO.getLovName())) &&
                            lovLineDTO.getAttribute4() != null) {
                        country = lovLineDTO.getAttribute4();
                    }
                }
            }
            specialMarketCache.put(coverCountry, country);
        }

        return country;
    }
}
