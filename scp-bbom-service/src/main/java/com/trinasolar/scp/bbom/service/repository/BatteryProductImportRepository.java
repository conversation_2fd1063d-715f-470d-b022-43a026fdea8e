package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.BatteryProductImport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池产品导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Repository
public interface BatteryProductImportRepository extends JpaRepository<BatteryProductImport, Long>, QuerydslPredicateExecutor<BatteryProductImport> {
}
