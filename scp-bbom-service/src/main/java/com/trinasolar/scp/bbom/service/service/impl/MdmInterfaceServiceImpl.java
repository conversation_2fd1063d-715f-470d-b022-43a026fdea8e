package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.trinasolar.scp.bbom.service.service.MdmInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Service("mdmInterfaceService")
@Slf4j
public class MdmInterfaceServiceImpl implements MdmInterfaceService {
    static final String USERNAME = "interface";

    static final String PASSWORD = "interface";

    @Autowired
    @Qualifier("ipassRestTemplate")
    RestTemplate restTemplate;

    @Value("${mdm.interface.platform.url}")
    private String url;

    @Value("${mdm.interface.platform.clientid}")
    private String clientId;

    @Value("${mdm.interface.platform.secret}")
    private String secret;


    @Override
    public <T> List<T> getObjectListByUrl(String path, String params, Class<T> clazz) {
        String url = this.url + path;
        log.info("接口调用URL: {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.set("tsl-clientid", clientId);
        headers.set("tsl-clientsecret", secret);
        headers.set("password", PASSWORD);
        headers.set("username", USERNAME);
        HttpEntity<String> httpEntity = new HttpEntity<>(params, headers);
        String resultJson = null;
        try {
            resultJson = restTemplate.postForObject(url, httpEntity, String.class);
            log.info("接口返回: {}", resultJson);
        } catch (Exception e) {
            log.error("接口请求错误 url: {}  接口返回: {} 错误: {}", url, resultJson, e.getMessage());
            throw e;
        }

        return JSON.parseArray(resultJson, clazz);
    }
}
