package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.dto.RuleDpDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpDetail;
import com.trinasolar.scp.bbom.domain.query.RuleDpDetailQuery;
import com.trinasolar.scp.bbom.domain.save.RuleDpDetailSaveDTO;
import com.trinasolar.scp.bbom.service.repository.RuleDpDetailRepository;
import com.trinasolar.scp.bbom.service.service.RuleDpDetailService;
import com.trinasolar.scp.bbom.service.service.RuleDpValueService;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * BOM规则DP因子
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:08
 */
@Slf4j
@Service("ruleDpDetailService")
@RequiredArgsConstructor
public class RuleDpDetailServiceImpl implements RuleDpDetailService {
    private final RuleDpDetailRepository repository;

    private final RuleDpValueService ruleDpValueService;

    private final LovUtil lovUtil;

    @Override
    public Page<RuleDpDetail> queryByPage(RuleDpDetailQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public RuleDpDetailDTO queryById(Long id) {
        RuleDpDetail queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        RuleDpDetailDTO result = new RuleDpDetailDTO();
        BeanUtils.copyProperties(queryObj, result);

        lovUtil.setLovLineName(result, Long.parseLong(result.getAttrOperator()), "attrOperatorName");

        return result;
    }

    @Override
    public RuleDpDetailDTO save(RuleDpDetailSaveDTO saveDTO) {
        RuleDpDetail newObj;
        if (saveDTO.getRuleDetailId() != null) {
            newObj = repository.findById(saveDTO.getRuleDetailId()).orElse(new RuleDpDetail());
        } else {
            newObj = new RuleDpDetail();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        ruleDpValueService.saveValues(newObj.getRuleDetailId(), saveDTO.getValues());

        return this.queryById(newObj.getRuleDetailId());
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    public void saveDetails(Long ruleLineId, List<RuleDpDetailSaveDTO> details) {
        verifyAndDelete(ruleLineId, details);
        if (details == null || details.isEmpty()) {
            return;
        }

        for (RuleDpDetailSaveDTO detail : details) {
            detail.setRuleLineId(ruleLineId);
            save(detail);
        }
    }

    @Override
    public List<RuleDpDetailDTO> listByRuleLineId(Long ruleLineId) {
        List<RuleDpDetail> details = repository.listByRuleLineId(ruleLineId);
        return details.stream().map(detail -> {
            RuleDpDetailDTO ruleDpDetailDTO = new RuleDpDetailDTO();
            BeanUtils.copyProperties(detail, ruleDpDetailDTO);
            ruleDpDetailDTO.setValues(ruleDpValueService.listByRuleDetailId(ruleDpDetailDTO.getRuleDetailId(), detail.getDpFiledName()));
            if (ruleDpDetailDTO.getAttrOperator() != null) {
                lovUtil.setLovLineName(ruleDpDetailDTO, Long.parseLong(ruleDpDetailDTO.getAttrOperator()), "attrOperatorName");
            }
            return ruleDpDetailDTO;
        }).collect(Collectors.toList());
    }

    private void verifyAndDelete(Long id, List<RuleDpDetailSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<RuleDpDetail> savedConfigs = repository.listByRuleLineId(id);

        if (lines == null || lines.isEmpty()) {
            if (!savedConfigs.isEmpty()) {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
            return;
        }
        List<Long> currentIds = lines.stream().filter(item -> item.getRuleDetailId() != null).map(RuleDpDetailSaveDTO::getRuleDetailId)
                .collect(Collectors.toList());
        List<RuleDpDetail> deleteConfigs = savedConfigs.stream().filter(item -> !currentIds.contains(item.getRuleDetailId()))
                .collect(Collectors.toList());

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }
    }
}
