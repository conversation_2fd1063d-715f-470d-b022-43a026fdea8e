package com.trinasolar.scp.bbom.service.feign;

import com.trinasolar.scp.bbom.domain.dto.CustomerDTO;
import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * @author: darke
 * @create: 2022年4月26日09:21:02
 */
@FeignClient(value = FeignConstant.SCP_DP_API, path = "/scp-dp-api")
public interface DPFeign {

    @PostMapping("/customer/detail")
    @ApiOperation(value = "查询客户详情")
    ResponseEntity<Results<CustomerDTO>> detail(@RequestBody IdDTO idDTO);

}
