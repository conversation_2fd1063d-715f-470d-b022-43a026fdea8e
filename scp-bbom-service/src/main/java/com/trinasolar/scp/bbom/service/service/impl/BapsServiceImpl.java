package com.trinasolar.scp.bbom.service.service.impl;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.baps.CellProductionPlanDTO;
import com.trinasolar.scp.bbom.domain.query.feign.baps.CellProductionPlanQuery;
import com.trinasolar.scp.bbom.service.feign.BAPSFeign;
import com.trinasolar.scp.bbom.service.service.BapsService;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/7
 */
@Slf4j
@Service("bapsService")
@RequiredArgsConstructor
public class BapsServiceImpl implements BapsService {
    private final BAPSFeign bapsFeign;

    @Override
    public List<CellProductionPlanDTO> queryCellProductionPlan(MaterielMatchHeaderDTO materielMatchHeaderDTO) {
        CellProductionPlanQuery query = new CellProductionPlanQuery();
        query.setBasePlace(materielMatchHeaderDTO.getBasePlace());
        query.setWorkshop(materielMatchHeaderDTO.getWorkshop());
        query.setWorkunit(materielMatchHeaderDTO.getWorkunit());
        query.setCellsType(materielMatchHeaderDTO.getBatteryType());
        query.setNumberLine(materielMatchHeaderDTO.getLine());
        query.setHTrace(materielMatchHeaderDTO.getHTrace());
        query.setAesthetics(materielMatchHeaderDTO.getAesthetics());
        query.setTransparentDoubleGlass(materielMatchHeaderDTO.getTransparentDoubleGlass());
        query.setCellSource(materielMatchHeaderDTO.getPcsSourceType());
        query.setMonth(materielMatchHeaderDTO.getMonth());
        /**************************新增字段***********************************/
        query.setIsSpecialRequirement(materielMatchHeaderDTO.getIsSpecialRequirements());
        query.setCellMfrs(materielMatchHeaderDTO.getBatteryManufacturer());
        query.setRegionalCountry(materielMatchHeaderDTO.getSpecialArea());
        query.setSiMfrs(materielMatchHeaderDTO.getSiliconMaterialManufacturer());
        query.setScreenPlateMfrs(materielMatchHeaderDTO.getScreenManufacturer());
        query.setSilverPulpMfrs(materielMatchHeaderDTO.getSilverSlurryManufacturer());
        query.setLowResistance(materielMatchHeaderDTO.getLowResistance());
        query.setDemandBasePlace(materielMatchHeaderDTO.getDemandPlace());
        query.setWaferGrade(materielMatchHeaderDTO.getPcsSourceLevel());
        query.setProcessCategory(materielMatchHeaderDTO.getProcessCategory());
        query.setProductionGrade(materielMatchHeaderDTO.getProductionGrade());
        ResponseEntity<Results<List<CellProductionPlanDTO>>> resultsResponseEntity = bapsFeign.listForMatchItem(query);
        return resultsResponseEntity.getBody().getData();
    }
}
