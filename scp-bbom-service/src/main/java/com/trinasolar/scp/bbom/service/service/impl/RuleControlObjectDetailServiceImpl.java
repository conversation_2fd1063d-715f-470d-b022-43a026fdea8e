package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectDetail;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectDetailQuery;
import com.trinasolar.scp.bbom.domain.save.RuleControlObjectDetailSaveDTO;
import com.trinasolar.scp.bbom.service.repository.RuleControlObjectDetailRepository;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectDetailService;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectValueService;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 规则管控对象详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
@Slf4j
@Service("ruleControlObjectDetailService")
@RequiredArgsConstructor
public class RuleControlObjectDetailServiceImpl implements RuleControlObjectDetailService {
    private final RuleControlObjectValueService ruleControlObjectValueService;
    private final LovUtil lovUtil;
    @Autowired
    private RuleControlObjectDetailRepository repository;

    @Override
    public Page<RuleControlObjectDetail> queryByPage(RuleControlObjectDetailQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public RuleControlObjectDetailDTO queryById(Long id) {
        RuleControlObjectDetail queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        RuleControlObjectDetailDTO result = new RuleControlObjectDetailDTO();
        BeanUtils.copyProperties(queryObj, result);
        lovUtil.setLovLineName(result, Long.parseLong(result.getAttrOperator()), "attrOperatorName");
        return result;
    }

    @Override
    public RuleControlObjectDetailDTO save(RuleControlObjectDetailSaveDTO saveDTO) {
        RuleControlObjectDetail newObj;
        if (saveDTO.getId() != null) {
            newObj = repository.findById(saveDTO.getId()).orElse(new RuleControlObjectDetail());
        } else {
            newObj = new RuleControlObjectDetail();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);
        ruleControlObjectValueService.saveValues(newObj.getId(), saveDTO.getValues());

        return this.queryById(newObj.getId());
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    public void saveDetails(Long id, List<RuleControlObjectDetailSaveDTO> details) {
        verifyAndDelete(id, details);
        if (details == null || details.isEmpty()) {
            return;
        }

        for (RuleControlObjectDetailSaveDTO header : details) {
            header.setRuleControlObjectHeaderId(id);
            save(header);
        }
    }

    @Override
    public List<RuleControlObjectDetailDTO> listByRuleControlObjectHeaderId(Long id) {
        List<RuleControlObjectDetail> details = repository.listByRuleControlObjectHeaderId(id);
        return details.stream().map(detail -> {
            RuleControlObjectDetailDTO detailDTO = new RuleControlObjectDetailDTO();
            BeanUtils.copyProperties(detail, detailDTO);
            detailDTO.setValues(ruleControlObjectValueService.listByRuleControlObjectDetailId(detailDTO.getId()));
            lovUtil.setLovLineName(detailDTO, Long.parseLong(detailDTO.getAttrOperator()), "attrOperatorName");

            return detailDTO;
        }).collect(Collectors.toList());
    }

    private void verifyAndDelete(Long id, List<RuleControlObjectDetailSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<RuleControlObjectDetail> savedConfigs = repository.listByRuleControlObjectHeaderId(id);

        if (lines == null || lines.isEmpty()) {
            if (savedConfigs.isEmpty()) {
                return;
            } else {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
        }

        List<Long> currentIds = new ArrayList<>();
        for (RuleControlObjectDetailSaveDTO line : lines) {
            if (line.getId() != null) {
                currentIds.add(line.getId());
            }
        }

        List<RuleControlObjectDetail> deleteConfigs = new ArrayList<>();
        if (!savedConfigs.isEmpty()) {
            for (RuleControlObjectDetail savedConfig : savedConfigs) {
                if (!currentIds.contains(savedConfig.getId())) {
                    deleteConfigs.add(savedConfig);
                }
            }
        }

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }

    }
}
