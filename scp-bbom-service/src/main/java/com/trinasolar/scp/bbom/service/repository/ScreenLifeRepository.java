package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.ScreenLife;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 网版寿命信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Repository
public interface ScreenLifeRepository extends JpaRepository<ScreenLife, Long>, QuerydslPredicateExecutor<ScreenLife> {
}
