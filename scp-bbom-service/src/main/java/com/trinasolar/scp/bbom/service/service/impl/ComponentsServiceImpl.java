package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.ComponentsDEConvert;
import com.trinasolar.scp.bbom.domain.dto.ComponentsDTO;
import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.entity.Components;
import com.trinasolar.scp.bbom.domain.entity.QComponents;
import com.trinasolar.scp.bbom.domain.query.ComponentsQuery;
import com.trinasolar.scp.bbom.domain.save.ComponentsSaveDTO;
import com.trinasolar.scp.bbom.service.repository.ComponentsRepository;
import com.trinasolar.scp.bbom.service.service.ComponentsService;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.bbom.service.service.StructuresService;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * BOM行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Slf4j
@Service("componentsService")
@RequiredArgsConstructor
@CacheConfig(cacheNames = "BBOM_Components", cacheManager = "scpRedisCacheManager")
public class ComponentsServiceImpl implements ComponentsService {
    private static final QComponents qComponents = QComponents.components;

    private final ComponentsDEConvert convert;

    private final ComponentsRepository repository;

    private final JPAQueryFactory jpaQueryFactory;

    private final ItemsService itemsService;

    @Autowired
    @Lazy
    private StructuresService structuresService;

    @Override
    public Page<ComponentsDTO> queryByPage(ComponentsQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<Components> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<ComponentsDTO> queryList() {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        List<Components> componentsList = IterableUtils.toList(repository.findAll(booleanBuilder));
        return convert.toDto(componentsList);
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ComponentsQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qComponents.id.eq(query.getId()));
        }
        if (query.getBomId() != null) {
            booleanBuilder.and(qComponents.bomId.eq(query.getBomId()));
        }
        if (query.getOperationSeqNum() != null) {
            booleanBuilder.and(qComponents.operationSeqNum.eq(query.getOperationSeqNum()));
        }
        if (query.getComponentItemId() != null) {
            booleanBuilder.and(qComponents.componentItemId.eq(query.getComponentItemId()));
        }
        if (StringUtils.isNotEmpty(query.getBomStructure())) {
            booleanBuilder.and(qComponents.bomStructure.eq(query.getBomStructure()));
        }
        if (StringUtils.isNotEmpty(query.getSubstituteFlag())) {
            booleanBuilder.and(qComponents.substituteFlag.eq(query.getSubstituteFlag()));
        }
        if (query.getLastUpdateDate() != null) {
            booleanBuilder.and(qComponents.lastUpdateDate.eq(query.getLastUpdateDate()));
        }
        if (query.getItemNum() != null) {
            booleanBuilder.and(qComponents.itemNum.eq(query.getItemNum()));
        }
        if (StringUtils.isNotEmpty(query.getComponentQuantity())) {
            booleanBuilder.and(qComponents.componentQuantity.eq(query.getComponentQuantity()));
        }
        if (query.getComponentYieldFactor() != null) {
            booleanBuilder.and(qComponents.componentYieldFactor.eq(query.getComponentYieldFactor()));
        }
        if (StringUtils.isNotEmpty(query.getComponentRemarks())) {
            booleanBuilder.and(qComponents.componentRemarks.eq(query.getComponentRemarks()));
        }
        if (query.getEffectivityDate() != null) {
            booleanBuilder.and(qComponents.effectivityDate.eq(query.getEffectivityDate()));
        }
        if (StringUtils.isNotEmpty(query.getChangeNotice())) {
            booleanBuilder.and(qComponents.changeNotice.eq(query.getChangeNotice()));
        }
        if (query.getImplementationDate() != null) {
            booleanBuilder.and(qComponents.implementationDate.eq(query.getImplementationDate()));
        }
        if (query.getDisableDate() != null) {
            booleanBuilder.and(qComponents.disableDate.eq(query.getDisableDate()));
        }
        if (StringUtils.isNotEmpty(query.getAttributeCategory())) {
            booleanBuilder.and(qComponents.attributeCategory.eq(query.getAttributeCategory()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute1())) {
            booleanBuilder.and(qComponents.attribute1.eq(query.getAttribute1()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute2())) {
            booleanBuilder.and(qComponents.attribute2.eq(query.getAttribute2()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute3())) {
            booleanBuilder.and(qComponents.attribute3.eq(query.getAttribute3()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute4())) {
            booleanBuilder.and(qComponents.attribute4.eq(query.getAttribute4()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute5())) {
            booleanBuilder.and(qComponents.attribute5.eq(query.getAttribute5()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute6())) {
            booleanBuilder.and(qComponents.attribute6.eq(query.getAttribute6()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute7())) {
            booleanBuilder.and(qComponents.attribute7.eq(query.getAttribute7()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute8())) {
            booleanBuilder.and(qComponents.attribute8.eq(query.getAttribute8()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute9())) {
            booleanBuilder.and(qComponents.attribute9.eq(query.getAttribute9()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute10())) {
            booleanBuilder.and(qComponents.attribute10.eq(query.getAttribute10()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute11())) {
            booleanBuilder.and(qComponents.attribute11.eq(query.getAttribute11()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute12())) {
            booleanBuilder.and(qComponents.attribute12.eq(query.getAttribute12()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute13())) {
            booleanBuilder.and(qComponents.attribute13.eq(query.getAttribute13()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute14())) {
            booleanBuilder.and(qComponents.attribute14.eq(query.getAttribute14()));
        }
        if (StringUtils.isNotEmpty(query.getAttribute15())) {
            booleanBuilder.and(qComponents.attribute15.eq(query.getAttribute15()));
        }
        if (query.getPlanningFactor() != null) {
            booleanBuilder.and(qComponents.planningFactor.eq(query.getPlanningFactor()));
        }
        if (query.getQuantityRelated() != null) {
            booleanBuilder.and(qComponents.quantityRelated.eq(query.getQuantityRelated()));
        }
        if (query.getAcdType() != null) {
            booleanBuilder.and(qComponents.acdType.eq(query.getAcdType()));
        }
        if (query.getComponentSequenceId() != null) {
            booleanBuilder.and(qComponents.componentSequenceId.eq(query.getComponentSequenceId()));
        }
        if (query.getSubstituteComponentId() != null) {
            booleanBuilder.and(qComponents.substituteComponentId.eq(query.getSubstituteComponentId()));
        }
        if (query.getBillSequenceId() != null) {
            booleanBuilder.and(qComponents.billSequenceId.eq(query.getBillSequenceId()));
        }
        if (query.getWipSupplyType() != null) {
            booleanBuilder.and(qComponents.wipSupplyType.eq(query.getWipSupplyType()));
        }
        if (StringUtils.isNotEmpty(query.getSupplySubinventory())) {
            booleanBuilder.and(qComponents.supplySubinventory.eq(query.getSupplySubinventory()));
        }
        if (query.getSupplyLocatorId() != null) {
            booleanBuilder.and(qComponents.supplyLocatorId.eq(query.getSupplyLocatorId()));
        }
        if (query.getBomItemType() != null) {
            booleanBuilder.and(qComponents.bomItemType.eq(query.getBomItemType()));
        }
        if (query.getBasisType() != null) {
            booleanBuilder.and(qComponents.basisType.eq(query.getBasisType()));
        }
    }

    @Override
    public ComponentsDTO queryById(Long id) {
        Components queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ComponentsDTO save(ComponentsSaveDTO saveDTO) {
        Components newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new Components());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ComponentsQuery query, HttpServletResponse response) {
        List<ComponentsDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "BOM行", "BOM行", excelPara.getSimpleHeader(), excelData);
    }

    private static List<ComponentsDTO> filterByDisableDate(List<ComponentsDTO> componentsDTOS) {
        // 过滤disable的数据
        LocalDateTime now = LocalDateTime.now();
        componentsDTOS = componentsDTOS.stream().filter(item -> {
            return item.getDisableDate() == null || item.getDisableDate().isAfter(now);
        }).collect(Collectors.toList());
        return componentsDTOS;
    }

    @Override
    public List<ComponentsDTO> findByStructure(StructuresDTO structuresDTO) {
        Iterable<Components> components = repository.findAll(
                qComponents.billSequenceId.eq(structuresDTO.getBillSequenceId())
        );
        List<ComponentsDTO> componentsDTOS = convert.toDto(Lists.newArrayList(components));

        componentsDTOS = filterByDisableDate(componentsDTOS);

        componentsDTOS.forEach(i -> {
            i.setItem(itemsService.getOneBySourceItemId(i.getComponentItemId()));
        });
        return componentsDTOS.stream().filter(i -> i.getItem() != null).collect(Collectors.toList());
    }

    @Override
    public List<StructuresDTO> findBomStructureByComponentItemIdAndOrganizationId(Long sourceItemId, Long organizationId) {
        List<Components> all = Lists.newArrayList(repository.findAll(qComponents.componentItemId.eq(sourceItemId)));
        List<ComponentsDTO> componentsDTO = convert.toDto(all);
        componentsDTO = filterByDisableDate(componentsDTO);
        if (CollectionUtils.isEmpty(componentsDTO)) {
            return new ArrayList<>();
        }
        return structuresService.findByComponentDTOAndOrganizationId(componentsDTO, organizationId);
    }

    @Override
    @Cacheable(cacheNames = "BBOM_Components_findByStructureIdAndSourceItemId", key = "#structureId+'_'+#sourceItemId", unless = "#result == null")
    public ComponentsDTO findByStructureIdAndSourceItemId(Long structureId, Long sourceItemId) {
        List<Components> components = jpaQueryFactory.selectFrom(qComponents)
                .where(qComponents.bomId.eq(structureId).and(qComponents.componentItemId.eq(sourceItemId)))
                .fetch();
        List<ComponentsDTO> dto = convert.toDto(components);
        List<ComponentsDTO> componentsDTOS = filterByDisableDate(dto);
        if (!componentsDTOS.isEmpty()) {
            return componentsDTOS.get(0);
        }
        return null;
    }


    @Override
    public Long findByStructureIdAndValidity(List<Long> structureIdList, Long sourceItemId) {
        Long componentsNums = jpaQueryFactory.selectFrom(qComponents)
                .where(qComponents.bomId.in(structureIdList).and(qComponents.componentItemId.eq(sourceItemId))
                        .and(qComponents.disableDate.isNull()).or(qComponents.disableDate.gt(LocalDateTime.now())))
                .fetchCount();
        if (ObjectUtils.isNotEmpty(componentsNums) && 0!=componentsNums) {
            return componentsNums;
        }
        return null;
    }

    @Override
    @CacheEvict(cacheNames = "BBOM_Structures_findOneByMrpQuery", allEntries = true)
    public void updateComponentScreenSubstituteFlag(String alternateBomDesignator, ComponentsDTO componentsDTO, String substituteFlag, String substituteEnableFlag) {
        Components components = repository.getOne(componentsDTO.getId());
        components.setScreenSubstituteFlag(substituteFlag);
        components.setScreenSubstituteEnableFlag(substituteEnableFlag);
        repository.save(components);
    }

    @Override
    @CacheEvict(cacheNames = "BBOM_Structures_findOneByMrpQuery", allEntries = true)
    public void updateComponentSlurrySubstituteFlag(String alternateBomDesignator, ComponentsDTO componentsDTO, String slurrySubstituteFlag, String substituteEnableFlag) {
        Components components = repository.getOne(componentsDTO.getId());
        components.setSlurrySubstituteFlag(slurrySubstituteFlag);
        components.setSlurrySubstituteEnableFlag(substituteEnableFlag);
        repository.save(components);
    }

    @Override
    public List<ComponentsDTO> getComponentsList(List<Long> componentItemId) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qComponents.componentItemId.in(componentItemId));
        List<ComponentsDTO> componentsDTOList = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        return filterByDisableDate(componentsDTOList);
    }
}
