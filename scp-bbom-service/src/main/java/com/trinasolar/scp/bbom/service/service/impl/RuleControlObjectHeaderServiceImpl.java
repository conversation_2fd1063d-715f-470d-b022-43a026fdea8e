package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectHeaderDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectHeader;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.RuleControlObjectHeaderSaveDTO;
import com.trinasolar.scp.bbom.service.repository.RuleControlObjectHeaderRepository;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectDetailService;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectHeaderService;
import com.trinasolar.scp.common.api.base.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 规则管控对象头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:32
 */
@Slf4j
@Service("ruleControlObjectHeaderService")
@RequiredArgsConstructor
public class RuleControlObjectHeaderServiceImpl implements RuleControlObjectHeaderService {

    private final RuleControlObjectHeaderRepository repository;

    private final RuleControlObjectDetailService ruleControlObjectDetailService;

    @Override
    public Page<RuleControlObjectHeader> queryByPage(RuleControlObjectHeaderQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public RuleControlObjectHeaderDTO queryById(Long id) {
        RuleControlObjectHeader queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        RuleControlObjectHeaderDTO result = new RuleControlObjectHeaderDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public RuleControlObjectHeaderDTO save(RuleControlObjectHeaderSaveDTO saveDTO) {
        RuleControlObjectHeader newObj;
        if (saveDTO.getId() != null) {
            newObj = repository.findById(saveDTO.getId()).orElse(new RuleControlObjectHeader());
        } else {
            newObj = new RuleControlObjectHeader();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);
        ruleControlObjectDetailService.saveDetails(newObj.getId(), saveDTO.getDetails());

        return this.queryById(newObj.getId());
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHeaders(Long ruleLineId, List<RuleControlObjectHeaderSaveDTO> controlObjectHeaders) {
        verifyAndDelete(ruleLineId, controlObjectHeaders);
        if (controlObjectHeaders == null || controlObjectHeaders.isEmpty()) {
            return;
        }

        for (RuleControlObjectHeaderSaveDTO header : controlObjectHeaders) {
            header.setRuleLineId(ruleLineId);
            save(header);
        }
    }

    @Override
    public List<RuleControlObjectHeaderDTO> listByRuleLineId(Long ruleLineId) {
        List<RuleControlObjectHeader> headers = repository.listByRuleLineId(ruleLineId);
        return headers.stream().filter(item -> item.getControlObjectId() != null).map(header -> {
            RuleControlObjectHeaderDTO headerDTO = new RuleControlObjectHeaderDTO();
            BeanUtils.copyProperties(header, headerDTO);
            headerDTO.setDetails(ruleControlObjectDetailService.listByRuleControlObjectHeaderId(headerDTO.getId()));

            return headerDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RuleControlObjectHeader> listByRuleLineIdAndControlObject(Long ruleLineId, String bomStructure) {
        return repository.findByRuleLineIdAndControlObject(ruleLineId, bomStructure);
    }

    @Override
    public List<RuleControlObjectHeader> findByRuleLineIdInAndControlObject(List<Long> ruleLineIds, String bomStructure) {
        return repository.findByRuleLineIdInAndControlObject(ruleLineIds, bomStructure);
    }

    private void verifyAndDelete(Long id, List<RuleControlObjectHeaderSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<RuleControlObjectHeader> savedConfigs = repository.listByRuleLineId(id);

        if (lines == null || lines.isEmpty()) {
            if (!savedConfigs.isEmpty()) {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
            return;
        }
        List<Long> currentIds = lines.stream().filter(item -> item.getId() != null).map(RuleControlObjectHeaderSaveDTO::getId)
                .collect(Collectors.toList());
        List<RuleControlObjectHeader> deleteConfigs = savedConfigs.stream().filter(item -> !currentIds.contains(item.getId()))
                .collect(Collectors.toList());

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }
    }
}
