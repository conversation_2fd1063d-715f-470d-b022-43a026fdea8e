package com.trinasolar.scp.bbom.service.service.impl;

import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ApprovedVendorDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ErpOpenPODTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ErpOpenPRDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.TjOnHandDTO;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.ApprovedVendorNamesQuery;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.ErpOpenPOQuery;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.ErpOpenPRQuery;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.TjOnHandQuery;
import com.trinasolar.scp.bbom.service.feign.BmrpFeign;
import com.trinasolar.scp.bbom.service.service.BMrpService;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/2
 */
@Slf4j
@Service("BMrpService")
@RequiredArgsConstructor
@CacheConfig(cacheNames = "BMrpService", cacheManager = "caffeineCacheManager")
public class BMrpServiceImpl implements BMrpService {
    private final BmrpFeign bmrpFeign;
    @Override
    @Cacheable(cacheNames = "BMrpService_getTjOnHandDTOSByItemCodeAndOrgId", key = "#itemCode+'_'+#organizationId", unless = "#result == null")
    public List<TjOnHandDTO> getTjOnHandDTOSByItemCodeAndOrgId(String itemCode, Long organizationId) {
        TjOnHandQuery tjOnHandQuery = new TjOnHandQuery();
        tjOnHandQuery.setItemCode(itemCode);
        tjOnHandQuery.setOrganizationId(organizationId);
        //批量更新 当天的时间
        tjOnHandQuery.setLocalDate(LocalDate.now());
        List<TjOnHandDTO> tjOnHandDTOList = bmrpFeign.queryInventory(tjOnHandQuery).getBody().getData();

        return tjOnHandDTOList;
    }

    @Override
    @Cacheable(cacheNames = "BMrpService_getPoByItemCodeAndOrgId", key = "#itemCode+'_'+#organizationId", unless = "#result == null")
    public List<ErpOpenPODTO> getPoByItemCodeAndOrgId(String itemCode, Long organizationId) {
        ErpOpenPOQuery erpOpenPOQuery = new ErpOpenPOQuery();
        erpOpenPOQuery.setOrganizationId(organizationId);
        erpOpenPOQuery.setItemCode(itemCode);
        List<ErpOpenPODTO> podtoList = bmrpFeign.list(erpOpenPOQuery).getBody().getData();

        return podtoList;
    }

    @Override
    @Cacheable(cacheNames = "BMrpService_getPrByItemCodeAndOrgId", key = "#itemCode+'_'+#organizationId", unless = "#result == null")
    public List<ErpOpenPRDTO> getPrByItemCodeAndOrgId(String itemCode, Long organizationId) {
        ErpOpenPRQuery erpOpenPRQuery = new ErpOpenPRQuery();
        erpOpenPRQuery.setOrganizationId(organizationId);
        erpOpenPRQuery.setItemCode(itemCode);
        List<ErpOpenPRDTO> prdtoList = bmrpFeign.queryQuantity(erpOpenPRQuery).getBody().getData();
        return prdtoList;
    }

    @Override
    public Map<String, ApprovedVendorDTO> getApprovedVendorDTOByVendorNames(List<String> vendorNames) {
        ApprovedVendorNamesQuery query = new ApprovedVendorNamesQuery();
        query.setVendorNames(vendorNames);
        ResponseEntity<Results<List<ApprovedVendorDTO>>> approvedVendorDTOByVendorNames =
                bmrpFeign.getApprovedVendorDTOByVendorNames(query);
        List<ApprovedVendorDTO> data = approvedVendorDTOByVendorNames.getBody().getData();
        return data.stream().collect(Collectors.toMap(ApprovedVendorDTO::getVendorName, v -> v, (v1, v2) -> v1));
    }
}
