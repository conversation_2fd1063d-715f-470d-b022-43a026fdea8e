package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.service.service.impl.MaterielMatchHeaderServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 料号匹配缓存服务
 * 用于优化doSingleMatch方法的性能问题
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaterielMatchCacheService {

    private final ItemsService itemsService;
    private final MaterielMatchHeaderServiceImpl materielMatchHeaderService;
    
    /**
     * 缓存所有5A料号数据
     * 缓存时间: 30分钟
     */
    @Cacheable(cacheNames = "items_all_5a", key = "'all_5a_items'", unless = "#result.size() == 0")
    public List<ItemsDTO> getAllItems() {
        log.info("从数据库加载所有5A料号数据");
        return itemsService.queryByMatchingAll();
    }
    
    /**
     * 缓存所有表达式规则
     * 缓存时间: 1小时
     */
    @Cacheable(cacheNames = "express_rules", key = "'all_rules'")
    public Map<Long, List<ExpressRuleLineDTO>> getAllExpressRules() {
        log.info("从数据库加载所有表达式规则");
        return materielMatchHeaderService.getExpressRuleLineList();
    }
    
    /**
     * 缓存DP属性和Item属性对应关系
     * 缓存时间: 1小时
     */
    @Cacheable(cacheNames = "dp_columns", key = "'dp_item_mapping'")
    public Map<String, String> getDpItemMapping() {
        log.info("从数据库加载DP属性映射关系");
        return materielMatchHeaderService.getDpColAndItemColMap();
    }
    
    /**
     * 缓存DP转换脚本映射
     * 缓存时间: 1小时
     */
    @Cacheable(cacheNames = "dp_trans_script", key = "'dp_trans_mapping'")
    public Map<String, String> getDpTransScriptMapping() {
        log.info("从数据库加载DP转换脚本映射");
        return materielMatchHeaderService.getDpColAndTransScriptMap();
    }
    
    /**
     * 缓存结构数据
     * 缓存时间: 30分钟
     */
    @Cacheable(cacheNames = "structures_all", key = "'all_structures'")
    public Map<String, StructuresDTO> getAllStructures() {
        log.info("从数据库加载所有结构数据");
        return materielMatchHeaderService.queryListByStructures();
    }
    
    /**
     * 缓存BOM替代项数据
     * 按车间分组缓存，缓存时间: 30分钟
     */
    @Cacheable(cacheNames = "bom_designator", key = "#workshop", unless = "#result.isEmpty()")
    public Map<Long, List<ErpAlternateDesignatorDTO>> getBomDesignatorByWorkshop(String workshop) {
        log.info("从远程服务加载BOM替代项数据: workshop={}", workshop);
        return materielMatchHeaderService.getBomDesigntorByMatchId(workshop);
    }
    
    /**
     * 缓存电池类型数据
     * 缓存时间: 1小时
     */
    @Cacheable(cacheNames = "battery_type", key = "#batteryName", unless = "#result == null")
    public BatteryTypeMainDTO getBatteryTypeByName(String batteryName) {
        log.info("从数据库加载电池类型数据: batteryName={}", batteryName);
        return materielMatchHeaderService.batteryTypeMainService.queryBatteryCodeTypeAllByBatteryName(batteryName);
    }
    
    /**
     * 批量查询料号信息，避免N+1查询
     */
    public Map<String, ItemsDTO> batchFindItemsByCodesAndOrg(List<String> itemCodes, Long orgId) {
        if (itemCodes == null || itemCodes.isEmpty()) {
            return new ConcurrentHashMap<>();
        }
        
        log.info("批量查询料号信息: itemCodes.size={}, orgId={}", itemCodes.size(), orgId);
        
        // 使用批量查询替代循环查询
        return itemCodes.parallelStream()
            .collect(ConcurrentHashMap::new,
                (map, itemCode) -> {
                    ItemsDTO item = itemsService.findOneByItemCodeAndOrganizationId(itemCode, orgId);
                    if (item != null) {
                        map.put(itemCode, item);
                    }
                },
                ConcurrentHashMap::putAll);
    }
    
    /**
     * 预热缓存
     * 在系统启动时或定时任务中调用
     */
    public void warmUpCache() {
        log.info("开始预热料号匹配缓存");
        
        try {
            // 预热基础数据缓存
            getAllItems();
            getAllExpressRules();
            getDpItemMapping();
            getDpTransScriptMapping();
            getAllStructures();
            
            log.info("料号匹配缓存预热完成");
        } catch (Exception e) {
            log.error("料号匹配缓存预热失败", e);
        }
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        log.info("清理料号匹配相关缓存");
        // 这里可以添加具体的缓存清理逻辑
        // 例如使用CacheManager来清理指定的缓存
    }
}
