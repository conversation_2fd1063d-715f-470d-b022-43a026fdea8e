package com.trinasolar.scp.bbom.service.feign;


import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;


/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ExternalFeign
 * @Description 外部接口调用-pdm
 * @Date 2024/01/19
 **/
@FeignClient(name = FeignConstant.PDM_SCP, url = "${ipass.url}")
public interface ExternalFeign {

    /**
     * 物料生命周期接口
     *
     * @return 查询结果
     */
    @PostMapping(value = "${pdm.queryItem.url}", headers = {"Content-Type=application/json;charset=UTF-8", "tsl-clientid=${pdm.item.clientid}", "tsl-clientsecret=${pdm.item.clientsecret}", "password=interface", "username=interface"})
    @ApiOperation(value = "物料生命周期接口", notes = "物料生命周期接口")
    Map<String, Object> queryItemService(@RequestBody Map map);

}
