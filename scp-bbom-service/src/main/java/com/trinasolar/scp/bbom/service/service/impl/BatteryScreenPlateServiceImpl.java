package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.ibm.dpf.common.domain.entity.User;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.BatteryScreenPlateDEConvert;
import com.trinasolar.scp.bbom.domain.convert.ItemsDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ApprovedVendorDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ErpOpenPODTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ErpOpenPRDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.TjOnHandDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryScreenPlate;
import com.trinasolar.scp.bbom.domain.entity.Items;
import com.trinasolar.scp.bbom.domain.entity.QBatteryScreenPlate;
import com.trinasolar.scp.bbom.domain.entity.QItems;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateExcelMailDTO;
import com.trinasolar.scp.bbom.domain.excel.BatterySiliconWaferExcelDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.FileUtil;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.BmrpFeign;
import com.trinasolar.scp.bbom.service.repository.BatteryScreenPlateRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.service.impl.scheduleEmail.SCPFileService;
import com.trinasolar.scp.bbom.service.util.LocalDateTimeUtil;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Slf4j
@Service("batteryScreenPlateService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class BatteryScreenPlateServiceImpl implements BatteryScreenPlateService {
    private static final QBatteryScreenPlate qBatteryScreenPlate = QBatteryScreenPlate.batteryScreenPlate;

    private final static String CHECK_A = "A";

    private final static String CHECK_B = "B";

    private final static String CHECK_C = "C";

    private final static String SWITCHING_TYPE = "NETWORK_VERSION_SWITCHING_TYPE";

    private final static String BASE_PLACE = "base_place";

    private final static String WORK_SHOP = "work_shop";

    private final static String WORK_UNIT = "work_unit";

    private final static String CELL_MACHINE = "CELL_MACHINE";

    private static final QItems qItems = QItems.items;

    private final static String XCPDR = "新产品导入";

    private final static String ZDY = "中大样";

    private final BatteryScreenPlateDEConvert convert;

    private final BatteryScreenPlateRepository repository;

    private final BatteryTypeMainService batteryTypeMainService;

    private final BatteryFeignService batteryFeignService;

    private final BomService bomService;

    private final BmrpFeign bmrpFeign;

    private final BMrpService bMrpService;

    private final JPAQueryFactory jpaQueryFactory;

    private final StructuresService structuresService;

    private final ComponentsService componentsService;

    private final ItemsService itemsService;

    private final MailService mailService;

    private final SlurryInformationService informationService;

    private final LovUtil lovUtil;


    @Autowired
    private SCPFileService scpFileService;

    private final ItemsDEConvert itemsDEConvert;

    @Autowired
    @Lazy
    BatteryScreenPlateService batteryScreenPlateService;

    private static void queryConvert(BatteryScreenPlateDTO excelDTO) {
        if (StringUtils.isNumeric(excelDTO.getWorkbench())) {
            LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(excelDTO.getWorkbench()));
            if (lovLineDTO != null) {
                excelDTO.setWorkbenchName(lovLineDTO.getLovName());
            }
        }
        if (null != excelDTO.getSwitchType()) {
            LovLineDTO lovLineDTO = LovUtils.get(excelDTO.getSwitchType());
            if (lovLineDTO != null) {
                excelDTO.setSwitchTypeName(lovLineDTO.getLovName());
            }
        }


        Map<String, LovLineDTO> singleGlassFlagLovs = LovUtils.getAllByHeaderCode("yes_or_no");
        LovLineDTO singleGlassFlagLov = singleGlassFlagLovs.get(excelDTO.getSingleGlassFlag());
        if(Objects.nonNull(singleGlassFlagLov)) {
            excelDTO.setSingleGlassFlagName(singleGlassFlagLov.getLovName());
        }
        LovLineDTO mainGridInfoLov= LovUtils.get("7A01500100117", excelDTO.getMainGridInfo());
        if(Objects.nonNull(mainGridInfoLov)) {
            excelDTO.setMainGridInfo(mainGridInfoLov.getLovName());
        }
        Map<String, LovLineDTO> mainGridSpaceLov= LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
        for(String key : mainGridSpaceLov.keySet()){
            LovLineDTO value = mainGridSpaceLov.get(key);
            if("main_grid_space".equals(value.getAttribute1()) && value.getLovValue().equals(excelDTO.getMainGridSpace())){
                excelDTO.setMainGridSpace(excelDTO.getMainGridSpace());
            }
        }
        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        excelDTO.setWorkunitName(excelDTO.getWorkunit());
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));
    }

    private static String checkSwitchSave(BatteryScreenPlateSaveDTO saveDTO) {
        // 查询切换类型
        String check = "";
        Map<String, LovLineDTO> switchingType = LovUtils.getAllByHeaderCode(SWITCHING_TYPE);
        // 新增修改校验
        if (StringUtils.isBlank(saveDTO.getSwitchTypeName())) {
            throw new BizException("bbom_valid_switchType_notBlank");
        }
        LovLineDTO switchingTypeLov = switchingType.get(saveDTO.getSwitchTypeName());
        if(Objects.isNull(switchingTypeLov)){
            throw new BizException("请维护正确的切换类型数据");
        }
        saveDTO.setSwitchType(switchingTypeLov.getLovLineId());
        LovLineDTO switchType = Optional.ofNullable(LovUtils.get(saveDTO.getSwitchType())).orElseThrow(() -> new BizException("bbom_valid_switchType_illegal"));
        if (switchType == null) {
            throw new BizException("bbom_valid_switchType_illegal");
        }
        if (CHECK_A.equals(switchType.getAttribute1())) {
            check = CHECK_A;
        } else if (CHECK_B.equals(switchType.getAttribute1())) {
            check = CHECK_B;
        } else if (CHECK_C.equals(switchType.getAttribute1())) {
            check = CHECK_C;
        } else {
            throw new BizException("bbom_valid_switchType_notMatch");
        }
        // 获取切换类
        saveDTO.setSwitchType(switchType.getLovLineId());
        return check;
    }

    private static String checkSwitchImport(BatteryScreenPlateExcelDTO excelDTO) {
        // 查询切换类型
        String check = "";
        Map<String, LovLineDTO> switchingType = LovUtils.getAllByHeaderCode(SWITCHING_TYPE);
        if (StringUtils.isBlank(excelDTO.getSwitchTypeName())) {
            throw new BizException("bbom_valid_switchType_notBlank");
        }
        LovLineDTO switchType = switchingType.get(excelDTO.getSwitchTypeName());
        if (switchType == null) {
            throw new BizException("bbom_valid_switchType_illegal");
        }
        if (CHECK_A.equals(switchType.getAttribute1())) {
            check = CHECK_A;
        } else if (CHECK_B.equals(switchType.getAttribute1())) {
            check = CHECK_B;
        } else if (CHECK_C.equals(switchType.getAttribute1())) {
            check = CHECK_C;
        } else {
            throw new BizException("bbom_valid_switchType_notMatch");
        }
        // 获取切换类
        excelDTO.setSwitchType(switchType.getLovLineId());
        return check;
    }

    private void verifySave(BatteryScreenPlateSaveDTO saveDTO) {
        String check = checkSwitchSave(saveDTO);
        //空值判断
        if (StringUtils.isBlank(saveDTO.getSwitchTypeName())) {
            throw new BizException("bbom_valid_switchType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlaceName())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshopName())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }else{
            saveDTO.setWorkshop(saveDTO.getWorkshopName());
        }
        if (StringUtils.isBlank(saveDTO.getWorkbenchName())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }
        // 补齐电池类型编码
        if (StringUtils.isNotEmpty(saveDTO.getBatteryName())) {
            BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryName(saveDTO.getBatteryName());
            Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
                saveDTO.setBatteryCode(w.getBatteryCode());
            });
            if (StringUtils.isBlank(saveDTO.getBatteryCode())) {
                throw new BizException("bbom_valid_batteryCode_illegal", saveDTO.getBatteryName());
            }
        }
        if ((!XCPDR.equals(saveDTO.getSwitchTypeName())) && null == saveDTO.getId()) {
            BatteryScreenPlateDTO plateDTO = returnScreenPlateDto(convert.saveToDTO(saveDTO));
            saveDTO.setIsExistBom(plateDTO.getIsExistBom());
        }
        //!"新产品导入".equals(saveDTO.getSwitchTypeName())
        if (!CHECK_B.equals(check)) {
            if (StringUtils.isBlank(saveDTO.getLine())) {
                throw new BizException("bbom_valid_line_notBlank");
            }
        }
        //切换类型是新产品导入时电池类型设置为允许不填
        if (StringUtils.isBlank(saveDTO.getBatteryName()) && !CHECK_B.equals(check)) {
            throw new BizException("bbom_valid_batteryName_notBlank");
        }
        //切换类型是新产品导入时供应商全称必填
        if (StringUtils.isBlank(saveDTO.getVendorName()) && CHECK_B.equals(check)) {
            throw new BizException("bbom_valid_vendorName_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getItemCodeNew())) {
            throw new BizException("bbom_valid_itemCodeNew_notBlank");
        }
        // 校验合法性
        // 生产基地 生产车间 api接口  2024.12.23去除“网版物料-旧”及其对应的“物料描述”和“物料状态”、“生产单元”、“旧网版库存”、“旧网版在途”
        List<ModuleBasePlaceDTO> moduleBasePlaceDTOList = batteryFeignService.
                findBasePlaceWorkshopWorkUnit(saveDTO.getBasePlaceName(), saveDTO.getWorkshopName(), null);
        if (CollectionUtils.isEmpty(moduleBasePlaceDTOList)) {
            throw new BizException("bbom_valid_basePlaceWorkshop_illegal", saveDTO.getBasePlaceName(), saveDTO.getWorkshopName());
        }
        ModuleBasePlaceDTO moduleBasePlaceDTO = moduleBasePlaceDTOList.get(0);

        // 获取基地
        if(StringUtils.isBlank(saveDTO.getBasePlace())){
            saveDTO.setBasePlace(saveDTO.getBasePlaceName());
        }
        if(StringUtils.isBlank(saveDTO.getWorkshop())){
            saveDTO.setWorkshop(saveDTO.getWorkshop());
        }
        //新料号拆分匹配逻辑
        if(StringUtils.isNotBlank(saveDTO.getItemCodeNew())){
            List<String> itemCodeNewList = Arrays.asList(saveDTO.getItemCodeNew().split(","));
            List<ItemsDTO> itemsDTOList = itemsDEConvert.toDto(jpaQueryFactory.selectFrom(qItems)
                    .where(qItems.itemCode.in(itemCodeNewList)
                            .and(qItems.organizationId.eq(82L)))
                    .orderBy(qItems.id.desc())
                    .fetch());
            if(itemsDTOList.size()<itemCodeNewList.size()){
                throw new BizException("请选择已维护的料号或不要输入相同的料号");
            }
            //2025.1.22产品确认暂时注释,匹配面太广,后期需要指定几个字段匹配
//            if(itemCodeNewList.size()>1){
//                itemsDTOList.forEach(itemDto->{
//                    itemsDTOList.forEach(contrasItemDto->{
//                        for(int i = 1; i <= 60; i++){
//                            Object value = ReflectUtil.getFieldValue(itemDto, "segment" + i);
//                            Object contrastValue = ReflectUtil.getFieldValue(contrasItemDto, "segment" + i);
//                            //校验segment1-segment60中除segment27以外字段是否一致
//                            if(i==27){
//                                continue;
//                            }
//                            if(ObjectUtils.isEmpty(value)){
//                                value = "";
//                            }
//                            if(ObjectUtils.isEmpty(contrastValue)){
//                                contrastValue = "";
//                            }
//                            if(!value.toString().equals(contrastValue.toString())){
//                                throw new BizException("料号属性不一致，请检查或分开维护");
//                            }
//                        }
//                    });
//                });
//            }
        }

        // 批量查询供应商数据
        List<String> vendorNames = new ArrayList<>();
        vendorNames.add(saveDTO.getVendorName());
        vendorNames=vendorNames.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, ApprovedVendorDTO> vendorMap = bMrpService.getApprovedVendorDTOByVendorNames(vendorNames);
        //供应商不为空
        if (StringUtils.isNotEmpty(saveDTO.getVendorName())) {
            if (null == vendorMap.get(saveDTO.getVendorName())) {
                throw new BizException("bbom_valid_vendorName_nonExist", saveDTO.getVendorName());
            } else {
                if (!vendorMap.containsKey(saveDTO.getVendorName())) {
                    throw new BizException("bbom_valid_vendorName_nonExist", saveDTO.getVendorName());
                }
                saveDTO.setVendorId(vendorMap.get(saveDTO.getVendorName()).getVendorId());
            }
        }
        // 线体数手工维护，但维护的数值小于等于APS接口中生产单元带出的数
        if (StringUtils.isNotBlank(saveDTO.getLine())
                && StringUtils.isNumeric(saveDTO.getLine())
                && moduleBasePlaceDTO.getTotalLine() != null) {
            Long line = Long.valueOf(saveDTO.getLine());
            if (line > moduleBasePlaceDTO.getTotalLine()) {
                throw new BizException("bbom_valid_totalLine");
            }
        }
        // 转换切换类型 lov name -> id
        // 校验机台合法性 lov
        LovLineDTO workbench = Optional.ofNullable(LovUtils.getByName(CELL_MACHINE,
                saveDTO.getWorkbenchName())).orElseThrow(() -> new BizException("bbom_valid_workbench_illegal", saveDTO.getWorkbenchName()));
        // 获取机台信息
        saveDTO.setWorkbench(String.valueOf(workbench.getLovLineId()));

        //日期校验
        if(StringUtils.isNotBlank(saveDTO.getEffectiveStartDates()) || StringUtils.isNotBlank(saveDTO.getEffectiveEndDates())){
            if(StringUtils.isNotBlank(saveDTO.getEffectiveStartDates())){
                saveDTO.setEffectiveStartDates(saveDTO.getEffectiveStartDates().replaceAll("/","-"));
            }
            if(StringUtils.isNotBlank(saveDTO.getEffectiveEndDates())){
                saveDTO.setEffectiveEndDates(saveDTO.getEffectiveEndDates().replaceAll("/","-"));
            }
            boolean isEffectiveStartDateVaild = isValidDateFormat(saveDTO.getEffectiveStartDates(), "yyyy-MM-dd");
            if (!isEffectiveStartDateVaild) {
                throw new BizException("bbom_valid_effectiveStartDate_format_illegal", saveDTO.getEffectiveStartDates());
            }
            boolean isEffectiveEndDateVaild = isValidDateFormat(saveDTO.getEffectiveEndDates(), "yyyy-MM-dd");
            if (!isEffectiveEndDateVaild) {
                throw new BizException("bbom_valid_effectiveEndDate_format_illegal", saveDTO.getEffectiveEndDates());
            }
            try{
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                saveDTO.setEffectiveStartDate(sdf.parse(saveDTO.getEffectiveStartDates()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                saveDTO.setEffectiveEndDate(sdf.parse(saveDTO.getEffectiveEndDates()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            }catch (Exception e){
                throw new BizException("时间日期格式有误请检查");
            }
        }
        //lov合法校验并赋值
        Map<String,LovLineDTO> mainGridSpaceLovs = lovUtil.getLovMapByCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
        Map<String,LovLineDTO> singleGlassFlagLovs = lovUtil.getLovMapByCode("yes_or_no");
        Map<String,LovLineDTO> screenPlateTypeLovs = lovUtil.getLovMapByCode("CELL_MACHINE");
        Map<String,LovLineDTO> mainGridInfo= lovUtil.getLovMapByCode("7A01500100117");

        LovLineDTO mainGridInfoLov = mainGridInfo.get(saveDTO.getMainGridInfo());
        if(StringUtils.isNotBlank(saveDTO.getMainGridInfo()) && Objects.isNull(mainGridInfoLov)){
            throw new BizException("请维护正确的主栅信息数据");
        }else if(Objects.nonNull(mainGridInfoLov)){
            saveDTO.setMainGridInfo(mainGridInfoLov.getLovValue());
        }
        //LOV：BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS 限制只取attribute1的值为main_grid_space
        LovLineDTO mainGridSpaceLov = mainGridSpaceLovs.get(saveDTO.getMainGridSpace());
        if(StringUtils.isNotBlank(saveDTO.getMainGridSpace()) && Objects.isNull(mainGridSpaceLov)){
            throw new BizException("请维护正确的主栅间距数据");
        }else if(Objects.nonNull(mainGridSpaceLov) && "main_grid_space".equals(mainGridSpaceLov.getAttribute1())){
            saveDTO.setMainGridSpace(mainGridSpaceLov.getLovValue());
        }
        //LOV：yes_or_no
        LovLineDTO singleGlassFlagLov = singleGlassFlagLovs.get(saveDTO.getSingleGlassFlag());
        LovLineDTO singleGlassFlagNameLov = singleGlassFlagLovs.get(saveDTO.getSingleGlassFlagName());
        if(StringUtils.isNotBlank(saveDTO.getSingleGlassFlag()) && Objects.isNull(singleGlassFlagLov)){
            throw new BizException("请维护正确的单玻数据");
        }else if(Objects.nonNull(singleGlassFlagLov)){
            saveDTO.setSingleGlassFlag(singleGlassFlagLov.getLovValue());
        }else if(Objects.nonNull(singleGlassFlagNameLov)){
            saveDTO.setSingleGlassFlag(singleGlassFlagNameLov.getLovValue());
        }
        //取原表中screen_plate_version_category_new；根据原表中机台信息解析。用机台查询LOV：CELL_MACHINE的attribute3
        if(StringUtils.isNotBlank(saveDTO.getWorkbench())){
            LovLineDTO screenPlateTypeLov = screenPlateTypeLovs.get(saveDTO.getWorkbench());
            if(StringUtils.isNotBlank(saveDTO.getWorkbench()) && Objects.isNull(screenPlateTypeLov)){
                throw new BizException("请维护正确的网版类型数据");
            }else if(Objects.nonNull(screenPlateTypeLov)){
                saveDTO.setScreenPlateVersionCategoryNew(screenPlateTypeLov.getAttribute3());
            }
        }
        //根据网版料号-新到bbom_items表中获取segment22的值（限制organization_id = '82'）
        if(StringUtils.isNotBlank(saveDTO.getItemCodeNew())){
            List<String> itemCodeList = Arrays.asList(saveDTO.getItemCodeNew().split(","));
            ItemsDTO itemsDTO = itemsDEConvert.toDto(jpaQueryFactory.selectFrom(qItems)
                    .where(qItems.itemCode.in(itemCodeList)
                            .and(qItems.organizationId.eq(82L)))
                    .orderBy(qItems.id.desc())
                    .fetchFirst());
            if(ObjectUtil.isNotEmpty(itemsDTO)){
                saveDTO.setGridsNumber(itemsDTO.getSegment22());
                saveDTO.setItemDescNew(itemsDTO.getItemDesc());
                saveDTO.setScreenPlateVersionCategoryNew(itemsDTO.getSegment2());

            }
        }
        //导入或保存时，由系统功能对生产单元默认为生产车间拼接“-1”，如车间名THAO_C3，默认对生产单元赋值为THAO_C3-1
        if(StringUtils.isNotBlank(saveDTO.getWorkshop())){
            saveDTO.setWorkunit(saveDTO.getWorkshop()+"-1");
            saveDTO.setWorkunitName(saveDTO.getWorkshop()+"-1");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkbench())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }
    }

    private static void verifyImport(BatteryScreenPlateExcelDTO saveDTO, String check) {
        // 导入校验
        if (StringUtils.isBlank(saveDTO.getSwitchTypeName())) {
            throw new BizException("bbom_valid_switchType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlaceName())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshopName())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkbenchName())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }
        //!"新产品导入".equals(saveDTO.getSwitchTypeName())
        if (!CHECK_B.equals(check)) {
            if (StringUtils.isBlank(saveDTO.getLine())) {
                throw new BizException("bbom_valid_line_notBlank");
            }
//            if (StringUtils.isBlank(saveDTO.getWorkunitName())) {
//                throw new BizException("bbom_valid_workunit_notBlank");
//            }
        }
        //切换类型是新产品导入时电池类型设置为允许不填
        if (StringUtils.isBlank(saveDTO.getBatteryName()) && !CHECK_B.equals(check)) {
            throw new BizException("bbom_valid_batteryName_notBlank");
        }
        //切换类型是新产品导入时供应商全称必填
        if (StringUtils.isBlank(saveDTO.getVendorName()) && CHECK_B.equals(check)) {
            throw new BizException("bbom_valid_vendorName_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getItemCodeNew())) {
            throw new BizException("bbom_valid_itemCodeNew_notBlank");
        }
        // 网版料号-旧默认不填写，旧网板消耗数量默认不填写
        if (StringUtils.isBlank(saveDTO.getNumberNew()) && CHECK_B.equals(check)) {
            throw new BizException("bbom_valid_screenNumberNew_notBlank");
        }
//        if (CHECK_C.equals(check)) {
//            // 新网版消耗数量默认不填写
//            if (StringUtils.isBlank(saveDTO.getItemCodeOld())) {
//                throw new BizException("bbom_valid_itemCodeOld_notBlank");
//            }
//            /*if (StringUtils.isBlank(saveDTO.getNumberOld())) {
//                throw new BizException("bbom_valid_screenNumberOld_notBlank");
//            }*/
//        }
    }

    private static void extractedSave(BatteryScreenPlateSaveDTO dto, BooleanBuilder booleanBuilder) {
        if (null != dto.getId()) {
            booleanBuilder.and(qBatteryScreenPlate.id.eq(dto.getId()));
        }
    }

    private static void extractedImport(BatteryScreenPlateSaveDTO excelDTO, BooleanBuilder booleanBuilder) {
        if (StringUtils.isNotEmpty(excelDTO.getBatteryName())) {
            booleanBuilder.and(qBatteryScreenPlate.batteryName.eq(excelDTO.getBatteryName()));
        } else {
            booleanBuilder.and(qBatteryScreenPlate.batteryName.isNull().or(qBatteryScreenPlate.batteryName.isEmpty()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getItemCodeNew())) {
            booleanBuilder.and(qBatteryScreenPlate.itemCodeNew.eq(excelDTO.getItemCodeNew()));
        }
        if (null != excelDTO.getSwitchType()) {
            booleanBuilder.and(qBatteryScreenPlate.switchType.eq(excelDTO.getSwitchType()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlate.basePlace.eq(excelDTO.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlate.workshop.eq(excelDTO.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getWorkbench())) {
            booleanBuilder.and(qBatteryScreenPlate.workbench.eq(excelDTO.getWorkbench()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getMainGridSpace())) {
            booleanBuilder.and(qBatteryScreenPlate.mainGridSpace.eq(excelDTO.getMainGridSpace()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getSingleGlassFlag())) {
            booleanBuilder.and(qBatteryScreenPlate.singleGlassFlag.eq(excelDTO.getSingleGlassFlag()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getGridsNumber())) {
            booleanBuilder.and(qBatteryScreenPlate.gridsNumber.eq(excelDTO.getGridsNumber()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getScreenPlateVersionCategoryNew())) {
            booleanBuilder.and(qBatteryScreenPlate.screenPlateVersionCategoryNew.eq(excelDTO.getScreenPlateVersionCategoryNew()));
        }
        //类型是新产品导入时唯一性校验修改为：切换类型+网版料号-新+电池类型+机台+生产基地+生产车间+供应商名称
        if (XCPDR.equals(excelDTO.getSwitchTypeName())) {
            booleanBuilder.and(qBatteryScreenPlate.vendorId.eq(excelDTO.getVendorId()));
        } else {
//            if (StringUtils.isNotEmpty(excelDTO.getWorkunit())) {
//                booleanBuilder.and(qBatteryScreenPlate.workunit.eq(excelDTO.getWorkunit()));
//            } else {
//                booleanBuilder.and(qBatteryScreenPlate.workunit.isNull().or(qBatteryScreenPlate.workunit.isEmpty()));
//            }
//            if (StringUtils.isNotEmpty(excelDTO.getItemCodeOld())) {
//                booleanBuilder.and(qBatteryScreenPlate.itemCodeOld.eq(excelDTO.getItemCodeOld()));
//            } else {
//                booleanBuilder.and(qBatteryScreenPlate.itemCodeOld.isNull().or(qBatteryScreenPlate.itemCodeOld.isEmpty()));
//            }
            //线体
            if (StringUtils.isNotEmpty(excelDTO.getLine())) {
                booleanBuilder.and(qBatteryScreenPlate.line.eq(excelDTO.getLine()));
            } else {
                booleanBuilder.and(qBatteryScreenPlate.line.isNull().or(qBatteryScreenPlate.line.isEmpty()));
            }
        }
    }

    /**
     * 设置发件人
     *
     * @param attribute3 多个发件人用;分割
     * @param emails
     */
    private static void setEmails(String attribute3, List<String> emails) {
        if (StringUtils.isNotBlank(attribute3)) {
            String[] es = attribute3.split(";");
            for (String e : es) {
                emails.add(e);
            }
        }
    }

    private static void extracted(BatteryScreenPlateSaveDTO dto, BooleanBuilder booleanBuilder) {
        //电池类型+生产基地+生产车间+网版料号-新+机台+有效日期_起+有效日期止不允许重复，若维护重复数据返回消息“电池类型+生产基地+生产车间+网版料号-新+机台+有效日期_起+有效日期止唯一性校验，不允许重复”
        if (StringUtils.isNotBlank(dto.getBatteryCode())) {
            booleanBuilder.and(qBatteryScreenPlate.batteryCode.eq(dto.getBatteryCode()));
        }else{
            booleanBuilder.and(qBatteryScreenPlate.batteryCode.isNull());
        }
        if (StringUtils.isNotBlank(dto.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlate.basePlace.eq(dto.getBasePlace()));
        }
        if (StringUtils.isNotBlank(dto.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlate.workshop.eq(dto.getWorkshop()));
        }
        if (StringUtils.isNotBlank(dto.getItemCodeNew())) {
            booleanBuilder.and(qBatteryScreenPlate.itemCodeNew.eq(dto.getItemCodeNew()));
        }
        if (StringUtils.isNotBlank(dto.getWorkbench())) {
            booleanBuilder.and(qBatteryScreenPlate.workbench.eq(dto.getWorkbench()));
        }
        if (ObjectUtils.isNotEmpty(dto.getEffectiveStartDate())) {
            booleanBuilder.and(qBatteryScreenPlate.effectiveStartDate.eq(dto.getEffectiveStartDate()));
        }else{
            booleanBuilder.and(qBatteryScreenPlate.effectiveStartDate.isNull());
        }
        if (ObjectUtils.isNotEmpty(dto.getEffectiveEndDate())) {
            booleanBuilder.and(qBatteryScreenPlate.effectiveEndDate.eq(dto.getEffectiveEndDate()));
        }else{
            booleanBuilder.and(qBatteryScreenPlate.effectiveEndDate.isNull());
        }
    }

    public static boolean isValidDateFormat(String dateStr, String dateFormat) {
        if (ObjectUtil.isEmpty(dateStr)) {
            return true;
        }
        try {
            DateUtil.parse(dateStr, dateFormat); // 将字符串解析为日期对象，如果解析成功，则说明字符串是有效的日期格式；否则说明字符串不是有效的日期格式。
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Page<BatteryScreenPlateDTO> queryByPage(BatteryScreenPlateQuery query) {
        User user = UserUtil.getUser();
        String userId = user.getId();
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        //查询用户数据权限
        List<DataPrivilegeDTO> privilegeDTOList = informationService.getDataPrivilegeDTOS(userId, LovHeaderCodeConstant.NETWORK_VERSION_SWITCHING_TYPE);
        if (CollectionUtils.isNotEmpty(privilegeDTOList)) {
            List<Long> ids = privilegeDTOList.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
            if (null == query.getSwitchType()) {
                if (!ids.contains(-1L)) {
                    booleanBuilder.and(qBatteryScreenPlate.switchType.in(ids));
                }
            }
        } else {
            booleanBuilder.and(qBatteryScreenPlate.switchType.in(-1L));
        }

        Page<BatteryScreenPlate> page = repository.findAll(booleanBuilder, pageable);
        List<BatteryScreenPlateDTO> batteryScreenPlateDTOS = convert.toDto(page.getContent());
        // 需要转码 lov id->name
        Map<String, LovLineDTO> singleGlassFlagLovs = lovUtil.getLovMapByCode("yes_or_no");
        for (BatteryScreenPlateDTO excelDTO : batteryScreenPlateDTOS) {
            queryConvert(excelDTO);
            judgeColorByLifecycleState(excelDTO);
        }
        batteryScreenPlateDTOS = batteryScreenPlateDTOS.stream().sorted(
                Comparator.comparing(BatteryScreenPlateDTO::getBatteryName,Comparator.nullsLast(String::compareTo))
                .thenComparing(BatteryScreenPlateDTO::getBasePlace,Comparator.nullsLast(String::compareTo))
                .thenComparing(BatteryScreenPlateDTO::getWorkshop,Comparator.nullsLast(String::compareTo))
                .thenComparing(BatteryScreenPlateDTO::getEffectiveStartDate, Comparator.comparingInt(data -> null==data ? Integer.MIN_VALUE : data.getYear()+data.getMonthValue()+data.getDayOfMonth()))
                .thenComparing(BatteryScreenPlateDTO::getWorkbench,Comparator.nullsLast(String::compareTo))
                .thenComparing(BatteryScreenPlateDTO::getItemCodeNew,Comparator.nullsLast(String::compareTo))
        ).collect(Collectors.toList());
        return new PageImpl(batteryScreenPlateDTOS, page.getPageable(), page.getTotalElements());
    }

    /**
     * 判断颜色根据LifecycleState
     *
     * @param excelDTO
     */
    private void judgeColorByLifecycleState(BatteryScreenPlateDTO excelDTO) {
        //获取库存组织
        LovLineDTO workShop = LovUtils.get("work_shop", excelDTO.getWorkshop());
        //根据库存组织查询
        LovLineDTO organization = LovUtils.get("inventory_organization", workShop.getAttribute10());
        if (organization == null) {
            throw new BizException("bbom_valid_workshopNotOrg", excelDTO.getWorkshop());
        }
        //网版料号新
        if (StringUtils.isNotBlank(excelDTO.getItemCodeNew())) {
            ItemsDTO newItem = itemsService.findOneByItemCodeAndOrganizationId(excelDTO.getItemCodeNew(), Long.parseLong(organization.getAttribute1()));
            if (newItem != null) {
                //如果状态为中大样的用前端淡橙色标注
                excelDTO.setLifecycleStateNew(newItem.getLifecycleState());
            }
        }
        //网版料号旧
//        if (StringUtils.isNotBlank(excelDTO.getItemCodeOld())) {
//            ItemsDTO oldItem = itemsService.findOneByItemCodeAndOrganizationId(excelDTO.getItemCodeOld(), Long.parseLong(organization.getAttribute1()));
//            if (oldItem != null) {
//                //如果状态为中大样的用前端淡橙色标注
//                excelDTO.setLifecycleStateOld(oldItem.getLifecycleState());
//            }
//        }
    }

    //批量更新 更新是否存在bom中、更新就网版在途库存、旧网版库存
    @Override
    public void batchUpdate(BatteryScreenPlateQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereByUpdate(booleanBuilder, query);
        List<BatteryScreenPlateDTO> screenPlateDTOList = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        screenPlateDTOList.parallelStream().forEach(item->{
            BatteryScreenPlateSaveDTO saveDTO = convert.dtoToSave(item);
            //非空校验 合法校验 lov赋值
            verifySave(saveDTO);
            // 重复校验
            checkRepeat(saveDTO);

            returnScreenPlateDto(item);
        });

        repository.saveAll(convert.toEntity(screenPlateDTOList));
    }

    //根据车间、单元、基地查询
    public BatteryScreenPlateDTO returnScreenPlateDto(BatteryScreenPlateDTO plateDTO) {
        plateDTO.setIsExistBom("N");
        if(StringUtils.isBlank(plateDTO.getWorkshopName())){
            throw new BizException("生产车间不能为空");
        }
        plateDTO.setWorkshop(plateDTO.getWorkshopName());
        //获取bom替代项、库存组织根据车间
        ErpAlternateDesignatorDTO designatorDTO = bomService.getErpAlternateDesignatorDTOByWorkshop(plateDTO.getWorkshop());

        if (null != designatorDTO && designatorDTO.getId() != null) {
            //根据电池编号、库存组织id查询  使用物料getSourceItemId的默认使用过库存组织82L的
            List<Items> itemsList = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.segment60.eq(plateDTO.getBatteryCode())
                            .and(qItems.organizationId.eq(82L).and(qItems.isDeleted.eq(0)))).fetch();
            //网版料号-新查询id 7A 唯一
            Items itemsDto = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.itemCode.eq(plateDTO.getItemCodeNew()).and(qItems.organizationId.eq(82L)).and(qItems.isDeleted.eq(0))).fetchFirst();
            if (CollectionUtils.isNotEmpty(itemsList)) {
                for (Items items : itemsList) {
                    //bom替代项、组织id、5A料号id 唯一
                    StructuresDTO structuresDTO = structuresService.findByAlternateDesignator(designatorDTO.getOrganizationId().toString(), items.getSourceItemId(), designatorDTO.getAlternateDesignatorCode());
                    if (null != structuresDTO && (Objects.nonNull(itemsDto) && null != itemsDto.getSourceItemId())) {
                        ComponentsDTO componentsDTO = componentsService.findByStructureIdAndSourceItemId(structuresDTO.getId(), itemsDto.getSourceItemId());
                        if (null != componentsDTO) {
                            plateDTO.setIsExistBom("Y");
                            break;
                        }

                    }
                }
            }
            //获取旧网版库存
//            List<TjOnHandDTO> tjOnHandDTOList = bMrpService.getTjOnHandDTOSByItemCodeAndOrgId(itemsDto.getItemCode(), designatorDTO.getOrganizationId());

//            if (CollectionUtils.isNotEmpty(tjOnHandDTOList)) {
//                plateDTO.setScreenPlateInventoryOld(String.valueOf(tjOnHandDTOList.get(0).getQuantity()));
//            } else {
//                plateDTO.setScreenPlateInventoryOld(BigDecimal.ZERO.toString());
//            }
            BigDecimal screenPlateNumberOld = BigDecimal.ZERO;
            //获取旧网版在途数据 po
//            List<ErpOpenPODTO> podtoList = bMrpService.getPoByItemCodeAndOrgId(itemsDto.getItemCode(), designatorDTO.getOrganizationId());
//            //获取旧网版在途数据 pr
//            List<ErpOpenPRDTO> prdtoList = bMrpService.getPrByItemCodeAndOrgId(itemsDto.getItemCode(), designatorDTO.getOrganizationId());
//            if (CollectionUtils.isNotEmpty(podtoList)) {
//                screenPlateNumberOld = screenPlateNumberOld.add(podtoList.get(0).getQuantity());
//            }
//            if (CollectionUtils.isNotEmpty(prdtoList)) {
//                screenPlateNumberOld = screenPlateNumberOld.add(prdtoList.get(0).getQuantity());
//            }
//            plateDTO.setScreenPlateNumberOld(screenPlateNumberOld.toString());
        }
//        if (StringUtils.isBlank(plateDTO.getScreenPlateInventoryOld())) {
//            plateDTO.setScreenPlateInventoryOld(BigDecimal.ZERO.toString());
//        }
//        if (StringUtils.isBlank(plateDTO.getScreenPlateNumberOld())) {
//            plateDTO.setScreenPlateNumberOld(BigDecimal.ZERO.toString());
//        }
        return plateDTO;

    }

    //获取Structures 所有数据
    private Map<String, StructuresDTO> queryListByStructures() {
        //bom替代项、组织id、5A料号id 唯一
        List<StructuresDTO> structuresDTOList = structuresService.queryList();
        Map<String, StructuresDTO> structuresDTOMap = structuresDTOList.stream().collect(Collectors.toMap(ele -> String.format("%s%s%s", ele.getAlternateBomDesignator(), ele.getOrganizationId(), ele.getAssemblyItemId()), Function.identity(), (v1, v2) -> v1));
        return structuresDTOMap;
    }

    //获取Components 所有数据
    private Map<String, ComponentsDTO> queryListByComponentsDTO() {
        //bom替代项、组织id、5A料号id 唯一
        List<ComponentsDTO> componentsDTOList = componentsService.queryList();
        //StructuresDTO主键+7A料号的id
        Map<String, ComponentsDTO> componentsDTOMap = componentsDTOList.stream().collect(Collectors.toMap(ele -> String.format("%s%s", ele.getBomId(), ele.getComponentItemId()), Function.identity(), (v1, v2) -> v1));
        return componentsDTOMap;
    }

    @Override
    public List<BatteryScreenPlateDTO> queryByAll(BatteryScreenPlateQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    @Override
    public void queryListBySendMail() {
        exportEmail();
    }

    /**
     * 发送邮件
     */
    private void sendEmail(JSONArray jsonArray, EmailDataResultDTO emailDataResultDTO) {
        //查询所有收件人
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.SEND_MAIL, LovHeaderCodeConstant.SCREEN_PLATE_MAIL);
        //发送邮件
        List<String> emails = Arrays.asList(StringUtils.split(lovLineDTO.getAttribute1(), ","));
        try {
            mailService.send(new ArrayList<>(emails), "battery_screen_plate.ftl", "网版切换维护邮件提醒", MapUtil.of("Data", emailDataResultDTO), jsonArray.toJSONString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatteryScreenPlateQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryScreenPlate.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryScreenPlate.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryScreenPlate.batteryName.eq(query.getBatteryName()));
        }
        if (null != query.getSwitchType()) {
            booleanBuilder.and(qBatteryScreenPlate.switchType.eq(query.getSwitchType()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlate.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlate.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qBatteryScreenPlate.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getLine())) {
            booleanBuilder.and(qBatteryScreenPlate.line.eq(query.getLine()));
        }
        if (StringUtils.isNotEmpty(query.getWorkbench())) {
            booleanBuilder.and(qBatteryScreenPlate.workbench.eq(query.getWorkbench()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeNew())) {
            booleanBuilder.and(qBatteryScreenPlate.itemCodeNew.contains(query.getItemCodeNew()));
        }
        if (StringUtils.isNotEmpty(query.getItemDescNew())) {
            booleanBuilder.and(qBatteryScreenPlate.itemDescNew.contains(query.getItemDescNew()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeOld())) {
            booleanBuilder.and(qBatteryScreenPlate.itemCodeOld.eq(query.getItemCodeOld()));
        }
        if (StringUtils.isNotEmpty(query.getItemDescOld())) {
            booleanBuilder.and(qBatteryScreenPlate.itemDescOld.eq(query.getItemDescOld()));
        }
        if (StringUtils.isNotEmpty(query.getNumberNew())) {
            booleanBuilder.and(qBatteryScreenPlate.numberNew.eq(query.getNumberNew()));
        }
        if (StringUtils.isNotEmpty(query.getNumberOld())) {
            booleanBuilder.and(qBatteryScreenPlate.numberOld.eq(query.getNumberOld()));
        }
        if (StringUtils.isNotEmpty(query.getTarget())) {
            booleanBuilder.and(qBatteryScreenPlate.target.eq(query.getTarget()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateInventoryOld())) {
            booleanBuilder.and(qBatteryScreenPlate.screenPlateInventoryOld.eq(query.getScreenPlateInventoryOld()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateNumberOld())) {
            booleanBuilder.and(qBatteryScreenPlate.screenPlateNumberOld.eq(query.getScreenPlateNumberOld()));
        }
        if (StringUtils.isNotEmpty(query.getRemarks())) {
            booleanBuilder.and(qBatteryScreenPlate.remarks.eq(query.getRemarks()));
        }
        if (StringUtils.isNotEmpty(query.getIsExistBom())) {
            booleanBuilder.and(qBatteryScreenPlate.isExistBom.eq(query.getIsExistBom()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatteryScreenPlate.effectiveStartDate.before(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatteryScreenPlate.effectiveEndDate.after(query.getEffectiveEndDate()));
        }
        if (query.getVeirfyDate() != null) {
            booleanBuilder.and(qBatteryScreenPlate.effectiveStartDate.loe(query.getVeirfyDate()));
            booleanBuilder.and(qBatteryScreenPlate.effectiveEndDate.goe(query.getVeirfyDate()));
        }
    }

    //批量更新使用 批量更新获取当天日期 且在有效期范围内
    private void buildWhereByUpdate(BooleanBuilder booleanBuilder, BatteryScreenPlateQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryScreenPlate.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryScreenPlate.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryScreenPlate.batteryName.eq(query.getBatteryName()));
        }
        if (null != query.getSwitchType()) {
            booleanBuilder.and(qBatteryScreenPlate.switchType.eq(query.getSwitchType()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlate.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlate.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qBatteryScreenPlate.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getLine())) {
            booleanBuilder.and(qBatteryScreenPlate.line.eq(query.getLine()));
        }
        if (StringUtils.isNotEmpty(query.getWorkbench())) {
            booleanBuilder.and(qBatteryScreenPlate.workbench.eq(query.getWorkbench()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeNew())) {
            booleanBuilder.and(qBatteryScreenPlate.itemCodeNew.eq(query.getItemCodeNew()));
        }
        if (StringUtils.isNotEmpty(query.getItemDescNew())) {
            booleanBuilder.and(qBatteryScreenPlate.itemDescNew.eq(query.getItemDescNew()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeOld())) {
            booleanBuilder.and(qBatteryScreenPlate.itemCodeOld.eq(query.getItemCodeOld()));
        }
        if (StringUtils.isNotEmpty(query.getItemDescOld())) {
            booleanBuilder.and(qBatteryScreenPlate.itemDescOld.eq(query.getItemDescOld()));
        }
        if (StringUtils.isNotEmpty(query.getNumberNew())) {
            booleanBuilder.and(qBatteryScreenPlate.numberNew.eq(query.getNumberNew()));
        }
        if (StringUtils.isNotEmpty(query.getNumberOld())) {
            booleanBuilder.and(qBatteryScreenPlate.numberOld.eq(query.getNumberOld()));
        }
        if (StringUtils.isNotEmpty(query.getTarget())) {
            booleanBuilder.and(qBatteryScreenPlate.target.eq(query.getTarget()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateInventoryOld())) {
            booleanBuilder.and(qBatteryScreenPlate.screenPlateInventoryOld.eq(query.getScreenPlateInventoryOld()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateNumberOld())) {
            booleanBuilder.and(qBatteryScreenPlate.screenPlateNumberOld.eq(query.getScreenPlateNumberOld()));
        }
        if (StringUtils.isNotEmpty(query.getRemarks())) {
            booleanBuilder.and(qBatteryScreenPlate.remarks.eq(query.getRemarks()));
        }
        if (StringUtils.isNotEmpty(query.getIsExistBom())) {
            booleanBuilder.and(qBatteryScreenPlate.isExistBom.eq(query.getIsExistBom()));
        }
        booleanBuilder.and(qBatteryScreenPlate.effectiveStartDate.before(LocalDateTime.now()));
        booleanBuilder.and(qBatteryScreenPlate.effectiveEndDate.after(LocalDateTime.now()));

    }

    @Override
    public BatteryScreenPlateDTO queryById(Long id) {
        BatteryScreenPlate queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public BatteryScreenPlateDTO save(BatteryScreenPlateSaveDTO saveDTO) {
        //非空校验 合法校验 lov赋值
        verifySave(saveDTO);
        // 重复校验
        checkRepeat(saveDTO);

        BatteryScreenPlate newObj = new BatteryScreenPlate();
        if (null != saveDTO.getId()) {
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            extractedSave(saveDTO, booleanBuilder);
            newObj = repository.findOne(booleanBuilder).orElse(new BatteryScreenPlate());
        }
        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        // 给初始值
        defaultDataFill(newObj);
        newObj.setId(saveDTO.getId());
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    private void defaultDataFill(BatteryScreenPlate newObj) {
        // 旧网版库存 和 旧网版在途,默认为0
        if (StringUtils.isBlank(newObj.getScreenPlateInventoryOld())) {
            newObj.setScreenPlateInventoryOld("0");
        }
        if (StringUtils.isBlank(newObj.getScreenPlateNumberOld())) {
            newObj.setScreenPlateNumberOld("0");
        }
    }

    //字段校验网版料号-新+网版料号-旧+电池类型+机台+生产基地+生产车间+生产单元+线体唯一性
    public void checkRepeat(BatteryScreenPlateSaveDTO saveDTO) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        extracted(saveDTO, booleanBuilder);
//        List<BatteryScreenPlate> newObjList = IterableUtils.toList(repository.findAll(booleanBuilder));
        List<BatteryScreenPlate> newObjList = IterableUtils.toList(repository.findAll(booleanBuilder));
//        BatteryScreenPlate newObj = repository.findOne(booleanBuilder).orElse(new BatteryScreenPlate());
        if (CollectionUtils.isNotEmpty(newObjList) && null==saveDTO.getId()) {
            throw new BizException("电池类型+生产基地+生产车间+网版料号-新+机台+有效日期_起+有效日期止唯一性校验，不允许重复");
        }else if(null!=saveDTO.getId()){
            newObjList.forEach(item->{
                if(!item.getId().equals(saveDTO.getId())){
                    throw new BizException("电池类型+生产基地+生产车间+网版料号-新+机台+有效日期_起+有效日期止唯一性校验，不允许重复");
                }
            });
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void importsEntity(MultipartFile file) {
        List<BatteryScreenPlateExcelDTO> excelDto = new LinkedList<>();
        EasyExcel.read(file.getInputStream(), BatteryScreenPlateExcelDTO.class, new ReadListener<BatteryScreenPlateExcelDTO>() {
            @Override
            public void invoke(BatteryScreenPlateExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        importDataSave(excelDto);
        log.info("电池类型动态属性-网版 数据导入成功");

    }

    private void importDataSave(List<BatteryScreenPlateExcelDTO> excelDto) {
//        Map<String, StructuresDTO> structuresDTOMap = queryListByStructures();
//        Map<String, ComponentsDTO> componentsDTOMap = queryListByComponentsDTO();

//        // 批量查询供应商数据
//        List<String> vendorNames = excelDto.stream().map(BatteryScreenPlateExcelDTO::getVendorName)
//                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
//        Map<String, ApprovedVendorDTO> vendorDTOMap = bMrpService.getApprovedVendorDTOByVendorNames(vendorNames);

        // 保存或更新数据 查询数据库是否存在数据（存在既更新）
        for (BatteryScreenPlateExcelDTO excelDTO : excelDto) {
            // 校验合法性和赋值
//            checkImport(excelDTO, vendorDTOMap);
            BatteryScreenPlateSaveDTO saveDTO = convert.toSaveDTO(excelDTO);
            //非空校验 合法校验 lov赋值
            verifySave(saveDTO);
            // 重复校验
            checkRepeat(saveDTO);
//            // 查询 区分新增 修改结果集合
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            extractedImport(saveDTO, booleanBuilder);
            BatteryScreenPlate newObj = repository.findOne(booleanBuilder).orElse(new BatteryScreenPlate());
            if (newObj.getId() != null) {
                excelDTO.setId(newObj.getId());
                excelDTO.setIsExistBom(newObj.getIsExistBom());
            }

            convert.saveDTOtoEntity(saveDTO, newObj);
            defaultDataFill(newObj);
            repository.save(newObj);
        }
    }

    private void checkImport(BatteryScreenPlateExcelDTO excelDTO, Map<String, ApprovedVendorDTO> vendorMap) {
        String check = checkSwitchImport(excelDTO);
        verifyImport(excelDTO, check);
        // 校验合法性 生产基地 生产车间 api接口  2024.12.23去除“网版物料-旧”及其对应的“物料描述”和“物料状态”、“生产单元”、“旧网版库存”、“旧网版在途”
        List<ModuleBasePlaceDTO> moduleBasePlaceDTOList = batteryFeignService.
                findBasePlaceWorkshopWorkUnit(excelDTO.getBasePlaceName(), excelDTO.getWorkshopName(), null);
        if (CollectionUtils.isEmpty(moduleBasePlaceDTOList)) {
            throw new BizException("bbom_valid_basePlaceWorkshop_illegal", excelDTO.getBasePlaceName(), excelDTO.getWorkshopName());
        }
        ModuleBasePlaceDTO moduleBasePlaceDTO = moduleBasePlaceDTOList.get(0);
        // 获取基地
        excelDTO.setBasePlace(excelDTO.getBasePlaceName());
        // 获取车间
        excelDTO.setWorkshop(excelDTO.getWorkshopName());
        // 获取生产单元
//        excelDTO.setWorkunit(excelDTO.getWorkunitName());
        //新
        ItemsDTO itemNew = itemsService.findOneByItemCode(excelDTO.getItemCodeNew());
        excelDTO.setItemDescNew(null != itemNew ? itemNew.getItemDesc() : "");
        excelDTO.setScreenPlateVersionCategoryNew(null != itemNew ? itemNew.getSegment2() : "");
        //供应商不为空
        if (StringUtils.isNotEmpty(excelDTO.getVendorName())) {
            if (null == vendorMap.get(excelDTO.getVendorName())) {
                throw new BizException("bbom_valid_vendorName_nonExist", excelDTO.getVendorName());
            } else {
                if (!vendorMap.containsKey(excelDTO.getVendorName())) {
                    throw new BizException("bbom_valid_vendorName_nonExist", excelDTO.getVendorName());
                }
                excelDTO.setVendorId(vendorMap.get(excelDTO.getVendorName()).getVendorId());
            }
        }
        // 线体数手工维护，但维护的数值小于等于APS接口中生产单元带出的数
        if (StringUtils.isNotBlank(excelDTO.getLine())
                && StringUtils.isNumeric(excelDTO.getLine())
                && moduleBasePlaceDTO.getTotalLine() != null) {
            Long line = Long.valueOf(excelDTO.getLine());
            if (line > moduleBasePlaceDTO.getTotalLine()) {
                throw new BizException("bbom_valid_totalLine");
            }
        }
        // 转换切换类型 lov name -> id
        // 校验机台合法性 lov
        LovLineDTO workbench = Optional.ofNullable(LovUtils.getByName(CELL_MACHINE,
                excelDTO.getWorkbenchName())).orElseThrow(() -> new BizException("bbom_valid_workbench_illegal", excelDTO.getWorkbenchName()));
        // 获取机台信息
        excelDTO.setWorkbench(String.valueOf(workbench.getLovLineId()));

        // 补齐电池类型编码
        if (StringUtils.isNotEmpty(excelDTO.getBatteryName())) {
            BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryName(excelDTO.getBatteryName());
            Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
                excelDTO.setBatteryCode(w.getBatteryCode());
            });
            if (StringUtils.isBlank(excelDTO.getBatteryCode())) {
                throw new BizException("bbom_valid_batteryCode_illegal", excelDTO.getBatteryName());
            }
        }
        boolean isEffectiveStartDateVaild = isValidDateFormat(excelDTO.getEffectiveStartDates(), "yyyy-MM-dd");
        if (!isEffectiveStartDateVaild) {
            throw new BizException("bbom_valid_effectiveStartDate_format_illegal", excelDTO.getEffectiveStartDates());
        }
        boolean isEffectiveEndDateVaild = isValidDateFormat(excelDTO.getEffectiveEndDates(), "yyyy-MM-dd");
        if (!isEffectiveEndDateVaild) {
            throw new BizException("bbom_valid_effectiveEndDate_format_illegal", excelDTO.getEffectiveEndDates());
        }
    }

    @Override
    @SneakyThrows
    public void export(BatteryScreenPlateQuery query, HttpServletResponse response) {
        List<BatteryScreenPlateDTO> dto = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        //数据转换方法
        dataConversion(dto);
        List<BatteryScreenPlateExcelDTO> exportDTOS = convert.toExcelDTO(dto);
        ExcelUtils.setExportResponseHeader(response, "网版切换信息导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "网版切换信息导出_").head(BatteryScreenPlateExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }


    public void exportEmail() {
        EmailDataResultDTO emailDataResultDTO = new EmailDataResultDTO();
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //减三个月
        booleanBuilder.and(qBatteryScreenPlate.createdTime.after(LocalDateTimeUtil.getDateTimePlusMinusMonths(3)));
        Iterable<BatteryScreenPlate> plateIterable = repository.findAll(booleanBuilder);
        //申请成功后发送邮件
        List<BatteryScreenPlateDTO> dtos = convert.toDto(IterableUtils.toList(plateIterable));
        //数据转换方法
        dataConversion(dtos);
        List<BatteryScreenPlateExcelMailDTO> exportDTOS = convert.toExcelMailDTO(dtos);
        //网版切换
        String content = "网版切换维护内容";
        JSONArray jsonArray = getObjects(emailDataResultDTO, exportDTOS, content);
        sendEmail(jsonArray, emailDataResultDTO);

    }

    public JSONArray getObjects(EmailDataResultDTO emailDataResultDTO, List<BatteryScreenPlateExcelMailDTO> exportDTOS, String content) {
        File file = buildExcelFile(exportDTOS);
        String fileUrl = this.fileUpload(file);
        JSONArray jsonArray = FileUtil.getObjects(emailDataResultDTO, content, file, fileUrl, "网版切换");
        return jsonArray;
    }

    @SneakyThrows
    private String fileUpload(File file) {
        MultipartFile multipartFile = FileUtil.getMultipartFile(file);
        return scpFileService.upload(multipartFile, false);
    }

    private File buildExcelFile(List<BatteryScreenPlateExcelMailDTO> exportDTOS) {
        //创建目录
        File file = FileUtil.createLocalFile("网版切换");
        // 文件输出位置
        ExcelWriter writer = EasyExcelFactory.write(file)
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build();


        WriteSheet sheet1 = EasyExcelFactory.writerSheet(0, "网版切换维护导出信息").head(BatteryScreenPlateExcelMailDTO.class).build();
        // 写数据
        writer.write(exportDTOS, sheet1);
        writer.finish();
        return file;
    }

    private List<BatteryScreenPlateDTO> dataConversion(List<BatteryScreenPlateDTO> dto) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        dto.stream().forEach(p -> {
            if ("Y".equals(p.getIsExistBom())) {
                p.setIsExistBom("是");
            } else if ("N".equals(p.getIsExistBom())) {
                p.setIsExistBom("否");
            }
            p.setEffectiveStartDates(null != p.getEffectiveStartDate() ? fmt.format(p.getEffectiveStartDate()) : "");
            p.setEffectiveEndDates(null != p.getEffectiveEndDate() ? fmt.format(p.getEffectiveEndDate()) : "");
            queryConvert(p);
        });
        return dto;
    }

    /**
     * 网版切换
     * 入参：生产基地
     * 默认参数：切换类型为新产品导入
     * 接口返回：生产基地、料号新、料号旧、数量、开始日期、供应商
     */
    public List<BatteryScreenPlateDTO> queryBatteryScreenByBasePlace(BatteryScreenPlateQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        //新产品导入
        LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.NETWORK_VERSION_SWITCHING_TYPE, XCPDR);
        booleanBuilder.and(qBatteryScreenPlate.switchType.eq(lovLineDTO.getLovLineId()));
        Iterable<BatteryScreenPlate> screenPlates = repository.findAll(booleanBuilder);
        return convert.toDto(IterableUtils.toList(screenPlates));

    }

    @Override
    @Cacheable(cacheNames = "BatteryScreenPlateService_getByMatchItem", key = "#batteryType+'_'+#basePlace+'_'+#workshop+'_'+#workunit+'_'+#verifyDate", unless = "#result == null")
    public List<BatteryScreenPlateDTO> getByMatchItem(String batteryType, String basePlace, String workshop, String workunit, LocalDateTime verifyDate) {
        // 获取全量数据后过滤返回
        List<BatteryScreenPlateDTO> all = batteryScreenPlateService.queryAllByCache();
        List<BatteryScreenPlateDTO> resultList = new ArrayList<>();
        for (BatteryScreenPlateDTO batteryScreenPlateDTO : all) {
            if (batteryScreenPlateDTO.getBatteryName().equals(batteryType)
                    && batteryScreenPlateDTO.getBasePlace().equals(basePlace)
                    && batteryScreenPlateDTO.getWorkshop().equals(workshop))
//                    && batteryScreenPlateDTO.getWorkunit().equals(workunit))
                if (Objects.isNull(batteryScreenPlateDTO.getEffectiveEndDate())
                        && Objects.nonNull(batteryScreenPlateDTO.getEffectiveStartDate())
                        && !batteryScreenPlateDTO.getEffectiveStartDate().isAfter(verifyDate)) {
                    resultList.add(batteryScreenPlateDTO);
                } else if (Objects.nonNull(batteryScreenPlateDTO.getEffectiveEndDate())
                        && Objects.nonNull(batteryScreenPlateDTO.getEffectiveStartDate())
                        && !batteryScreenPlateDTO.getEffectiveStartDate().isAfter(verifyDate)
                        && !batteryScreenPlateDTO.getEffectiveEndDate().isBefore(verifyDate)) {
                    resultList.add(batteryScreenPlateDTO);
                }
        }
        // 25.03.12 仲华要求进行注释
//        //如果生失效时间内没有获取到数据，就获取最近一次生效的数据
//        if (CollectionUtils.isEmpty(resultList)) {
//            try {
//                Optional<LocalDateTime> effectiveEndDate = all.stream().filter(i -> {
//                    return i.getBatteryName().equals(batteryType)
//                            && i.getBasePlace().equals(basePlace)
//                            && i.getWorkshop().equals(workshop)
////                            && i.getWorkunit().equals(workunit)
//                            && Objects.nonNull(i.getEffectiveEndDate());
//                }).map(BatteryScreenPlateDTO::getEffectiveEndDate).sorted(Comparator.nullsFirst(LocalDateTime::compareTo).reversed()).findFirst();
//                //获取上一版数据
//                if (effectiveEndDate.isPresent()) {
////                    resultList = all.stream().filter(i -> {
////                        return !i.getBatteryName().equals(batteryType)
////                                || !i.getBasePlace().equals(basePlace)
////                                || !i.getWorkshop().equals(workshop)
//////                                && i.getWorkunit().equals(workunit)
////                                || (Objects.nonNull(i.getEffectiveStartDate()) && !i.getEffectiveStartDate().isAfter(effectiveEndDate.get()))
////                                && (Objects.isNull(i.getEffectiveEndDate()) || !i.getEffectiveEndDate().isBefore(effectiveEndDate.get()));
////                    }).collect(Collectors.toList());
//                    resultList = all.stream().filter(i -> {
//                        return i.getBatteryName().equals(batteryType)
//                                && i.getBasePlace().equals(basePlace)
//                                && i.getWorkshop().equals(workshop)
//                                && i.getWorkunit().equals(workunit)
//                                && (Objects.nonNull(i.getEffectiveStartDate()) && !i.getEffectiveStartDate().isAfter(effectiveEndDate.get()))
//                                && (Objects.isNull(i.getEffectiveEndDate()) || !i.getEffectiveEndDate().isBefore(effectiveEndDate.get()));
//                    }).collect(Collectors.toList());
//                }
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//
//        }
        return resultList;
    }
    @Override
    @Cacheable(cacheNames = "BatteryScreenPlateService_queryAllByCache")
    public List<BatteryScreenPlateDTO> queryAllByCache() {
        List<BatteryScreenPlate> all = repository.findAll();
        return convert.toDto(all);
    }
}
