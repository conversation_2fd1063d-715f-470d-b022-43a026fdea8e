package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalItemsDTO;
import com.trinasolar.scp.bbom.domain.entity.Items;
import com.trinasolar.scp.bbom.domain.query.BatchItemQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsNewQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsQuery;
import com.trinasolar.scp.bbom.domain.save.ItemsSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.StructureItemVO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.lang.NonNull;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 物料基础数据表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
public interface ItemsService {
    /**
     * 分页获取物料基础数据表
     *
     * @param query 查询对象
     * @return 物料基础数据表分页对象
     */
    Page<ItemsDTO> queryByPage(ItemsQuery query);

    /**
     * 料号匹配后查询筛选数据
     *
     * @param query
     * @return
     */
    List<ItemsDTO> queryByMatching(ItemsQuery query);

    List<ItemsDTO> queryByMatchingAll();

    //query对象过滤集合
    List<ItemsDTO> filterList(List<ItemsDTO> itemsDTOList, ItemsQuery query);

    /**
     * 查询物料基础数据
     *
     * @param query
     * @return
     */
    Page<ItemsDTO> queryByItemCode(ItemsQuery query);

    /**
     * 查询物料基础数据
     *
     * @param query
     * @return
     */
    Map<String, ItemsDTO> queryByItemCodeAll(ItemsNewQuery query);


    ItemsDTO getOneByItemCodeAndOrganizationId(String itemCode, Long organizationId);

    /**
     * 查询物料基础数据
     *
     * @param sourceItemId
     * @return
     */
    Map<Long, ItemsDTO> queryBySourceItemCode(List<Long> sourceItemId, List<String> categorySegment4);

    /**
     * 根据主键获取物料基础数据表详情
     *
     * @param id 主键
     * @return 物料基础数据表详情
     */
    ItemsDTO queryById(Long id);

    /**
     * 保存或更新物料基础数据表
     *
     * @param saveDTO 物料基础数据表保存对象
     * @return 物料基础数据表对象
     */
    ItemsDTO save(ItemsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除物料基础数据表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ItemsQuery query, HttpServletResponse response);

    void importByExternalItems(ExternalItemsDTO externalItemsDTO);

    @NonNull
    ItemsDTO findOneByItemCode(String itemCode);

    @Cacheable(cacheNames = "BBOM_items_findOneByItemCode", key = "#itemCode", unless = "#result == null")
    ItemsDTO getOneByItemCode(String itemCode);

    StructureItemVO getOneBySourceItemId(Long sourceItemId);

    /**
     * 保存或更新物料基础数据
     *
     * @param saveDTO
     * @return
     */
    List<Items> batchSaveUpdate(List<ItemsDTO> saveDTO);

    void ItemLifecycleStateSync(LocalDate date);

    Map<String, List<ItemsDTO>> queryStructureItemByComponentItem(BatchItemQuery query);

    ItemsDTO findOneByItemCodeAndOrganizationId(String itemCode, Long organizationId);

    List<String> getCellsTypeBy7AItemCodes(List<String> itemCodes);

    List<ItemsDTO> findReworkItemCode(ItemsDTO itemsDTO);
    List<ItemsDTO> findItemsByItemCodeAndProductGrades(List<String> itemCodes,List<String> productGrades,Long organizationId);

    ItemsDTO findOneByItemId(Long itemId);

    Map<String, String> findLifecycleStateByItems(List<String> itemCodes);

    ItemsDTO getOneByItemCodeLifecycleState(String itemCode);

    Map<String, ItemsDTO> queryByItemCodeAllNew(ItemsNewQuery query);

    Map<Long, ItemsDTO> getItemsDTOMapBySourceItemIds(List<Long> sourceItemIds);
}

