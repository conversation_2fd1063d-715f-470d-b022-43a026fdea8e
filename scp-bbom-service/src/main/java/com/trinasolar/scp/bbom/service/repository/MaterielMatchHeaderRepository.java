package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.MaterielMatchHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Repository
public interface MaterielMatchHeaderRepository extends JpaRepository<MaterielMatchHeader, Long>, QuerydslPredicateExecutor<MaterielMatchHeader> {
}
