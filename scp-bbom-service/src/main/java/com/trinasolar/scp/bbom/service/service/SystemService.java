package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ErpApprovalSupplierPageNumDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpApprovalSupplierQuery;
import com.trinasolar.scp.bbom.domain.dto.LovHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.OrganizationDefinitionsDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.LovLineSaveDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18
 */
public interface SystemService {
    List<OrganizationDefinitionsDTO> getOrganizationScpFlagIsY();

    List<OrganizationDefinitionsDTO> getOrganizationCellsScpFlagIsY();

    /**
     * 批准供应商分页列表
     *
     * @param query
     * @return
     */
    ErpApprovalSupplierPageNumDTO querySupplierPage(ErpApprovalSupplierQuery query);

    List<String> getDataPrivilegeList(String privilegeType);

    LovHeaderDTO getLovHeaderByCode(String headerCode);

    void saveOne(LovLineSaveDTO lovLineDTO);
}
