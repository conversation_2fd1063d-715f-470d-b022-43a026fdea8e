package com.trinasolar.scp.bbom.service.feign.BapsDto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 5A料号匹配明细Dto
 */
@Data
public class Cell5AItemCodeDetailsDto {
    /**
     * Bbom拆后的Id
     */
    private Long bbomId;
    /**
     * 线体数量
     */
    private BigDecimal numberLine;
    /**
     * 投产数量
     */
    private BigDecimal qtyPc;
    /**
     * 拆的比例
     */
    private BigDecimal rate;
    /**
     * 5A料号
     */
    private String itemCode;
    /**
     * 背面细栅
     */
    private String backFineGrid;
    /**
     * 正面细栅
     */
    private String frontFineGrid;
    /**
     * 硅片厚度
     */
    private String siliconWaferThickness;
    /**
     * 硅片尺寸
     */
    private String siliconWaferSize;
}
