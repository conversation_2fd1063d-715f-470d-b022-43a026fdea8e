package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.ConversionCoefficientMw;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 兆瓦转换系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@Repository
public interface ConversionCoefficientMwRepository extends JpaRepository<ConversionCoefficientMw, Long>, QuerydslPredicateExecutor<ConversionCoefficientMw> {
}
