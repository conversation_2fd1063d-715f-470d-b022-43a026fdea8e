package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.Structures;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * BBOM结构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Repository
public interface StructuresRepository extends JpaRepository<Structures, Long>, QuerydslPredicateExecutor<Structures> {
    Structures findByBillSequenceId(Long billSequenceId);
}
