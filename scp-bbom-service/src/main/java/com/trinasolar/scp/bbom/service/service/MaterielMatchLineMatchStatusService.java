package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineMatchStatusDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchLineMatchStatusQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineMatchStatusSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池料号匹配明细行匹配状态 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
public interface MaterielMatchLineMatchStatusService {
    /**
     * 分页获取电池料号匹配明细行匹配状态
     *
     * @param query 查询对象
     * @return 电池料号匹配明细行匹配状态分页对象
     */
    Page<MaterielMatchLineMatchStatusDTO> queryByPage(MaterielMatchLineMatchStatusQuery query);

    /**
     * 根据主键获取电池料号匹配明细行匹配状态详情
     *
     * @param id 主键
     * @return 电池料号匹配明细行匹配状态详情
     */
    MaterielMatchLineMatchStatusDTO queryById(Long id);

    /**
     * 保存或更新电池料号匹配明细行匹配状态
     *
     * @param saveDTO 电池料号匹配明细行匹配状态保存对象
     * @return 电池料号匹配明细行匹配状态对象
     */
    MaterielMatchLineMatchStatusDTO save(MaterielMatchLineMatchStatusSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池料号匹配明细行匹配状态
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(MaterielMatchLineMatchStatusQuery query, HttpServletResponse response);

    void deleteByLineIds(List<Long> lineIds);

    void saveByLine(MaterielMatchLine materielMatchLine, List<MaterielMatchLineMatchStatusDTO> matchLineMatchStatusDTOS);

    List<MaterielMatchLineMatchStatusDTO> queryByLineId(Long lineId);

    List<MaterielMatchLineMatchStatusDTO> queryAll();

    List<MaterielMatchLineMatchStatusDTO> queryByLineIds(List<Long> lineIdList);

    void clearErrorData();
}

