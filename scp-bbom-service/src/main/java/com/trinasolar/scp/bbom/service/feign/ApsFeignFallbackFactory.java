package com.trinasolar.scp.bbom.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.CellRelationDTO;
import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.dto.MwCoefficientDTO;
import com.trinasolar.scp.bbom.domain.query.ModuleBasePlaceQuery;
import com.trinasolar.scp.bbom.domain.query.MwCoefficientQuery;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ApsFeignFallbackFactory
 * @Description scp-aps-api服务降级处理
 * @Date 2023/11/30 10:16
 **/
@Component
@Slf4j
public class ApsFeignFallbackFactory implements FallbackFactory<ApsFeign> {

    @Override
    public ApsFeign create(Throwable cause) {
        return new ApsFeign() {
            @Override
            public ResponseEntity<Results<ModuleBasePlaceDTO>> findByBasePlaceAndWorkshopAndWorkunit(ModuleBasePlaceQuery query) {
                log.warn("【ApsFeign-findByBasePlaceAndWorkshopAndWorkunit】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ModuleBasePlaceDTO>>> findListByBasePlaceAndWorkshopAndWorkunit(ModuleBasePlaceQuery query) {
                log.warn("【ApsFeign-findListByBasePlaceAndWorkshopAndWorkunit】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<MwCoefficientDTO>>> findMwCoefficient(MwCoefficientQuery query) {
                log.warn("【ApsFeign-findMwCoefficient】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<CellRelationDTO>>> queryByMaterialNoList(List<String> materialNoList) {
                log.warn("【ApsFeign-queryByMaterialNoList】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ModuleBasePlaceDTO>>> allData(@RequestBody ModuleBasePlaceQuery query) {
                log.warn("【ApsFeign-findAllWorkunit】发生异常：{}", cause);
                return null;
            }

            @Override
            public ResponseEntity<Results<List<String>>> getCellBasePlace() {
                log.warn("【ApsFeign-getCellBasePlace】发生异常：{}", cause);
                return Results.createFailRes();
            }
        };
    }
}
