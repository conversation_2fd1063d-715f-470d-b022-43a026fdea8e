package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchHeader;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchLineQuery;
import com.trinasolar.scp.bbom.domain.query.ScreenMainReplacementQuery;
import com.trinasolar.scp.bbom.domain.query.SlurryMainReplacementQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineAppointItemSaveDTO;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.ScreenMainReplacementVO;
import com.trinasolar.scp.bbom.domain.vo.SlurryMainReplacementVO;
import com.trinasolar.scp.bbom.service.feign.BapsDto.Cell5AItemCodeListDto;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 电池料号匹配明细行 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
public interface MaterielMatchLineService {
    /**
     * 分页获取电池料号匹配明细行
     *
     * @param query 查询对象
     * @return 电池料号匹配明细行分页对象
     */
    Page<MaterielMatchLineDTO> queryByPage(MaterielMatchLineQuery query);

    /**
     * 根据主键获取电池料号匹配明细行详情
     *
     * @param id 主键
     * @return 电池料号匹配明细行详情
     */
    MaterielMatchLineDTO queryById(Long id);

    /**
     * 保存或更新电池料号匹配明细行
     *
     * @param saveDTO 电池料号匹配明细行保存对象
     * @return 电池料号匹配明细行对象
     */
    MaterielMatchLineDTO save(MaterielMatchLineSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池料号匹配明细行
     *
     * @param ids 主键集合
     */
    void deleteMatchStatusByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(MaterielMatchLineQuery query, HttpServletResponse response);

    List<MaterielMatchLineDTO> findByHeaderId(Long headerId);

    List<MaterielMatchLineDTO> saveBatch(List<MaterielMatchLineDTO> newLineDTOS);

    List<MaterielMatchLineDTO> queryByHeaderId(Long headerId, String matchStatus, String isCatchProduction);

    void appointItemCode(List<MaterielMatchLineAppointItemSaveDTO> saveDTOs);

    Cell5AItemCodeListDto pushBapsCell5AItemCode(List<MaterielMatchLine> matchLineList);

    List<MaterielMatchLineDTO> queryMaterielMatchLineList();

    List<MaterielMatchLineDTO> queryMatchLineByHeaderId(MaterielMatchHeaderDTO headerDTO);

    /**
     * 查询 isdelete=0的且版本号最大的数据 获取指定料号的数据集合
     *
     * @param version、month
     * @return
     */
    List<MaterielMatchLineDTO> queryMatchLineByVersion(MaterielMatchHeaderDTO headerDTO, String version, String month, Long isOverseaId);

    void deleteByHeadId(List<MaterielMatchHeaderDTO> headerDTOList);

    void deleteByMonth(String month, Long isOverseaId,List<Long> headerIds);

    void addMatchLineInfo(MaterielMatchHeader header, MaterielMatchHeaderDTO headerDTO, Map<String, StructuresDTO> structuresDTOMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap);

    String getMaxFinalVersion(String month, Long isOverseaId);

    /**
     * 推送当月的料号匹配行明细数据
     *
     * @return
     */
    void sendMailByqueryList();

    void flushAlternateBomDesignator(Long matchId, String month, Map<String, StructuresDTO> structuresDTOMap, Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap);

    void flushItemDesc();

    void clearItemCode(List<Long> ids);

    String getInheritItemCode(MaterielMatchLineDTO dto,Map<String,List<MaterielMatchLine>> matchLineaAllMap);

    Page<SlurryMainReplacementVO> slurryMainReplacementPage(SlurryMainReplacementQuery query);

    void slurryMainReplacementPageExport(SlurryMainReplacementQuery query, HttpServletResponse response);

    void importSlurryMainReplacementPageEntity(MultipartFile file);

    List<MaterielMatchLineDTO> queryPlanMaterielMatchLineList();

    Page<ScreenMainReplacementVO> screenMainReplacementPage(ScreenMainReplacementQuery query);

    void exportScreenMainReplacement(ScreenMainReplacementQuery query, HttpServletResponse response);

    void importScreenMainReplacement(MultipartFile file);
}

