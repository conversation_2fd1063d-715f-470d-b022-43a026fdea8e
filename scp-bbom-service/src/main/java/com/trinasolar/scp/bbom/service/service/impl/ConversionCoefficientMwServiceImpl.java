package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Maps;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.*;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.enums.ItemCategoryEnum;
import com.trinasolar.scp.bbom.domain.excel.ConversionCoefficientMwExcelDTO;
import com.trinasolar.scp.bbom.domain.query.*;
import com.trinasolar.scp.bbom.domain.save.ConversionCoefficientMwSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.ApsFeign;
import com.trinasolar.scp.bbom.service.feign.BAPSFeign;
import com.trinasolar.scp.bbom.service.repository.*;
import com.trinasolar.scp.bbom.service.service.ConversionCoefficientMwService;
import com.trinasolar.scp.bbom.service.service.SystemService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 兆瓦转换系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@Slf4j
@Service("conversionCoefficientMwService")
@RequiredArgsConstructor
public class ConversionCoefficientMwServiceImpl implements ConversionCoefficientMwService {
    private static final QConversionCoefficientMw qConversionCoefficientMw = QConversionCoefficientMw.conversionCoefficientMw;
    private final static String NUMBER_1 = "1";
    private final ConversionCoefficientMwDEConvert convert;
    private final ConversionCoefficientMwRepository repository;
    private final SystemService systemService;
    private final ApsFeign apsFeign;
    private final BAPSFeign bapsFeign;
    private final BatteryTypeMainRepository batteryTypeMainRepository;
    private final BatteryTypeMainDEConvert batteryTypeMainDEConvert;
    private final ScreenLifeRepository screenLifeRepository;
    private final ScreenLifeDEConvert screenLifeDEConvert;
    private final BatterySiliconWaferRepository batterySiliconWaferRepository;
    private final BatterySiliconWaferDEConvert batterySiliconWaferDEConvert;
    private final SlurryInformationRepository slurryInformationRepository;
    private final SlurryInformationDEConvert slurryInformationDEConvert;
    @Autowired
    public RedisTemplate redisTemplate;
    @Autowired
    ExecutorService threadPoolExecutor;
    @Autowired
    RedissonClient redissonClient;

    private void convertLovValueToName(List<ConversionCoefficientMwDTO> dataList) {
        dataList.forEach(dto -> {
            if (StringUtils.isNotBlank(dto.getBasePlace())) {
                dto.setBasePlaceName(LovUtils.getNameByValue(LovHeaderCodeConstant.ITEM_CATEGORY, dto.getBasePlace()));
            }
            if (StringUtils.isNotBlank(dto.getWorkshop())) {
                dto.setWorkshopName(LovUtils.getNameByValue(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop()));
            }
            if (StringUtils.isNotBlank(dto.getClassify())) {
                if (!ItemCategoryEnum.SILICON.getValue().equals(dto.getClassify())) {
                    dto.setBatteryEfficiencyQty(BigDecimal.ONE);
                }
                if (ItemCategoryEnum.SILICON.getValue().equals(dto.getClassify())) {
                    dto.setUnitConsumption(BigDecimal.ONE);
                }
                dto.setClassify(LovUtils.getNameByValue(LovHeaderCodeConstant.ITEM_CATEGORY, dto.getClassify()));
            }
        });
    }

    private static ConversionCoefficientMwSaveDTO getCoefficientMwSaveDTO(BatteryTypeMainDTO e, BigDecimal batteryMWQty, BigDecimal batteryEfficiencyQty) {
        // 组装数据
        ConversionCoefficientMwSaveDTO batteryTypeMWDTO = new ConversionCoefficientMwSaveDTO();
        batteryTypeMWDTO.setBatteryCode(e.getBatteryCode());
        batteryTypeMWDTO.setBatteryName(e.getBatteryName());
        batteryTypeMWDTO.setCategory(e.getCategory());
        batteryTypeMWDTO.setBatteryMwQty(BigDecimal.ZERO.compareTo(batteryMWQty) == 0 ? BigDecimal.ONE : batteryMWQty);
        batteryTypeMWDTO.setBatteryEfficiencyQty(BigDecimal.ZERO.compareTo(batteryEfficiencyQty) == 0 ? BigDecimal.ONE : batteryEfficiencyQty);
        return batteryTypeMWDTO;
    }

    private static void getBatterySiliconWaferDTO(ConversionCoefficientMwSaveDTO dto, String classify,
                                                  String unitConsumption, String basePlace, String workshop,
                                                  BigDecimal materielMWQty, String error) {
        if (StringUtils.isNotBlank(unitConsumption) && StringUtils.isNumeric(unitConsumption)) {
            dto.setUnitConsumption(new BigDecimal(unitConsumption));
        }
        dto.setMaterielMwQty(materielMWQty);
        if (StringUtils.isNotBlank(basePlace)) {
            dto.setBasePlace(basePlace);
        }
        if (StringUtils.isNotBlank(workshop)) {
            dto.setWorkshop(workshop);
        }
        if (StringUtils.isNotEmpty(error)) {
            dto.setWarningReason(error);
        }
        dto.setClassify(classify);
        LovLineDTO lovLineDTO = LovUtils.getByName("work_shop",
                workshop);
        if (null != lovLineDTO) {
            LovLineDTO orgLov = LovUtils.getByName("inventory_organization",
                    lovLineDTO.getAttribute10());
            dto.setOrganizationId(null != orgLov ? Long.parseLong(orgLov.getAttribute1()) : null);
        }
    }

    /**
     * 组装参数
     *
     * @param classify
     * @param unitConsumption
     * @param basePlace
     * @param workshop
     * @param materielMWQty
     * @return
     */
    private static void getBatteryTypeMWDTO(ConversionCoefficientMwSaveDTO dto, String classify,
                                            String unitConsumption, String basePlace, String workshop,
                                            BigDecimal materielMWQty, String error) {
        if (StringUtils.isNotBlank(unitConsumption)) {
            dto.setUnitConsumption(new BigDecimal(unitConsumption));
        }
        dto.setMaterielMwQty(materielMWQty);
        if (StringUtils.isNotBlank(basePlace)) {
            dto.setBasePlace(basePlace);
        }
        if (StringUtils.isNotBlank(workshop)) {
            dto.setWorkshop(workshop);
        }
        if (StringUtils.isNotEmpty(error)) {
            dto.setWarningReason(error);
        }
        dto.setClassify(classify);
        LovLineDTO lovLineDTO = LovUtils.getByName("work_shop",
                workshop);
        if (null != lovLineDTO) {
            LovLineDTO orgLov = LovUtils.getByName("inventory_organization",
                    lovLineDTO.getAttribute10());
            dto.setOrganizationId(null != orgLov ? Long.parseLong(orgLov.getAttribute1()) : null);
        }
    }

    /**
     * 物料MW系数计算规则
     * 硅片：电池目标良率 * 电池MW系数
     *
     * @param batteryMWQty
     * @param batteryEfficiencyQty
     * @return
     */
    private static BigDecimal getSiliconWaferQty(BigDecimal batteryMWQty, BigDecimal batteryEfficiencyQty) {
        BigDecimal materielMWQty = new BigDecimal(0);
        try {
            if (batteryMWQty == null) {
                throw new BizException("bbom_valid_batteryMWQty_notBlank");
            }
            if (batteryMWQty.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_batteryMWQty_notZero");
            }
            if (batteryEfficiencyQty == null) {
                throw new BizException("bbom_valid_batteryEfficiencyQty_notBlank");
            }
            if (batteryEfficiencyQty.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_batteryEfficiencyQty_notBlank");
            }
            materielMWQty = batteryEfficiencyQty.multiply(batteryMWQty).setScale(6, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.warn("硅片：电池目标良率 * 电池MW系数 计算出现异常错误: {}", e.getMessage());
        }
        return materielMWQty;
    }

    /**
     * 物料MW系数计算规则
     * 网版：1/网版寿命*电池MW系数
     *
     * @param lifetime
     * @param batteryMWQty
     * @return
     */
    private static BigDecimal getScreenPlateQty(String lifetime, BigDecimal batteryMWQty) {
        BigDecimal materielMWQty = new BigDecimal(0);
        try {
            if (!NumberUtil.isNumber(lifetime)) {
                throw new BizException("bbom_valid_lifetime_illegal");
            }
            BigDecimal lifetimeBig = new BigDecimal(lifetime);
            if (lifetimeBig.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_lifetime_illegal");
            }
            if (batteryMWQty == null) {
                throw new BizException("bbom_valid_batteryMWQty_notBlank");
            }
            if (batteryMWQty.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_batteryMWQty_notZero");
            }
            BigDecimal lifetimeMultiply = lifetimeBig.multiply(batteryMWQty);
            if (lifetimeMultiply.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_DividingByZero");
            }
            materielMWQty = new BigDecimal(1).divide(lifetimeMultiply, 6, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.warn("网版：1/网版寿命*电池MW系数 计算出现异常错误: {}", e.getMessage());
        }
        return materielMWQty;
    }

    /**
     * 物料MW系数计算规则
     * 浆料：单耗/电池MW系数
     *
     * @param unitConsumption
     * @param batteryMWQty
     * @return
     */
    private static BigDecimal getSlurryQty(String unitConsumption, BigDecimal batteryMWQty) {
        BigDecimal materielMWQty = new BigDecimal(0);
        try {
            if (!NumberUtil.isNumber(unitConsumption)) {
                throw new BizException("bbom_valid_unitConsumption_notNumber");
            }
            BigDecimal unitConsumptionBig = new BigDecimal(unitConsumption);
            if (unitConsumptionBig.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_unitConsumption_notZero");
            }
            if (batteryMWQty == null) {
                throw new BizException("bbom_valid_batteryMWQty_notBlank");
            }
            if (batteryMWQty.compareTo(BigDecimal.ZERO) == 0) {
                throw new BizException("bbom_valid_DividingByZero");
            }
            materielMWQty = unitConsumptionBig.divide(batteryMWQty, 6, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.warn("浆料：单耗/电池MW系数 计算出现异常错误: {}", e.getMessage());
        }
        return materielMWQty;
    }

    @Override
    public Page<ConversionCoefficientMwDTO> queryByPage(ConversionCoefficientMwQuery query) {
        // 查询
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //查询用户数据权限
        List<String> privilegeDTOList = systemService.getDataPrivilegeList(LovHeaderCodeConstant.ITEM_CATEGORY.toLowerCase());
        if (!privilegeDTOList.contains("-1")) {
            booleanBuilder.and(qConversionCoefficientMw.classify.in(privilegeDTOList));
        }
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ConversionCoefficientMw> page = repository.findAll(booleanBuilder, pageable);

        // lov id->name 转码
        List<ConversionCoefficientMwDTO> coefficientMwDTOList = convert.toDto(page.getContent());
        convertLovValueToName(coefficientMwDTOList);
        return new PageImpl(coefficientMwDTOList, page.getPageable(), page.getTotalElements());
    }

    /**
     * 计算逻辑:
     * 步骤 1: 在电池类型基础数据界面，获取电池类型+物料分类+基地+车间信息；
     * 步骤 2: 依据电池类型+车间，在电池分配获取电池 MW 系数;
     * 按投产电池类型，获取产出的电池类型，再到电池分配取电池 MW 系数，取平均值;
     * 获取不到数据时，默认为 100%，状态为“警告”;并在预警栏写入“未获取电池MW系数”;
     * 步骤 3:按电池类型+基地+车间的维度，获取电池排产中，“电池目标良率”的值;
     * 预警:获取不到数据时，默认为 100%，并在预警栏写入“未获取到目标良率”
     * 步骤 4: 依据电池类型+物料分类+基地+车间，取电池 BOM 的网版寿命/浆料单耗信息;多个网版/浆料，分行显示;
     * 预警:获取不到数据时，值为空，并在预警栏写入“单耗数据缺失”
     * 步骤 5:物料MW 系数计算规则
     * 硅片: 电池目标良率*电池 MW 系数；
     * 网版:1/网版寿命*电池MW 系数;
     *
     * @param resultList
     */
    private void computeSaveExtracted(List<ConversionCoefficientMwSaveDTO> resultList) {

        // 1.查询电池静态属性信息 条件 电池类型 品类
        List<BatteryTypeMain> batteryTypeMainList = batteryTypeMainRepository.findAll();
        List<BatteryTypeMainDTO> batteryTypeMainDTOList = batteryTypeMainDEConvert.toDto(batteryTypeMainList);
        //分组电池类型
        List<String> cellsType = batteryTypeMainDTOList.stream().map(batteryTypeMainDTO -> batteryTypeMainDTO.getBatteryName()).collect(Collectors.toList());
        MwCoefficientQuery mwCoefficientQuery = new MwCoefficientQuery();
        mwCoefficientQuery.setCellTypeList(cellsType);
        //MW折算系数
        List<MwCoefficientDTO> mwDTOList = apsFeign.findMwCoefficient(mwCoefficientQuery).getBody().getData();
        //一对多
        Map<String, List<MwCoefficientDTO>> map = mwDTOList.stream()
                .collect(Collectors.groupingBy(MwCoefficientDTO::getCellType));
        // 3.根据品类拆分 硅片 网版 浆料
        String redisKey = "ConversionCoefficientMwServiceImpl.computeSaveExtracted";
        RLock lock = redissonClient.getLock(redisKey);
        if (!lock.isLocked()) {
            try {
                // 获取锁
                lock.lock();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    extracted(resultList, batteryTypeMainDTOList, map);
                }, threadPoolExecutor);
                // 等待所有 CompletableFuture 任务完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(future);
                allFutures.join(); // 等待所有任务完成
                lock.unlock();  // 释放锁
            } finally {
                // 确保释放锁
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            throw new RuntimeException("其他用户正在操作中，请稍后");
        }
    }

    private void extracted(List<ConversionCoefficientMwSaveDTO> resultList, List<BatteryTypeMainDTO> batteryTypeMainDTOList, Map<String, List<MwCoefficientDTO>> map) {
        batteryTypeMainDTOList.stream().forEach(e -> {
            // Todo  2.电池MW系数 电池目标良率 调用2个接口 MOCK数据
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            booleanBuilder.and(QBatterySiliconWafer.batterySiliconWafer.batteryCode.eq(e.getBatteryCode()));
            // 查询硅片信息
            List<BatterySiliconWafer> siliconWaferList = IterableUtils.toList(batterySiliconWaferRepository.findAll(booleanBuilder));
            List<BatterySiliconWaferDTO> batterySiliconWaferDTOList = batterySiliconWaferDEConvert.toDto(siliconWaferList);
            if (CollectionUtils.isNotEmpty(batterySiliconWaferDTOList)) {
                batterySiliconWaferDTOList.stream().forEach(batterySilicon -> {
                    //获取基地对应的国内国外信息
                    LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, batterySilicon.getBasePlace());
                    LovLineDTO isOverseaLov = LovUtils.get(Long.valueOf(lovLineDTO.getAttribute2()));
                    // 硅片 电池MW系数
                    BigDecimal batteryMWQty = WMToalSum(map, e.getBatteryName(), isOverseaLov.getLovValue());

                    // 电池目标良率
                    CellFineQuery cellFineQuery = new CellFineQuery();
                    cellFineQuery.setCellsType(e.getBatteryName());
                    cellFineQuery.setBasePlace(batterySilicon.getBasePlace());
                    cellFineQuery.setWorkshop(batterySilicon.getWorkshop());
                    //获取当前年份
                    cellFineQuery.setYear(LocalDateTime.now().getYear());
                    BigDecimal batteryEfficiencyQty = batteryEfficiencyToalSum(cellFineQuery);

                    ConversionCoefficientMwSaveDTO batteryTypeMWDTO = getCoefficientMwSaveDTO(e, batteryMWQty, batteryEfficiencyQty);
                    //物料MW系数=电池目标良率*电池 MW 系数:
                    BigDecimal materielMWQty = getSiliconWaferQty(batteryMWQty, batteryTypeMWDTO.getBatteryEfficiencyQty());
                    //电池MW系数、电池目标良率、单耗、物料MW系数
                    //硅片类型组装参数
                    getBatterySiliconWaferDTO(batteryTypeMWDTO, ItemCategoryEnum.SILICON.getValue(), NUMBER_1,
                            batterySilicon.getBasePlace(), batterySilicon.getWorkshop(), materielMWQty, returnError(batteryMWQty, batteryEfficiencyQty, "1"));
                    resultList.add(batteryTypeMWDTO);
                });

            }
            ScreenLifeQuery screenLifeQuery = new ScreenLifeQuery();
            screenLifeQuery.setBatteryType(e.getBatteryCode());
            booleanBuilder = new BooleanBuilder();
            booleanBuilder.and(QScreenLife.screenLife.batteryType.eq(e.getBatteryCode()));
            List<ScreenLife> screenLifeList = IterableUtils.toList(screenLifeRepository.findAll(booleanBuilder));
            List<ScreenLifeDTO> screenLifeDTOList = screenLifeDEConvert.toDto(screenLifeList);
            if (CollectionUtils.isNotEmpty(screenLifeDTOList)) {
                screenLifeDTOList.stream().forEach(screenLifeDTO -> {
                    //获取基地对应的国内国外信息
                    LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, screenLifeDTO.getBasePlace());
                    LovLineDTO isOverseaLov = LovUtils.get(Long.valueOf(lovLineDTO.getAttribute2()));
                    // 网版 电池MW系数、电池目标良率、单耗
                    BigDecimal batteryMWQty = WMToalSum(map, e.getBatteryName(), isOverseaLov.getLovValue());
                    // 电池目标良率
                    CellFineQuery cellFineQuery = new CellFineQuery();
                    cellFineQuery.setCellsType(e.getBatteryName());
                    cellFineQuery.setBasePlace(screenLifeDTO.getBasePlace());
                    cellFineQuery.setWorkshop(screenLifeDTO.getWorkshop());
                    //获取当前年份
                    cellFineQuery.setYear(LocalDateTime.now().getYear());
                    BigDecimal batteryEfficiencyQty = batteryEfficiencyToalSum(cellFineQuery);
                    // 网版寿命 单耗取寿命万
                    // 组装数据
                    ConversionCoefficientMwSaveDTO batteryTypeMWDTO = getCoefficientMwSaveDTO(e, batteryMWQty, batteryEfficiencyQty);
                    //电池MW系数=1/网版寿命*电池 MW 系数
                    BigDecimal screenPlateQty = getScreenPlateQty(screenLifeDTO.getLifetime(), batteryMWQty);
                    getBatteryTypeMWDTO(batteryTypeMWDTO, ItemCategoryEnum.SCREEN_PLATE.getValue(),
                            screenLifeDTO.getLifetime(), screenLifeDTO.getBasePlace(), screenLifeDTO.getWorkshop(), screenPlateQty, returnError(batteryMWQty, batteryEfficiencyQty, screenLifeDTO.getLifetime()));
                    resultList.add(batteryTypeMWDTO);
                });

            }
            SlurryInformationQuery slurryQuery = new SlurryInformationQuery();
            slurryQuery.setBatteryType(e.getBatteryCode());
            booleanBuilder = new BooleanBuilder();
            booleanBuilder.and(QSlurryInformation.slurryInformation.batteryType.eq(e.getBatteryCode()));
            // 查询浆料
            List<SlurryInformation> slurryInformationList = IterableUtils.toList(slurryInformationRepository.findAll(booleanBuilder));
            List<SlurryInformationDTO> slurryInfoList = slurryInformationDEConvert.toDto(slurryInformationList);
            if (CollectionUtils.isNotEmpty(slurryInfoList)) {
                slurryInfoList.stream().forEach(slurryInfo -> {
                    //获取基地对应的国内国外信息
                    LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, slurryInfo.getBasePlace());
                    LovLineDTO isOverseaLov = LovUtils.get(Long.valueOf(lovLineDTO.getAttribute2()));
                    // 浆料 电池MW系数、电池目标良率、单耗
                    BigDecimal batteryMWQty = WMToalSum(map, e.getBatteryName(), isOverseaLov.getLovValue());
                    // 电池目标良率
                    CellFineQuery cellFineQuery = new CellFineQuery();
                    cellFineQuery.setCellsType(e.getBatteryName());
                    cellFineQuery.setBasePlace(slurryInfo.getBasePlace());
                    cellFineQuery.setWorkshop(slurryInfo.getWorkshop());
                    //获取当前年份
                    cellFineQuery.setYear(LocalDateTime.now().getYear());
                    BigDecimal batteryEfficiencyQty = batteryEfficiencyToalSum(cellFineQuery);
                    // 浆料车间 单耗
                    ConversionCoefficientMwSaveDTO batteryTypeMWDTO = getCoefficientMwSaveDTO(e, batteryMWQty, batteryEfficiencyQty);
                    //电池MW系数=单耗/电池MW系数
                    BigDecimal slurryQty = getSlurryQty(slurryInfo.getUnitConsumption(), batteryMWQty);
                    getBatteryTypeMWDTO(batteryTypeMWDTO, ItemCategoryEnum.SLURRY.getValue(), slurryInfo.getUnitConsumption(),
                            slurryInfo.getBasePlace(), slurryInfo.getWorkshop(), slurryQty, returnError(batteryMWQty, batteryEfficiencyQty, slurryInfo.getUnitConsumption()));
                    resultList.add(batteryTypeMWDTO);
                });

            }
        });
    }

    //异常错误返回
    private String returnError(BigDecimal batteryMWQty, BigDecimal batteryEfficiencyQty, String unitConsumption) {
        StringBuffer sbError = new StringBuffer("");
        if (BigDecimal.ZERO.compareTo(batteryMWQty) == 0) {
            sbError.append("未获取电池MW系数");
        }
        if (BigDecimal.ZERO.compareTo(batteryEfficiencyQty) == 0) {
            if (sbError.length() > 0) {
                sbError.append(",未获取到目标良率");
            } else {
                sbError.append("未获取到目标良率");
            }
        }
        if (StringUtils.isEmpty(unitConsumption)) {
            if (sbError.length() > 0) {
                sbError.append(",单耗数据缺失");
            } else {
                sbError.append("单耗数据缺失");
            }
        }
        return sbError.toString();
    }

    //电池MW系数
    public BigDecimal WMToalSum(Map<String, List<MwCoefficientDTO>> map, String batteryName, String isOversea) {
        if (map.containsKey(batteryName)) {
            // 组装数据
            BigDecimal WMToalSum = map.get(batteryName).stream().filter(p -> p.getIsOversea().equals(isOversea)).map(dto -> Optional.ofNullable(dto.getCoefficient()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (WMToalSum.compareTo(BigDecimal.ZERO) == 0) {
                return BigDecimal.ZERO;
            }
            //电池MW系数 等于根据电池类型名称分组的集合数据累加除以集合size 保留2位 向上四舍五入
            BigDecimal batteryMWQty = WMToalSum.divide(new BigDecimal(map.get(batteryName).stream().filter(p -> p.getIsOversea().equals(isOversea)).collect(Collectors.toList()).size()), 2, RoundingMode.HALF_UP);
            return batteryMWQty;
        }
        return BigDecimal.ZERO;
    }

    //电池目标良率
    public BigDecimal batteryEfficiencyToalSum(CellFineQuery cellFineQuery) {
        List<CellFineDTO> cellFineDTOList = bapsFeign.queryList(cellFineQuery).getBody().getData();
        //获取电池目标良率不为0的集合
        List<BigDecimal> notEqZeroList = new ArrayList<>();
        cellFineDTOList.stream().forEach(cellFineDTO -> {
            //一行数据有12个月
            for (int i = 1; i < 13; i++) {
                BigDecimal decimal = ReflectUtil.invoke(cellFineDTO, "getM" + i);
                if (BigDecimal.ZERO.compareTo(decimal) != 0) {
                    notEqZeroList.add(decimal);
                }
            }
        });
        if (CollectionUtils.isEmpty(notEqZeroList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal totalSum = cellFineDTOList.stream().map(cellFine -> cellFine.getM1().add(cellFine.getM2())
                .add(cellFine.getM3()).add(cellFine.getM4()).add(cellFine.getM5()).add(cellFine.getM6())
                .add(cellFine.getM7()).add(cellFine.getM8()).add(cellFine.getM9())
                .add(cellFine.getM10()).add(cellFine.getM11()).add(cellFine.getM12())
        ).reduce(BigDecimal.ZERO, BigDecimal::add);
        //电池目标良率等于集合汇总的12个月的累加之和除以集合size 保留2位 向上四舍五入
        if (totalSum.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal batteryEfficiencyQty = totalSum.divide(new BigDecimal(notEqZeroList.size()), 2, RoundingMode.HALF_UP);
        return batteryEfficiencyQty;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ConversionCoefficientMwQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qConversionCoefficientMw.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qConversionCoefficientMw.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qConversionCoefficientMw.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getCategory())) {
            booleanBuilder.and(qConversionCoefficientMw.category.eq(query.getCategory()));
        }
        if (StringUtils.isNotEmpty(query.getClassify())) {
            booleanBuilder.and(qConversionCoefficientMw.classify.eq(query.getClassify()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qConversionCoefficientMw.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qConversionCoefficientMw.workshop.eq(query.getWorkshop()));
        }
        if (query.getBatteryMwQty() != null) {
            booleanBuilder.and(qConversionCoefficientMw.batteryMwQty.eq(query.getBatteryMwQty()));
        }
        if (query.getBatteryEfficiencyQty() != null) {
            booleanBuilder.and(qConversionCoefficientMw.batteryEfficiencyQty.eq(query.getBatteryEfficiencyQty()));
        }
        if (query.getUnitConsumption() != null) {
            booleanBuilder.and(qConversionCoefficientMw.unitConsumption.eq(query.getUnitConsumption()));
        }
        if (query.getMaterielMwQty() != null) {
            booleanBuilder.and(qConversionCoefficientMw.materielMwQty.eq(query.getMaterielMwQty()));
        }
        if (StringUtils.isNotEmpty(query.getWarningReason())) {
            booleanBuilder.and(qConversionCoefficientMw.warningReason.eq(query.getWarningReason()));
        }
    }

    @Override
    public ConversionCoefficientMwDTO queryById(Long id) {
        ConversionCoefficientMw queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ConversionCoefficientMwDTO save(ConversionCoefficientMwSaveDTO saveDTO) {
        ConversionCoefficientMw newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ConversionCoefficientMw());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    /**
     * 计算按钮 保存
     *
     * @param saveDTO
     * @return
     */
    @Override
    public Page<ConversionCoefficientMwDTO> computeSave(ConversionCoefficientMwSaveDTO saveDTO) {
        // 获取数据
        List<ConversionCoefficientMwSaveDTO> resultList = new ArrayList<>();
        computeSaveExtracted(resultList);
        // 如果查询计算数据不为空 先清除数据库历史数据
        if (CollectionUtils.isNotEmpty(resultList)) {
            repository.deleteAll();
            resultList.forEach(e -> {
                ConversionCoefficientMw dto = new ConversionCoefficientMw();
                BeanUtils.copyProperties(e, dto);
                repository.save(dto);
            });
        }
        ConversionCoefficientMwQuery query = new ConversionCoefficientMwQuery();
        BeanUtils.copyProperties(saveDTO, query);
        return this.queryByPage(query);
    }

    /**
     * job 计算按钮 保存
     *
     * @return
     */
    @Override
    public void computeSaveWithJob() {
        // 获取数据
        List<ConversionCoefficientMwSaveDTO> resultList = new ArrayList<>();
        computeSaveExtracted(resultList);
        // 如果查询计算数据不为空 先清除数据库历史数据
        if (CollectionUtils.isNotEmpty(resultList)) {
            repository.deleteAll();
            resultList.forEach(e -> {
                ConversionCoefficientMw dto = new ConversionCoefficientMw();
                BeanUtils.copyProperties(e, dto);
                repository.save(dto);
            });
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(ConversionCoefficientMwQuery query, HttpServletResponse response) {
        List<ConversionCoefficientMwDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<ConversionCoefficientMwExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "兆瓦转换系数-导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "兆瓦转换系数").head(ConversionCoefficientMwExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    @Override
    public Map<String, BigDecimal> getBatteryTypeMWForInventoryTrends() {
        List<ConversionCoefficientMw> coefficientMws = repository.findAll();
        return coefficientMws.stream().collect(Collectors.toMap(ele ->
                        String.format("%s_%s_%s", ele.getBatteryName(), ele.getBasePlace(), ele.getClassify()),
                ConversionCoefficientMw::getMaterielMwQty, (v1, v2) -> v1));
    }

    @Override
    public Map<String, BigDecimal> getAverageMVForInventoryTrends() {
        List<ConversionCoefficientMw> coefficientMws = repository.findAll();
        Map<String, List<BigDecimal>> allMap = coefficientMws.stream().collect(Collectors.groupingBy(ele ->
                        String.format("%s_%s_%s", ele.getClassify(), ele.getCategory(), ele.getBasePlace()),
                Collectors.mapping(ConversionCoefficientMw::getMaterielMwQty, Collectors.toList())));
        Map<String, BigDecimal> resultMap = Maps.newHashMap();
        allMap.forEach((k, v) -> {
            BigDecimal totalMaterielMwQty = v.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            resultMap.put(k, totalMaterielMwQty.divide(new BigDecimal(v.size()), 6, RoundingMode.HALF_UP));
        });
        return resultMap;
    }
}
