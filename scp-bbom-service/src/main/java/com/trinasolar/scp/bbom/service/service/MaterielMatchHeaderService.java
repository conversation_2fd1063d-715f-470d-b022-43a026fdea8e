package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchHeaderSaveDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 电池物料号匹配 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
public interface MaterielMatchHeaderService {
    /**
     * 分页获取电池物料号匹配
     *
     * @param query 查询对象
     * @return 电池物料号匹配分页对象
     */
    Page<MaterielMatchHeaderDTO> queryByPage(MaterielMatchHeaderQuery query);

    /**
     * 查询全部数据
     *
     * @param query
     * @return
     */
    List<MaterielMatchHeaderDTO> queryByAll(MaterielMatchHeaderQuery query);

    /**
     * 根据主键获取电池物料号匹配详情
     *
     * @param id 主键
     * @return 电池物料号匹配详情
     */
    MaterielMatchHeaderDTO queryById(Long id);

    /**
     * @param
     * @return
     */
    void matchItem(Long matchId, Map<String,List<MaterielMatchLine>> matchLineaAllMap);

    void allMatchItemPage(MaterielMatchHeaderQuery query);

    Map<Long, List<String>> query4AByMatchHeadDto(List<MaterielMatchHeaderDTO> headerDTOs);

    //获取Structures 所有数据
    Map<String, StructuresDTO> queryListByStructures();

    Map<Long, String> query5AByMatchHeadDto(List<MaterielMatchHeaderDTO> headerDTOs);

    Map<Long, List<ErpAlternateDesignatorDTO>> getErpAlternateDesignatorByOrgId(String orgId);

    String getAlternateDesignatorCode(String workshop, Long sourceItemId,
                                      Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                      Map<String, StructuresDTO> structuresDTOMap);

    /**
     * 保存或更新电池物料号匹配
     *
     * @param saveDTO 电池物料号匹配保存对象
     * @return 电池物料号匹配对象
     */
    MaterielMatchHeaderDTO save(MaterielMatchHeaderSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池物料号匹配
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(MaterielMatchHeaderQuery query, HttpServletResponse response);

    void saveMatch(MaterielMatchHeaderDTO materielMatchHeaderDTO, List<MaterielMatchLineDTO> materielMatchLineDTOS);

    /**
     * 获取电池物料号匹配
     *
     * @param query 查询对象
     * @return 电池物料号匹配分页对象
     */
    List<MaterielMatchHeaderDTO> queryList(MaterielMatchHeaderQuery query);

    void addMatchInfo(MaterielMatchHeaderDTO matchHeaderDTO);

    MaterielMatchLineDTO getMaterielMatchLineDTOByLine(MaterielMatchHeaderDTO materielMatchHeaderDTO, CellPlanLineDTO cellPlanLineDTO, Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap, Map<String, StructuresDTO> structuresDTOMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap);

    void requestErpItemCodeSyncExternalItems();

    void externalItemsDistributeBomItem();

    void confirm();

    List<MaterielMatchLineMatchStatusDTO> appointItemList(IdsDTO idsDTO);

    Map<Long, List<ExpressRuleLineDTO>> getExpressRuleLineList();

    Map<String,List<MaterielMatchLine>> getAllLineMap();
}


