package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.SpecialCellMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.query.SpecialCellMatchRuleQuery;
import com.trinasolar.scp.bbom.domain.save.SpecialCellMatchRuleSaveDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
public interface SpecialCellMatchRuleService {

    Page<SpecialCellMatchRuleDTO> queryByPage(SpecialCellMatchRuleQuery query);

    SpecialCellMatchRuleDTO queryById(Long id);

    SpecialCellMatchRuleDTO save(SpecialCellMatchRuleSaveDTO saveDTO);

    void logicDeleteByIds(List<Long> ids);

    void importData(MultipartFile multipartFile, ExcelPara excelPara);

    void export(SpecialCellMatchRuleQuery query, HttpServletResponse response);
}
