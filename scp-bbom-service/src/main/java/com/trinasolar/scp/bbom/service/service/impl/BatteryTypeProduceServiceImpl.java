package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.BatteryTypeProduceDEConvert;
import com.trinasolar.scp.bbom.domain.dto.BatteryTypeProduceDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryTypeProduce;
import com.trinasolar.scp.bbom.domain.entity.QBatteryTypeProduce;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeProduceNewQuery;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeProduceQuery;
import com.trinasolar.scp.bbom.domain.query.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryTypeProduceSaveDTO;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.repository.BatteryTypeProduceRepository;
import com.trinasolar.scp.bbom.service.service.BatteryTypeProduceService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 电池类型动态属性-产出电池类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Slf4j
@Service("batteryTypeProduceService")
@RequiredArgsConstructor
public class BatteryTypeProduceServiceImpl implements BatteryTypeProduceService {
    private static final QBatteryTypeProduce qBatteryTypeProduce = QBatteryTypeProduce.batteryTypeProduce;

    private final BatteryTypeProduceDEConvert convert;

    private final BatteryTypeProduceRepository repository;

    @Autowired
    private BomFeign bomFeign;

    @Override
    public Page<BatteryTypeProduceDTO> queryByPage(BatteryTypeProduceQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<BatteryTypeProduce> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<BatteryTypeProduceDTO> queryByAll(BatteryTypeProduceQuery query) {
        return queryByPage(query).getContent();
    }

    @Override
    @SneakyThrows
    public Page<ErpAlternateDesignatorDTO> queryErpByPageAll(ErpAlternateDesignatorQuery query) {
        ResponseEntity<Results<Page<ErpAlternateDesignatorDTO>>> resultsResponseEntity = bomFeign.queryByPage(query);
        // 根据车间查询org_id
        Map<String, LovLineDTO> inventoryOrganization = LovUtils.getAllByHeaderCode("inventory_organization");
        if (resultsResponseEntity.getBody().isSuccess()) {
            Page<ErpAlternateDesignatorDTO> data = resultsResponseEntity.getBody().getData();
            // 转换 org_id -> 库存组织
            Optional.ofNullable(data).ifPresent(u -> {
                List<ErpAlternateDesignatorDTO> content = u.getContent();
                for (ErpAlternateDesignatorDTO con : content) {
                    Long organizationId = con.getOrganizationId();
                    inventoryOrganization.forEach((key, value) -> {
                        if (null != organizationId && String.valueOf(organizationId).equals(value.getAttribute1())) {
                            String lovName = value.getLovName();
                            con.setInventoryOrganization(lovName);
                        }
                    });
                }
            });
            return data;
        }
        return null;
    }

    @Override
    public List<BatteryTypeProduceDTO> queryByBatteryType(BatteryTypeProduceNewQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereBatteryType(booleanBuilder, query);
        return convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
    }

    @Override
    public List<BatteryTypeProduceDTO> querySaveProduce(BatteryTypeProduceQuery query) {
        // 查询电池类型名称 lov
        List<BatteryTypeProduceDTO> list = new ArrayList<>();
        Map<String, LovLineDTO> batteryType = LovUtils.getAllByHeaderCode("aps_power_cell_type");
        batteryType.forEach((key, value) -> {
            if (key.contains(query.getBatteryName())) {
                BatteryTypeProduceDTO dto = new BatteryTypeProduceDTO();
                dto.setBatteryCode(query.getBatteryCode());
                dto.setBatteryName(query.getBatteryName());
                dto.setName(value.getLovName());
                dto.setEffectiveStartDate(LocalDateTime.now());
                dto.setEffectiveEndDate(LocalDateTime.now());
                BooleanBuilder booleanBuilder = new BooleanBuilder();
                if (StringUtils.isNotBlank(dto.getBatteryCode())) {
                    booleanBuilder.and(qBatteryTypeProduce.batteryCode.eq(dto.getBatteryCode()));
                }
                if (StringUtils.isNotBlank(dto.getBatteryName())) {
                    booleanBuilder.and(qBatteryTypeProduce.batteryName.eq(dto.getBatteryName()));
                }
                if (StringUtils.isNotBlank(dto.getName())) {
                    booleanBuilder.and(qBatteryTypeProduce.name.eq(dto.getName()));
                }
                List<BatteryTypeProduceDTO> batteryTypeProduceDTOS = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
                batteryTypeProduceDTOS.forEach(e -> {
                    dto.setId(e.getId());
                });
                list.add(dto);
            }
        });
        return convert.toDto(repository.saveAll(convert.toEntity(list)));
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatteryTypeProduceQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryTypeProduce.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryTypeProduce.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryTypeProduce.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getName())) {
            booleanBuilder.and(qBatteryTypeProduce.name.eq(query.getName()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatteryTypeProduce.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatteryTypeProduce.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }
    }

    private void buildWhereBatteryType(BooleanBuilder booleanBuilder, BatteryTypeProduceNewQuery query) {
        if (CollectionUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qBatteryTypeProduce.batteryName.in(query.getBatteryType()));
        }
    }

    @Override
    public BatteryTypeProduceDTO queryById(Long id) {
        BatteryTypeProduce queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public BatteryTypeProduceDTO save(BatteryTypeProduceSaveDTO saveDTO) {
        BatteryTypeProduce newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new BatteryTypeProduce());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(BatteryTypeProduceQuery query, HttpServletResponse response) {
        List<BatteryTypeProduceDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, MessageHelper.getMessage("bbom_filename_BatteryTypeProduce").getDesc(), MessageHelper.getMessage("bbom_filename_BatteryTypeProduce").getDesc(), excelPara.getSimpleHeader(), excelData);
    }
}
