package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.SlurryInformationDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.query.SlurryInformationQuery;
import com.trinasolar.scp.bbom.domain.save.SlurryInformationSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 浆料车间单耗及线数维护 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
public interface SlurryInformationService {
    /**
     * 分页获取浆料车间单耗及线数维护
     *
     * @param query 查询对象
     * @return 浆料车间单耗及线数维护分页对象
     */
    Page<SlurryInformationDTO> queryByPage(String userId, SlurryInformationQuery query);

    List<DataPrivilegeDTO> getDataPrivilegeDTOS(String userId, String privilegeType);

    List<SlurryInformationDTO> queryList(SlurryInformationQuery query);

    /**
     * 根据主键获取浆料车间单耗及线数维护详情
     *
     * @param id 主键
     * @return 浆料车间单耗及线数维护详情
     */
    SlurryInformationDTO queryById(Long id);

    /**
     * 保存或更新浆料车间单耗及线数维护
     *
     * @param saveDTO 浆料车间单耗及线数维护保存对象
     * @return 浆料车间单耗及线数维护对象
     */
    SlurryInformationDTO save(SlurryInformationSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除浆料车间单耗及线数维护
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(SlurryInformationQuery query, HttpServletResponse response, String userId);

    /**
     * 导出模版
     *
     * @param query
     * @param response
     */
    void queryByPageExport(SlurryInformationQuery query, HttpServletResponse response, String userId);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);

    List<String> versionList();
}

