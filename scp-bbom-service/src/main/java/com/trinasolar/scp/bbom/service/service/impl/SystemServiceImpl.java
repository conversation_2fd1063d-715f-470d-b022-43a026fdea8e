package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.collect.Lists;
import com.trinasolar.scp.bbom.domain.dto.ErpApprovalSupplierPageNumDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpApprovalSupplierQuery;
import com.trinasolar.scp.bbom.domain.dto.LovHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.OrganizationDefinitionsDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.OrganizationDefinitionsQuery;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeQuery;
import com.trinasolar.scp.bbom.domain.dto.feign.system.LovLineSaveDTO;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.bbom.service.service.SystemService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/18
 */
@Slf4j
@Service("systemService")
@RequiredArgsConstructor
public class SystemServiceImpl implements SystemService {
    private static final String NO_PERMISSION = "-9999";
    private final SystemFeign systemFeign;

    @Override
    public List<OrganizationDefinitionsDTO> getOrganizationScpFlagIsY() {
        OrganizationDefinitionsQuery query = new OrganizationDefinitionsQuery();
        query.setScpFlag("Y");
        Results<List<OrganizationDefinitionsDTO>> body = systemFeign.queryOrganizationByList(query).getBody();
        if (!body.isSuccess()) {
            throw new BizException("组织信息查询失败");
        }
        return body.getData();
    }

    @Override
    public List<OrganizationDefinitionsDTO> getOrganizationCellsScpFlagIsY() {
        OrganizationDefinitionsQuery query = new OrganizationDefinitionsQuery();
        query.setCellsScpFlag("Y");
        Results<List<OrganizationDefinitionsDTO>> body = systemFeign.queryOrganizationByList(query).getBody();
        if (!body.isSuccess()) {
            throw new BizException("组织信息查询失败");
        }
        return body.getData();
    }

    @Override
    public ErpApprovalSupplierPageNumDTO querySupplierPage(ErpApprovalSupplierQuery query) {
        Results<ErpApprovalSupplierPageNumDTO> body = systemFeign.querySupplierPage(query).getBody();
        if (!body.isSuccess()) {
            throw new BizException("批准供应商分页列表查询失败");
        }
        return body.getData();
    }

    @Override
    public List<String> getDataPrivilegeList(String privilegeType) {
        DataPrivilegeQuery dataPrivilegeQuery = new DataPrivilegeQuery();
        dataPrivilegeQuery.setUserId(MyThreadLocal.get().getUserId());
        dataPrivilegeQuery.setPrivilegeType(privilegeType);
        ResponseEntity<Results<List<DataPrivilegeDTO>>> resultsResponseEntity = systemFeign.queryDataPrivilege(dataPrivilegeQuery);
        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            throw new BizException("获取权限数据失败！");
        }
        List<DataPrivilegeDTO> dataPrivilegeList = resultsResponseEntity.getBody().getData();
        if (CollectionUtils.isNotEmpty(dataPrivilegeList)) {
            return dataPrivilegeList.stream().map(DataPrivilegeDTO::getDataCode).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList(NO_PERMISSION);
    }

    @Override
    public LovHeaderDTO getLovHeaderByCode(String headerCode) {
        LovLineQuery lovLineQuery = new LovLineQuery();
        lovLineQuery.setCode(headerCode);
        Results<LovHeaderDTO> lovHeaderDTOBody = systemFeign.queryLovHeaderByCode(lovLineQuery).getBody();
        return lovHeaderDTOBody.getData();
    }

    @Override
    public void saveOne(LovLineSaveDTO lovLineDTO) {
        ResponseEntity<Results<LovLineDTO>> resultsResponseEntity = systemFeign.lovLineSaveOne(lovLineDTO);
        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            throw new BizException("保存失败！");
        }
    }
}
