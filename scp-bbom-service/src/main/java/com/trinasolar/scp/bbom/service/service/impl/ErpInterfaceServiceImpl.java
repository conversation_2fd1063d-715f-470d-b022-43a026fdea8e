package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.ExternalInterfaceDTO;
import com.trinasolar.scp.bbom.service.service.ErpInterfaceService;
import com.trinasolar.scp.common.api.util.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2022/9/7
 */
@Slf4j
@Service("erpInterfaceService")
public class ErpInterfaceServiceImpl implements ErpInterfaceService {
    @Autowired
    @Qualifier("ipassRestTemplate")
    RestTemplate restTemplate;

    @Override
    public String postForString(ExternalInterfaceDTO dto) {
        String url = dto.getUrl() + dto.getPath();

        log.info("接口调用URL: {} , param: {}", url, dto.getRequestBody());
        HttpHeaders headers = new HttpHeaders();
        headers.set("tsl-clientid", dto.getClientId());
        headers.set("tsl-clientsecret", dto.getSecret());
        HttpEntity<String> httpEntity = new HttpEntity<>(dto.getRequestBody(), headers);
        String resultJson = null;
        try {
            try {
                resultJson = restTemplate.postForObject(url, httpEntity, String.class);
            } catch (Exception restException) {
                // 重试一次
                log.warn("调用失败,正在重试...  url: {} 请求参数: {} ", url, dto.getRequestBody());
                resultJson = restTemplate.postForObject(url, httpEntity, String.class);
            }
            JSONObject jsonObject = JSON.parseObject(resultJson);
            Integer bizcode = jsonObject.getInteger("bizcode");
            if (bizcode == 500) {
                throw new BizException(resultJson);
            }
        } catch (Exception e) {
            log.error("接口请求错误 url: {} 请求参数: {}  接口返回: {} 错误: {}", url, dto.getRequestBody(), resultJson, e.getMessage());
            throw e;
        }
//        JSONObject jsonObject = JSON.parseObject(resultJson);
//        JSONObject dataJsonObject = jsonObject.getJSONObject("data");
//        String xReturnCode = dataJsonObject.getString("x_return_code");
//        String xReturnMesg = dataJsonObject.getString("x_return_mesg");

        return resultJson;
    }

    @Override
    public String getForString(ExternalInterfaceDTO dto) {
        String url = dto.getUrl() + dto.getPath();

        log.info("接口调用URL: {} , param: {}", url, dto.getRequestBody());
        HttpHeaders headers = new HttpHeaders();
        headers.set("tsl-clientid", dto.getClientId());
        headers.set("tsl-clientsecret", dto.getSecret());
        HttpEntity<String> httpEntity = new HttpEntity<>(headers);

        String resultJson = null;
        try {
            ResponseEntity<String> resultJsonRes = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
            resultJson = resultJsonRes.getBody();
        } catch (Exception e) {
            log.error("接口请求错误 url: {}  接口返回: {} 错误: {}", url, resultJson, e.getMessage());
            throw e;
        }

        return resultJson;
    }
}
