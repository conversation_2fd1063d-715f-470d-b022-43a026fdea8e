package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.SiliconWaferLossDEConvert;
import com.trinasolar.scp.bbom.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.dto.SiliconWaferLossDTO;
import com.trinasolar.scp.bbom.domain.entity.QSiliconWaferLoss;
import com.trinasolar.scp.bbom.domain.entity.SiliconWaferLoss;
import com.trinasolar.scp.bbom.domain.excel.SiliconWaferLossExcelDTO;
import com.trinasolar.scp.bbom.domain.query.SiliconWaferLossQuery;
import com.trinasolar.scp.bbom.domain.save.SiliconWaferLossSaveDTO;
import com.trinasolar.scp.bbom.service.repository.SiliconWaferLossRepository;
import com.trinasolar.scp.bbom.service.service.BatteryFeignService;
import com.trinasolar.scp.bbom.service.service.BatteryTypeMainService;
import com.trinasolar.scp.bbom.service.service.SiliconWaferLossService;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * 硅片损耗率信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Slf4j
@Service("siliconWaferLossService")
@RequiredArgsConstructor
public class SiliconWaferLossServiceImpl implements SiliconWaferLossService {
    private static final QSiliconWaferLoss qSiliconWaferLoss = QSiliconWaferLoss.siliconWaferLoss;

    private final static String BASE_PLACE = "base_place";

    private final static String WORK_SHOP = "work_shop";

    private final SiliconWaferLossDEConvert convert;

    private final SiliconWaferLossRepository repository;

    private final BatteryTypeMainService batteryTypeMainService;

    private final BatteryFeignService batteryFeignService;

    /**
     * 新增修改校验
     *
     * @param saveDTO
     */
    private static void verifySave(SiliconWaferLossSaveDTO saveDTO) {
        // 新增修改校验
        if (StringUtils.isBlank(saveDTO.getBatteryType())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_workshop_notBlank");
        }

        // 公共部分
        if (StringUtils.isBlank(saveDTO.getSiliconWaferYield())) {
            throw new BizException("bbom_valid_batteryEfficiencyQty_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getSiliconIsolationRate())) {
            throw new BizException("bbom_valid_SiliconIsolationRate_notBlank");
        }
    }

    /**
     * 导入校验
     *
     * @param saveDTO
     */
    private static void verifyImport(SiliconWaferLossExcelDTO saveDTO) {
        // 导入校验
        if (StringUtils.isBlank(saveDTO.getBatteryTypeName())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlaceName())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshopName())) {
            throw new BizException("bbom_workshop_notBlank");
        }

        // 公共部分
        if (StringUtils.isBlank(saveDTO.getSiliconWaferYield())) {
            throw new BizException("bbom_valid_batteryEfficiencyQty_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getSiliconIsolationRate())) {
            throw new BizException("bbom_valid_SiliconIsolationRate_notBlank");
        }
    }

    private static StringBuilder getStringBuilder(SiliconWaferLossExcelDTO excelDTO, HashSet<String> hashSet) {
        StringBuilder builder = new StringBuilder();
        builder.append(excelDTO.getBatteryType()).append(excelDTO.getBasePlace())
                .append(excelDTO.getWorkshop());
        if (hashSet.contains(builder.toString())) {
            throw new BizException("bbom_valid_importData_repeat");
        }
        return builder;
    }

    private static void extractedSave(SiliconWaferLossSaveDTO saveDTO, BooleanBuilder booleanBuilder, String type) {
        if ("check".equals(type)) {
            if (null != saveDTO.getId()) {
                booleanBuilder.and(qSiliconWaferLoss.id.ne(saveDTO.getId()));
            }
            //重复校验逻辑电池类型+生产基地+生产车间为唯一性校验，不允许重复
            if (StringUtils.isNotEmpty(saveDTO.getBatteryType())) {
                booleanBuilder.and(qSiliconWaferLoss.batteryType.eq(saveDTO.getBatteryType()));
            }
            if (StringUtils.isNotEmpty(saveDTO.getBasePlace())) {
                booleanBuilder.and(qSiliconWaferLoss.basePlace.eq(saveDTO.getBasePlace()));
            }
            if (StringUtils.isNotEmpty(saveDTO.getWorkshop())) {
                booleanBuilder.and(qSiliconWaferLoss.workshop.eq(saveDTO.getWorkshop()));
            }
        } else {
            if (null != saveDTO.getId()) {
                booleanBuilder.and(qSiliconWaferLoss.id.eq(saveDTO.getId()));
            }
        }
    }

    private static void extractedImport(SiliconWaferLossExcelDTO excelDTO, BooleanBuilder booleanBuilder) {
        if (StringUtils.isNotEmpty(excelDTO.getBatteryType())) {
            booleanBuilder.and(qSiliconWaferLoss.batteryType.eq(excelDTO.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getBasePlace())) {
            booleanBuilder.and(qSiliconWaferLoss.basePlace.eq(excelDTO.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getWorkshop())) {
            booleanBuilder.and(qSiliconWaferLoss.workshop.eq(excelDTO.getWorkshop()));
        }
    }

    @Override
    public Page<SiliconWaferLossDTO> queryByPage(SiliconWaferLossQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<SiliconWaferLoss> page = repository.findAll(booleanBuilder, pageable);
        // 类型转码 id->name
        List<SiliconWaferLossDTO> siliconWaferLossDTOS = convert.toDto(page.getContent());
        for (SiliconWaferLossDTO dto : siliconWaferLossDTOS) {
            queryConvert(dto);
        }
        return new PageImpl(siliconWaferLossDTOS, page.getPageable(), page.getTotalElements());
    }

    private void queryConvert(SiliconWaferLossDTO excelDTO) {
        // 电池类型 id->name 转码用别名
        // 转换电池名称
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryCode(excelDTO.getBatteryType());
        if (StringUtils.isNotBlank(batteryTypeMainDTO.getBatteryName())) {
            excelDTO.setBatteryTypeName(batteryTypeMainDTO.getBatteryName());
        }
        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));
    }

    private void buildWhere(BooleanBuilder booleanBuilder, SiliconWaferLossQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qSiliconWaferLoss.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qSiliconWaferLoss.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qSiliconWaferLoss.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qSiliconWaferLoss.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferYield())) {
            booleanBuilder.and(qSiliconWaferLoss.siliconWaferYield.eq(query.getSiliconWaferYield()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconIsolationRate())) {
            booleanBuilder.and(qSiliconWaferLoss.siliconIsolationRate.eq(query.getSiliconIsolationRate()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qSiliconWaferLoss.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qSiliconWaferLoss.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }
    }

    @Override
    public SiliconWaferLossDTO queryById(Long id) {
        SiliconWaferLoss queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SiliconWaferLossDTO save(SiliconWaferLossSaveDTO saveDTO) {
        // 校验为空
        verifySave(saveDTO);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        SiliconWaferLoss newObj = new SiliconWaferLoss();

        checkRepeat(saveDTO, booleanBuilder);
        if (null != saveDTO.getId()) {
            booleanBuilder = new BooleanBuilder();
            // 查询 区分新增 修改结果集合
            extractedSave(saveDTO, booleanBuilder, "");
            newObj = repository.findOne(booleanBuilder).orElse(new SiliconWaferLoss());
        }
        convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    public void checkRepeat(SiliconWaferLossSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        extractedSave(saveDTO, booleanBuilder, "check");
        SiliconWaferLoss newObj = repository.findOne(booleanBuilder).orElse(new SiliconWaferLoss());
        if (null != newObj.getId()) {
            throw new BizException("bbom_valid_SiliconWaferLoss_repeat");
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(SiliconWaferLossQuery query, HttpServletResponse response) {
        List<SiliconWaferLossDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "电池良率维护", "电池良率维护", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @SneakyThrows
    public void queryByPageExport(SiliconWaferLossQuery query, HttpServletResponse response) {
        List<SiliconWaferLossDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<SiliconWaferLossExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "电池良率维护导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "电池良率维护").head(SiliconWaferLossExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    /**
     * 导入模版数据
     *
     * @param file
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importsEntity(MultipartFile file) {
        List<SiliconWaferLossExcelDTO> excelDto = new LinkedList<>();
        EasyExcel.read(file.getInputStream(), SiliconWaferLossExcelDTO.class, new ReadListener<SiliconWaferLossExcelDTO>() {
            @Override
            public void invoke(SiliconWaferLossExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        importDataSave(excelDto);
        log.info("硅片损耗率信息维护 数据导入成功");
    }

    private void importDataSave(List<SiliconWaferLossExcelDTO> excelDto) {
        HashSet<String> hashSet = new HashSet<>();
        for (SiliconWaferLossExcelDTO excelDTO : excelDto) {
            // 校验合法性和赋值
            checkImport(excelDTO, hashSet);
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            extractedImport(excelDTO, booleanBuilder);
            // 查询 区分新增 修改结果集合
            SiliconWaferLoss newObj = repository.findOne(booleanBuilder).orElse(new SiliconWaferLoss());
            if (newObj.getId() != null) {
                excelDTO.setId(newObj.getId());
            }
            BigDecimal siliconWaferYield = new BigDecimal(excelDTO.getSiliconWaferYield());
            BigDecimal siliconIsolationRate = new BigDecimal(excelDTO.getSiliconIsolationRate());
            if (siliconWaferYield.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("bbom_valid_siliconWaferYield_gtZero");
            }
            if (siliconIsolationRate.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("bbom_valid_siliconIsolationRate_gtZero");
            }
            excelDTO.setSiliconWaferYield(siliconWaferYield.setScale(2, RoundingMode.HALF_UP).toString());
            excelDTO.setSiliconIsolationRate(siliconIsolationRate.setScale(2, RoundingMode.HALF_UP).toString());
            SiliconWaferLossSaveDTO saveDTO = convert.toSaveDTO(excelDTO);
            //save(saveDTO);
            convert.saveDTOtoEntity(saveDTO, newObj);
            repository.save(newObj);
        }
    }

    private void checkImport(SiliconWaferLossExcelDTO excelDTO, HashSet<String> hashSet) {
        verifyImport(excelDTO);
        // 校验合法性 生产基地 生产车间 api接口
        List<ModuleBasePlaceDTO> moduleBasePlaceDTOList = batteryFeignService.
                findBasePlaceWorkshopWorkUnit(excelDTO.getBasePlaceName(), excelDTO.getWorkshopName(), null);
        if (CollectionUtils.isEmpty(moduleBasePlaceDTOList)) {
            throw new BizException("bbom_valid_basePlaceWorkshop_illegal", excelDTO.getBasePlaceName(), excelDTO.getWorkshopName());
        }
        // 获取基地
        excelDTO.setBasePlace(excelDTO.getBasePlaceName());
        // 获取车间
        excelDTO.setWorkshop(excelDTO.getWorkshopName());

        // 补齐电池类型编码
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryName(excelDTO.getBatteryTypeName());
        Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
            excelDTO.setBatteryType(w.getBatteryCode());
        });
        if (StringUtils.isBlank(excelDTO.getBatteryType())) {
            throw new BizException("bbom_valid_batteryCode_illegal", excelDTO.getBatteryType());
        }
        // 校验模版数据是否重复记录
        StringBuilder builder = getStringBuilder(excelDTO, hashSet);
        hashSet.add(builder.toString());
    }
}
