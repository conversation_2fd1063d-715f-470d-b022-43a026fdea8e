package com.trinasolar.scp.bbom.service.util;


import java.time.LocalDate;

public class DateOverlapChecker {

    /**
     * 校验两组日期是否有重叠, 日期相等则也认为有重叠
     *
     * @param start1 第一组: 开始日期
     * @param end1   第一组: 结束日期
     * @param start2 第二组: 开始日期
     * @param end2   第二组: 结束日期
     * @return 是否重叠  重叠 true   不重叠 false
     */
    public static boolean isOverlapping(LocalDate start1, LocalDate end1, LocalDate start2, LocalDate end2) {
        if (start1 == null) {
            start1 = LocalDate.MIN;
        }
        if (start2 == null) {
            start2 = LocalDate.MIN;
        }
        if (end1 == null) {
            end1 = LocalDate.MAX;
        }
        if (end2 == null) {
            end2 = LocalDate.MAX;
        }
        return !start1.isAfter(end2) && !end1.isBefore(start2);
    }

    /**
     * 校验两组日期是否有重叠
     *
     * @param vaildDate 有效日期
     * @param start1 第一组: 开始日期
     * @param end1   第一组: 结束日期
     * @return 是否重叠  重叠 true  不重叠 false
     * @return
     */
    public static boolean isOverlapping(LocalDate vaildDate, LocalDate start1, LocalDate end1) {
        return isOverlapping(start1, end1, vaildDate, vaildDate);
    }
}