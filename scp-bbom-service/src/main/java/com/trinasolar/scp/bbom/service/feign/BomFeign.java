package com.trinasolar.scp.bbom.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpOperatRouteDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalAttrMapViewDTO;
import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.bbom.domain.query.DateTimeQuery;
import com.trinasolar.scp.bbom.domain.query.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.bbom.domain.query.ErpOperatRouteQuery;
import com.trinasolar.scp.bbom.domain.query.ItemCodesQuery;
import com.trinasolar.scp.bbom.domain.save.ItemsSaveDTO;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * DP服务feign调用
 *
 * @author: darke
 * @create: 2022年6月28日09:12:08
 */
@FeignClient(path = FeignConstant.SCP_BOM_API, value = FeignConstant.SCP_BOM_API, fallbackFactory = BomFeignFallbackFactory.class/*, url = "https://scpapitest.trinasolar.com"*/)
public interface BomFeign {

    /**
     * 获取网版料号信息
     *
     * @param query
     * @return
     */
    @PostMapping("/items/findItemDescByItemCodes")
    ResponseEntity<Results<Map<String, String>>> findItemDescByItemCodes(@RequestBody ItemCodesQuery query);

    /**
     * ERP替代项表分页列表
     *
     * @param query
     * @return
     */
    @PostMapping("/erp-alternate-designator/page")
    @ApiOperation(value = "ERP替代项表分页列表", notes = "获得ERP替代项表分页列表")
    ResponseEntity<Results<Page<ErpAlternateDesignatorDTO>>> queryByPage(@RequestBody ErpAlternateDesignatorQuery query);

    @PostMapping("/erp-alternate-designator/page")
    @ApiOperation(value = "ERP替代项表分页列表", notes = "获得ERP替代项表分页列表")
    JSONObject queryByPage2(@RequestBody ErpAlternateDesignatorQuery query);


    @PostMapping("/erp-alternate-designator/queryByOrganizationIdAndDescription")
    @ApiOperation(value = "通过OrgId和Desc查找一个")
    public ResponseEntity<Results<ErpAlternateDesignatorDTO>> queryByOrganizationIdAndDescription(@RequestBody ErpAlternateDesignatorQuery query);

    @PostMapping("/erp-alternate-designator/queryByOrganizationIds")
    @ApiOperation(value = "通过OrgId和Desc查找一个")
    public ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> queryByOrganizationIds(@RequestBody ErpAlternateDesignatorQuery query);

    @PostMapping("/external-attr-map-view/findBySrcCategorySegment4")
    @ApiOperation(value = "通过主键查询单条数据")
    ResponseEntity<Results<List<ExternalAttrMapViewDTO>>> findBySrcCategorySegment4(@RequestBody IdDTO idDTO);

    @PostMapping("/erp-alternate-designator/queryByOrgID")
    @ApiOperation(value = "根据库存组织筛选-分页获取ERP替代项表分页列表")
    ResponseEntity<Results<ErpAlternateDesignatorDTO>> queryByOrgID(@RequestBody ErpAlternateDesignatorQuery query);

    @PostMapping("/erp-alternate-designator/queryList")
    @ApiOperation(value = "根据库存组织筛选-获取ERP替代项表列表")
    ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> queryList(@RequestBody ErpAlternateDesignatorQuery query);

    @PostMapping("/erp-operat-route/findBy5AAndAlternateRoutingDesignator")
    @ApiOperation(value = "5A料号+对应的替代项", notes = "5A料号+对应的替代项")
    ResponseEntity<Results<ErpOperatRouteDTO>> findBy5AAndAlternateRoutingDesignator(@RequestBody ErpOperatRouteQuery query);

    /**
     * 料号匹配使用
     * 查询是否存在工艺路线
     *
     * @param
     * @return
     */
    @PostMapping("/erp-operat-route/list")
    @ApiOperation(value = "根据库存组织、bom替代项、5A料号id查询")
    ResponseEntity<Results<List<ErpOperatRouteDTO>>> list(@RequestBody ErpOperatRouteQuery query);

    /**
     * 料号匹配使用
     * 查询是否存在工艺路线
     *
     * @param
     * @return
     */
    @PostMapping("/external-items/dailySyncInterface")
    @ApiOperation(value = "[每日定时拉取]定时拉取某一个组织Id")
    ResponseEntity<Results<Object>> dailySyncInterface(@RequestBody DateTimeQuery query);

    /**
     * 料号匹配使用
     * 查询是否存在工艺路线
     *
     * @param
     * @return
     */
    @PostMapping("/external-items/syncBbom")
    @ApiOperation(value = "把ExternalItem的数据同步到BBOM中")
    ResponseEntity<Results<Object>> syncBbom();

    @PostMapping("/erp-alternate-designator/queryByAlternateDesignatorCodes")
    @ApiOperation(value = "[每日定时拉取]定时拉取某一个组织Id")
    ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> queryByAlternateDesignatorCodes(@RequestBody List<String> slternateDesignatorCodes);

    @PostMapping("/items/syncLifecycleBatchSave")
    @ApiOperation(value = "更新同步一期组件生命周期状态")
    ResponseEntity<Results<Object>> syncLifecycleBatchSave(@RequestBody List<ItemsSaveDTO> saveDTOS);
}
