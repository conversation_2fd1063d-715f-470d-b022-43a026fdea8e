package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.LowEfficiencyCellPercentDEConvert;
import com.trinasolar.scp.bbom.domain.dto.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.bbom.domain.entity.LowEfficiencyCellPercent;
import com.trinasolar.scp.bbom.domain.excel.LowEfficiencyCellPercentExportExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.LowEfficiencyCellPercentImportExcelDTO;
import com.trinasolar.scp.bbom.domain.query.LowEfficiencyCellPercentQuery;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.domain.validation.group.AddGroup;
import com.trinasolar.scp.bbom.domain.validation.group.DefaultGroup;
import com.trinasolar.scp.bbom.service.repository.LowEfficiencyCellPercentRepository;
import com.trinasolar.scp.bbom.service.service.LowEfficiencyCellPercentService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.LovUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.trinasolar.scp.bbom.domain.entity.QLowEfficiencyCellPercent.lowEfficiencyCellPercent;

@Service
public class LowEfficiencyCellPercentServiceImpl implements LowEfficiencyCellPercentService {

    @Resource
    private LowEfficiencyCellPercentRepository repository;

    @Resource
    private LowEfficiencyCellPercentDEConvert convert;

    @Resource(name = "defaultValidator")
    private Validator validator;

    private final static Joiner JOINER = Joiner.on("_").useForNull("null");

    @Override
    public Page<LowEfficiencyCellPercentDTO> queryByPage(LowEfficiencyCellPercentQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<LowEfficiencyCellPercent> page = repository.findAll(booleanBuilder, pageable);
        List<LowEfficiencyCellPercentDTO> dtoList = convert.toDto(page.getContent());
        convertLovValue2Name(dtoList);
        return new PageImpl<>(dtoList, page.getPageable(), page.getTotalElements());
    }

    @Transactional
    @Override
    public void importFromExcel(MultipartFile file) {
        //读取excel
        List<LowEfficiencyCellPercentImportExcelDTO> excelDTOList = ExcelUtils.read(file, LowEfficiencyCellPercentImportExcelDTO.class);
        if (CollectionUtils.isEmpty(excelDTOList)) {
            throw new BizException("请导入数据");
        }
        //转DTO
        List<LowEfficiencyCellPercentDTO> lowEfficiencyCellPercentDTOListFromExcel = convert.excelToDTO(excelDTOList);

        lowEfficiencyCellPercentDTOListFromExcel.forEach(this::initPercentIfBlank);
        //必填校验
        notBlankValidate(lowEfficiencyCellPercentDTOListFromExcel, lineErrorMessage -> {
            if (!lineErrorMessage.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                lineErrorMessage.forEach((i, value) -> sb.append(String.format("必填检验失败:第【%d】行%s", i + 2, value)));
                throw new BizException(sb.toString());
            }
        }, AddGroup.class, DefaultGroup.class);
        //lovName校验
        lovNameValidate(lowEfficiencyCellPercentDTOListFromExcel, lineErrorMessage -> {
            if (!lineErrorMessage.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                lineErrorMessage.forEach((i, value) -> sb.append(String.format("LOV校验失败:第【%d】行%s", i + 2, value)));
                throw new BizException(sb.toString());
            }
        });
        //excel中是否有同纬度数据：国内/海外+生产基地+生产车间+电池型号+年份
        uniqueValidate(lowEfficiencyCellPercentDTOListFromExcel, lineErrorMessage -> {
            if (!lineErrorMessage.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                lineErrorMessage.forEach((i, value) -> sb.append(String.format("唯一性校验失败:第【%d】行%s", i + 2, value)));
                throw new BizException(sb.toString());
            }
        });
        if (CollectionUtils.isNotEmpty(lowEfficiencyCellPercentDTOListFromExcel)) {//新增
            //同年份的先删除
            Set<String> year = lowEfficiencyCellPercentDTOListFromExcel.stream().map(LowEfficiencyCellPercentDTO::getYear).filter(Objects::nonNull).collect(Collectors.toSet());
            repository.deleteByYears(year);
            repository.saveAll(convert.toEntity(lowEfficiencyCellPercentDTOListFromExcel));
        }
    }

    private void initPercentIfBlank(LowEfficiencyCellPercentDTO dto) {
        String defaultPercent = "0%";
        if (StringUtils.isBlank(dto.getM1Percent())) {
            dto.setM1Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM2Percent())) {
            dto.setM2Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM3Percent())) {
            dto.setM3Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM4Percent())) {
            dto.setM4Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM5Percent())) {
            dto.setM5Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM6Percent())) {
            dto.setM6Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM7Percent())) {
            dto.setM7Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM8Percent())) {
            dto.setM8Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM9Percent())) {
            dto.setM9Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM10Percent())) {
            dto.setM10Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM11Percent())) {
            dto.setM11Percent(defaultPercent);
        }
        if (StringUtils.isBlank(dto.getM12Percent())) {
            dto.setM12Percent(defaultPercent);
        }
    }


    private void notBlankValidate(List<LowEfficiencyCellPercentDTO> validateList, Consumer<Map<Integer, String>> messageProcessor, Class<?>... groups) {
        Map<Integer, String> lineErrorMessage = new HashMap<>();
        for (int i = 0; i < validateList.size(); i++) {
            StringBuilder columnErrorMessage = new StringBuilder();
            Set<ConstraintViolation<LowEfficiencyCellPercentDTO>> validate = validator.validate(validateList.get(i), groups);
            if (!validate.isEmpty()) {
                columnErrorMessage.append(validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(",")));
            }
            if (columnErrorMessage.length() > 0) {
                lineErrorMessage.put(i, columnErrorMessage.toString());
                break;
            }
        }
        if (messageProcessor != null) {
            messageProcessor.accept(lineErrorMessage);
        }
    }

    private void uniqueValidate(List<LowEfficiencyCellPercentDTO> dtoList, Consumer<Map<Integer, String>> messageProcessor) {
        Map<Integer, String> lineErrorMessage = new HashMap<>();
        Set<String> uniqueKeySet = Sets.newHashSet();
        for (int i = 0; i < dtoList.size(); i++) {
            LowEfficiencyCellPercentDTO dto = dtoList.get(i);
            //国内/海外+生产基地+生产车间+电池型号+年份
            String uniqueKey = JOINER.join(dto.getCountryFlagName(), dto.getBasePlaceName(), dto.getWorkshopName(), dto.getCellTypeName(), dto.getYear());
            if (uniqueKeySet.contains(uniqueKey)) {
                lineErrorMessage.put(i, String.format("维度【%s】", uniqueKey));
                break;
            } else {
                uniqueKeySet.add(uniqueKey);
            }
        }
        if (messageProcessor != null) {
            messageProcessor.accept(lineErrorMessage);
        }
    }

    private void lovNameValidate(List<LowEfficiencyCellPercentDTO> dtoList, Consumer<Map<Integer, String>> messageProcessor) {
        Map<Integer, String> lineErrorMessage = new HashMap<>();
        for (int i = 0; i < dtoList.size(); i++) {
            StringBuilder columnErrorMessage = new StringBuilder();
            LowEfficiencyCellPercentDTO dto = dtoList.get(i);
            if (StringUtils.isNotBlank(dto.getCellTypeName())) {
                LovLineDTO cellTypeLovLine = LovUtils.getByName(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellTypeName());
                if (cellTypeLovLine == null) {
                    columnErrorMessage.append(String.format("电池类型【%s】", dto.getCellTypeName())).append(",");
                } else {
                    dto.setCellType(cellTypeLovLine.getLovValue());
                }
            }
            if (StringUtils.isNotBlank(dto.getBasePlaceName())) {
                LovLineDTO basePlaceLovLine = LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlaceName());
                if (basePlaceLovLine == null) {
                    columnErrorMessage.append(String.format("生产基地【%s】", dto.getBasePlaceName())).append(",");
                } else {
                    dto.setBasePlace(basePlaceLovLine.getLovValue());
                }
            }
            if (StringUtils.isNotBlank(dto.getCountryFlagName())) {
                LovLineDTO countryFlagLovLine = LovUtils.getByName(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, dto.getCountryFlagName());
                if (countryFlagLovLine == null) {
                    columnErrorMessage.append(String.format("国内/海外【%s】", dto.getCountryFlagName())).append(",");
                } else {
                    dto.setCountryFlag(countryFlagLovLine.getLovValue());
                }
            }
            if (StringUtils.isNotBlank(dto.getWorkshopName())) {
                LovLineDTO workshopLovLine = LovUtils.getByName(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshopName());
                if (workshopLovLine == null) {
                    columnErrorMessage.append(String.format("生产车间【%s】", dto.getWorkshopName())).append(",");
                } else {
                    dto.setWorkshop(workshopLovLine.getLovValue());
                }
            }
            if (columnErrorMessage.length() > 0) {
                columnErrorMessage.setLength(columnErrorMessage.length() - 1);
                lineErrorMessage.put(i, columnErrorMessage.toString());
                break;
            }
        }
        if (messageProcessor != null) {
            messageProcessor.accept(lineErrorMessage);
        }
    }

    private void lovValueValidate(List<LowEfficiencyCellPercentDTO> dtoList, Consumer<Map<Integer, String>> messageProcessor) {
        Map<Integer, String> lineErrorMessage = Maps.newHashMap();
        for (int i = 0; i < dtoList.size(); i++) {
            StringBuilder columnErrorMessage = new StringBuilder();
            LowEfficiencyCellPercentDTO dto = dtoList.get(i);
            if (StringUtils.isNotBlank(dto.getCellType())) {
                LovLineDTO cellTypeLovLine = LovUtils.get(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellType());
                if (cellTypeLovLine == null) {
                    columnErrorMessage.append(String.format("电池类型【%s】", dto.getCellType())).append(",");
                }
            }
            if (StringUtils.isNotBlank(dto.getBasePlace())) {
                LovLineDTO basePlaceLovLine = LovUtils.get(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace());
                if (basePlaceLovLine == null) {
                    columnErrorMessage.append(String.format("生产基地【%s】", dto.getBasePlace())).append(",");
                }
            }
            if (StringUtils.isNotBlank(dto.getCountryFlag())) {
                LovLineDTO countryFlagLovLine = LovUtils.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, dto.getCountryFlag());
                if (countryFlagLovLine == null) {
                    columnErrorMessage.append(String.format("国内/海外【%s】", dto.getCountryFlag())).append(",");
                }
            }
            if (StringUtils.isNotBlank(dto.getWorkshop())) {
                LovLineDTO workshopLovLine = LovUtils.get(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop());
                if (workshopLovLine == null) {
                    columnErrorMessage.append(String.format("生产车间【%s】", dto.getWorkshop())).append(",");
                }
            }
            if (columnErrorMessage.length() > 0) {
                columnErrorMessage.setLength(columnErrorMessage.length() - 1);
                lineErrorMessage.put(i, columnErrorMessage.toString());
                break;
            }
        }
        if (messageProcessor != null) {
            messageProcessor.accept(lineErrorMessage);
        }
    }

    @Transactional
    @Override
    public void batchDelete(List<Long> ids) {
        ids.forEach(id -> repository.deleteById(id));
    }

    @Transactional
    @Override
    public void update(LowEfficiencyCellPercentDTO dto) {
        repository.findById(dto.getId()).orElseThrow(() -> new BizException("数据不存在，id：" + dto.getId()));
        //lovValue校验
        lovValueValidate(Collections.singletonList(dto), lineErrorMessage -> {
            if (!lineErrorMessage.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                lineErrorMessage.forEach((i, value) -> sb.append(String.format("LOV校验失败:%s", value)));
                throw new BizException(sb.toString());
            }
        });
        //国内/海外+生产基地+生产车间+电池型号+年份唯一性校验(都是必填项可以用等于)
        long count = repository.count(
                new BooleanBuilder()
                        .and(lowEfficiencyCellPercent.countryFlag.eq(dto.getCountryFlag()))
                        .and(lowEfficiencyCellPercent.basePlace.eq(dto.getBasePlace())
                                .and(lowEfficiencyCellPercent.workshop.eq(dto.getWorkshop()))
                                .and(lowEfficiencyCellPercent.cellType.eq(dto.getCellType()))
                                .and(lowEfficiencyCellPercent.year.eq(dto.getYear()))));
        if (count > 1) {
            throw new BizException("已存在相同维度的数据");
        }
        repository.save(convert.toEntity(dto));
    }

    @Override
    public void exportToExcel(LowEfficiencyCellPercentQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(10 * 10000);
        Page<LowEfficiencyCellPercentDTO> page = queryByPage(query);
        List<LowEfficiencyCellPercentDTO> content = page.getContent();
        convertLovValue2Name(content);
        String fileName = "自产低效电池比例配置导出";
        List<LowEfficiencyCellPercentExportExcelDTO> excelDTOList = convert.dtoToExcel(content);
        ExcelUtils.export(response, fileName, fileName, excelDTOList, LowEfficiencyCellPercentExportExcelDTO.class);
    }

    private void convertLovValue2Name(List<LowEfficiencyCellPercentDTO> dtoList) {
        dtoList.forEach(dto -> {
            LovLineDTO basePlaceLovLine = LovUtils.get(LovHeaderCodeConstant.BASE_PLACE, dto.getBasePlace());
            LovLineDTO cellTypeLovLine = LovUtils.get(LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellType());
            LovLineDTO countryFlagLovLine = LovUtils.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, dto.getCountryFlag());
            LovLineDTO workshopLovLine = LovUtils.get(LovHeaderCodeConstant.WORK_SHOP, dto.getWorkshop());
            if (basePlaceLovLine != null) {
                dto.setBasePlaceName(basePlaceLovLine.getLovName());
            }
            if (cellTypeLovLine != null) {
                dto.setCellTypeName(cellTypeLovLine.getLovName());
            }
            if (countryFlagLovLine != null) {
                dto.setCountryFlagName(countryFlagLovLine.getLovName());
            }
            if (workshopLovLine != null) {
                dto.setWorkshopName(workshopLovLine.getLovName());
            }
        });

    }

    @Override
    public List<LowEfficiencyCellPercentDTO> list(LowEfficiencyCellPercentQuery query) {
        BooleanBuilder builder = buildWhere(query);
        List<LowEfficiencyCellPercent> lowEfficiencyCellPercents = IterableUtils.toList(repository.findAll(builder));
        return convert.toDto(lowEfficiencyCellPercents);
    }

    private BooleanBuilder buildWhere(LowEfficiencyCellPercentQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotEmpty(query.getYear())) {
            booleanBuilder.and(lowEfficiencyCellPercent.year.eq(query.getYear()));
        }
        if (StringUtils.isNotEmpty(query.getCountryFlag())) {
            booleanBuilder.and(lowEfficiencyCellPercent.countryFlag.eq(query.getCountryFlag()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(lowEfficiencyCellPercent.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getCellType())) {
            booleanBuilder.and(lowEfficiencyCellPercent.cellType.eq(query.getCellType()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(lowEfficiencyCellPercent.basePlace.eq(query.getBasePlace()));
        }
        return booleanBuilder;
    }
}
