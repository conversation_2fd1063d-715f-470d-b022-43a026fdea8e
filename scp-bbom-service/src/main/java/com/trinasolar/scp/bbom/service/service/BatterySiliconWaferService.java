package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatterySiliconWaferDTO;
import com.trinasolar.scp.bbom.domain.dto.BatteryWaferPropertyDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.query.BatterySiliconWaferQuery;
import com.trinasolar.scp.bbom.domain.save.BatterySiliconWaferSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电池类型动态属性-硅片 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
public interface BatterySiliconWaferService {
    /**
     * 分页获取电池类型动态属性-硅片
     *
     * @param query 查询对象
     * @return 电池类型动态属性-硅片分页对象
     */
    Page<BatterySiliconWaferDTO> queryByPage(BatterySiliconWaferQuery query);

    /**
     * 根据编码和电池类型查询硅片信息
     *
     * @param query
     * @return
     */
    List<BatterySiliconWaferDTO> queryByBatteryCode(BatterySiliconWaferQuery query);

    /**
     * 查询行列硅片属性编码下拉接口
     *
     * @return
     */
    List<BatteryWaferPropertyDTO> queryWaferPropertyList();

    /**
     * 查询行列电池属性编码下拉接口
     *
     * @return
     */
    List<BatteryWaferPropertyDTO> queryBatteryPropertyList();

    /**
     * 根据行列硅片属性编码 查询值下拉接口
     *
     * @param id
     * @return
     */
    List<BatteryWaferPropertyDTO> queryWaferPropertyValueList(String id);

    /**
     * 根据主键获取电池类型动态属性-硅片详情
     *
     * @param id 主键
     * @return 电池类型动态属性-硅片详情
     */
    BatterySiliconWaferDTO queryById(Long id);

    /**
     * 保存或更新电池类型动态属性-硅片
     *
     * @param saveDTO 电池类型动态属性-硅片保存对象
     * @return 电池类型动态属性-硅片对象
     */
    BatterySiliconWaferDTO save(BatterySiliconWaferSaveDTO saveDTO);

    /**
     * 保存或更新电池类型动态属性-硅片
     *
     * @param saveDTO 电池类型动态属性-硅片保存对象
     * @return 电池类型动态属性-硅片对象
     */
    BatterySiliconWaferDTO saveOrUpdate(BatterySiliconWaferSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型动态属性-硅片
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatterySiliconWaferQuery query, HttpServletResponse response);

    List<BatterySiliconWaferDTO> listByMatchItem(String batteryType, String basePlace, String workshop, String workunit, LocalDateTime verifyDate);

    List<BatterySiliconWaferDTO> listByMatchItem(MaterielMatchHeaderDTO headerDTO);

    List<BatterySiliconWaferDTO> getAllByCache();

    void importsEntity(MultipartFile file);
}

