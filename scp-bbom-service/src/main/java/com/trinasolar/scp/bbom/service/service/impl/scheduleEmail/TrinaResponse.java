package com.trinasolar.scp.bbom.service.service.impl.scheduleEmail;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class TrinaResponse<T> implements Serializable {
    @ApiModelProperty(
            value = "返回状态码，10000代表成功",
            example = "10000"
    )
    private int bizcode;
    @ApiModelProperty(
            value = "返回消息，当状态码为非10000，该字段为异常信息",
            example = ""
    )
    private String bizmsg;
    @ApiModelProperty(
            value = "返回的数据",
            example = ""
    )
    private T data;
    private String sign;
    private long timestamp;
    private String postToken;

    public TrinaResponse(String bizmsg, T data, int bizcode) {
        this(bizcode, bizmsg, data, (String) null);
    }

    public TrinaResponse(int bizcode, String bizmsg, T data, String postToken) {
        this.bizcode = CommonErrorCode.SUCCESS.getStatus();
        this.bizmsg = "操作成功";
        this.timestamp = System.currentTimeMillis();
        this.bizcode = bizcode;
        this.bizmsg = bizmsg;
        this.data = data;
        this.postToken = postToken;
        this.timestamp = System.currentTimeMillis();
    }

    public TrinaResponse(T data) {
        this("", data, CommonErrorCode.SUCCESS.getStatus());
    }

    public TrinaResponse() {
        this("", null, CommonErrorCode.SUCCESS.getStatus());
    }

    public static TrinaResponse ok() {
        return new TrinaResponse();
    }

    public static <T> TrinaResponse ok(T data) {
        return new TrinaResponse(data);
    }

    public static <T> TrinaResponse<T> ok(String msg, T data) {
        return new TrinaResponse(msg, data, CommonErrorCode.SUCCESS.getStatus());
    }

    public static TrinaResponse error() {
        return new TrinaResponse("操作失败", (Object) null, CommonErrorCode.ERROR.getStatus());
    }

    public static TrinaResponse error(String msg) {
        return new TrinaResponse(msg, (Object) null, CommonErrorCode.ERROR.getStatus());
    }

    public static TrinaResponse error(String msg, int bizcode) {
        return new TrinaResponse(msg, (Object) null, bizcode);
    }

    public int getBizcode() {
        return this.bizcode;
    }

    public void setBizcode(int bizcode) {
        this.bizcode = bizcode;
    }

    public String getBizmsg() {
        return this.bizmsg;
    }

    public void setBizmsg(String bizmsg) {
        this.bizmsg = bizmsg;
    }

    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getSign() {
        return this.sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public long getTimestamp() {
        return this.timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getPostToken() {
        return this.postToken;
    }

    public void setPostToken(String postToken) {
        this.postToken = postToken;
    }
}
