package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.BatterySlurry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池类型动态属性-浆料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Repository
public interface BatterySlurryRepository extends JpaRepository<BatterySlurry, Long>, QuerydslPredicateExecutor<BatterySlurry> {
}
