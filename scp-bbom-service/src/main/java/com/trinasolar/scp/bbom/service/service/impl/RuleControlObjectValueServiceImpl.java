package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectValue;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectValueQuery;
import com.trinasolar.scp.bbom.domain.save.RuleControlObjectValueSaveDTO;
import com.trinasolar.scp.bbom.service.repository.RuleControlObjectValueRepository;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectValueService;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
@Slf4j
@Service("ruleControlObjectValueService")
@RequiredArgsConstructor
public class RuleControlObjectValueServiceImpl implements RuleControlObjectValueService {

    private final RuleControlObjectValueRepository repository;

    private final LovUtil lovUtil;

    @Override
    public Page<RuleControlObjectValue> queryByPage(RuleControlObjectValueQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(Optional.ofNullable(query).map(PageDTO::getPageNumber).orElse(1) - 1, query.getPageSize(), sort);

        return repository.findAll(booleanBuilder, pageable);
    }

    @Override
    public RuleControlObjectValueDTO queryById(Long id) {
        RuleControlObjectValue queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        RuleControlObjectValueDTO result = new RuleControlObjectValueDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Override
    public RuleControlObjectValueDTO save(RuleControlObjectValueSaveDTO saveDTO) {
        RuleControlObjectValue newObj;
        if (saveDTO.getId() != null) {
            newObj = repository.findById(saveDTO.getId()).orElse(new RuleControlObjectValue());
        } else {
            newObj = new RuleControlObjectValue();
        }

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    public void saveValues(Long detailId, List<RuleControlObjectValueSaveDTO> values) {
        verifyAndDelete(detailId, values);
        if (values == null || values.isEmpty()) {
            return;
        }
        for (RuleControlObjectValueSaveDTO value : values) {
            value.setRuleControlObjectDetailId(detailId);
            save(value);
        }
    }

    @Override
    public List<RuleControlObjectValueDTO> listByRuleControlObjectDetailId(Long id) {
        List<RuleControlObjectValue> ruleDpValues = repository.listByRuleControlObjectDetailId(id);
        return ruleDpValues.stream().map(value -> {
            RuleControlObjectValueDTO ruleDpValueDTO = new RuleControlObjectValueDTO();
            BeanUtils.copyProperties(value, ruleDpValueDTO);
            lovUtil.setLovLineName(ruleDpValueDTO, ruleDpValueDTO.getAttrValueId(), "attrValueName", ruleDpValueDTO.getAttrValue());
            lovUtil.setLovLineName(ruleDpValueDTO, ruleDpValueDTO.getAttrValueToId(), "attrValueToName", ruleDpValueDTO.getAttrValueToName());
            return ruleDpValueDTO;
        }).collect(Collectors.toList());
    }

    private void verifyAndDelete(Long id, List<RuleControlObjectValueSaveDTO> lines) {
        // 1. 先找出以前存在但现在没有的
        List<RuleControlObjectValue> savedConfigs = repository.listByRuleControlObjectDetailId(id);

        if (lines == null || lines.isEmpty()) {
            if (!savedConfigs.isEmpty()) {
                // 删除所有然后返回
                repository.deleteAll(savedConfigs);
            }
            return;
        }
        List<Long> currentIds = lines.stream().filter(item -> item.getId() != null).map(RuleControlObjectValueSaveDTO::getId)
                .collect(Collectors.toList());
        List<RuleControlObjectValue> deleteConfigs = savedConfigs.stream().filter(item -> !currentIds.contains(item.getId()))
                .collect(Collectors.toList());

        if (!deleteConfigs.isEmpty()) {
            repository.deleteAll(deleteConfigs);
        }
    }
}
