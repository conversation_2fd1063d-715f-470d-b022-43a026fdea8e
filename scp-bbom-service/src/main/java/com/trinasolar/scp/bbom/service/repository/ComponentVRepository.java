package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.ComponentV;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 同步cux3_bbom_component_v
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Repository
public interface ComponentVRepository extends JpaRepository<ComponentV, Long>, QuerydslPredicateExecutor<ComponentV> {
    @Query(value = "SELECT * FROM bbom_component_v AS tb WHERE tb.organization_id = ?1 AND tb.last_update_date IS NOT NULL " +
            "AND tb.is_deleted=0 ORDER BY  tb.last_update_date DESC LIMIT 1", nativeQuery = true)
    ComponentV findByLastRecordByOrganizationid(Long organization);
}
