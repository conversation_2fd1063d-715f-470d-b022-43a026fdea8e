package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLineMatchStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Repository
public interface MaterielMatchLineMatchStatusRepository extends JpaRepository<MaterielMatchLineMatchStatus, Long>, QuerydslPredicateExecutor<MaterielMatchLineMatchStatus> {
    @Query("select m from MaterielMatchLineMatchStatus m where m.lineId in ?1")
    List<MaterielMatchLineMatchStatus> findByLineId(List<Long> lineIds);

    @Query("select m from MaterielMatchLineMatchStatus m where m.lineId = ?1")
    List<MaterielMatchLineMatchStatus> findByLineId(Long lineId);

    @Query("select m from MaterielMatchLineMatchStatus m where m.lineId in ?1")
    List<MaterielMatchLineMatchStatus> selectByLineId(List<Long> lineId);
}
