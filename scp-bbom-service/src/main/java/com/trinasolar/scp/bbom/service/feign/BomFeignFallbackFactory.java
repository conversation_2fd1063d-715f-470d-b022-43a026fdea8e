package com.trinasolar.scp.bbom.service.feign;


import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpOperatRouteDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalAttrMapViewDTO;
import com.trinasolar.scp.bbom.domain.query.DateTimeQuery;
import com.trinasolar.scp.bbom.domain.query.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.bbom.domain.query.ErpOperatRouteQuery;
import com.trinasolar.scp.bbom.domain.query.ItemCodesQuery;
import com.trinasolar.scp.bbom.domain.save.ItemsSaveDTO;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * scp-bom-api服务降级处理
 *
 * <AUTHOR>
 * @date 2022年4月26日12:01:48
 */
@Component
@Slf4j
public class BomFeignFallbackFactory implements FallbackFactory<BomFeign> {
    @Override
    public BomFeign create(Throwable cause) {
        return new BomFeign() {
            @Override
            public ResponseEntity<Results<Map<String, String>>> findItemDescByItemCodes(ItemCodesQuery query) {
                log.warn("【BomFeign-findItemDescByItemCodes】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Page<ErpAlternateDesignatorDTO>>> queryByPage(ErpAlternateDesignatorQuery query) {
                log.warn("【BomFeign-queryByPage】发生异常：{}", cause);
                return Results.createFailRes();
            }

            public JSONObject queryByPage2(ErpAlternateDesignatorQuery query) {
                log.warn("【BomFeign-queryByPage】发生异常：{}", cause);
                return null;
            }

            @Override
            public ResponseEntity<Results<ErpAlternateDesignatorDTO>> queryByOrganizationIdAndDescription(ErpAlternateDesignatorQuery query) {
                log.warn("【BomFeign-queryByOrganizationIdAndDescription】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> queryByOrganizationIds(ErpAlternateDesignatorQuery query) {
                log.warn("【BomFeign-queryByOrganizationIds】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ExternalAttrMapViewDTO>>> findBySrcCategorySegment4(IdDTO idDTO) {
                log.warn("【BomFeign-findBySrcCategorySegment4】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<ErpAlternateDesignatorDTO>> queryByOrgID(ErpAlternateDesignatorQuery query) {
                log.warn("【BomFeign-queryByOrgID】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> queryList(ErpAlternateDesignatorQuery query) {
                log.warn("【BomFeign-queryList】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<ErpOperatRouteDTO>> findBy5AAndAlternateRoutingDesignator(ErpOperatRouteQuery query) {
                log.warn("【BomFeign-queryList】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ErpOperatRouteDTO>>> list(ErpOperatRouteQuery query) {
                log.warn("【BomFeign-list获取工艺路线】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Object>> dailySyncInterface(DateTimeQuery query) {
                log.warn("【[每日定时拉取]定时拉取某一个组织Id】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Object>> syncBbom() {
                log.warn("【把ExternalItem的数据同步到BBOM中】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ErpAlternateDesignatorDTO>>> queryByAlternateDesignatorCodes(List<String> slternateDesignatorCodes) {
                log.warn("【电池BOM主料查询ERP替代项表】发生异常：{}", cause);
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<Object>> syncLifecycleBatchSave(List<ItemsSaveDTO> saveDTOS) {
                log.warn("【更新同步一期组件生命周期状态】发生异常：{}", cause);
                return Results.createFailRes();
            }

        };
    }
}
