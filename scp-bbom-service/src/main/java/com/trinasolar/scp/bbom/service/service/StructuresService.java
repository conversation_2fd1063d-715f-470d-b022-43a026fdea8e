package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ComponentsDTO;
import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.query.StructuresMrpQuery;
import com.trinasolar.scp.bbom.domain.query.StructuresQuery;
import com.trinasolar.scp.bbom.domain.save.StructuresSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * BBOM结构 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
public interface StructuresService {
    /**
     * 分页获取BBOM结构
     *
     * @param query 查询对象
     * @return BBOM结构分页对象
     */
    Page<StructuresDTO> queryByPage(StructuresQuery query);

    /**
     * 分页获取BBOM结构
     *
     * @param query 查询对象
     * @return BBOM结构分页对象
     */
    List<StructuresDTO> queryList();

    /**
     * 根据主键获取BBOM结构详情
     *
     * @param id 主键
     * @return BBOM结构详情
     */
    StructuresDTO queryById(Long id);

    /**
     * 保存或更新BBOM结构
     *
     * @param saveDTO BBOM结构保存对象
     * @return BBOM结构对象
     */
    StructuresDTO save(StructuresSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除BBOM结构
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(StructuresQuery query, HttpServletResponse response);

    StructuresDTO findOneByMrpQuery(StructuresMrpQuery structuresMrpQuery);

    List<StructuresDTO> findByComponentDTOAndOrganizationId(List<ComponentsDTO> componentsDTO, Long organizationId);

    StructuresDTO findByAlternateDesignator(String orgId, Long sourceItemId, String alternateDesignatorCode);

    List<Long> findByAlternateDesignatorWithoutSourceItemId(String orgId, String alternateDesignatorCode);

    List<StructuresDTO> findAll();
}

