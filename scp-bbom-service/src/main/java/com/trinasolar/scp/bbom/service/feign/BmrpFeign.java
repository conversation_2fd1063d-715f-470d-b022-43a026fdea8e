package com.trinasolar.scp.bbom.service.feign;

import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.*;
import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.ApprovedVendorNamesQuery;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.ErpOpenPOQuery;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.ErpOpenPRQuery;
import com.trinasolar.scp.bbom.domain.query.feign.bmrp.TjOnHandQuery;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * bmrp服务feign调用
 *
 * @author: huang<PERSON><PERSON>
 * @create: 2024/02/04
 */
@FeignClient(path = FeignConstant.SCP_BATTERY_MRP_API, value = FeignConstant.SCP_BATTERY_MRP_API/*, url = "https://scpapitest.trinasolar.com"*/)
public interface BmrpFeign {
    /**
     * 获取旧网版库存
     *
     * @param query
     * @return
     */
    @PostMapping("/tj-on-hand/query-inventory")
    ResponseEntity<Results<List<TjOnHandDTO>>> queryInventory(@RequestBody TjOnHandQuery query);

    /**
     * 获取旧网版在途数据 pr
     *
     * @param query
     * @return
     */
    @PostMapping("/erp-open-pr/summary/list")
    ResponseEntity<Results<List<ErpOpenPRDTO>>> queryQuantity(@RequestBody ErpOpenPRQuery query);

    /**
     * 获取旧网版在途数据 po
     *
     * @param query
     * @return
     */
    @PostMapping("/erp-open-po/summary/list")
    ResponseEntity<Results<List<ErpOpenPODTO>>> list(@RequestBody ErpOpenPOQuery query);

    /**
     * 获取供应商
     *
     * @param query
     * @return
     */
    @PostMapping("/approved-vendor/vendor-list")
    ResponseEntity<Results<List<ApprovedVendorDTO>>> vendorList(@RequestBody ApprovedVendorQuery query);

    @PostMapping("/approved-vendor/getApprovedVendorDTOByVendorNames")
    ResponseEntity<Results<List<ApprovedVendorDTO>>> getApprovedVendorDTOByVendorNames(@RequestBody ApprovedVendorNamesQuery query);
}
