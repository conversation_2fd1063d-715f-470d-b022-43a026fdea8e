package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.LowEfficiencyCellPercent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;


@Repository
public interface LowEfficiencyCellPercentRepository extends JpaRepository<LowEfficiencyCellPercent, Long>, QuerydslPredicateExecutor<LowEfficiencyCellPercent> {
    @Transactional
    @Query(nativeQuery = true,
            value = "update bbom_low_efficiency_cell_percent set is_deleted = 1 where year in :years")
    @Modifying(clearAutomatically = true)
    void deleteByYears(@Param("years") Set<String> year);
}
