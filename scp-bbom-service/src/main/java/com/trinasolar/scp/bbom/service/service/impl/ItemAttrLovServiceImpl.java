package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.ItemAttrLovDEConvert;
import com.trinasolar.scp.bbom.domain.dto.ItemAttrLovDTO;
import com.trinasolar.scp.bbom.domain.entity.ItemAttrLov;
import com.trinasolar.scp.bbom.domain.entity.QItemAttrLov;
import com.trinasolar.scp.bbom.domain.enums.Constant;
import com.trinasolar.scp.bbom.domain.query.ItemAttrLovQuery;
import com.trinasolar.scp.bbom.domain.save.ItemAttrLovSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.MdmItemAttrLovHeaderVO;
import com.trinasolar.scp.bbom.domain.vo.MdmItemAttrLovLineVO;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.bbom.service.repository.ItemAttrLovRepository;
import com.trinasolar.scp.bbom.service.service.ItemAttrLovService;
import com.trinasolar.scp.bbom.service.service.MdmInterfaceService;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 物料属性字段Lov
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Slf4j
@Service("itemAttrLovService")
@RequiredArgsConstructor
public class ItemAttrLovServiceImpl implements ItemAttrLovService {
    private static final QItemAttrLov qItemAttrLov = QItemAttrLov.itemAttrLov;

    private final ItemAttrLovDEConvert convert;

    private final ItemAttrLovRepository repository;

    private final MdmInterfaceService mdmInterfaceService;

    private final JPAQueryFactory jpaQueryFactory;

    private final SystemFeign systemFeign;


    @Override
    public Page<ItemAttrLovDTO> queryByPage(ItemAttrLovQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ItemAttrLov> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ItemAttrLovQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qItemAttrLov.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getLovId())) {
            booleanBuilder.and(qItemAttrLov.lovId.eq(query.getLovId()));
        }
        if (StringUtils.isNotEmpty(query.getLovName())) {
            booleanBuilder.and(qItemAttrLov.lovName.eq(query.getLovName()));
        }
        if (StringUtils.isNotEmpty(query.getSrcAttrId())) {
            booleanBuilder.and(qItemAttrLov.srcAttrId.eq(query.getSrcAttrId()));
        }
        if (StringUtils.isNotEmpty(query.getSrcAttrName())) {
            booleanBuilder.and(qItemAttrLov.srcAttrName.eq(query.getSrcAttrName()));
        }
        if (StringUtils.isNotEmpty(query.getLovLineId())) {
            booleanBuilder.and(qItemAttrLov.lovLineId.eq(query.getLovLineId()));
        }
        if (StringUtils.isNotEmpty(query.getLovLineValue())) {
            booleanBuilder.and(qItemAttrLov.lovLineValue.eq(query.getLovLineValue()));
        }
        if (StringUtils.isNotEmpty(query.getLanguage())) {
            booleanBuilder.and(qItemAttrLov.language.eq(query.getLanguage()));
        }
        if (StringUtils.isNotEmpty(query.getIsRequired())) {
            booleanBuilder.and(qItemAttrLov.isRequired.eq(query.getIsRequired()));
        }
    }

    @Override
    public ItemAttrLovDTO queryById(Long id) {
        ItemAttrLov queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ItemAttrLovDTO save(ItemAttrLovSaveDTO saveDTO) {
        ItemAttrLov newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ItemAttrLov());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ItemAttrLovQuery query, HttpServletResponse response) {
        List<ItemAttrLovDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "物料属性字段Lov", "物料属性字段Lov", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public void sync() {
        for (String category : Constant.CATEGORYS) {
            saveMdmVos(getFromMDM(category, "en"), "en");
            saveMdmVos(getFromMDM(category, "cn"), "cn");
        }
    }

    @Override
    public void transToLov() {
        LocalDateTime findTime = LocalDateTime.now().minusDays(3);
        List<ItemAttrLov> itemAttrLovs =
                jpaQueryFactory.selectFrom(qItemAttrLov)
                        .where(qItemAttrLov.language.eq("cn"))
                        .where(qItemAttrLov.updatedTime.after(findTime))
                        .fetch();
        List<ItemAttrLovDTO> itemAttrLovDTOS = itemAttrLovs.stream().map(item -> {
            ItemAttrLovDTO dto = new ItemAttrLovDTO();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());

        for (ItemAttrLovDTO itemAttrLovDTO : itemAttrLovDTOS) {
            systemFeign.syncItemAttrLov(itemAttrLovDTO);
        }
    }


    private void saveMdmVos(List<MdmItemAttrLovHeaderVO> vos, String language) {
        for (MdmItemAttrLovHeaderVO vo : vos) {
            for (MdmItemAttrLovLineVO option : vo.getOptions()) {
                BooleanBuilder booleanBuilder = new BooleanBuilder();
                booleanBuilder.and(qItemAttrLov.isDeleted.eq(DeleteEnum.NO.getCode()));
                booleanBuilder.and(qItemAttrLov.lovLineId.eq(option.getId()));
                booleanBuilder.and(qItemAttrLov.lovId.eq(vo.getLovID()));
                booleanBuilder.and(qItemAttrLov.language.eq(language));
                if (!repository.findOne(booleanBuilder).isPresent()) {
                    ItemAttrLov itemAttrLov = new ItemAttrLov();
                    itemAttrLov.setLovId(vo.getLovID());
                    itemAttrLov.setLovName(vo.getLovName());
                    itemAttrLov.setSrcAttrId(vo.getAttributeID());
                    itemAttrLov.setLovLineId(option.getId());
                    itemAttrLov.setLovLineValue(option.getValue());
                    itemAttrLov.setLanguage(language);
                    repository.save(itemAttrLov);
                }
            }
        }
    }

    private List<MdmItemAttrLovHeaderVO> getFromMDM(String category, String language) {
        String url = "/ea009/v1/extension/execute/getLOVFromMDM?context=Context1";
        String param = "{" +
                "\"AttrIDs\":\"\"," +
                "\"CategoryID\":\"" + category + "\"," +
                "\"context\":\"" + language + "\"" +
                "}";
        List<MdmItemAttrLovHeaderVO> vos = mdmInterfaceService.getObjectListByUrl(url, param, MdmItemAttrLovHeaderVO.class);
        return vos;
    }
}
