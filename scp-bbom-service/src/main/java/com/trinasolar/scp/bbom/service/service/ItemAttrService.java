package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ItemAttrDTO;
import com.trinasolar.scp.bbom.domain.query.ItemAttrQuery;
import com.trinasolar.scp.bbom.domain.save.ItemAttrSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物料属性字段别名 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
public interface ItemAttrService {
    /**
     * 分页获取物料属性字段别名
     *
     * @param query 查询对象
     * @return 物料属性字段别名分页对象
     */
    Page<ItemAttrDTO> queryByPage(ItemAttrQuery query);

    /**
     * 根据主键获取物料属性字段别名详情
     *
     * @param id 主键
     * @return 物料属性字段别名详情
     */
    ItemAttrDTO queryById(Long id);

    /**
     * 保存或更新物料属性字段别名
     *
     * @param saveDTO 物料属性字段别名保存对象
     * @return 物料属性字段别名对象
     */
    ItemAttrDTO save(ItemAttrSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除物料属性字段别名
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ItemAttrQuery query, HttpServletResponse response);

    void sync();

    List<ItemAttrDTO> findAll(ItemAttrQuery query);
}

