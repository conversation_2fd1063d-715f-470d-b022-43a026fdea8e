/**
 * @Function: SubstituteService.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 14:11:55
 */
package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.query.SubstituteQuery;

/**
 *
 * @Function: SubstituteService.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuweiwei
 * @date: 2024年1月3日 14:11:55
 */
public interface SubstituteService {

    /**
     * @param organizationId
     * @return
     */
    StructuresDTO getItemCodeSubs(SubstituteQuery query);

}
