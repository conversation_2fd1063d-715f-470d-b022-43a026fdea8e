package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ItemAttrLovDTO;
import com.trinasolar.scp.bbom.domain.query.ItemAttrLovQuery;
import com.trinasolar.scp.bbom.domain.save.ItemAttrLovSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物料属性字段Lov 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
public interface ItemAttrLovService {
    /**
     * 分页获取物料属性字段Lov
     *
     * @param query 查询对象
     * @return 物料属性字段Lov分页对象
     */
    Page<ItemAttrLovDTO> queryByPage(ItemAttrLovQuery query);

    /**
     * 根据主键获取物料属性字段Lov详情
     *
     * @param id 主键
     * @return 物料属性字段Lov详情
     */
    ItemAttrLovDTO queryById(Long id);

    /**
     * 保存或更新物料属性字段Lov
     *
     * @param saveDTO 物料属性字段Lov保存对象
     * @return 物料属性字段Lov对象
     */
    ItemAttrLovDTO save(ItemAttrLovSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除物料属性字段Lov
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ItemAttrLovQuery query, HttpServletResponse response);

    void sync();

    void transToLov();
}

