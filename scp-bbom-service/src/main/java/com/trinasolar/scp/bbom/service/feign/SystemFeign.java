package com.trinasolar.scp.bbom.service.feign;

import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.OrganizationDefinitionsDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.OrganizationDefinitionsQuery;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeQuery;
import com.trinasolar.scp.bbom.domain.dto.feign.system.LovLineSaveDTO;
import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.bbom.domain.query.AttrTypeLineQuery;
import com.trinasolar.scp.bbom.domain.query.LovLineQuery;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName: SystemFeign.java, Created by IntelliJ IDEA
 * @Description: TODO
 * @Author: wang_jun
 * @Date: 2022/7/19 22:37
 * @Version :1.0
 **/
@FeignClient(value = FeignConstant.SCP_SYSTEM_API, path = "/scp-system-api")
//@FeignClient(value = FeignConstant.SYSTEM_SERVICE, path = "/scp-system-api")
public interface SystemFeign {

    @PostMapping("/lovLine/queryByCode")
    ResponseEntity<Results<List<LovLineDTO>>> queryByCode(@RequestBody LovLineQuery lovLineQuery);

    @PostMapping("/attrTypeLine/get")
    Results<AttrTypeLineDTO> queryAttrTypeLineById(@RequestBody IdDTO idDTO);

    @PostMapping("/attrTypeHeader/get")
    Results<AttrTypeHeaderDTO> queryAttrTypeHeaderById(@RequestBody IdDTO idDTO);

    @PostMapping("/attrTypeLine/queryByHeaderCode")
    @ApiOperation(value = "通过Header Code查询数据")
    ResponseEntity<Results<List<AttrTypeLineDTO>>> queryAttrTypeLinesByHeaderCode(@RequestBody AttrTypeLineQuery query);

    @PostMapping("/attrTypeHeader/getByCategorySegment5")
    ResponseEntity<Results<AttrTypeHeaderDTO>> getAttrTypeHeaderByCategorySegment5(@RequestBody IdDTO idDTO);

    @PostMapping("/attrTypeHeader/findItemTransToLovAttrLines")
    ResponseEntity<Results<List<AttrTypeLineDTO>>> findItemTransToLovAttrLines(@RequestBody IdDTO idDTO);

    @PostMapping("/lovHeader/syncItemAttrLov")
    @ApiOperation(value = "同步ItemAttrLov")
    ResponseEntity<Results<Object>> syncItemAttrLov(@RequestBody ItemAttrLovDTO itemAttrLovDTO);

    @PostMapping("/lovLine/saveAll")
    ResponseEntity<Results<Object>> saveLovLineList(@RequestBody List<LovLineDTO> lovLineDTOS);

    @PostMapping("/lovLine/saveOne")
    @ApiOperation(value = "LOV行明细单行保存")
    ResponseEntity<Results<LovLineDTO>> lovLineSaveOne(@Valid @RequestBody LovLineSaveDTO lovLineSaveDTO);

    @PostMapping("/organization-definitions/list2")
    ResponseEntity<Results<List<OrganizationDefinitionsDTO>>> queryOrganizationByList(@RequestBody OrganizationDefinitionsQuery query);

    @PostMapping("/lovHeader/queryByCode")
    ResponseEntity<Results<LovHeaderDTO>> queryLovHeaderByCode(@RequestBody com.trinasolar.scp.common.api.base.LovLineQuery lovLineQuery);

    @PostMapping("/erp-approval-supplier/querySupplierPage")
    @ApiOperation(value = "批准供应商分页列表", notes = "获得批准供应商分页列表")
    ResponseEntity<Results<ErpApprovalSupplierPageNumDTO>> querySupplierPage(@RequestBody ErpApprovalSupplierQuery query);

    //获取用户数据查询权限
    @PostMapping("/data-privilege/list")
    ResponseEntity<Results<List<DataPrivilegeDTO>>> queryDataPrivilege(@RequestBody DataPrivilegeQuery query);
}
