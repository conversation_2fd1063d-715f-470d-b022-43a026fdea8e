package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.SlurryInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 浆料车间单耗及线数维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Repository
public interface SlurryInformationRepository extends JpaRepository<SlurryInformation, Long>, QuerydslPredicateExecutor<SlurryInformation> {
}
