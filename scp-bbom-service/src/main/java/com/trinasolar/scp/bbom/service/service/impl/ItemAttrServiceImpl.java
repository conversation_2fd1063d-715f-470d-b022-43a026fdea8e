package com.trinasolar.scp.bbom.service.service.impl;

import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.ItemAttrDEConvert;
import com.trinasolar.scp.bbom.domain.dto.ItemAttrDTO;
import com.trinasolar.scp.bbom.domain.entity.ItemAttr;
import com.trinasolar.scp.bbom.domain.entity.QItemAttr;
import com.trinasolar.scp.bbom.domain.enums.Constant;
import com.trinasolar.scp.bbom.domain.query.ItemAttrQuery;
import com.trinasolar.scp.bbom.domain.save.ItemAttrSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.MdmItemAttrHeaderVO;
import com.trinasolar.scp.bbom.domain.vo.MdmItemAttrLineVO;
import com.trinasolar.scp.bbom.service.repository.ItemAttrRepository;
import com.trinasolar.scp.bbom.service.service.ItemAttrService;
import com.trinasolar.scp.bbom.service.service.MdmInterfaceService;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Slf4j
@Service("itemAttrService")
@RequiredArgsConstructor
public class ItemAttrServiceImpl implements ItemAttrService {
    private static final QItemAttr qItemAttr = QItemAttr.itemAttr;

    private final ItemAttrDEConvert convert;

    private final ItemAttrRepository repository;

    private final MdmInterfaceService mdmInterfaceService;

    @Override
    public Page<ItemAttrDTO> queryByPage(ItemAttrQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<ItemAttr> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ItemAttrQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qItemAttr.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getSrcAttrId())) {
            booleanBuilder.and(qItemAttr.srcAttrId.eq(query.getSrcAttrId()));
        }
        if (StringUtils.isNotEmpty(query.getSrcAttrAlias())) {
            booleanBuilder.and(qItemAttr.srcAttrAlias.eq(query.getSrcAttrAlias()));
        }
        if (StringUtils.isNotEmpty(query.getSrcCategorySegment4Id())) {
            booleanBuilder.and(qItemAttr.srcCategorySegment4Id.eq(query.getSrcCategorySegment4Id()));
        }
        if (StringUtils.isNotEmpty(query.getSrcCategorySegment4())) {
            booleanBuilder.and(qItemAttr.srcCategorySegment4.like("%".concat(query.getSrcCategorySegment4()).concat("%")));
        }
        if (StringUtils.isNotEmpty(query.getSrcAttrType())) {
            booleanBuilder.and(qItemAttr.srcAttrType.eq(query.getSrcAttrType()));
        }
        if (StringUtils.isNotEmpty(query.getSrcOptionFlag())) {
            booleanBuilder.and(qItemAttr.srcOptionFlag.eq(query.getSrcOptionFlag()));
        }
        if (StringUtils.isNotEmpty(query.getSrcAttrColumn())) {
            booleanBuilder.and(qItemAttr.srcAttrColumn.eq(query.getSrcAttrColumn()));
        }
        if (StringUtils.isNotEmpty(query.getLanguage())) {
            booleanBuilder.and(qItemAttr.language.eq(query.getLanguage()));
        }
    }

    @Override
    public ItemAttrDTO queryById(Long id) {
        ItemAttr queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public ItemAttrDTO save(ItemAttrSaveDTO saveDTO) {
        ItemAttr newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ItemAttr());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(ItemAttrQuery query, HttpServletResponse response) {
        List<ItemAttrDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "物料属性字段别名", "物料属性字段别名", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    public void sync() {
        for (String category : Constant.CATEGORYS) {
            saveMdmVos(getFromMDM(category, "en"), "en");
            saveMdmVos(getFromMDM(category, "cn"), "cn");
        }
    }

    @Override
    public List<ItemAttrDTO> findAll(ItemAttrQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
//        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
//        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Iterable<ItemAttr> all = repository.findAll(booleanBuilder);
        return convert.toDto(Lists.newArrayList(all));
    }

    private void saveMdmVos(List<MdmItemAttrHeaderVO> vos, String language) {
        for (MdmItemAttrHeaderVO vo : vos) {
            // 先判断是否存在
            for (MdmItemAttrLineVO attribute : vo.getAttributes()) {
                BooleanBuilder booleanBuilder = new BooleanBuilder();
                booleanBuilder.and(qItemAttr.srcCategorySegment4Id.eq(vo.getCategoryID()));
                booleanBuilder.and(qItemAttr.srcAttrId.eq(attribute.getAttributeID()));
                booleanBuilder.and(qItemAttr.language.eq(language));
                booleanBuilder.and(qItemAttr.isDeleted.eq(DeleteEnum.NO.getCode()));
                // 不存在时保存
                if (!repository.findOne(booleanBuilder).isPresent()) {
                    ItemAttr itemAttr = new ItemAttr();
                    itemAttr.setSrcAttrId(attribute.getAttributeID());
                    itemAttr.setSrcAttrAlias(attribute.getAttributeName());
                    itemAttr.setSrcCategorySegment4Id(vo.getCategoryID());
                    itemAttr.setSrcCategorySegment4(vo.getCategoryName());
                    itemAttr.setSrcAttrType("true".equals(attribute.getHasLOV()) ? "lov" : "text");
//                    itemAttr.setSrcOptionFlag();
                    String seg = attribute.getSeg();
                    String replace = seg.replace("Seg", "segment");
                    itemAttr.setSrcAttrColumn(replace);
                    itemAttr.setLanguage(language);
                    repository.save(itemAttr);
                }
            }
        }
    }

    private List<MdmItemAttrHeaderVO> getFromMDM(String category, String language) {
        String url = "/ea010/v1/extension/execute/getCategory4Info?context=Context1&username=&password=";
        String param = "{\n" +
                "\t\"CategoryIDs\":\"" + category + "\",\n" +
                "\t\"context\":\"" + language + "\"\n" +
                "}";
        List<MdmItemAttrHeaderVO> mdmItemAttrHeaderVOS = mdmInterfaceService.getObjectListByUrl(url, param, MdmItemAttrHeaderVO.class);
        return mdmItemAttrHeaderVOS;
    }
}
