package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.SiliconWaferLossDTO;
import com.trinasolar.scp.bbom.domain.query.SiliconWaferLossQuery;
import com.trinasolar.scp.bbom.domain.save.SiliconWaferLossSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 硅片损耗率信息维护 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
public interface SiliconWaferLossService {
    /**
     * 分页获取硅片损耗率信息维护
     *
     * @param query 查询对象
     * @return 硅片损耗率信息维护分页对象
     */
    Page<SiliconWaferLossDTO> queryByPage(SiliconWaferLossQuery query);

    /**
     * 根据主键获取硅片损耗率信息维护详情
     *
     * @param id 主键
     * @return 硅片损耗率信息维护详情
     */
    SiliconWaferLossDTO queryById(Long id);

    /**
     * 保存或更新硅片损耗率信息维护
     *
     * @param saveDTO 硅片损耗率信息维护保存对象
     * @return 硅片损耗率信息维护对象
     */
    SiliconWaferLossDTO save(SiliconWaferLossSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除硅片损耗率信息维护
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(SiliconWaferLossQuery query, HttpServletResponse response);

    /**
     * 导出模版
     *
     * @param query
     * @param response
     */
    void queryByPageExport(SiliconWaferLossQuery query, HttpServletResponse response);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);
}

