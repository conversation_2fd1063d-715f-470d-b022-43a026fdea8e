package com.trinasolar.scp.bbom.service.feign;

import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.bbom.domain.query.CodeSeqQuery;
import com.trinasolar.scp.common.api.util.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/6/9
 */
@FeignClient(value = FeignConstant.SCP_SYSTEM_API, path = "/scp-system-api")
public interface CodeFeign {
    @PostMapping("/code-seq/createCodeSeq")
    ResponseEntity<Results<String>> createCodeSeq(@RequestBody CodeSeqQuery query);
}
