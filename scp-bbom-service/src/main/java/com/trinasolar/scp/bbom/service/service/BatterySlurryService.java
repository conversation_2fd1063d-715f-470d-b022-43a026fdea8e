package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatterySlurryDTO;
import com.trinasolar.scp.bbom.domain.entity.BatterySlurry;
import com.trinasolar.scp.bbom.domain.query.BatterySlurryQuery;
import com.trinasolar.scp.bbom.domain.save.BatterySlurrySaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池类型动态属性-浆料 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
public interface BatterySlurryService {
    /**
     * 分页获取电池类型动态属性-浆料
     *
     * @param query 查询对象
     * @return 电池类型动态属性-浆料分页对象
     */
    Page<BatterySlurryDTO> queryByPage(String userId, BatterySlurryQuery query);

    /**
     * 查询浆料信息
     *
     * @param query
     * @return
     */
    List<BatterySlurryDTO> queryByAll(BatterySlurryQuery query);

    /**
     * 根据主键获取电池类型动态属性-浆料详情
     *
     * @param id 主键
     * @return 电池类型动态属性-浆料详情
     */
    BatterySlurryDTO queryById(Long id);

    /**
     * 保存或更新电池类型动态属性-浆料
     *
     * @param saveDTO 电池类型动态属性-浆料保存对象
     * @return 电池类型动态属性-浆料对象
     */
    BatterySlurryDTO save(BatterySlurrySaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型动态属性-浆料
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatterySlurryQuery query, HttpServletResponse response, String userId);

    void queryListBySendMail();

    /**
     * 浆料切换
     * 入参：生产基地
     * 接口返回：生产基地、料号新、料号旧、数量、开始日期、供应商
     */
    List<BatterySlurryDTO> queryBatterySlurryByBasePlace(BatterySlurryQuery query);

    void transcationSaves(List<BatterySlurry> saves);
}

