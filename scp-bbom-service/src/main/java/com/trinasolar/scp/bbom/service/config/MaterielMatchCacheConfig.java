package com.trinasolar.scp.bbom.service.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 料号匹配缓存配置
 * 针对doSingleMatch方法的性能优化
 * 
 * <AUTHOR> Assistant
 */
@Configuration
@EnableCaching
public class MaterielMatchCacheConfig {

    /**
     * Redis缓存管理器配置
     */
    @Bean
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30)) // 默认30分钟过期
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .disableCachingNullValues(); // 不缓存null值
        
        // 针对不同缓存设置不同的过期时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 5A料号数据缓存 - 30分钟
        cacheConfigurations.put("items_all_5a", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 表达式规则缓存 - 1小时
        cacheConfigurations.put("express_rules", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // DP属性映射缓存 - 1小时
        cacheConfigurations.put("dp_columns", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // DP转换脚本缓存 - 1小时
        cacheConfigurations.put("dp_trans_script", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 结构数据缓存 - 30分钟
        cacheConfigurations.put("structures_all", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // BOM替代项缓存 - 30分钟
        cacheConfigurations.put("bom_designator", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 电池类型缓存 - 1小时
        cacheConfigurations.put("battery_type", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // Items查询缓存 - 15分钟
        cacheConfigurations.put("BBOM_items_getOneByItemCodeAndOrganizationId", defaultConfig.entryTtl(Duration.ofMinutes(15)));
        cacheConfigurations.put("BBOM_items_findOneByItemCodeAndOrganizationId", defaultConfig.entryTtl(Duration.ofMinutes(15)));
        cacheConfigurations.put("BBOM_items_findOneByItemCode", defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // 其他业务缓存
        cacheConfigurations.put("MaterielMatchHeaderService_queryListByStructures", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put("MaterielMatchHeaderService_getErpAlternateDesignatorByOrgId", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put("BatteryTypeMainService_queryByBatteryName", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
    
    /**
     * 本地缓存管理器（作为备用）
     */
    @Bean("localCacheManager")
    public CacheManager localCacheManager() {
        return new ConcurrentMapCacheManager(
            "items_all_5a",
            "express_rules", 
            "dp_columns",
            "dp_trans_script",
            "structures_all",
            "bom_designator",
            "battery_type"
        );
    }
}
