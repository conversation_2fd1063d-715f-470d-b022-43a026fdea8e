package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Repository
public interface RuleControlObjectValueRepository extends JpaRepository<RuleControlObjectValue, Long>, QuerydslPredicateExecutor<RuleControlObjectValue> {
    @Query(value = "SELECT * FROM bbom_rule_control_object_value WHERE bbom_rule_control_object_value.rule_control_object_detail_id = ?1 AND is_deleted=0",
            nativeQuery = true)
    List<RuleControlObjectValue> listByRuleControlObjectDetailId(Long detailId);
}
