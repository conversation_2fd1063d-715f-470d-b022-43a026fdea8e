package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.constant.CommonConstant;
import com.trinasolar.scp.bbom.domain.convert.BatteryScreenPlateWorkshopDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.bmrp.ApprovedVendorDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryScreenPlateWorkshop;
import com.trinasolar.scp.bbom.domain.entity.QBatteryScreenPlateWorkshop;
import com.trinasolar.scp.bbom.domain.entity.QItems;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateWorkshopExcelDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateWorkshopQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateWorkshopSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.FileUtil;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.BmrpFeign;
import com.trinasolar.scp.bbom.service.repository.BatteryScreenPlateWorkshopRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.service.impl.scheduleEmail.SCPFileService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import jodd.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trinasolar.cloud.lapetus.logback.appender.data.AuditDTO.OperateType.query;

/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Slf4j
@Service("batteryScreenPlateWorkshopService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class BatteryScreenPlateWorkshopServiceImpl implements BatteryScreenPlateWorkshopService {
    private static final QBatteryScreenPlateWorkshop qBatteryScreenPlateWorkshop = QBatteryScreenPlateWorkshop.batteryScreenPlateWorkshop;

    private final static String CHECK_A = "A";

    private final static String CHECK_B = "B";

    private final static String CHECK_C = "C";

    private final static String SWITCHING_TYPE = "NETWORK_VERSION_SWITCHING_TYPE";

    private final static String BASE_PLACE = "base_place";

    private final static String WORK_SHOP = "work_shop";

    private final static String WORK_UNIT = "work_unit";

    private final static String CELL_MACHINE = "CELL_MACHINE";

    private static final QItems qItems = QItems.items;

    private final static String XCPDR = "新产品导入";

    private final static String ZDY = "中大样";

    private final BatteryScreenPlateWorkshopDEConvert convert;

    private final BatteryScreenPlateWorkshopRepository repository;

    private final BatteryTypeMainService batteryTypeMainService;

    private final BatteryFeignService batteryFeignService;

    private final BomService bomService;

    private final BmrpFeign bmrpFeign;

    private final BMrpService bMrpService;

    private final JPAQueryFactory jpaQueryFactory;

    private final StructuresService structuresService;

    private final ComponentsService componentsService;

    private final ItemsService itemsService;

    private final MailService mailService;

    private final SlurryInformationService informationService;

    @Autowired
    private SCPFileService scpFileService;

    @Autowired
    @Lazy
    BatteryScreenPlateWorkshopService batteryScreenPlateWorkshopService;

    private static void queryConvert(BatteryScreenPlateWorkshopDTO excelDTO) {
        LovLineDTO batteryTypeLov = LovUtils.get("BATTERY_TYPE", excelDTO.getBatteryCode());
        LovLineDTO yesOrNoLov = LovUtils.get("yes_or_no", excelDTO.getSingleGlassFlag());
        LovLineDTO positiveScreenLov = LovUtils.get("7A01500100122", excelDTO.getPositiveElectrodeScreenFineGrid());
        LovLineDTO negativeScreenLov = LovUtils.get("7A01500100122", excelDTO.getNegativeElectrodeScreenFineGrid());
        Map<String, LovLineDTO> mainGridSpaceLov= LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
        LovLineDTO mainGridInfoLov= LovUtils.get("7A01500100117", excelDTO.getMainGridInfo());

        boolean mainGridSpaceCheckFlag = false;
        for(String key : mainGridSpaceLov.keySet()){
            LovLineDTO value = mainGridSpaceLov.get(key);
            if("main_grid_space".equals(value.getAttribute1()) && value.getLovValue().equals(excelDTO.getMainGridSpace())){
                mainGridSpaceCheckFlag = true;
            }
        }
        if(StringUtils.isNotBlank(excelDTO.getMainGridSpace()) && !mainGridSpaceCheckFlag){
            throw new BizException("请填写正确的主栅间距");
        }
        if(StringUtils.isNotBlank(excelDTO.getBatteryCode()) && Objects.isNull(batteryTypeLov)){
            throw new BizException("请填写正确的电池类型");
        }else if(StringUtils.isNotBlank(excelDTO.getBatteryCode()) && Objects.nonNull(batteryTypeLov)){
            excelDTO.setBatteryName(batteryTypeLov.getLovName());
        }
        if(StringUtils.isNotBlank(excelDTO.getSingleGlassFlag()) && Objects.isNull(yesOrNoLov)){
            throw new BizException("请填写正确的单玻");
        }else if(StringUtils.isNotBlank(excelDTO.getSingleGlassFlag()) && Objects.nonNull(yesOrNoLov)){
            excelDTO.setSingleGlassFlagName(yesOrNoLov.getLovName());
        }
        if(StringUtils.isNotBlank(excelDTO.getPositiveElectrodeScreenFineGrid()) && Objects.isNull(positiveScreenLov)){
            throw new BizException("请填写正确的正电极网版细栅");
        }else if(StringUtils.isNotBlank(excelDTO.getPositiveElectrodeScreenFineGrid()) && Objects.nonNull(positiveScreenLov)){
            excelDTO.setPositiveElectrodeScreenFineGridName(positiveScreenLov.getLovName());
        }
        if(StringUtils.isNotBlank(excelDTO.getNegativeElectrodeScreenFineGrid()) && Objects.isNull(negativeScreenLov)){
            throw new BizException("请填写正确的背电极网版细栅");
        }else if (StringUtils.isNotBlank(excelDTO.getNegativeElectrodeScreenFineGrid()) && Objects.nonNull(negativeScreenLov)){
            excelDTO.setNegativeElectrodeScreenFineGridName(negativeScreenLov.getLovName());
        }
        if(Objects.nonNull(mainGridInfoLov)){
            excelDTO.setMainGridInfo(mainGridInfoLov.getLovName());
        }

        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if(Objects.nonNull(excelDTO.getEffectiveEndDate())){
            excelDTO.setEffectiveEndDateName(excelDTO.getEffectiveEndDate().format(fmt));
        }else {
            excelDTO.setEffectiveEndDateName("");
        }
        if(Objects.nonNull(excelDTO.getEffectiveStartDate())){
            excelDTO.setEffectiveStartDateName(excelDTO.getEffectiveStartDate().format(fmt));
        }else {
            excelDTO.setEffectiveStartDateName("");
        }
    }


    private static void verifySave(BatteryScreenPlateWorkshopSaveDTO saveDTO) {
        // 新增修改校验
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBatteryCode())) {
            throw new BizException("bbom_valid_batteryName_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getNegativeElectrodeScreenFineGrid())) {
            throw new BizException("背电极网版细栅不能为空");
        }
        if (StringUtils.isBlank(saveDTO.getPositiveElectrodeScreenFineGrid())) {
            throw new BizException("正电极网版细栅不能为空");
        }
        if (Objects.isNull(saveDTO.getEffectiveStartDate())) {
            throw new BizException("有效日期-起不能为空");
        }

    }

    public static boolean isValidDateFormat(String dateStr, String dateFormat) {
        if (ObjectUtil.isEmpty(dateStr)) {
            return true;
        }
        try {
            DateUtil.parse(dateStr, dateFormat); // 将字符串解析为日期对象，如果解析成功，则说明字符串是有效的日期格式；否则说明字符串不是有效的日期格式。
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Page<BatteryScreenPlateWorkshopDTO> queryByPage(BatteryScreenPlateWorkshopQuery query) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Page<BatteryScreenPlateWorkshop> page = repository.findAll(booleanBuilder, pageable);
        List<BatteryScreenPlateWorkshopDTO> BatteryScreenPlateWorkshopDTOS = convert.toDto(page.getContent());
        // 需要转码 lov id->name
        for (BatteryScreenPlateWorkshopDTO excelDTO : BatteryScreenPlateWorkshopDTOS) {
            queryConvert(excelDTO);
        }
        return new PageImpl(BatteryScreenPlateWorkshopDTOS, page.getPageable(), page.getTotalElements());
    }

    @Override
    public BatteryScreenPlateWorkshopDTO getScreenWorkshopByHead(MaterielMatchHeaderDTO materielMatchHeaderDTO) {
        //排产日期
        LocalDateTime monthTime= LocalDateTimeUtil.of(DateUtil.parse(materielMatchHeaderDTO.getMonth(),"yyyyMM"));
        LocalDate month= monthTime.toLocalDate();
        if(Objects.nonNull(materielMatchHeaderDTO.getScheduleDate())){
            month = materielMatchHeaderDTO.getScheduleDate();
        }
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryName.eq(materielMatchHeaderDTO.getBatteryType()));
        if(StringUtils.isNotEmpty(materielMatchHeaderDTO.getTransparentDoubleGlass()) && "单玻".equals(materielMatchHeaderDTO.getTransparentDoubleGlass())){
            booleanBuilder.and(qBatteryScreenPlateWorkshop.singleGlassFlag.eq("Y"));
        }else{
            booleanBuilder.and(qBatteryScreenPlateWorkshop.singleGlassFlag.eq("N")
                    .or(qBatteryScreenPlateWorkshop.singleGlassFlag.isNull())
                    .or(qBatteryScreenPlateWorkshop.singleGlassFlag.eq("")));
        }
        if(StringUtils.isNotEmpty(materielMatchHeaderDTO.getMainGridSpace()) && !Objects.equals(CommonConstant.NON, materielMatchHeaderDTO.getMainGridSpace())){
            booleanBuilder.and(qBatteryScreenPlateWorkshop.mainGridSpace.eq(materielMatchHeaderDTO.getMainGridSpace()));
        }else{
            booleanBuilder.and(qBatteryScreenPlateWorkshop.mainGridSpace.isNull().or(qBatteryScreenPlateWorkshop.mainGridSpace.eq("")).or(qBatteryScreenPlateWorkshop.mainGridSpace.eq(CommonConstant.NON)));
        }
        booleanBuilder.and(qBatteryScreenPlateWorkshop.workshop.eq(materielMatchHeaderDTO.getWorkshop()));
        booleanBuilder.and(qBatteryScreenPlateWorkshop.basePlace.eq(materielMatchHeaderDTO.getBasePlace()));
        booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveStartDate.isNull()
                .or(qBatteryScreenPlateWorkshop.effectiveStartDate.loe(month)));
        booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveEndDate.isNull()
                .or(qBatteryScreenPlateWorkshop.effectiveEndDate.goe(month)));
        List<BatteryScreenPlateWorkshopDTO> dtoList = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        if(CollectionUtils.isNotEmpty(dtoList)){
            return dtoList.get(0);
        }else{
            return null;
        }
    }

    //批量更新 更新是否存在bom中、更新就网版在途库存、旧网版库存
    @Override
    public void batchUpdate(BatteryScreenPlateWorkshopQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereByUpdate(booleanBuilder, query);
        List<BatteryScreenPlateWorkshopDTO> screenPlateDTOList = convert.toDto(IterableUtils.toList(repository.findAll(booleanBuilder)));
        repository.saveAll(convert.toEntity(screenPlateDTOList));
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatteryScreenPlateWorkshopQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getSingleGlassFlag())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.singleGlassFlag.eq(query.getSingleGlassFlag()));
        }
        if (StringUtils.isNotEmpty(query.getMainGridSpace())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.mainGridSpace.eq(query.getMainGridSpace()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getPositiveElectrodeScreenFineGrid())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.positiveElectrodeScreenFineGrid.eq(query.getPositiveElectrodeScreenFineGrid()));
        }
        if (StringUtils.isNotEmpty(query.getNegativeElectrodeScreenFineGrid())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.negativeElectrodeScreenFineGrid.eq(query.getNegativeElectrodeScreenFineGrid()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveStartDate.before(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveEndDate.after(query.getEffectiveEndDate()));
        }
    }

    //批量更新使用 批量更新获取当天日期 且在有效期范围内
    private void buildWhereByUpdate(BooleanBuilder booleanBuilder, BatteryScreenPlateWorkshopQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getSingleGlassFlag())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.singleGlassFlag.eq(query.getSingleGlassFlag()));
        }
        if (StringUtils.isNotEmpty(query.getMainGridSpace())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.mainGridSpace.eq(query.getMainGridSpace()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getPositiveElectrodeScreenFineGrid())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.positiveElectrodeScreenFineGrid.eq(query.getPositiveElectrodeScreenFineGrid()));
        }
        if (StringUtils.isNotEmpty(query.getNegativeElectrodeScreenFineGrid())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.negativeElectrodeScreenFineGrid.eq(query.getNegativeElectrodeScreenFineGrid()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveStartDate.before(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveEndDate.after(query.getEffectiveEndDate()));
        }

    }

    private void buildWhereRepeat(BooleanBuilder booleanBuilder, BatteryScreenPlateWorkshopSaveDTO query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getSingleGlassFlag())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.singleGlassFlag.eq(query.getSingleGlassFlag()));
        }
        if (StringUtils.isNotEmpty(query.getMainGridSpace())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.mainGridSpace.eq(query.getMainGridSpace()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.batteryName.eq(query.getBatteryName()));
        }

        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.workshop.eq(query.getWorkshop()));
        }
//        if (StringUtils.isNotEmpty(query.getPositiveElectrodeScreenFineGrid())) {
//            booleanBuilder.and(qBatteryScreenPlateWorkshop.positiveElectrodeScreenFineGrid.eq(query.getPositiveElectrodeScreenFineGrid()));
//        }
//        if (StringUtils.isNotEmpty(query.getNegativeElectrodeScreenFineGrid())) {
//            booleanBuilder.and(qBatteryScreenPlateWorkshop.negativeElectrodeScreenFineGrid.eq(query.getNegativeElectrodeScreenFineGrid()));
//        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }else{
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveStartDate.isNull());
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }else{
            booleanBuilder.and(qBatteryScreenPlateWorkshop.effectiveEndDate.isNull());
        }
    }

    @Override
    public BatteryScreenPlateWorkshopDTO queryById(Long id) {
        BatteryScreenPlateWorkshop queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public BatteryScreenPlateWorkshopDTO save(BatteryScreenPlateWorkshopSaveDTO saveDTO) {
        LovLineDTO batteryTypeLov = LovUtils.getByName("BATTERY_TYPE", saveDTO.getBatteryName());
        LovLineDTO yesOrNoLov = LovUtils.getByName("yes_or_no", saveDTO.getSingleGlassFlagName());
        Map<String, LovLineDTO> mainLov = LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
        LovLineDTO positiveScreenLov = LovUtils.get("7A01500100122", saveDTO.getPositiveElectrodeScreenFineGrid());
        LovLineDTO negativeScreenLov = LovUtils.get("7A01500100122", saveDTO.getNegativeElectrodeScreenFineGrid());
        LovLineDTO mainGridInfoLov = LovUtils.get("7A01500100117", saveDTO.getMainGridInfo());
        if(StringUtils.isNotBlank(saveDTO.getPositiveElectrodeScreenFineGrid()) && Objects.isNull(positiveScreenLov)){
            throw new BizException("请填写正确的正电极网版细栅");
        }
        if(StringUtils.isNotBlank(saveDTO.getNegativeElectrodeScreenFineGrid()) && Objects.isNull(negativeScreenLov)){
            throw new BizException("请填写正确的背电极网版细栅");
        }
        if(StringUtils.isNotBlank(saveDTO.getMainGridInfo()) && Objects.isNull(mainGridInfoLov)){
            throw new BizException("请填写正确的主栅信息");
        }

        if(Objects.nonNull(yesOrNoLov)){
            saveDTO.setSingleGlassFlag(yesOrNoLov.getLovValue());
        }else if(StringUtils.isNotBlank(saveDTO.getSingleGlassFlagName())){
            throw new BizException("请维护正确的单玻");
        }
        if(Objects.nonNull(batteryTypeLov)){
            saveDTO.setBatteryCode(batteryTypeLov.getLovValue());
        }else{
            throw new BizException("请维护正确的电池类型");
        }
        List<String> mainList = Lists.newArrayList();
        mainLov.forEach((key,value)->{
            if("main_grid_space".equals(value.getAttribute1()) && value.getLovName().equals(saveDTO.getMainGridSpace())){
                mainList.add(saveDTO.getMainGridSpace());
            }
        });
        if(CollectionUtils.isEmpty(mainList) && StringUtils.isNotBlank(saveDTO.getMainGridSpace())){
            throw new BizException("请维护正确的主栅间距");
        }
        verifySave(saveDTO);
        // 查询 区分新增 修改结果集合
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhereRepeat(booleanBuilder,saveDTO);
        Iterator<BatteryScreenPlateWorkshop> data = repository.findAll(booleanBuilder).iterator();
        List<BatteryScreenPlateWorkshop> dataList = Lists.newArrayList(data);
        String errorMsg = "";
        if(saveDTO.getIndex()!=null){
            errorMsg += "第"+saveDTO.getIndex()+"行数据重复,";
        }
        if (null != saveDTO.getId() && CollectionUtils.isNotEmpty(dataList) && dataList.size()>1) {
            throw new BizException(errorMsg+"电池类型+单玻+主栅间距+生产基地+生产车间+有效日期_起+有效日期止存在唯一性校验，不允许重复");
        }else if (null == saveDTO.getId() && CollectionUtils.isNotEmpty(dataList) && dataList.size()>0){
            throw new BizException(errorMsg+"电池类型+单玻+主栅间距+生产基地+生产车间+有效日期_起+有效日期止存在唯一性校验，不允许重复");
        }
        BatteryScreenPlateWorkshop entity = repository.save(convert.toEntity(saveDTO));
        return this.queryById(entity.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        try{
            ids.stream().forEach(id -> repository.deleteById(id));
        }catch (Exception e){
            throw new BizException("该数据已被删除,请刷新页面");
        }
    }

    @Override
    @SneakyThrows
    @Transactional(rollbackOn = Exception.class)
    public void importsEntity(MultipartFile file) {
        List<BatteryScreenPlateWorkshopExcelDTO> excelDto = new LinkedList<>();
        EasyExcel.read(file.getInputStream(), BatteryScreenPlateWorkshopExcelDTO.class, new ReadListener<BatteryScreenPlateWorkshopExcelDTO>() {
            @Override
            public void invoke(BatteryScreenPlateWorkshopExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();

        //校验数据是否重复
        Map<BatteryScreenPlateWorkshopExcelDTO, Long> countMap = excelDto.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        countMap.forEach((k, v) -> {
            if (v > 1) {
                Integer times = 0;
                for(Integer i =0;i<excelDto.size();i++){
                    BatteryScreenPlateWorkshopExcelDTO dto = excelDto.get(i);
                    if(k.equals(dto)){
                        times = i +1;
                    }
                }
                String errorMsg = "";
                errorMsg += "第"+times+"行数据重复,";
                throw new BizException(errorMsg+"电池类型+单玻+主栅间距+生产基地+生产车间+有效日期_起+有效日期止存在唯一性校验，不允许重复");
            }
        });

        importDataSave(excelDto);
        log.info("电池类型动态属性-网版 数据导入成功");

    }

    @Transactional(rollbackOn = Exception.class)
    public void importDataSave(List<BatteryScreenPlateWorkshopExcelDTO> excelDtoList) {
        // 保存或更新数据 查询数据库是否存在数据（存在既更新）

        for (Integer i = 1; i <= excelDtoList.size(); i++) {
            BatteryScreenPlateWorkshopExcelDTO excelDTO = excelDtoList.get(i - 1);
            String fmtString = "";

            try{
                if(StringUtils.isNotBlank(excelDTO.getEffectiveStartDateName())){
                    if(StringUtils.isNotBlank(excelDTO.getEffectiveStartDateName())
                            && excelDTO.getEffectiveStartDateName().contains("/")){
                        fmtString = "yyyy/MM/dd";
                    }else{
                        fmtString = "yyyy-MM-dd";
                    }
                    DateTime date = DateUtil.parse(excelDTO.getEffectiveStartDateName(),fmtString);
                    excelDTO.setEffectiveStartDate(date.toLocalDateTime().toLocalDate());
                }
                if(StringUtils.isNotBlank(excelDTO.getEffectiveEndDateName())){
                    if(StringUtils.isNotBlank(excelDTO.getEffectiveEndDateName())
                            && excelDTO.getEffectiveEndDateName().contains("/")){
                        fmtString = "yyyy/MM/dd";
                    }else{
                        fmtString = "yyyy-MM-dd";
                    }
                    DateTime date = DateUtil.parse(excelDTO.getEffectiveEndDateName(),fmtString);
                    excelDTO.setEffectiveEndDate(date.toLocalDateTime().toLocalDate());
                }
            }catch (Exception e){
                e.printStackTrace();
                log.error(ExceptionUtil.exceptionChainToString(e));
                throw new BizException("日期时间格式解析失败,请输入正确的日期格式:yyyy-MM-dd");
            }
            BatteryScreenPlateWorkshopSaveDTO saveDTO = convert.toSaveDTO(excelDTO);
            saveDTO.setIndex(i);
            this.save(saveDTO);
        }
    }

    @Override
    @SneakyThrows
    public void export(BatteryScreenPlateWorkshopQuery query, HttpServletResponse response) {
        List<BatteryScreenPlateWorkshopDTO> dto = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        //数据转换方法
        List<BatteryScreenPlateWorkshopExcelDTO> exportDTOS = convert.toExcelDTO(dto);
        ExcelUtils.setExportResponseHeader(response, "车间级网版切换导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "车间级网版切换导出_").head(BatteryScreenPlateWorkshopExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    @SneakyThrows
    private String fileUpload(File file) {
        MultipartFile multipartFile = FileUtil.getMultipartFile(file);
        return scpFileService.upload(multipartFile, false);
    }

    @Override
    @Cacheable(cacheNames = "BatteryScreenPlateWorkshopService_queryAllByCache")
    public List<BatteryScreenPlateWorkshopDTO> queryAllByCache() {
        List<BatteryScreenPlateWorkshop> all = repository.findAll();
        return convert.toDto(all);
    }
}
