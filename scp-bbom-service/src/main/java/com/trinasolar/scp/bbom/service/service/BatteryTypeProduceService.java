package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatteryTypeProduceDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeProduceNewQuery;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeProduceQuery;
import com.trinasolar.scp.bbom.domain.query.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryTypeProduceSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池类型动态属性-产出电池类型 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
public interface BatteryTypeProduceService {
    /**
     * 分页获取电池类型动态属性-产出电池类型
     *
     * @param query 查询对象
     * @return 电池类型动态属性-产出电池类型分页对象
     */
    Page<BatteryTypeProduceDTO> queryByPage(BatteryTypeProduceQuery query);

    /**
     * 查询产出电池类型
     *
     * @param query
     * @return
     */
    List<BatteryTypeProduceDTO> queryByAll(BatteryTypeProduceQuery query);

    /**
     * 全量查询 ERP替代项表分页列表
     *
     * @return
     */
    Page<ErpAlternateDesignatorDTO> queryErpByPageAll(ErpAlternateDesignatorQuery query);

    /**
     * 根据电池类型查询产出电池类型数据
     *
     * @param query
     * @return
     */
    List<BatteryTypeProduceDTO> queryByBatteryType(BatteryTypeProduceNewQuery query);


    /**
     * 产出电池类型页面-查询保存电池类型名称
     *
     * @param query
     * @return
     */
    List<BatteryTypeProduceDTO> querySaveProduce(BatteryTypeProduceQuery query);

    /**
     * 根据主键获取电池类型动态属性-产出电池类型详情
     *
     * @param id 主键
     * @return 电池类型动态属性-产出电池类型详情
     */
    BatteryTypeProduceDTO queryById(Long id);

    /**
     * 保存或更新电池类型动态属性-产出电池类型
     *
     * @param saveDTO 电池类型动态属性-产出电池类型保存对象
     * @return 电池类型动态属性-产出电池类型对象
     */
    BatteryTypeProduceDTO save(BatteryTypeProduceSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池类型动态属性-产出电池类型
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatteryTypeProduceQuery query, HttpServletResponse response);
}

