package com.trinasolar.scp.bbom.service.util;

import com.trinasolar.scp.bbom.domain.enums.CodeEnum;
import com.trinasolar.scp.bbom.domain.query.CodeSeqQuery;
import com.trinasolar.scp.bbom.service.feign.CodeFeign;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Component
@Slf4j
public class CodeUtil {
    static CodeFeign codeFeign;

    /**
     * 获取指定序列code的值
     */
    public static String getCode(CodeEnum codeEnum) {
        String seqCode = codeEnum.getCode();
        CodeSeqQuery codeSeqQuery = new CodeSeqQuery();
        codeSeqQuery.setSeqCode(seqCode);

        Results<String> codeSeq = codeFeign.createCodeSeq(codeSeqQuery).getBody();
        if (codeSeq == null) {
            throw new BizException("code生成错误: " + seqCode + " execuption: codeSeq is null");
        }
        String data = codeSeq.getData();
        if (data == null) {
            throw new BizException("code生成错误: " + seqCode + " execuption:" + codeSeq);
        }
        return data;
    }

    @Autowired
    public void setLovFeign(CodeFeign codeFeign) {
        setCodeFeign(codeFeign);
    }

    private static void setCodeFeign(CodeFeign codeFeign) {
        CodeUtil.codeFeign = codeFeign;
    }

}
