package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.ibm.dpf.common.domain.entity.User;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.MaterielMatchHeaderDEConvert;
import com.trinasolar.scp.bbom.domain.convert.MaterielMatchLineDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchHeader;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import com.trinasolar.scp.bbom.domain.entity.QMaterielMatchHeader;
import com.trinasolar.scp.bbom.domain.entity.QMaterielMatchLine;
import com.trinasolar.scp.bbom.domain.excel.MaterielMatchLineExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.ScreenMainReplacementExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.SlurryMainReplacementExcelDTO;
import com.trinasolar.scp.bbom.domain.query.*;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineAppointItemSaveDTO;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.DateUtil;
import com.trinasolar.scp.bbom.domain.utils.FileUtil;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.domain.vo.ScreenMainReplacementVO;
import com.trinasolar.scp.bbom.domain.vo.SlurryMainReplacementVO;
import com.trinasolar.scp.bbom.service.feign.BAPSFeign;
import com.trinasolar.scp.bbom.service.feign.BapsDto.Cell5AItemCodeDetailsDto;
import com.trinasolar.scp.bbom.service.feign.BapsDto.Cell5AItemCodeDto;
import com.trinasolar.scp.bbom.service.feign.BapsDto.Cell5AItemCodeListDto;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.bbom.service.repository.MaterielMatchHeaderRepository;
import com.trinasolar.scp.bbom.service.repository.MaterielMatchLineMatchStatusRepository;
import com.trinasolar.scp.bbom.service.repository.MaterielMatchLineRepository;
import com.trinasolar.scp.bbom.service.repository.StructuresRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.service.impl.scheduleEmail.SCPFileService;
import com.trinasolar.scp.bbom.service.util.StringTools;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.common.api.util.i18n.easyExcel.EasyExcelI18nHeaderParserUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Slf4j
@Service("materielMatchLineService")
@RequiredArgsConstructor
public class MaterielMatchLineServiceImpl implements MaterielMatchLineService {
    @Autowired
    private StructuresRepository structuresRepository;

    private static final QMaterielMatchLine qMaterielMatchLine = QMaterielMatchLine.materielMatchLine;

    private static final QMaterielMatchHeader qMaterielMatchHeader = QMaterielMatchHeader.materielMatchHeader;

    private final MaterielMatchLineDEConvert convert;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @PersistenceContext
    @Autowired
    private EntityManager entityManager;

    private final MaterielMatchLineRepository repository;

    private final MaterielMatchHeaderRepository headerRepository;

    private final MaterielMatchLineMatchStatusService materielMatchLineMatchStatusService;

    private final MaterielMatchLineMatchStatusRepository matchStatusRepository;

    private final StructuresService structuresService;

    private final ItemsService itemsService;

    private final MaterielMatchHeaderDEConvert headerDEConvert;

    private final BomFeign bomFeign;

    private final MaterielMatchLineMatchStatusService matchStatusService;

    private final BAPSFeign bapsFeign;

    private final JPAQueryFactory jpaQueryFactory;

    private final MailService mailService;

    private final SystemFeign systemFeign;

    private final SlurryInformationService informationService;

    private final ComponentsService componentsService;


    @Autowired
    @Lazy
    private MaterielMatchHeaderService headerService;

    @Autowired
    private SCPFileService scpFileService;

    @Autowired
    private BomService bomService;

    private static final Joiner groupKeyJoiner = Joiner.on("_").useForNull("null");

    @Value("${matchItem.dynamicKey}")
    public String matchItemDynamicKey;

    @Override
    public Page<MaterielMatchLineDTO> queryByPage(MaterielMatchLineQuery query) {
        User user = null;
        if (StringUtils.isNotEmpty(query.getUserId())) {
            user = UserUtil.getUserById(query.getUserId());
        } else {
            user = UserUtil.getUser();
        }
        String userId = user.getId();
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        buildWhereByLine(booleanBuilder, query);
        //查询用户数据权限 研发/量产
        List<DataPrivilegeDTO> privilegeDTOList = informationService.getDataPrivilegeDTOS(userId, LovHeaderCodeConstant.ENG_MFG);
        if (CollectionUtils.isNotEmpty(privilegeDTOList)) {
            List<Long> ids = privilegeDTOList.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
            if (StringUtils.isEmpty(query.getIsCatchProduction())) {
                if (!ids.contains(-1L)) {
                    booleanBuilder.and(qMaterielMatchLine.isCatchProductionId.in(ids));
                }
            }
        } else {
            booleanBuilder.and(qMaterielMatchLine.isCatchProductionId.in(-1L));
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, Integer.MAX_VALUE, sort);

        Page<MaterielMatchLine> page = repository.findAll(booleanBuilder, pageable);

        List<MaterielMatchLineDTO> matchLineDTOS = Lists.newArrayList(page.getContent()).parallelStream().map(convert::toDto).collect(Collectors.toList());

        //赋值与匹配计划类型字段
        List<Long> headerIds = new ArrayList<>(matchLineDTOS.stream().collect(Collectors.groupingBy(MaterielMatchLineDTO::getHeaderId)).keySet());
        QMaterielMatchHeader materielMatchHeader = QMaterielMatchHeader.materielMatchHeader;
        List<MaterielMatchHeader> headerList = jpaQueryFactory.select(materielMatchHeader).from(materielMatchHeader)
                .where(materielMatchHeader.id.in(headerIds).and(materielMatchHeader.isDeleted.eq(0))).fetch();
        Map<Long,List<MaterielMatchHeader>> headerMap = headerList.stream().collect(Collectors.groupingBy(MaterielMatchHeader::getId));
        //枚举转换
        matchLineDTOS.forEach(item->{
            List<MaterielMatchHeader> headers = headerMap.get(item.getHeaderId());
            if(CollectionUtils.isNotEmpty(headers) && "plan".equals(headers.get(0).getPlanType())) {
                item.setPlanType("投产计划");
            }else if(CollectionUtils.isNotEmpty(headers) && "instock".equals(headers.get(0).getPlanType())) {
                item.setPlanType("入库计划");
            }
            if(StringUtils.isBlank(item.getPlanType())){
                item.setPlanType("");
            }
        });
        if(StringUtils.isNotBlank(query.getPlanType()) && "Production Plan".equals(query.getPlanType())){
            query.setPlanType("投产计划");
        }else if(StringUtils.isNotBlank(query.getPlanType()) && "Instock Plan".equals(query.getPlanType())){
            query.setPlanType("入库计划");
        }
        if(StringUtils.isNotBlank(query.getPlanType())){
            matchLineDTOS = matchLineDTOS.stream().filter(item->item.getPlanType().equals(query.getPlanType())).collect(Collectors.toList());
        }

        List<MaterielMatchLineDTO> sortList = getMatchLineDTOS(matchLineDTOS);
        List<MaterielMatchLineDTO> result = sortList.stream().skip((long) (query.getPageNumber() - 1) * query.getPageSize()).limit(query.getPageSize()).collect(Collectors.toList());

        result = result.stream().sorted(Comparator.comparing(MaterielMatchLineDTO::getUpdatedTime).reversed()).collect(Collectors.toList());
        Pageable pageable2 = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        return new PageImpl(result, pageable2, sortList.size());
        //return new PageImpl(sortList.subList((query.getPageNumber() - 1) * query.getPageSize(), Math.min(query.getPageNumber() * query.getPageSize(), sortList.size())), pageable, sortList.size());
    }

    private List<MaterielMatchLineDTO> getMatchLineDTOS(List<MaterielMatchLineDTO> matchLineDTOS) {
        Map<String, List<MaterielMatchLineDTO>> lineMap = matchLineDTOS.stream().collect(Collectors.groupingBy(MaterielMatchLineDTO::getGroupFiledByQuery));
        List<MaterielMatchLineDTO> matchLineDTOList = new ArrayList<>();
        Map<String, LovLineDTO> gridLov = LovUtils.getAllByHeaderCode("7A01500100122");
        List<Long> lineIdList = matchLineDTOS.stream().map(MaterielMatchLineDTO::getId).collect(Collectors.toList());
        Map<Long, List<MaterielMatchLineMatchStatusDTO>> matchStatusMap = getMatchStatusMap(lineIdList);

        if (CollectionUtils.isNotEmpty(matchLineDTOS)) {
            List<MaterielMatchLineDTO> collect = lineMap.values().parallelStream().map(listDto -> {
                listDto.forEach(value -> {
                    value.setMatchCodes(matchStatusMap.get(value.getId()));
                });
                final BigDecimal[] total = {BigDecimal.ZERO};
                List<MaterielMatchLineDTO> sortedLines = listDto.stream().sorted(Comparator.comparing(MaterielMatchLineDTO::getScheduleDate))
                        .peek(p -> {
                            total[0] = total[0].add(p.getCellQty());
                        }).collect(Collectors.toList());

                MaterielMatchLineDTO lineGroupDTO = convert.deepCopy(sortedLines.get(0));
                lineGroupDTO.setStartTimeStart(lineGroupDTO.getScheduleDate());
                lineGroupDTO.setDetailLines(sortedLines);
                lineGroupDTO.setCellQty(BigDecimal.ZERO);
                lineGroupDTO.setStartTimeEnd(sortedLines.get(sortedLines.size() - 1).getScheduleDate());

                lineGroupDTO.setCellQty(total[0]);
                LovLineDTO positiveLov = gridLov.get(lineGroupDTO.getPositiveElectrodeScreenFineGrid());
                LovLineDTO negativeLov = gridLov.get(lineGroupDTO.getNegativeElectrodeScreenFineGrid());
                if(Objects.nonNull(positiveLov)) {
                    lineGroupDTO.setPositiveElectrodeScreenFineGridName(positiveLov.getLovName());
                }
                if(Objects.nonNull(negativeLov)) {
                    lineGroupDTO.setNegativeElectrodeScreenFineGridName(negativeLov.getLovName());
                }
                return lineGroupDTO;
            }).collect(Collectors.toList());
            matchLineDTOList.addAll(collect);
        }

        List<MaterielMatchLineDTO> sortList = matchLineDTOList.stream().sorted(Comparator.comparing(MaterielMatchLineDTO::getScheduleDate)).collect(Collectors.toList());


        return sortList;
    }

    private void buildWhereByLine(BooleanBuilder booleanBuilder, MaterielMatchLineQuery query) {
        booleanBuilder.and(qMaterielMatchLine.splitFlag.eq(YesOrNoEnum.YES.getCode()).or(qMaterielMatchLine.splitFlag.isNull()));
        if (StringUtils.isNotEmpty(query.getMatchStatus())) {
            booleanBuilder.and(qMaterielMatchLine.matchStatus.eq(query.getMatchStatus()));
        }
        if (CollectionUtils.isNotEmpty(query.getMatchStatusList())) {
            booleanBuilder.and(qMaterielMatchLine.matchStatus.in(query.getMatchStatusList()));
        }
        if (null != query.getIsOverseaId()) {
            booleanBuilder.and(qMaterielMatchLine.isOverseaId.eq(query.getIsOverseaId()));
        }
        if (StringUtils.isNotEmpty(query.getIsCatchProduction())) {
            booleanBuilder.and(qMaterielMatchLine.isCatchProduction.eq(query.getIsCatchProduction()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qMaterielMatchLine.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getAesthetics())) {
            booleanBuilder.and(qMaterielMatchLine.aesthetics.eq(query.getAesthetics()));
        }
        if (StringUtils.isNotEmpty(query.getTransparentDoubleGlass())) {
            booleanBuilder.and(qMaterielMatchLine.transparentDoubleGlass.eq(query.getTransparentDoubleGlass()));
        }
        if (StringUtils.isNotEmpty(query.getSpecialArea())) {
            booleanBuilder.and(qMaterielMatchLine.specialArea.eq(query.getSpecialArea()));
        }
        if (StringUtils.isNotEmpty(query.getHTrace())) {
            booleanBuilder.and(qMaterielMatchLine.hTrace.eq(query.getHTrace()));
        }
        if (StringUtils.isNotEmpty(query.getPcsSourceType())) {
            booleanBuilder.and(qMaterielMatchLine.pcsSourceType.eq(query.getPcsSourceType()));
        }
        if (StringUtils.isNotEmpty(query.getPcsSourceLevel())) {
            booleanBuilder.and(qMaterielMatchLine.pcsSourceLevel.eq(query.getPcsSourceLevel()));
        }
        if (StringUtils.isNotEmpty(query.getIsSpecialRequirements())) {
            booleanBuilder.and(qMaterielMatchLine.isSpecialRequirements.eq(query.getIsSpecialRequirements()));
        }
        if (StringUtils.isNotEmpty(query.getScreenManufacturer())) {
            booleanBuilder.and(qMaterielMatchLine.screenManufacturer.eq(query.getScreenManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconMaterialManufacturer())) {
            booleanBuilder.and(qMaterielMatchLine.siliconMaterialManufacturer.eq(query.getSiliconMaterialManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryManufacturer())) {
            booleanBuilder.and(qMaterielMatchLine.batteryManufacturer.eq(query.getBatteryManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getSilverSlurryManufacturer())) {
            booleanBuilder.and(qMaterielMatchLine.silverSlurryManufacturer.eq(query.getSilverSlurryManufacturer()));
        }
        if (StringUtils.isNotEmpty(query.getLowResistance())) {
            booleanBuilder.and(qMaterielMatchLine.lowResistance.eq(query.getLowResistance()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferPurchaseMethod())) {
            booleanBuilder.and(qMaterielMatchLine.siliconWaferPurchaseMethod.eq(query.getSiliconWaferPurchaseMethod()));
        }
        if (StringUtils.isNotEmpty(query.getDemandPlace())) {
            booleanBuilder.and(qMaterielMatchLine.demandPlace.like("%" + query.getDemandPlace() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qMaterielMatchLine.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qMaterielMatchLine.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qMaterielMatchLine.workunit.eq(query.getWorkunit()));
        }

        if (StringUtils.isNotEmpty(query.getProcessCategory())) {
            booleanBuilder.and(qMaterielMatchLine.processCategory.eq(query.getProcessCategory()));
        }
        //产品等级
        if (StringUtils.isNotEmpty(query.getProductionGrade())) {
            booleanBuilder.and(qMaterielMatchLine.productionGrade.eq(query.getProductionGrade()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qMaterielMatchLine.itemCode.eq(query.getItemCode()));
        }
        if (query.getMonth() != null) {
            booleanBuilder.and(qMaterielMatchLine.oldMonth.eq(query.getMonth()));
        }
        if (query.getLine() != null) {
            booleanBuilder.and(qMaterielMatchLine.line.eq(query.getLine()));
        }
        if (StringUtils.isNotEmpty(query.getPositiveElectrodeScreenFineGrid())) {
            booleanBuilder.and(qMaterielMatchLine.positiveElectrodeScreenFineGrid.eq(query.getPositiveElectrodeScreenFineGrid()));
        }
        if (StringUtils.isNotEmpty(query.getNegativeElectrodeScreenFineGrid())) {
            booleanBuilder.and(qMaterielMatchLine.negativeElectrodeScreenFineGrid.eq(query.getNegativeElectrodeScreenFineGrid()));
        }
    }

    private void buildWhere(BooleanBuilder booleanBuilder, MaterielMatchLineQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qMaterielMatchLine.id.eq(query.getId()));
        }
        if (query.getHeaderId() != null) {
            booleanBuilder.and(qMaterielMatchLine.headerId.eq(query.getHeaderId()));
        }
        if (query.getScheduleDate() != null) {
            booleanBuilder.and(qMaterielMatchLine.scheduleDate.eq(query.getScheduleDate()));
        }
        if (query.getScheduleQty() != null) {
            booleanBuilder.and(qMaterielMatchLine.scheduleQty.eq(query.getScheduleQty()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qMaterielMatchLine.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getMatchStatus())) {
            booleanBuilder.and(qMaterielMatchLine.matchStatus.eq(query.getMatchStatus()));
        }
        if (StringUtils.isNotEmpty(query.getIsCatchProduction())) {
            booleanBuilder.and(qMaterielMatchLine.isCatchProduction.eq(query.getIsCatchProduction()));
        }
        if (StringUtils.isNotEmpty(query.getScreenPlateItemCode())) {
            booleanBuilder.and(qMaterielMatchLine.screenPlateItemCode.eq(query.getScreenPlateItemCode()));
        }
        if (query.getSwitchEndDate() != null) {
            booleanBuilder.and(qMaterielMatchLine.switchEndDate.eq(query.getSwitchEndDate()));
        }
        if (query.getSwitchStartDate() != null) {
            booleanBuilder.and(qMaterielMatchLine.switchStartDate.eq(query.getSwitchStartDate()));
        }
        if (query.getLine() != null) {
            booleanBuilder.and(qMaterielMatchLine.line.eq(query.getLine()));
        }
        if (query.getCellQty() != null) {
            booleanBuilder.and(qMaterielMatchLine.cellQty.eq(query.getCellQty()));
        }
        if (StringUtils.isNotEmpty(query.getRemark())) {
            booleanBuilder.and(qMaterielMatchLine.remark.eq(query.getRemark()));
        }
        if (StringUtils.isNotEmpty(query.getAlternateBomDesignator())) {
            booleanBuilder.and(qMaterielMatchLine.alternateBomDesignator.eq(query.getAlternateBomDesignator()));
        }
    }

    @Override
    public MaterielMatchLineDTO queryById(Long id) {
        MaterielMatchLine queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public MaterielMatchLineDTO save(MaterielMatchLineSaveDTO saveDTO) {
        MaterielMatchLine newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new MaterielMatchLine());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        // 保存匹配状态
        materielMatchLineMatchStatusService.saveByLine(newObj, saveDTO.getMatchCodes());

        return this.queryById(newObj.getId());
    }

    @Override
    public void deleteMatchStatusByIds(List<Long> ids) {
        //ids.stream().forEach(id -> repository.deleteById(id));
        // 删除匹配状态
        materielMatchLineMatchStatusService.deleteByLineIds(ids);
    }


    @Override
    @SneakyThrows
    public void export(MaterielMatchLineQuery query, HttpServletResponse response) {
        List<MaterielMatchLineDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        dtos.stream().forEach(line -> {
            if (CollectionUtils.isNotEmpty(line.getMatchCodes())) {
                Map<String, MaterielMatchLineMatchStatusDTO> map = line.getMatchCodes().stream().collect(Collectors.toMap(MaterielMatchLineMatchStatusDTO::getItemCode, Function.identity(), (i1, i2) -> i1));
                if (StringUtils.isNotEmpty(line.getItemCode()) && map.containsKey(line.getItemCode())) {
                    line.setItemDesc(map.get(line.getItemCode()).getItemDesc());
                    line.setRoute("yes".equals(map.get(line.getItemCode()).getRoute()) ? "是" : "否");
                    line.setCertifiedModels(map.get(line.getItemCode()).getCertifiedModels());
                }
            }
        });
        List<MaterielMatchLineExcelDTO> exportDTOS = convert.toExcelDTO(dtos);

        ExcelUtils.setExportResponseHeader(response, "电池料号匹配导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "排产明细").head(MaterielMatchLineExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    @Override
    public List<MaterielMatchLineDTO> findByHeaderId(Long headerId) {
        List<MaterielMatchLineDTO> matchLineDTOS = convert.toDto(Lists.newArrayList(repository.findAll(qMaterielMatchLine.headerId.eq(headerId))));
        return Optional.ofNullable(matchLineDTOS).orElse(new ArrayList<>());
    }

    @Override
    public List<MaterielMatchLineDTO> saveBatch(List<MaterielMatchLineDTO> newLineDTOS) {
        List<MaterielMatchLineDTO> addLinesList = new CopyOnWriteArrayList<>();
        convert.dtoToSaveDTO(newLineDTOS).forEach(p -> {
            addLinesList.add(save(p));
        });
        return addLinesList;
    }

    @Override
    public List<MaterielMatchLineDTO> queryByHeaderId(Long headerId, String matchStatus, String isCatchProduction) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qMaterielMatchLine.headerId.eq(headerId));
        booleanBuilder.and(qMaterielMatchLine.splitFlag.ne("N").or(qMaterielMatchLine.splitFlag.isNull()));
        if (StringUtils.isNotEmpty(matchStatus)) {
            booleanBuilder.and(qMaterielMatchLine.matchStatus.eq(matchStatus));
        }
        if (StringUtils.isNotEmpty(isCatchProduction)) {
            booleanBuilder.and(qMaterielMatchLine.isCatchProduction.eq(isCatchProduction));
        }
        Iterable<MaterielMatchLine> matchLines = repository.findAll(booleanBuilder);
        List<MaterielMatchLineDTO> matchLineDTOS = convert.toDto(Lists.newArrayList(matchLines));
        // 查询匹配状态
        matchLineDTOS.forEach(i -> {
            List<MaterielMatchLineMatchStatusDTO> matchStatusDTOS = materielMatchLineMatchStatusService.queryByLineId(i.getId());
            i.setMatchCodes(matchStatusDTOS);
        });
        return matchLineDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void appointItemCode(List<MaterielMatchLineAppointItemSaveDTO> saveDTOs) {
        List<MaterielMatchLine> matchLineList = new ArrayList<>();
        for (MaterielMatchLineAppointItemSaveDTO saveDTO : saveDTOs) {
            MaterielMatchLine materielMatchLine = repository.findById(saveDTO.getId()).orElse(null);
            if (materielMatchLine == null) {
                throw new RuntimeException("未找到匹配行");
            }
            String itemCode = saveDTO.getItemCode();
            if(StringUtils.isBlank(itemCode)){
                continue;
            }
            materielMatchLine.setItemCode(itemCode);
            materielMatchLine.setMatchStatus(MaterielMatchLineDTO.MatchStatus.APPOINT_MATCH.getCode());
            // 清除问题点
            materielMatchLine.setRemark("");

//            materielMatchLine.setAlternateBomDesignator(saveDTO.getAlternateBomDesignator());
            // 替代项重新处理
            //获取库存组织
            LovLineDTO workShop = LovUtils.get("work_shop", materielMatchLine.getWorkshop());
            //根据库存组织查询
            LovLineDTO organization = LovUtils.get("inventory_organization", workShop.getAttribute10());
            if (organization == null) {
                throw new BizException("bbom_notFound_organization_workshop", materielMatchLine.getWorkshop());
            }
            String orgId = organization.getAttribute1();
            ErpAlternateDesignatorDTO erpAlternateDesignator = bomService.findErpAlternateDesignator(Long.valueOf(orgId), workShop.getLovValue());
            String alternateDesignatorCode = null;
            if (erpAlternateDesignator != null) {
                alternateDesignatorCode = erpAlternateDesignator.getAlternateDesignatorCode();
            } else {
                alternateDesignatorCode = saveDTO.getAlternateBomDesignator();
            }
            ItemsDTO oneByItemCode = itemsService.findOneByItemCode(itemCode);
            if (StringUtils.isNotBlank(alternateDesignatorCode)) {
                // 查找Bom bom 存在才放入
                // 将ItemCode转换为ItemId
                StructuresDTO structuresDTO = structuresService.findByAlternateDesignator(orgId, oneByItemCode.getSourceItemId(), alternateDesignatorCode);
                if (structuresDTO != null) {
                    materielMatchLine.setAlternateBomDesignator(alternateDesignatorCode);
                }
            }

            //手工指定标识
            materielMatchLine.setHandWorkFlag("Y");

            materielMatchLine.setItemDesc(oneByItemCode.getItemDesc());
            ErpOperatRouteDTO operatRouteDTO = bomService.findBy5AAndAlternateRoutingDesignator(itemCode, materielMatchLine.getAlternateBomDesignator());
            if (operatRouteDTO == null) {
                materielMatchLine.setRoute("否");
            } else {
                materielMatchLine.setRoute("是");
            }
            materielMatchLine.setCertifiedModels(oneByItemCode.getSegment28());

            repository.save(materielMatchLine);
            matchLineList.add(materielMatchLine);
        }
        bapsFeign.changeDataBy5AMatch(pushBapsCell5AItemCode(matchLineList));
    }

    public Cell5AItemCodeListDto pushBapsCell5AItemCode(List<MaterielMatchLine> matchLineList) {
        Cell5AItemCodeListDto itemCodeListDto = new Cell5AItemCodeListDto();
        List<Cell5AItemCodeDto> itemDtos = new ArrayList<>();
        //过滤出5A数据
        List<MaterielMatchLine> filterMatchLine = matchLineList.stream().filter(p -> "Y".equals(p.getSplitFlag()) || StringUtils.isNotEmpty(p.getItemCode())).collect(Collectors.toList());
        List<Long> filterIds = filterMatchLine.stream().map(MaterielMatchLine::getId).collect(Collectors.toList());
        List<MaterielMatchLine> emptyMatchLine = matchLineList.stream().filter(ele -> !filterIds.contains(ele.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(emptyMatchLine)) {
            log.info("二期料号匹配 pushBapsCell5AItemCode 被过滤的Size：{} emptyMatchLine：{}", emptyMatchLine.size(), JSON.toJSONString(emptyMatchLine));
        }
        //bapsFeign
        if (CollectionUtils.isNotEmpty(filterMatchLine)) {
            Map<Long, List<MaterielMatchLine>> listMap = matchLineList.stream().collect(Collectors.groupingBy(MaterielMatchLine::getCellProductionPlanId));
            listMap.keySet().stream().forEach(key -> {
                Cell5AItemCodeDto cell5AItemCodeDto = new Cell5AItemCodeDto();
                cell5AItemCodeDto.setId(key);

                List<Cell5AItemCodeDetailsDto> codeDetailsDtos = new ArrayList<>();
                List<MaterielMatchLine> matchLines = listMap.get(key);
                if(CollectionUtils.isNotEmpty(matchLines)){
                    MaterielMatchHeader materielMatchHeader = headerRepository.findById(matchLines.get(0).getHeaderId()).orElse(null);
                    cell5AItemCodeDto.setPlanType(materielMatchHeader.getPlanType());
                }
                matchLines.stream().forEach(value -> {
                    Cell5AItemCodeDetailsDto detailsDto = new Cell5AItemCodeDetailsDto();
                    detailsDto.setBbomId(value.getId());
                    detailsDto.setNumberLine(value.getLine());
                    detailsDto.setQtyPc(value.getCellQty());
                    //null==value.getTotalLine() 说明没有匹配到网版切换 没有拆分
//                    detailsDto.setRate(value.getLine().divide(null == value.getTotalLine() ? value.getLine() : value.getTotalLine(), 2, RoundingMode.HALF_UP));
                    detailsDto.setRate(BigDecimal.ONE);
                    detailsDto.setItemCode(value.getItemCode());
                    /** 电池投产计划中增加7A料号信息 begin **/
                    //背面细栅
                    detailsDto.setBackFineGrid(value.getNegativeElectrodeScreenFineGrid());
                    //正面细栅
                    detailsDto.setFrontFineGrid(value.getPositiveElectrodeScreenFineGrid());
                    //硅片尺寸
                    detailsDto.setSiliconWaferSize(value.getWaferCategory());
                    //硅片厚度
                    detailsDto.setSiliconWaferThickness(value.getSiliconWaferValue());
                    /** 电池投产计划中增加7A料号信息 end **/
                    codeDetailsDtos.add(detailsDto);
                });
                cell5AItemCodeDto.setDetailsDtos(codeDetailsDtos);
                itemDtos.add(cell5AItemCodeDto);
            });
            itemCodeListDto.setItemDtos(itemDtos);
        } else {
            log.info("二期料号匹配 pushBapsCell5AItemCode 过滤出5A数据为空 so skip... Size：{} matchLineList：{}", matchLineList.size(), JSON.toJSONString(matchLineList));
        }
        return itemCodeListDto;
    }

    @Override
    public List<MaterielMatchLineDTO> queryMaterielMatchLineList() {
        //查询 当前月的数据以及下个月的数据
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qMaterielMatchLine.month.eq(DateUtil.getMonth(LocalDate.now()))
                .or(qMaterielMatchLine.month.eq(DateUtil.getMonth(LocalDate.now().plusMonths(1)))));
        Iterable<MaterielMatchLine> matchLines = repository.findAll(booleanBuilder);
        List<MaterielMatchLineDTO> matchLineDTOS = convert.toDto(Lists.newArrayList(matchLines));
        return matchLineDTOS;
    }

    @Override
    public List<MaterielMatchLineDTO> queryMatchLineByHeaderId(MaterielMatchHeaderDTO headerDTO) {
        //查询isDelete=0的数据
        Iterable<MaterielMatchLine> matchLines = repository.findAll(qMaterielMatchLine.headerId.eq(headerDTO.getId()));
        List<MaterielMatchLineDTO> matchLineDTOS = convert.toDto(Lists.newArrayList(matchLines));
        return matchLineDTOS;
    }

    @Override
    public List<MaterielMatchLineDTO> queryMatchLineByVersion(MaterielMatchHeaderDTO headerDTO, String version, String month, Long isOverseaId) {
        //查询isDelete=1的数据
        List<MaterielMatchLineDTO> matchLineDTOS = convert.toDto(repository.findLineByVersion(version, month, isOverseaId));
        return matchLineDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByHeadId(List<MaterielMatchHeaderDTO> headerDTOList) {
        headerDTOList.stream().forEach(y -> {
            Iterable<MaterielMatchLine> matchLines = repository.findAll(qMaterielMatchLine.headerId.eq(y.getId()));
            List<MaterielMatchLine> matchLineList = Lists.newArrayList(matchLines);
            if (CollectionUtils.isNotEmpty(matchLineList)) {
                //行删除
                repository.deleteAll(matchLineList);

                List<Long> lineIds = matchLineList.stream().map(MaterielMatchLine::getId).collect(Collectors.toList());
                materielMatchLineMatchStatusService.deleteByLineIds(lineIds);
            }
        });
        headerRepository.deleteAll(headerDEConvert.toEntity(headerDTOList));
    }

    @Override
    public void addMatchLineInfo(MaterielMatchHeader header, MaterielMatchHeaderDTO headerDTO,
                                 Map<String, StructuresDTO> structuresDTOMap, Map<String, ErpOperatRouteDTO> erpOperatRouteMap) {
        Set<Long> set = new HashSet<>();
        LovLineDTO lovLineDTO = LovUtils.get("work_shop", headerDTO.getWorkshop());
        if (StringUtils.isNotEmpty(lovLineDTO.getAttribute10())) {
            String orgCode = lovLineDTO.getAttribute10();
            LovLineDTO orgLov = LovUtils.get("inventory_organization", orgCode);
            if (null != orgLov) {
                String orgId = orgLov.getAttribute1();
                set.add(Long.valueOf(orgId));
            }
        }
        Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap = new HashMap<>();
        if (!set.isEmpty()) {
            //获取bom替代项集合
            ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
            designatorQuery.setOrganizationIds(new ArrayList<>(set));
            List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryByOrganizationIds(designatorQuery).getBody().getData();
            designatorMap = designatorDTOList.stream().collect(Collectors.groupingBy(ErpAlternateDesignatorDTO::getOrganizationId));
        }
        List<MaterielMatchLineDTO> lineDTOList = buildMatchLine(headerDTO, headerDTO.getPlanDTOList(), designatorMap, structuresDTOMap, erpOperatRouteMap);
        lineDTOList.stream().forEach(x -> {
            x.setHeaderId(header.getId());
        });
        saveBatch(lineDTOList);
    }

    private List<MaterielMatchLineDTO> buildMatchLine(MaterielMatchHeaderDTO materielMatchHeaderDTO,
                                                      List<CellPlanLineDTO> cellPlanLineDTOList,
                                                      Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap,
                                                      Map<String, StructuresDTO> structuresDTOMap
            , Map<String, ErpOperatRouteDTO> erpOperatRouteMap) {

        List<MaterielMatchLineDTO> matchLineDTOList = new ArrayList<>();
        for (CellPlanLineDTO cellPlanLineDTO : cellPlanLineDTOList) {
            MaterielMatchLineDTO materielMatchLineDTO = headerService.getMaterielMatchLineDTOByLine(materielMatchHeaderDTO, cellPlanLineDTO, designatorMap, structuresDTOMap, erpOperatRouteMap);
            matchLineDTOList.add(materielMatchLineDTO);
        }
        return matchLineDTOList;
    }

    public String getMaxFinalVersion(String month, Long isOverseaId) {
        return repository.getMaxFinalVersion(month, isOverseaId);
    }

    public void deleteByMonth(String month, Long isOverseaId,List<Long> headerIds) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qMaterielMatchLine.oldMonth.eq(month));
        booleanBuilder.and(qMaterielMatchLine.isOverseaId.eq(isOverseaId));
        if(CollectionUtils.isNotEmpty(headerIds)) {
            booleanBuilder.and(qMaterielMatchLine.headerId.in(headerIds));
            Iterable<MaterielMatchLine> matchLines = repository.findAll(booleanBuilder);
            repository.deleteAll(matchLines);
        }
    }

    @Override
    public void sendMailByqueryList() {
        exportEmail();
    }

    /**
     * 刷新替代项(匹配了料号,但是没有替代项的)
     *
     * @param matchId
     * @param month
     * @param structuresDTOMap
     * @param designatorMap
     */
    @Override
    public void flushAlternateBomDesignator(Long matchId, String month, Map<String, StructuresDTO> structuresDTOMap, Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap) {
        JPAQuery<MaterielMatchLine> where = jpaQueryFactory.select(QMaterielMatchLine.materielMatchLine).
                from(QMaterielMatchLine.materielMatchLine).
                where(
                        // handWorkFlag 指定料号标识 为空或者不为Y
                        QMaterielMatchLine.materielMatchLine.handWorkFlag.isNull()
                                .or(QMaterielMatchLine.materielMatchLine.handWorkFlag.ne("Y")
                                        .or(QMaterielMatchLine.materielMatchLine.handWorkFlag.isEmpty()))
                                // splitFlag 拆分标识 为空或者为Y
                                .and(QMaterielMatchLine.materielMatchLine.splitFlag.isNull()
                                        .or(QMaterielMatchLine.materielMatchLine.splitFlag.eq("Y")
                                                .or(QMaterielMatchLine.materielMatchLine.splitFlag.isEmpty())))
                );
        if (null != matchId) {
            where.where(QMaterielMatchLine.materielMatchLine.headerId.eq(matchId));
        }
        if (StringUtils.isNotEmpty(month)) {
            where.where(QMaterielMatchLine.materielMatchLine.oldMonth.eq(month));
        }
        where.where(
                QMaterielMatchLine.materielMatchLine.itemCode.isNotEmpty().and(
                        QMaterielMatchLine.materielMatchLine.alternateBomDesignator.isEmpty()));
        List<MaterielMatchLine> fetch = where.fetch();

        List<MaterielMatchLine> saveList = new ArrayList<>();
        for (MaterielMatchLine materielMatchLine : fetch) {
            ItemsDTO itemsDTO = itemsService.getOneByItemCode(materielMatchLine.getItemCode());
            String alternateDesignatorCode = headerService.getAlternateDesignatorCode(materielMatchLine.getWorkshop(), itemsDTO.getSourceItemId(), designatorMap, structuresDTOMap);
            if (StringUtils.isNotEmpty(alternateDesignatorCode)) {
                materielMatchLine.setAlternateBomDesignator(alternateDesignatorCode);
                saveList.add(materielMatchLine);
            }
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            repository.saveAll(saveList);
        }
    }

    @Override
    public void flushItemDesc() {
        List<MaterielMatchLine> saveList = new LinkedList<>();
        jpaQueryFactory.select(qMaterielMatchLine)
                .from(qMaterielMatchLine)
                .where(qMaterielMatchLine.itemCode.isNotEmpty())
                .where(qMaterielMatchLine.itemDesc.isEmpty().or(qMaterielMatchLine.itemDesc.isNull()))
                .fetch().forEach(p -> {
                    ItemsDTO itemsDTO = itemsService.getOneByItemCode(p.getItemCode());
                    if (null != itemsDTO) {
                        p.setItemDesc(itemsDTO.getItemDesc());
                        saveList.add(p);
                    }
                });
        if (CollectionUtils.isNotEmpty(saveList)) {
            repository.saveAll(saveList);
        }
    }

    @Override
    public void clearItemCode(List<Long> ids) {
        List<MaterielMatchLine> matchLineList = new ArrayList<>();
        for (Long id : ids) {
            MaterielMatchLine materielMatchLine = repository.findById(id).orElse(null);
            if (materielMatchLine == null) {
                throw new RuntimeException("未找到匹配行");
            }
            materielMatchLine.setItemCode(null);
            materielMatchLine.setMatchStatus(MaterielMatchLineDTO.MatchStatus.NON_MATCH.getCode());
            // 清除问题点
            materielMatchLine.setRemark("");
            materielMatchLine.setAlternateBomDesignator(null);
            //手工指定标识
            materielMatchLine.setHandWorkFlag("N");

            materielMatchLine.setItemDesc(null);
            materielMatchLine.setRoute(null);
            materielMatchLine.setCertifiedModels(null);
            materielMatchLine.setItemMatchStatus(null);

            materielMatchLine.setScreenPlateItemCode(null);
            materielMatchLine.setScreenPlateItemCodeDesc(null);
            materielMatchLine.setScreenPlateItemCodeFineGrid(null);
            materielMatchLine.setScreenPlateItemCodeDescFineGrid(null);
            materielMatchLine.setScreenPlateCodeFilter(null);
            materielMatchLine.setScreenPlateCodeDescFilter(null);
            materielMatchLine.setScreenPlateCodeFilterFineGrid(null);
            materielMatchLine.setScreenPlateCodeDescFilterFineGrid(null);

            materielMatchLine.setSwitchStartDate(null);
            materielMatchLine.setSwitchEndDate(null);

            materielMatchLine.setSplitFlag(null);

            repository.save(materielMatchLine);
            matchLineList.add(materielMatchLine);
        }
        bapsFeign.changeDataBy5AMatch(pushBapsCell5AItemCode(matchLineList));
    }

    private static void buildPath(MaterielMatchLineDTO dto, String field, BooleanBuilder booleanBuilder) {
        String fieldValue = (String) ReflectUtil.getFieldValue(dto, field);
        StringPath path = (StringPath) ReflectUtil.getFieldValue(qMaterielMatchLine, field);
        if (fieldValue == null) {
            booleanBuilder.and(path.isNull());
        } else {
            booleanBuilder.and(path.eq(fieldValue));
        }
    }

    @Override
    public String getInheritItemCode(MaterielMatchLineDTO dto,Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        Map<String, LovLineDTO> inheritLovHashMap = LovUtils.getAllByHeaderCode("BBOM_CELL_MATCH_RULE");
        TreeMap<String, LovLineDTO> inheritLovMap = new TreeMap<>(inheritLovHashMap);
        StringBuilder redisKey = new StringBuilder();
        StringBuilder dynamicRedisKey = new StringBuilder();
        for(String key : inheritLovMap.keySet()){
            LovLineDTO value = inheritLovMap.get(key);
            if("Y".equals(value.getEnableFlag())){
                String upCase = StrUtil.toCamelCase(value.getLovName());
                if(StringUtils.isNotBlank(value.getAttribute1())){
                    upCase = value.getAttribute1();
                }
                Object dtoValue = StringTools.getValueByLineDTOEntity(dto, upCase);

                String dtoValueString = "";
                if(Objects.nonNull(dtoValue)){
                    dtoValueString = dtoValue.toString();
                }
                redisKey.append(dtoValueString);
                // p2_2444 电池BOM-料号匹配继承逻辑优化 增加动态属性判断
                if(StringUtils.isEmpty(value.getAttribute1()) || !matchItemDynamicKey.equals(value.getAttribute1())){
                    dynamicRedisKey.append(dtoValueString);
                }
            }
        }

        //缓存优化

//        String itemCode = inheritCache.get(redisKey.toString());
        if (StringUtils.isNotEmpty(dynamicRedisKey)) {
            String dynamicItemCode = redisTemplate.opsForValue().get(dynamicRedisKey.toString());
            if(StringUtils.isNotBlank(dynamicItemCode)){
                return dynamicItemCode;
            }
        }
        String itemCode = redisTemplate.opsForValue().get(redisKey.toString());
        if(StringUtils.isNotBlank(itemCode)){
            return itemCode;
        }

        //开始过滤条件
        String redisGroupKey = null;
        List<MaterielMatchLine> matchedData = matchLineaAllMap.get(redisKey.toString());
        if(CollectionUtils.isEmpty(matchedData) && StringUtils.isNotEmpty(dynamicRedisKey)){
            matchedData = matchLineaAllMap.get(dynamicRedisKey.toString());
            redisGroupKey = dynamicRedisKey.toString();
        } else {
            redisGroupKey = redisKey.toString();
        }
        if(CollectionUtils.isEmpty(matchedData)){
            return null;
        }
        MaterielMatchLine line = matchedData.stream().filter(i -> StringUtils.isNotBlank(i.getItemCode())).max(Comparator.comparing(MaterielMatchLine::getUpdatedTime)).orElse(null);
        if (line == null) {
            return null;
        }
        redisTemplate.opsForValue().set(redisGroupKey, line.getItemCode(), 2, TimeUnit.HOURS);
//        inheritCache.put(redisKey.toString(),line.getItemCode());
        return line.getItemCode();
    }

    @Override
    public Page<SlurryMainReplacementVO> slurryMainReplacementPage(SlurryMainReplacementQuery query) {
        // 表中的数据取bbom_materiel_match_line中is_deleted= 0的数据，根据电池料号+替代项去重；
        // 获取当前月份和次月
        LocalDate now = LocalDate.now();
        String curMonth = DateUtil.getMonth(now);
        String nextMonth = DateUtil.getMonth(now.plusMonths(1L));

        JPAQuery<Tuple> itemCodeAndAlt = jpaQueryFactory.select(qMaterielMatchLine.itemCode, qMaterielMatchLine.alternateBomDesignator)
                .from(qMaterielMatchLine)
                .where(
                        qMaterielMatchLine.itemCode.isNotNull()
                                .and(qMaterielMatchLine.alternateBomDesignator.isNotNull())
                                .and(qMaterielMatchLine.matchStatus.eq(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode()))
                                .and(qMaterielMatchLine.oldMonth.in(curMonth, nextMonth))
                                .and(qMaterielMatchLine.isDeleted.eq(0))
                ).groupBy(qMaterielMatchLine.itemCode, qMaterielMatchLine.alternateBomDesignator).fetchAll();
        List<SlurryMainReplacementVO> result = new LinkedList<>();
        List<SlurryMainReplacementVO> finalResult = result;

        // 获取替代项信息
        //获取bom替代项、库存组织根据车间
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        //designatorQuery.setDescription(headerDTO.getWorkshop());
        List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryList(designatorQuery).getBody().getData();
        Map<String, String> altDesAndWorkshopMap = designatorDTOList.stream().filter(i -> i.getDescription() != null).collect(Collectors.toMap(ErpAlternateDesignatorDTO::getAlternateDesignatorCode, ErpAlternateDesignatorDTO::getDescription, (k1, k2) -> k1));

        itemCodeAndAlt.fetch().forEach(tuple -> {
            String mainItemCode = tuple.get(qMaterielMatchLine.itemCode);
            String alternateBomDesignator = tuple.get(qMaterielMatchLine.alternateBomDesignator);
            ItemsDTO mainItem = itemsService.getOneByItemCode(mainItemCode);


            StructuresMrpQuery structuresMrpQuery = new StructuresMrpQuery();
            structuresMrpQuery.setAssemblyItemId(mainItem.getSourceItemId());
            structuresMrpQuery.setAlternateBomDesignator(alternateBomDesignator);
            StructuresDTO structuresDTO = structuresService.findOneByMrpQuery(structuresMrpQuery);
            if (structuresDTO == null) {
                return;
            }

            // 只保留浆料
            structuresDTO.getComponents().stream()
                    .filter(i -> "浆料".equals(i.getItem().getCategorySegment5()))
                    .sorted(Comparator.comparing(ComponentsDTO::getComponentSequenceId))
                    .forEach(component -> {
                        SlurryMainReplacementVO vo = new SlurryMainReplacementVO();
                        if (!"单玻".equals(mainItem.getSegment23())) {
                            vo.setMainItemCode("");
                            vo.setMainItemDesc("");
                        } else {
                            vo.setMainItemCode(mainItemCode);
                            vo.setMainItemDesc(mainItem.getItemDesc());
                        }
                        vo.setAlternateBomDesignator(alternateBomDesignator);
                        vo.setWorkshop(altDesAndWorkshopMap.get(alternateBomDesignator));
                        vo.setFrontOrBack(component.getItem().getSegment7());
                        vo.setOpPosition(component.getItem().getSegment4());
                        vo.setItemCode(component.getItem().getItemCode());
                        vo.setItemDesc(component.getItem().getItemDesc());
                        vo.setSubstituteFlag(YesOrNoEnum.getDesc(component.getSlurrySubstituteFlag()));
                        vo.setVendorAltName(component.getItem().getSegment8());
                        vo.setComponentSequenceId(component.getComponentSequenceId());
                        vo.setSubstituteEnableFlag(YesOrNoEnum.getDesc(component.getSlurrySubstituteEnableFlag()));
                        finalResult.add(vo);
                    });
        });
        // 合并
        Map<String, List<SlurryMainReplacementVO>> groupedResult = result.stream().collect(
                Collectors.groupingBy(
                        i -> groupKeyJoiner.join(i.getMainItemCode(), i.getAlternateBomDesignator(), i.getWorkshop(), i.getItemCode())
                )
        );
        result = groupedResult.values()
                .stream()
                .map(i -> i.get(0))
                .collect(Collectors.toList());

        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize());
        // 通过query进行过滤
        result = filterByQuery(result, query);

        return new PageImpl<>(result, pageable, result.size());
    }

    @Override
    @SneakyThrows
    public void slurryMainReplacementPageExport(SlurryMainReplacementQuery query, HttpServletResponse response) {
        Page<SlurryMainReplacementVO> pageVo = slurryMainReplacementPage(query);
        List<SlurryMainReplacementExcelDTO> excelDTOS = convert.toSlurryMainReplacementExcelDTO(pageVo.getContent());
        ExcelUtils.setExportResponseHeader(response, MessageHelper.getMessage("bbom_filename_SlurryMainReplacement").getDesc() + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {
            WriteSheet sheet = EasyExcel.writerSheet(0, MessageHelper.getMessage("bbom_filename_SlurryMainReplacement").getDesc()).head(SlurryMainReplacementExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(excelDTOS, sheet);
        }
    }

    @Override
    @SneakyThrows
    public void importSlurryMainReplacementPageEntity(MultipartFile file) {
        List<SlurryMainReplacementExcelDTO> excelDto = new LinkedList<>();
        ExcelReaderBuilder read = EasyExcel.read(file.getInputStream(), SlurryMainReplacementExcelDTO.class, new ReadListener<SlurryMainReplacementExcelDTO>() {
            @Override
            public void invoke(SlurryMainReplacementExcelDTO data, AnalysisContext context) {
                excelDto.add(data);
            }

            @Override
            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                EasyExcelI18nHeaderParserUtil.analysisI18nHeader(headMap, context);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("所有数据解析完成！");
            }
        });
        read.sheet().doRead();
        doImport(excelDto);
    }

    @Override
    public List<MaterielMatchLineDTO> queryPlanMaterielMatchLineList() {
        //当前月的数据以及下个月的数据且类型为plan
        return convert.toDto(jpaQueryFactory
                .select(qMaterielMatchLine)
                .from(qMaterielMatchHeader)
                .join(qMaterielMatchLine)
                .on(qMaterielMatchHeader.id.eq(qMaterielMatchLine.headerId))
                .where(qMaterielMatchHeader.month.eq(DateUtil.getMonth(LocalDate.now())).or(qMaterielMatchHeader.month.eq(DateUtil.getMonth(LocalDate.now().plusMonths(1)))))
                .where(qMaterielMatchHeader.planType.eq("plan").and(qMaterielMatchLine.itemCode.isNotNull()).and(qMaterielMatchHeader.isDeleted.eq(DeleteEnum.NO.getCode())).and(qMaterielMatchLine.isDeleted.eq(DeleteEnum.NO.getCode())))
                .fetch());
    }

    @Override
    public Page<ScreenMainReplacementVO> screenMainReplacementPage(ScreenMainReplacementQuery query) {
        // 表中的数据取bbom_materiel_match_line中is_deleted= 0的数据，根据电池料号+替代项去重；
        // 获取当前月份和次月
        LocalDate now = LocalDate.now();
        String curMonth = DateUtil.getMonth(now);
        String nextMonth = DateUtil.getMonth(now.plusMonths(1L));

        JPAQuery<Tuple> itemCodeAndAlt = jpaQueryFactory.select(qMaterielMatchLine.itemCode, qMaterielMatchLine.alternateBomDesignator)
                .from(qMaterielMatchLine)
                .where(
                        qMaterielMatchLine.itemCode.isNotNull()
                                .and(qMaterielMatchLine.alternateBomDesignator.isNotNull())
                                .and(qMaterielMatchLine.matchStatus.eq(MaterielMatchLineDTO.MatchStatus.MATCHED.getCode()))
                                .and(qMaterielMatchLine.oldMonth.in(curMonth, nextMonth))
                                .and(qMaterielMatchLine.isDeleted.eq(0))
                ).groupBy(qMaterielMatchLine.itemCode, qMaterielMatchLine.alternateBomDesignator).fetchAll();
        List<ScreenMainReplacementVO> result = new LinkedList<>();
        List<ScreenMainReplacementVO> finalResult = result;

        // 获取替代项信息
        //获取bom替代项、库存组织根据车间
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        //designatorQuery.setDescription(headerDTO.getWorkshop());
        List<ErpAlternateDesignatorDTO> designatorDTOList = bomFeign.queryList(designatorQuery).getBody().getData();
        Map<String, String> altDesAndWorkshopMap = designatorDTOList.stream().filter(i -> i.getDescription() != null).collect(Collectors.toMap(ErpAlternateDesignatorDTO::getAlternateDesignatorCode, ErpAlternateDesignatorDTO::getDescription, (k1, k2) -> k1));

        itemCodeAndAlt.fetch().forEach(tuple -> {
            String mainItemCode = tuple.get(qMaterielMatchLine.itemCode);
            String alternateBomDesignator = tuple.get(qMaterielMatchLine.alternateBomDesignator);
            ItemsDTO mainItem = itemsService.getOneByItemCode(mainItemCode);


            StructuresMrpQuery structuresMrpQuery = new StructuresMrpQuery();
            structuresMrpQuery.setAssemblyItemId(mainItem.getSourceItemId());
            structuresMrpQuery.setAlternateBomDesignator(alternateBomDesignator);
            StructuresDTO structuresDTO = structuresService.findOneByMrpQuery(structuresMrpQuery);
            if (structuresDTO == null) {
                return;
            }

            // 只保留网版
            structuresDTO.getComponents().stream()
                    .filter(i -> "网版".equals(i.getItem().getCategorySegment5()))
                    .sorted(Comparator.comparing(ComponentsDTO::getComponentSequenceId))
                    .forEach(component -> {
                        ScreenMainReplacementVO vo = new ScreenMainReplacementVO();
                        if (!"单玻".equals(mainItem.getSegment23())) {
                            vo.setMainItemCode("");
                            vo.setMainItemDesc("");
                        } else {
                            vo.setMainItemCode(mainItemCode);
                            vo.setMainItemDesc(mainItem.getItemDesc());
                        }
                        vo.setAlternateBomDesignator(alternateBomDesignator);
                        vo.setWorkshop(altDesAndWorkshopMap.get(alternateBomDesignator));
                        vo.setFrontOrBack(component.getItem().getSegment13());
                        vo.setOpPosition(component.getItem().getSegment9());
                        vo.setItemCode(component.getItem().getItemCode());
                        vo.setItemDesc(component.getItem().getItemDesc());
                        vo.setSubstituteFlag(YesOrNoEnum.getDesc(component.getScreenSubstituteFlag()));
                        vo.setVendorAltName(component.getItem().getSegment27());
                        vo.setComponentSequenceId(component.getComponentSequenceId());
                        vo.setSubstituteEnableFlag(YesOrNoEnum.getDesc(component.getScreenSubstituteEnableFlag()));
                        finalResult.add(vo);
                    });
        });
        // 合并
        Map<String, List<ScreenMainReplacementVO>> groupedResult = result.stream().collect(
                Collectors.groupingBy(
                        i -> groupKeyJoiner.join(i.getMainItemCode(), i.getAlternateBomDesignator(), i.getWorkshop(), i.getItemCode())
                )
        );
        result = groupedResult.values()
                .stream()
                .map(i -> i.get(0))
                .collect(Collectors.toList());

        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize());
        // 通过query进行过滤
        result = filterByQuery(result, query);

        return new PageImpl<>(result, pageable, result.size());
    }

    @SneakyThrows
    @Override
    public void exportScreenMainReplacement(ScreenMainReplacementQuery query, HttpServletResponse response) {
        Page<ScreenMainReplacementVO> pageVo =screenMainReplacementPage(query);
        List<ScreenMainReplacementExcelDTO> excelDTOS = convert.toScreenMainReplacementExcelDTO(pageVo.getContent());
        ExcelUtils.setExportResponseHeader(response, MessageHelper.getMessage("bbom_filename_screen_main_replacement").getDesc() + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {
            WriteSheet sheet = EasyExcel.writerSheet(0, MessageHelper.getMessage("bbom_filename_screen_main_replacement").getDesc()).head(ScreenMainReplacementExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(excelDTOS, sheet);
        }
    }

    @SneakyThrows
    @Override
    public void importScreenMainReplacement(MultipartFile file) {
        List<ScreenMainReplacementExcelDTO> excelDto = new LinkedList<>();
        ExcelReaderBuilder read = EasyExcel.read(file.getInputStream(), ScreenMainReplacementExcelDTO.class, new ReadListener<ScreenMainReplacementExcelDTO>() {
            @Override
            public void invoke(ScreenMainReplacementExcelDTO data, AnalysisContext context) {
                excelDto.add(data);
            }

            @Override
            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                EasyExcelI18nHeaderParserUtil.analysisI18nHeader(headMap, context);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("所有数据解析完成！");
            }
        });
        read.sheet().doRead();
        doScreenMainReplacementImport(excelDto);
    }

    private void doScreenMainReplacementImport(List<ScreenMainReplacementExcelDTO> excelDto) {
        // 获取所有5A相关的
        List<StructuresDTO> structuresDTOS = structuresService.findAll();
        Map<String, List<StructuresDTO>> altAndStructuresMap = structuresDTOS
                .stream()
                .filter(i -> i.getItem() != null && !"单玻".equals(i.getItem().getSegment23()))
                .filter(i -> i.getAlternateBomDesignator() != null)
                .collect(Collectors.groupingBy(StructuresDTO::getAlternateBomDesignator));


        Map<String, Map<String, List<ComponentsDTO>>> altAndItemCodeAndComponents = new HashMap<>();
        altAndStructuresMap
                .values()
                .stream()
                .flatMap(List::stream)
                .forEach(i -> {
                    // 查询子行
                    List<ComponentsDTO> componentsDTOS = componentsService.findByStructure(i);
                    List<ComponentsDTO> components = componentsDTOS.stream()
                            .filter(c -> "网版".equals(c.getItem().getCategorySegment5()))
                            .collect(Collectors.toList());
                    Map<String, List<ComponentsDTO>> stringListMap = altAndItemCodeAndComponents.computeIfAbsent(i.getAlternateBomDesignator(), k -> new HashMap<>());
                    for (ComponentsDTO component : components) {
                        stringListMap.computeIfAbsent(component.getItem().getItemCode(), k -> new ArrayList<>())
                                .add(component);
                    }
                });

        // 通过替代项进行分组
        excelDto.stream().collect(Collectors.groupingBy(ScreenMainReplacementExcelDTO::getAlternateBomDesignator))
                .forEach((alternateBomDesignator, list) -> {
                    for (ScreenMainReplacementExcelDTO screenMainReplacementExcelDTO : list) {
                        if (StringUtils.isNotBlank(screenMainReplacementExcelDTO.getMainItemCode())) {
                            updateScreenByMainItemCode(alternateBomDesignator, screenMainReplacementExcelDTO);
                        } else {
                            updateScreenByAlternateBomDesignator(alternateBomDesignator, screenMainReplacementExcelDTO, altAndItemCodeAndComponents);
                        }
                    }
                });

    }

    private void updateScreenByAlternateBomDesignator(String alternateBomDesignator, ScreenMainReplacementExcelDTO screenMainReplacementExcelDTO, Map<String, Map<String, List<ComponentsDTO>>> altAndItemCodeAndComponents) {
        Map<String, List<ComponentsDTO>> itemCodeAndComponentsMap = altAndItemCodeAndComponents.getOrDefault(alternateBomDesignator, new HashMap<>());
        String itemCode = screenMainReplacementExcelDTO.getItemCode();
        List<ComponentsDTO> componentsDTOS = itemCodeAndComponentsMap.getOrDefault(itemCode, new ArrayList<>());
        for (ComponentsDTO componentsDTO : componentsDTOS) {
            if (!componentsDTO.getScreenSubstituteFlag().equals(YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteFlag())) || !componentsDTO.getScreenSubstituteEnableFlag().equals(YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteEnableFlag()))) {
                componentsService.updateComponentScreenSubstituteFlag(alternateBomDesignator, componentsDTO, YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteFlag()),YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteEnableFlag()));
            }
        }
    }

    private void doImport(List<SlurryMainReplacementExcelDTO> excelDto) {
        // 一行一行处理
        Map<String, List<SlurryMainReplacementExcelDTO>> grouped = null;

        // 获取所有5A相关的
        List<StructuresDTO> structuresDTOS = structuresService.findAll();
        Map<String, List<StructuresDTO>> altAndStructuresMap = structuresDTOS
                .stream()
                .filter(i -> i.getItem() != null && !"单玻".equals(i.getItem().getSegment23()))
                .filter(i -> i.getAlternateBomDesignator() != null)
                .collect(Collectors.groupingBy(StructuresDTO::getAlternateBomDesignator));


        Map<String, Map<String, List<ComponentsDTO>>> altAndItemCodeAndComponents = new HashMap<>();
        altAndStructuresMap
                .values()
                .stream()
                .flatMap(List::stream)
                .forEach(i -> {
                    // 查询子行
                    List<ComponentsDTO> componentsDTOS = componentsService.findByStructure(i);
                    List<ComponentsDTO> components = componentsDTOS.stream()
                            .filter(c -> "浆料".equals(c.getItem().getCategorySegment5()))
                            .collect(Collectors.toList());
                    Map<String, List<ComponentsDTO>> stringListMap = altAndItemCodeAndComponents.computeIfAbsent(i.getAlternateBomDesignator(), k -> new HashMap<>());
                    for (ComponentsDTO component : components) {
                        stringListMap.computeIfAbsent(component.getItem().getItemCode(), k -> new ArrayList<>())
                                .add(component);
                    }
                });

        // 通过替代项进行分组
        excelDto.stream().collect(Collectors.groupingBy(SlurryMainReplacementExcelDTO::getAlternateBomDesignator))
                .forEach((alternateBomDesignator, list) -> {
                    for (SlurryMainReplacementExcelDTO slurryMainReplacementExcelDTO : list) {
                        if (StringUtils.isNotBlank(slurryMainReplacementExcelDTO.getMainItemCode())) {
                            updateByMainItemCode(alternateBomDesignator, slurryMainReplacementExcelDTO);
                        } else {
                            updateByAlternateBomDesignator(alternateBomDesignator, slurryMainReplacementExcelDTO, altAndItemCodeAndComponents);
                        }
                    }
                });

    }

    private void updateByAlternateBomDesignator(String alternateBomDesignator, SlurryMainReplacementExcelDTO slurryMainReplacementExcelDTO, Map<String, Map<String, List<ComponentsDTO>>> altAndItemCodeAndComponents) {
        Map<String, List<ComponentsDTO>> itemCodeAndComponentsMap = altAndItemCodeAndComponents.getOrDefault(alternateBomDesignator, new HashMap<>());
        String itemCode = slurryMainReplacementExcelDTO.getItemCode();
        List<ComponentsDTO> componentsDTOS = itemCodeAndComponentsMap.getOrDefault(itemCode, new ArrayList<>());
        for (ComponentsDTO componentsDTO : componentsDTOS) {
            if (!componentsDTO.getSlurrySubstituteFlag().equals(YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteFlag())) || !componentsDTO.getSlurrySubstituteEnableFlag().equals(YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteEnableFlag()))) {
                componentsService.updateComponentSlurrySubstituteFlag(alternateBomDesignator, componentsDTO, YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteFlag()),YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteEnableFlag()));
            }
        }
    }

    private void updateScreenByMainItemCode(String alternateBomDesignator, ScreenMainReplacementExcelDTO screenMainReplacementExcelDTO) {
        // 获取第一个
        String mainItemCode = screenMainReplacementExcelDTO.getMainItemCode();
        ItemsDTO mainItem = itemsService.getOneByItemCode(mainItemCode);

        StructuresMrpQuery structuresMrpQuery = new StructuresMrpQuery();
        structuresMrpQuery.setAssemblyItemId(mainItem.getSourceItemId());
        structuresMrpQuery.setAlternateBomDesignator(alternateBomDesignator);
        StructuresDTO structuresDTO = structuresService.findOneByMrpQuery(structuresMrpQuery);
        // 只保留浆料
        Map<String, ComponentsDTO> componentSequenceIdAndItemCodeMap = structuresDTO.getComponents().stream()
                .filter(i -> "网版".equals(i.getItem().getCategorySegment5()))
                .collect(Collectors.toMap(i -> i.getItem().getItemCode(), i -> i));
        ComponentsDTO componentsDTO = componentSequenceIdAndItemCodeMap.get(screenMainReplacementExcelDTO.getItemCode());
        if (componentsDTO == null) {
            return;
        }
        if (!componentsDTO.getScreenSubstituteFlag().equals(YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteFlag())) ||  !componentsDTO.getScreenSubstituteEnableFlag().equals(YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteEnableFlag()))) {
            componentsService.updateComponentScreenSubstituteFlag(alternateBomDesignator, componentsDTO, YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteFlag()),YesOrNoEnum.getCode(screenMainReplacementExcelDTO.getSubstituteEnableFlag()));
        }
    }

    private void updateByMainItemCode(String alternateBomDesignator, SlurryMainReplacementExcelDTO slurryMainReplacementExcelDTO) {
        // 获取第一个
        String mainItemCode = slurryMainReplacementExcelDTO.getMainItemCode();
        ItemsDTO mainItem = itemsService.getOneByItemCode(mainItemCode);

        StructuresMrpQuery structuresMrpQuery = new StructuresMrpQuery();
        structuresMrpQuery.setAssemblyItemId(mainItem.getSourceItemId());
        structuresMrpQuery.setAlternateBomDesignator(alternateBomDesignator);
        StructuresDTO structuresDTO = structuresService.findOneByMrpQuery(structuresMrpQuery);
        // 只保留浆料
        Map<String, ComponentsDTO> componentSequenceIdAndItemCodeMap = structuresDTO.getComponents().stream()
                .filter(i -> "浆料".equals(i.getItem().getCategorySegment5()))
                .collect(Collectors.toMap(i -> i.getItem().getItemCode(), i -> i));
        ComponentsDTO componentsDTO = componentSequenceIdAndItemCodeMap.get(slurryMainReplacementExcelDTO.getItemCode());
        if (componentsDTO == null) {
            return;
        }
        if (!componentsDTO.getSlurrySubstituteFlag().equals(YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteFlag())) || !componentsDTO.getSlurrySubstituteEnableFlag().equals(YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteEnableFlag()))) {
            componentsService.updateComponentSlurrySubstituteFlag(alternateBomDesignator, componentsDTO, YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteFlag()), YesOrNoEnum.getCode(slurryMainReplacementExcelDTO.getSubstituteEnableFlag()));
        }
    }


    private List<SlurryMainReplacementVO> filterByQuery(List<SlurryMainReplacementVO> result, SlurryMainReplacementQuery query) {
        if (StringUtils.isNotBlank(query.getMainItemCode())) {
            result = result.stream().filter(i -> query.getMainItemCode().equals(i.getMainItemCode())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(query.getAlternateBomDesignator())) {
            result = result.stream().filter(i -> query.getAlternateBomDesignator().equals(i.getAlternateBomDesignator())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(query.getItemCode())) {
            result = result.stream().filter(i -> query.getItemCode().equals(i.getItemCode())).collect(Collectors.toList());
        }

        return result;
    }

    private List<ScreenMainReplacementVO> filterByQuery(List<ScreenMainReplacementVO> result, ScreenMainReplacementQuery query) {
        if (StringUtils.isNotBlank(query.getMainItemCode())) {
            result = result.stream().filter(i -> query.getMainItemCode().equals(i.getMainItemCode())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(query.getAlternateBomDesignator())) {
            result = result.stream().filter(i -> query.getAlternateBomDesignator().equals(i.getAlternateBomDesignator())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(query.getItemCode())) {
            result = result.stream().filter(i -> query.getItemCode().equals(i.getItemCode())).collect(Collectors.toList());
        }

        return result;
    }

    public void exportEmail() {
        EmailDataResultDTO emailDataResultDTO = new EmailDataResultDTO();
        //匹配状态为未匹配、属性未匹配、网版未匹配
        JPAQuery<MaterielMatchLine> jpaQuery = jpaQueryFactory.select(qMaterielMatchLine).from(qMaterielMatchLine)
                .where(qMaterielMatchLine.matchStatus.in("FAIL_MATCH", "ATTR_NON_MATCH", "NON_MATCH"));
        jpaQuery.where(
                (qMaterielMatchLine.handWorkFlag.isNull()
                        .or(qMaterielMatchLine.handWorkFlag.ne("Y"))
                        .or(qMaterielMatchLine.handWorkFlag.isEmpty()))
                        .and(qMaterielMatchLine.splitFlag.isNull()
                                .or(qMaterielMatchLine.splitFlag.eq("Y"))
                                .or(qMaterielMatchLine.splitFlag.isEmpty()))
        );
        jpaQuery.where(QMaterielMatchLine.materielMatchLine.itemCode.isNull());
        jpaQuery.where(qMaterielMatchLine.oldMonth.eq(DateUtil.getMonth(LocalDate.now())));
        List<MaterielMatchLine> lineList = jpaQuery.fetch();
        //匹配明细行查询
        if (CollectionUtils.isNotEmpty(lineList)) {
            List<MaterielMatchLineDTO> lineDTOList = convert.toDto(lineList);

            List<MaterielMatchLineDTO> sortList = getMatchLineDTOS(lineDTOList);
            //数据转换方法
            List<MaterielMatchLineExcelDTO> exportDTOS = convert.toExcelDTO(sortList);
            //网版切换
            String content = "料号匹配内容";
            JSONArray jsonArray = getObjects(emailDataResultDTO, exportDTOS, content);
            sendEmail(jsonArray, emailDataResultDTO);
        }
    }

    //获取匹配明细行数据 分组
    private Map<Long, List<MaterielMatchLineMatchStatusDTO>> getMatchStatusMap(List<Long> lineIdList) {
        List<MaterielMatchLineMatchStatusDTO> matchLineMatchStatusDTOList = matchStatusService.queryByLineIds(lineIdList);
        Map<Long, List<MaterielMatchLineMatchStatusDTO>> matchStatusMap = matchLineMatchStatusDTOList.stream().collect(Collectors.groupingBy(MaterielMatchLineMatchStatusDTO::getLineId));
        return matchStatusMap;
    }

    public JSONArray getObjects(EmailDataResultDTO emailDataResultDTO, List<MaterielMatchLineExcelDTO> exportDTOS, String content) {
        File file = buildExcelFile(exportDTOS);
        String fileUrl = this.fileUpload(file);
        JSONArray jsonArray = FileUtil.getObjects(emailDataResultDTO, content, file, fileUrl, "料号匹配");
        return jsonArray;
    }

    @SneakyThrows
    private String fileUpload(File file) {
        MultipartFile multipartFile = FileUtil.getMultipartFile(file);
        return scpFileService.upload(multipartFile, false);
    }

    private File buildExcelFile(List<MaterielMatchLineExcelDTO> exportDTOS) {
        //创建目录
        File file = FileUtil.createLocalFile("料号匹配");
        // 文件输出位置
        ExcelWriter writer = EasyExcelFactory.write(file)
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build();


        WriteSheet sheet1 = EasyExcelFactory.writerSheet(0, "料号匹配导出信息").head(MaterielMatchLineExcelDTO.class).build();
        // 写数据
        writer.write(exportDTOS, sheet1);
        writer.finish();
        return file;
    }

    /**
     * 发送邮件
     */
    private void sendEmail(JSONArray jsonArray, EmailDataResultDTO emailDataResultDTO) {
        //查询所有收件人
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.SEND_MAIL, LovHeaderCodeConstant.MATCH_HEADER_MAIL);
        //发送邮件
        List<String> emails = Arrays.asList(StringUtils.split(lovLineDTO.getAttribute1(), ","));

        try {
            mailService.send(new ArrayList<>(emails), "battery_screen_plate.ftl", "料号匹配邮件提醒", MapUtil.of("Data", emailDataResultDTO), jsonArray.toJSONString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
