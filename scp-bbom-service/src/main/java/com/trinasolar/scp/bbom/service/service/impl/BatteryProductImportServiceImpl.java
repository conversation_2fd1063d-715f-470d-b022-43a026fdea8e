package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.BatteryProductImportDEConvert;
import com.trinasolar.scp.bbom.domain.dto.BatteryProductImportDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryProductImport;
import com.trinasolar.scp.bbom.domain.entity.QBatteryProductImport;
import com.trinasolar.scp.bbom.domain.excel.BatteryProductImportExcelDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryProductImportQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryProductImportSaveDTO;
import com.trinasolar.scp.bbom.service.repository.BatteryProductImportRepository;
import com.trinasolar.scp.bbom.service.service.BatteryProductImportService;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import com.trinasolar.scp.common.api.util.i18n.easyExcel.EasyExcelI18nHeaderParserUtil;
import com.trinasolar.scp.common.api.util.i18n.easyExcel.I18nCellWriteHandler;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 电池产品导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Slf4j
@Service("batteryProductImportService")
@RequiredArgsConstructor
public class BatteryProductImportServiceImpl implements BatteryProductImportService {
    private static final QBatteryProductImport qBatteryProductImport = QBatteryProductImport.batteryProductImport;

    private final BatteryProductImportDEConvert convert;

    private final BatteryProductImportRepository repository;

    private static void extracted(BatteryProductImportSaveDTO dto, BooleanBuilder booleanBuilder) {
        if (null != dto.getId()) {
            booleanBuilder.and(qBatteryProductImport.id.ne(dto.getId()));
        }
        //电池产品编码
        if (StringUtils.isNotEmpty(dto.getBatteryCode())) {
            booleanBuilder.and(qBatteryProductImport.batteryCode.eq(dto.getBatteryCode()));
        }
        //电池片晶体类型
        if (StringUtils.isNotEmpty(dto.getBatteryCrystalType())) {
            booleanBuilder.and(qBatteryProductImport.batteryCrystalType.eq(dto.getBatteryCrystalType()));
        }
        //产品类型
        if (StringUtils.isNotEmpty(dto.getProductType())) {
            booleanBuilder.and(qBatteryProductImport.productType.eq(dto.getProductType()));
        }
        //电池片尺寸编码
        if (StringUtils.isNotEmpty(dto.getBatteryDimensionCode())) {
            booleanBuilder.and(qBatteryProductImport.batteryDimensionCode.eq(dto.getBatteryDimensionCode()));
        }
        //主栅数
        if (StringUtils.isNotEmpty(dto.getNumberMainGrids())) {
            booleanBuilder.and(qBatteryProductImport.numberMainGrids.eq(dto.getNumberMainGrids()));
        }
        //分片数
        if (StringUtils.isNotEmpty(dto.getShardingNumber())) {
            booleanBuilder.and(qBatteryProductImport.shardingNumber.eq(dto.getShardingNumber()));
        }
    }

    private static void convertLovValue(BatteryProductImportExcelDTO excelDTO, List<String> subErrors) {
        // 全量查询 电池产品导入表数据 BATTERY_CELL_CRYSTAL_TYPE
        Map<String, LovLineDTO> batteryCellCrystalType = LovUtils.getAllByHeaderCode("BATTERY_CELL_CRYSTAL_TYPE");
        // 电池片晶体类型 参考页面数据晶体类型为 "N"
        Optional.ofNullable(batteryCellCrystalType.get(excelDTO.getBatteryCrystalType()))
                .map(v -> {
                    excelDTO.setBatteryCrystalType(v.getLovValue());
                    return v;
                })
                .orElseGet(() -> {
                    subErrors.add(excelDTO.getBatteryCrystalType() + ":电池片晶体类型不合法");
                    return null;
                });
        // 产品类型 PRODUCT_TYPE 参考页面数据产品类型为 "B" 单双面
        Map<String, LovLineDTO> productType = LovUtils.getAllByHeaderCode("PRODUCT_TYPE");
        Optional.ofNullable(productType.get(excelDTO.getProductType()))
                .map(v -> {
                    excelDTO.setProductType(v.getLovValue());
                    return v;
                })
                .orElseGet(() -> {
                    subErrors.add(excelDTO.getProductType() + ":产品类型不合法");
                    return null;
                });


        // 电池片尺寸编码 G122 品类 ENCODING_OF_BATTERY_CELL_SIZE
        Map<String, LovLineDTO> encodingOfBatteryCellSize = LovUtils.getAllByHeaderCode("ENCODING_OF_BATTERY_CELL_SIZE");
        Optional.ofNullable(encodingOfBatteryCellSize.get(excelDTO.getBatteryDimensionCode()))
                .map(v -> {
                    excelDTO.setBatteryDimensionCode(v.getLovValue());
                    return v;
                })
                .orElseGet(() -> {
                    subErrors.add(excelDTO.getBatteryDimensionCode() + ":电池片尺寸编码不合法");
                    return null;
                });

        // 主栅数 MAIN_GRID
        Map<String, LovLineDTO> mainGrid = LovUtils.getAllByHeaderCode("MAIN_GRID");
        Optional.ofNullable(mainGrid.get(excelDTO.getNumberMainGrids()))
                .map(v -> {
                    excelDTO.setNumberMainGrids(v.getLovValue());
                    return v;
                })
                .orElseGet(() -> {
                    subErrors.add(excelDTO.getNumberMainGrids() + ":主栅数不合法");
                    return null;
                });

        // 分片数 CELL_SHARDING
        Map<String, LovLineDTO> sharding = LovUtils.getAllByHeaderCode("CELL_SHARDING");
        Optional.ofNullable(sharding.get(excelDTO.getShardingNumber()))
                .map(v -> {
                    excelDTO.setShardingNumber(v.getLovValue());
                    return v;
                })
                .orElseGet(() -> {
                    subErrors.add(excelDTO.getShardingNumber() + ":分片数不合法");
                    return null;
                });
    }

    private static void verifySave(BatteryProductImportSaveDTO saveDTO) {
        //判断校验
        if (StringUtils.isBlank(saveDTO.getBatteryCode())) {
            throw new BizException("bbom_valid_batteryCode_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBatteryCrystalType())) {
            throw new BizException("bbom_valid_batteryCrystalType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getProductType())) {
            throw new BizException("bbom_valid_productType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBatteryDimensionCode())) {
            throw new BizException("bbom_valid_batteryDimensionCode_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getNumberMainGrids())) {
            throw new BizException("bbom_valid_NumberMainGrids_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getShardingNumber())) {
            throw new BizException("bbom_valid_shardingNumber_notBlank");
        }
    }

    private static void verifyImport(BatteryProductImportExcelDTO excelDTO, List<String> errors) {
        //判断校验
        if (StringUtils.isBlank(excelDTO.getBatteryCode())) {
            errors.add(MessageHelper.getMessage("bbom_valid_batteryCode_notBlank").getDesc());
        }
        if (StringUtils.isBlank(excelDTO.getBatteryCrystalType())) {
            errors.add(MessageHelper.getMessage("bbom_valid_batteryCrystalType_notBlank").getDesc());
        }
        if (StringUtils.isBlank(excelDTO.getProductType())) {
            errors.add(MessageHelper.getMessage("bbom_valid_productType_notBlank").getDesc());
        }
        if (StringUtils.isBlank(excelDTO.getBatteryDimensionCode())) {
            errors.add(MessageHelper.getMessage("bbom_valid_batteryDimensionCode_notBlank").getDesc());
        }
        if (StringUtils.isBlank(excelDTO.getNumberMainGrids())) {
            errors.add(MessageHelper.getMessage("bbom_valid_NumberMainGrids_notBlank").getDesc());
        }
        if (StringUtils.isBlank(excelDTO.getShardingNumber())) {
            errors.add(MessageHelper.getMessage("bbom_valid_shardingNumber_notBlank").getDesc());
        }
    }

    private static void extractedSave(BatteryProductImportSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        if (null != saveDTO.getId()) {
            booleanBuilder.and(qBatteryProductImport.id.eq(saveDTO.getId()));
        }
    }

    private static void extractedImport(BatteryProductImportExcelDTO dto, BooleanBuilder booleanBuilder) {
        //电池产品编码
        if (StringUtils.isNotEmpty(dto.getBatteryCode())) {
            booleanBuilder.and(qBatteryProductImport.batteryCode.eq(dto.getBatteryCode()));
        }
        //电池片晶体类型
        if (StringUtils.isNotEmpty(dto.getBatteryCrystalType())) {
            booleanBuilder.and(qBatteryProductImport.batteryCrystalType.eq(dto.getBatteryCrystalType()));
        }
        //产品类型
        if (StringUtils.isNotEmpty(dto.getProductType())) {
            booleanBuilder.and(qBatteryProductImport.productType.eq(dto.getProductType()));
        }
        //电池片尺寸编码
        if (StringUtils.isNotEmpty(dto.getBatteryDimensionCode())) {
            booleanBuilder.and(qBatteryProductImport.batteryDimensionCode.eq(dto.getBatteryDimensionCode()));
        }
        //主栅数
        if (StringUtils.isNotEmpty(dto.getNumberMainGrids())) {
            booleanBuilder.and(qBatteryProductImport.numberMainGrids.eq(dto.getNumberMainGrids()));
        }
        //分片数
        if (StringUtils.isNotEmpty(dto.getShardingNumber())) {
            booleanBuilder.and(qBatteryProductImport.shardingNumber.eq(dto.getShardingNumber()));
        }
    }

    @Override
    public Page<BatteryProductImportDTO> queryByPage(BatteryProductImportQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<BatteryProductImport> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<BatteryProductImportDTO> queryAll() {
        return convert.toDto(repository.findAll());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatteryProductImportQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatteryProductImport.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatteryProductImport.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatteryProductImport.batteryName.like("%" + query.getBatteryName() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCrystalType())) {
            booleanBuilder.and(qBatteryProductImport.batteryCrystalType.eq(query.getBatteryCrystalType()));
        }
        if (StringUtils.isNotEmpty(query.getProductType())) {
            booleanBuilder.and(qBatteryProductImport.productType.eq(query.getProductType()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryDimensionCode())) {
            booleanBuilder.and(qBatteryProductImport.batteryDimensionCode.eq(query.getBatteryDimensionCode()));
        }
        if (StringUtils.isNotEmpty(query.getNumberMainGrids())) {
            booleanBuilder.and(qBatteryProductImport.numberMainGrids.eq(query.getNumberMainGrids()));
        }
        if (StringUtils.isNotEmpty(query.getShardingNumber())) {
            booleanBuilder.and(qBatteryProductImport.shardingNumber.eq(query.getShardingNumber()));
        }
    }

    @Override
    public BatteryProductImportDTO queryById(Long id) {
        BatteryProductImport queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public BatteryProductImportDTO save(BatteryProductImportSaveDTO saveDTO) {
        // 为空校验
        verifySave(saveDTO);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        checkRepeat(saveDTO, booleanBuilder);

        BatteryProductImport newObj = new BatteryProductImport();
        if (null != saveDTO.getId()) {
            booleanBuilder = new BooleanBuilder();
            extractedSave(saveDTO, booleanBuilder);
            newObj = repository.findOne(booleanBuilder).orElse(new BatteryProductImport());
        }
        convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    //字段校验电池产品编码+电池片晶体类型+产品类型+电池片尺寸编码+主栅数+分片数 维度校验唯一
    public void checkRepeat(BatteryProductImportSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        extracted(saveDTO, booleanBuilder);
        BatteryProductImport newObj = repository.findOne(booleanBuilder).orElse(new BatteryProductImport());
        if (null != newObj.getId()) {
            throw new BizException("bbom_valid_BatteryProduct_unique");
        }
    }

    @Override
    public BatteryProductImportDTO updateById(BatteryProductImport saveDTO) {
        return convert.toDto(repository.save(saveDTO));
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(BatteryProductImportQuery query, HttpServletResponse response) {
        List<BatteryProductImportDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        List<BatteryProductImportExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "电池产品导入表-导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "电池产品导入表").head(BatteryProductImportExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    /**
     * 电池产品导入数据
     *
     * @param file
     */
    @Override
    @SneakyThrows
    public void importsEntity(MultipartFile file) {
        List<BatteryProductImportExcelDTO> excelDto = new LinkedList<>();
        ExcelReaderBuilder read = EasyExcel.read(file.getInputStream(), BatteryProductImportExcelDTO.class, new ReadListener<BatteryProductImportExcelDTO>() {
            @Override
            public void invoke(BatteryProductImportExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
            }

            /**
             * 这里会一行行的返回头
             *
             * @param headMap
             * @param context
             */
            @Override
            public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
                log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
                EasyExcelI18nHeaderParserUtil.analysisI18nHeader(headMap, context);
            }


            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("所有数据解析完成！");
            }
        });
        read.sheet().doRead();
        importDataSave(excelDto);
        log.info("电池产品导入表 数据导入成功");
    }

    /**
     * 导出数据
     *
     * @param query
     * @param response
     */
    @Override
    @SneakyThrows
    public void queryByPageExport(BatteryProductImportQuery query, HttpServletResponse response) {
        List<BatteryProductImportDTO> dtos = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }

        List<BatteryProductImportExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, MessageHelper.getMessage("bbom_filename_batteryProduct_export").getDesc() + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .registerWriteHandler(new I18nCellWriteHandler())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, MessageHelper.getMessage("bbom_filename_batteryProduct_import").getDesc())
                    .head(BatteryProductImportExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    private void importDataSave(List<BatteryProductImportExcelDTO> excelDto) {
        // 批量导入数据
        List<String> errors = new LinkedList<>();

        // 数据校验
        for (int i = 0; i < excelDto.size(); i++) {
            BatteryProductImportExcelDTO excelDTO = excelDto.get(i);
            List<String> subErrors = new LinkedList<>();
            verifyImport(excelDTO, subErrors);
            convertLovValue(excelDTO, subErrors);
            if (!subErrors.isEmpty()) {
                errors.add(MessageHelper.getMessage("bbom_lineNumber", String.valueOf(i + 2)).getDesc() + "：" + String.join(",", subErrors));
            }
        }

        if (CollectionUtils.isNotEmpty(errors)) {
            throw new BizException(MessageHelper.getMessage("bbom_importValidError").getDesc() + "：" + String.join(";\n", errors));
        }

        // 保存或更新数据 查询数据库是否存在数据（存在既更新）
        for (BatteryProductImportExcelDTO excelDTO : excelDto) {
            // 判断
            // lov 名称 -> 值 转换
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            extractedImport(excelDTO, booleanBuilder);
            // 查询 区分新增 修改结果集合
            BatteryProductImport newObj = repository.findOne(booleanBuilder).orElse(new BatteryProductImport());
            if (newObj.getId() != null) {
                excelDTO.setId(newObj.getId());
            }
            BatteryProductImportSaveDTO saveDTO = convert.toSaveDTO(excelDTO);
            if (null != saveDTO.getId()) {
                booleanBuilder = new BooleanBuilder();
                extractedSave(saveDTO, booleanBuilder);
                newObj = repository.findOne(booleanBuilder).orElse(new BatteryProductImport());
            }
            convert.saveDTOtoEntity(saveDTO, newObj);
            repository.save(newObj);
        }
    }
}
