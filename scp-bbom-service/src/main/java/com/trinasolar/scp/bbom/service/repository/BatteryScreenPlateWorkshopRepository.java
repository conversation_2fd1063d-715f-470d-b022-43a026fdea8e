package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.BatteryScreenPlate;
import com.trinasolar.scp.bbom.domain.entity.BatteryScreenPlateWorkshop;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Repository
public interface BatteryScreenPlateWorkshopRepository extends JpaRepository<BatteryScreenPlateWorkshop, Long>, QuerydslPredicateExecutor<BatteryScreenPlateWorkshop> {
}
