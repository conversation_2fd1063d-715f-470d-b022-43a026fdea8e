package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 规则管控对象头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Repository
public interface RuleControlObjectHeaderRepository extends JpaRepository<RuleControlObjectHeader, Long>, QuerydslPredicateExecutor<RuleControlObjectHeader> {
    @Query(value = "SELECT * FROM bbom_rule_control_object_header WHERE bbom_rule_control_object_header.rule_line_id = ?1 AND is_deleted=0",
            nativeQuery = true)
    List<RuleControlObjectHeader> listByRuleLineId(Long ruleLineId);

    List<RuleControlObjectHeader> findByRuleLineIdAndControlObject(Long ruleLineId, String bomStructure);

    List<RuleControlObjectHeader> findByRuleLineIdInAndControlObject(List<Long> ruleLineIds, String bomStructure);
}
