package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.SiliconCellGradeDTO;
import com.trinasolar.scp.bbom.domain.query.SiliconCellGradeQuery;
import com.trinasolar.scp.bbom.domain.save.SiliconCellGradeSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 硅片等级与电池等级映射 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
public interface SiliconCellGradeService {
    /**
     * 分页获取硅片等级与电池等级映射
     *
     * @param query 查询对象
     * @return 硅片等级与电池等级映射分页对象
     */
    Page<SiliconCellGradeDTO> queryByPage(SiliconCellGradeQuery query);

    /**
     * 根据主键获取硅片等级与电池等级映射详情
     *
     * @param id 主键
     * @return 硅片等级与电池等级映射详情
     */
    SiliconCellGradeDTO queryById(Long id);

    /**
     * 保存或更新硅片等级与电池等级映射
     *
     * @param saveDTO 硅片等级与电池等级映射保存对象
     * @return 硅片等级与电池等级映射对象
     */
    SiliconCellGradeDTO save(SiliconCellGradeSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除硅片等级与电池等级映射
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(SiliconCellGradeQuery query, HttpServletResponse response);
}

