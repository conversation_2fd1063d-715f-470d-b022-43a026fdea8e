package com.trinasolar.scp.bbom.service.feign;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ApsFeign
 * @Description DP服务feign调用
 * @Date 2023/11/30 10:07
 **/

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.CellRelationDTO;
import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.dto.MwCoefficientDTO;
import com.trinasolar.scp.bbom.domain.enums.FeignConstant;
import com.trinasolar.scp.bbom.domain.query.ModuleBasePlaceQuery;
import com.trinasolar.scp.bbom.domain.query.MwCoefficientQuery;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * DP服务feign调用
 *
 * @author: darke
 * @create: 2022年6月28日09:12:08
 */
@FeignClient(path = FeignConstant.SCP_APS_API, value = FeignConstant.SCP_APS_API, fallbackFactory = ApsFeignFallbackFactory.class/*, url = "https://scpapitest.trinasolar.com"*/)
public interface ApsFeign {

    /**
     * 获取通过基地获取该基地+车间下的单元
     */
    @PostMapping("/module-base-place/findByBasePlaceAndWorkshopAndWorkunit")
    @ApiOperation(value = " 获取该基地+车间+单元下的数据")
    ResponseEntity<Results<ModuleBasePlaceDTO>> findByBasePlaceAndWorkshopAndWorkunit(@RequestBody ModuleBasePlaceQuery query);


    /**
     * 获取该基地+车间+单元下的数据-新
     *
     * @param query
     * @return
     */
    @PostMapping("/module-base-place/findListByBasePlaceAndWorkshopAndWorkunit")
    @ApiOperation(value = " 获取该基地+车间+单元下的数据")
    ResponseEntity<Results<List<ModuleBasePlaceDTO>>> findListByBasePlaceAndWorkshopAndWorkunit(@RequestBody ModuleBasePlaceQuery query);

    /**
     * 获取该基地+车间+单元下的数据-新
     *
     * @param query
     * @return
     */
    @PostMapping("/module-base-place/allData")
    @ApiOperation(value = " 获取所有数据")
    ResponseEntity<Results<List<ModuleBasePlaceDTO>>> allData(@RequestBody ModuleBasePlaceQuery query);

    /**
     * 根据电池类型获取电池WM系数
     */
    @PostMapping("/mw-coefficient/list")
    @ApiOperation(value = " 根据电池类型获取电池WM系数")
    ResponseEntity<Results<List<MwCoefficientDTO>>> findMwCoefficient(@RequestBody MwCoefficientQuery query);

    /**
     * 电池系列与料号关系列表
     */
    @PostMapping("/cell-relation/queryByMaterialNoList")
    @ApiOperation(value = " 电池系列与料号关系列表")
    ResponseEntity<Results<List<CellRelationDTO>>> queryByMaterialNoList(@RequestBody List<String> materialNoList);
    @PostMapping("/module-base-place/findCellBasePlace")
    @ApiOperation(value = "电池基地查询")
    ResponseEntity<Results<List<String>>> getCellBasePlace();
}
