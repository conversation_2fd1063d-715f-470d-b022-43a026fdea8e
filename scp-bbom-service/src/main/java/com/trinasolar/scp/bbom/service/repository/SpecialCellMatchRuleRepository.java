package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.SpecialCellMatchRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Repository
public interface SpecialCellMatchRuleRepository extends JpaRepository<SpecialCellMatchRule, Long>, QuerydslPredicateExecutor<SpecialCellMatchRule> {
}
