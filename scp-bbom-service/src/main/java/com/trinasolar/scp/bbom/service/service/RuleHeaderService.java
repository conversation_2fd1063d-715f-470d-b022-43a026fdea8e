package com.trinasolar.scp.bbom.service.service;


import com.trinasolar.scp.bbom.domain.dto.RuleHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleLineDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleHeader;
import com.trinasolar.scp.bbom.domain.query.BatteryWorkShopQuery;
import com.trinasolar.scp.bbom.domain.query.RuleHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.RuleHeaderSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * BOM规则头表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 15:49:10
 */
public interface RuleHeaderService {
    Page<RuleHeader> queryByPage(RuleHeaderQuery query);

    RuleHeaderDTO queryById(Long id);

    RuleHeaderDTO save(RuleHeaderSaveDTO saveDTO);

    void deleteById(Long id);

    void export(HttpServletResponse response, RuleHeaderQuery query) throws IOException;

    RuleHeader getByRuleNumber(String ruleNumber);

    List<RuleLineDTO> listLinesByLov(RuleHeaderQuery query);

    /**
     * 电池投产导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void exportRuleDetail(RuleHeaderQuery query, HttpServletResponse response);

    /**
     * 品类匹配规则导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void exportRule(RuleHeaderQuery query, HttpServletResponse response, String exportName);

    Map<String, List<String>> getBatteryWorkShopInfo(List<BatteryWorkShopQuery> query);

    List<RuleLineDTO> listLineByRuleCategoryId(Long ruleCategoryId);
}

