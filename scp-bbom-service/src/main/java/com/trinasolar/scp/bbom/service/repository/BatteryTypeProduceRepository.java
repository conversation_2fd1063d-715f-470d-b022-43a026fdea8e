package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.BatteryTypeProduce;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * 电池类型动态属性-产出电池类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Repository
public interface BatteryTypeProduceRepository extends JpaRepository<BatteryTypeProduce, Long>, QuerydslPredicateExecutor<BatteryTypeProduce> {
}
