package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.google.common.collect.ComparisonChain;
import com.google.common.collect.Lists;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.BatterySiliconWaferDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.entity.BatterySiliconWafer;
import com.trinasolar.scp.bbom.domain.entity.QBatterySiliconWafer;
import com.trinasolar.scp.bbom.domain.enums.ItemCategoryEnum;
import com.trinasolar.scp.bbom.domain.excel.BatterySiliconWaferExcelDTO;
import com.trinasolar.scp.bbom.domain.query.BatterySiliconWaferQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsQuery;
import com.trinasolar.scp.bbom.domain.save.BatterySiliconWaferSaveDTO;
import com.trinasolar.scp.bbom.service.repository.BatterySiliconWaferRepository;
import com.trinasolar.scp.bbom.service.service.BatterySiliconWaferService;
import com.trinasolar.scp.bbom.service.service.BatteryTypeMainService;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.bbom.service.util.AttrUtil;
import com.trinasolar.scp.bbom.service.util.DateOverlapChecker;
import com.trinasolar.scp.bbom.service.util.LovUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-硅片
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Slf4j
@Service("batterySiliconWaferService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class BatterySiliconWaferServiceImpl implements BatterySiliconWaferService {

    private static final QBatterySiliconWafer qBatterySiliconWafer = QBatterySiliconWafer.batterySiliconWafer;

    public static final String ATTR_TYPE_018 = "ATTR_TYPE_018";

    private final BatterySiliconWaferDEConvert convert;

    private final BatterySiliconWaferRepository repository;

    private final BatteryTypeMainService batteryTypeMainService;

    private final AttrUtil attrUtil;

    private final ItemsService itemsService;

    private static final Joiner groupKeyJoiner = Joiner.on("_").useForNull("null");

    @Autowired
    @Lazy
    BatterySiliconWaferService batterySiliconWaferService;

    private LovUtil lovUtil;

    private static void verify(BatterySiliconWaferSaveDTO saveDTO) {
        //判断校验
        if (StringUtils.isBlank(saveDTO.getBatteryCode())) {
            throw new BizException("bbom_valid_batteryCode_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_valid_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_valid_workshop_notBlank");
        }
        Map<String, LovLineDTO> lowCarbonFlagLovMap = LovUtils.getAllByHeaderCode("yes_or_no");
        List<String> lowCarbonFlagLovList = new ArrayList<>(lowCarbonFlagLovMap.keySet());
        Map<String, LovLineDTO> waferThicknessLovMap = LovUtils.getAllByHeaderCode("4A00100100113");
        Map<String, LovLineDTO> waferCategoryLovMap = LovUtils.getAllByHeaderCode("4A00100100109");
        if (StringUtils.isNotBlank(saveDTO.getLowCarbonFlag())
                && !lowCarbonFlagLovList.contains(saveDTO.getLowCarbonFlag())) {
            throw new BizException("是否低碳,数值维护有误,请按照规范输入");
        }
        if (StringUtils.isNotBlank(saveDTO.getWaferThickness())
                && ObjectUtils.isEmpty(waferThicknessLovMap.get(saveDTO.getWaferThickness()))) {
            throw new BizException("硅片厚度,数值维护有误,请按照规范输入");
        }
        if (StringUtils.isNotBlank(saveDTO.getWaferCategory())
                && ObjectUtils.isEmpty(waferCategoryLovMap.get(saveDTO.getWaferCategory()))) {
            throw new BizException("硅片品类,数值维护有误,请按照规范输入");
        }
//        if (StringUtils.isNotBlank(saveDTO.getOldSiliconWaferProperties()) && saveDTO.getLineQty() == null) {
//            throw new BizException("bbom_valid_line_notBlank");
//        }
//        if (StringUtils.isBlank(saveDTO.getSiliconWaferProperties())) {
//            throw new BizException("bbom_valid_siliconWaferProperties_notBlank");
//        }
//        if (StringUtils.isBlank(saveDTO.getConditionItem())) {
//            throw new BizException("bbom_valid_conditionItem_notBlank");
//        }
//        if (StringUtils.isBlank(saveDTO.getBatteryValue())) {
//            throw new BizException("bbom_valid_value_notBlank");
//        }
//        if (StringUtils.isBlank(saveDTO.getOldSiliconWaferProperties())) {
//            throw new BizException("旧硅片属性不能为空");
//        }
//        if (StringUtils.isNotBlank(saveDTO.getOldSiliconWaferProperties())) {
//            if (StringUtils.isBlank(saveDTO.getOldConditionItem())) {
//                throw new BizException("bbom_valid_oldConditionItem_notBlank");
//            }
//            if (StringUtils.isBlank(saveDTO.getOldBatteryValue())) {
//                throw new BizException("bbom_valid_oldValue_notBlank");
//            }
//        }
        if (saveDTO.getEffectiveStartDate() != null && saveDTO.getEffectiveEndDate() != null && saveDTO.getEffectiveStartDate().isAfter(saveDTO.getEffectiveEndDate())) {
            throw new BizException("有效日期_止不能小于有效日期_起");
        }
    }

    private void queryConvert(BatterySiliconWaferDTO excelDTO) {
        excelDTO.setWaferCategoryName(excelDTO.getWaferCategory());
        excelDTO.setLowCarbonFlagName(excelDTO.getLowCarbonFlag());
        if(StringUtils.isNotEmpty(excelDTO.getLowCarbonFlag()) && "是".equals(excelDTO.getLowCarbonFlag())) {
            excelDTO.setLowCarbonFlag("Y");
        }else if(StringUtils.isNotEmpty(excelDTO.getLowCarbonFlag()) && "否".equals(excelDTO.getLowCarbonFlag())){
            excelDTO.setLowCarbonFlag("N");
        }
        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        excelDTO.setWorkunitName(excelDTO.getWorkunit());
        excelDTO.setLineQty(excelDTO.getLineQty());
        if (StringUtils.isNotBlank(excelDTO.getSiliconWaferProperties())) {
            String siliconWaferPropertiesName = Optional.ofNullable(attrUtil.getAttrLineByHeaderCodeAndValue(ATTR_TYPE_018, excelDTO.getSiliconWaferProperties()))
                    .map(AttrTypeLineDTO::getAttrCnName).orElse(null);
            excelDTO.setSiliconWaferPropertiesName(siliconWaferPropertiesName);
        }
        if (StringUtils.isNumeric(excelDTO.getSiliconWaferProperties())) {
            LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(excelDTO.getSiliconWaferProperties()));
            if (lovLineDTO != null) {
                excelDTO.setSiliconWaferPropertiesName(lovLineDTO.getLovName());
            }
        }
        excelDTO.setConditionItemName(LovUtils.getNameByValue("bomOperator", excelDTO.getConditionItem()));
        excelDTO.setBatteryValueName(excelDTO.getBatteryValue());
        // 旧值
        if (StringUtils.isNotBlank(excelDTO.getOldSiliconWaferProperties())) {
            String siliconWaferPropertiesName = Optional.ofNullable(attrUtil.getAttrLineByHeaderCodeAndValue(ATTR_TYPE_018, excelDTO.getOldSiliconWaferProperties()))
                    .map(AttrTypeLineDTO::getAttrCnName).orElse(null);
            excelDTO.setOldSiliconWaferPropertiesName(siliconWaferPropertiesName);
        }
        if (StringUtils.isNumeric(excelDTO.getOldSiliconWaferProperties())) {
            LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(excelDTO.getOldSiliconWaferProperties()));
            if (lovLineDTO != null) {
                excelDTO.setOldSiliconWaferPropertiesName(lovLineDTO.getLovName());
            }
        }
        excelDTO.setOldConditionItemName(LovUtils.getNameByValue("bomOperator", excelDTO.getOldConditionItem()));
        excelDTO.setOldBatteryValueName(excelDTO.getOldBatteryValue());
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));
    }

    @Override
    public Page<BatterySiliconWaferDTO> queryByPage(BatterySiliconWaferQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<BatterySiliconWafer> page = repository.findAll(booleanBuilder, pageable);
        // lov id->name 转码
        List<BatterySiliconWaferDTO> batterySiliconWaferDTOList = convert.toDto(page.getContent());
        for (BatterySiliconWaferDTO bat : batterySiliconWaferDTOList) {
            queryConvert(bat);
        }
        return new PageImpl(batterySiliconWaferDTOList, page.getPageable(), page.getTotalElements());
    }

    @Override
    public List<BatterySiliconWaferDTO> queryByBatteryCode(BatterySiliconWaferQuery query) {
        return queryByPage(query).getContent();
    }

    @Override
    public List<BatteryWaferPropertyDTO> queryWaferPropertyList() {
        List<BatteryWaferPropertyDTO> list = new ArrayList<>();
        Map<String, LovLineDTO> batterType008 = LovUtils.getAllByHeaderCode(ATTR_TYPE_018);
        batterType008.forEach((key, value) -> {
            BatteryWaferPropertyDTO dto = new BatteryWaferPropertyDTO();
            dto.setId(value.getLovLineId());
            dto.setName(value.getLovName());
            dto.setLovId(value.getAttribute4());
            list.add(dto);
        });
        return list;
    }

    @Override
    public List<BatteryWaferPropertyDTO> queryBatteryPropertyList() {
        List<BatteryWaferPropertyDTO> list = new ArrayList<>();
        Map<String, LovLineDTO> batterType007 = LovUtils.getAllByHeaderCode("ATTR_TYPE_007");
        batterType007.forEach((key, value) -> {
            BatteryWaferPropertyDTO dto = new BatteryWaferPropertyDTO();
            dto.setId(value.getLovLineId());
            dto.setName(value.getLovName());
            dto.setLovId(value.getAttribute4());
            list.add(dto);
        });
        return list;
    }

    @Override
    public List<BatteryWaferPropertyDTO> queryWaferPropertyValueList(String id) {
        List<BatteryWaferPropertyDTO> list = new ArrayList<>();
        if (StringUtils.isNotBlank(id)) {
            Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(id);
            HashSet<Long> lovLineIdSet = new HashSet<>();
            allByHeaderCode.forEach((key, value) -> {
                Long lovLineId = value.getLovLineId();
                lovLineIdSet.add(lovLineId);
            });
            lovLineIdSet.forEach(e -> {
                Optional.ofNullable(lovLineIdSet).ifPresent(p -> {
                    BatteryWaferPropertyDTO dto = new BatteryWaferPropertyDTO();
                    LovLineDTO lovLineDTO = allByHeaderCode.get("" + e + "");
                    Optional.ofNullable(lovLineDTO).ifPresent(u -> {
                        dto.setId(u.getLovLineId());
                        dto.setName(u.getLovName());
                        list.add(dto);
                    });
                });
            });
        }
        return list;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, BatterySiliconWaferQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qBatterySiliconWafer.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryCode())) {
            booleanBuilder.and(qBatterySiliconWafer.batteryCode.eq(query.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryName())) {
            booleanBuilder.and(qBatterySiliconWafer.batteryName.eq(query.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qBatterySiliconWafer.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qBatterySiliconWafer.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getWorkunit())) {
            booleanBuilder.and(qBatterySiliconWafer.workunit.eq(query.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(query.getSiliconWaferProperties())) {
            booleanBuilder.and(qBatterySiliconWafer.siliconWaferProperties.eq(query.getSiliconWaferProperties()));
        }
        if (StringUtils.isNotEmpty(query.getConditionItem())) {
            booleanBuilder.and(qBatterySiliconWafer.conditionItem.eq(query.getConditionItem()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryValue())) {
            booleanBuilder.and(qBatterySiliconWafer.batteryValue.eq(query.getBatteryValue()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }
        if (query.getVeirfyDate() != null) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveStartDate.loe(query.getVeirfyDate()));
            booleanBuilder.and(qBatterySiliconWafer.effectiveEndDate.goe(query.getVeirfyDate()));
        }
        if (StringUtils.isNotEmpty(query.getItemCodeNew())) {
            booleanBuilder.and(qBatterySiliconWafer.itemCodeNew.eq(query.getItemCodeNew()));
        }
    }

    @Override
    public BatterySiliconWaferDTO queryById(Long id) {
        BatterySiliconWafer queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public BatterySiliconWaferDTO save(BatterySiliconWaferSaveDTO saveDTO) {
        BatterySiliconWafer newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new BatterySiliconWafer());
        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public BatterySiliconWaferDTO saveOrUpdate(BatterySiliconWaferSaveDTO saveDTO) {
        // 为空校验
        verify(saveDTO);
        // 查询 区分新增 修改结果集合
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        BatterySiliconWafer newObj = new BatterySiliconWafer();
        checkRepeat(saveDTO, booleanBuilder);
        // 补齐电池类型名称
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryCode(saveDTO.getBatteryCode());
        if (null != saveDTO.getId()) {
            booleanBuilder = new BooleanBuilder();
            booleanBuilder.and(qBatterySiliconWafer.id.eq(saveDTO.getId()));
            newObj = repository.findOne(booleanBuilder).orElse(new BatterySiliconWafer());
        }
        convert.saveDTOtoEntity(saveDTO, newObj);
        BatterySiliconWafer finalNewObj = newObj;
        Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
            finalNewObj.setBatteryName(w.getBatteryName());
        });
        repository.save(finalNewObj);
        return this.queryById(finalNewObj.getId());
    }

    //字段校验生产基地+生产车间+生产单元+硅片属性唯一性
    public void checkRepeat(BatterySiliconWaferSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        if (null != saveDTO.getId()) {
            booleanBuilder.and(qBatterySiliconWafer.id.ne(saveDTO.getId()));
        }
        //重复校验逻辑电池类型+生产基地+生产车间+生产单元+硅片属性唯一性
        if (StringUtils.isNotEmpty(saveDTO.getBatteryCode())) {
            booleanBuilder.and(qBatterySiliconWafer.batteryCode.eq(saveDTO.getBatteryCode()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getBasePlace())) {
            booleanBuilder.and(qBatterySiliconWafer.basePlace.eq(saveDTO.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getWorkshop())) {
            booleanBuilder.and(qBatterySiliconWafer.workshop.eq(saveDTO.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getWorkunit())) {
            booleanBuilder.and(qBatterySiliconWafer.workunit.eq(saveDTO.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getItemCodeNew())) {
            booleanBuilder.and(qBatterySiliconWafer.itemCodeNew.eq(saveDTO.getItemCodeNew()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getLowCarbonFlag())) {
            booleanBuilder.and(qBatterySiliconWafer.lowCarbonFlag.eq(saveDTO.getLowCarbonFlag()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getWaferThickness())) {
            booleanBuilder.and(qBatterySiliconWafer.waferThickness.eq(saveDTO.getWaferThickness()));
        }
        if (StringUtils.isNotEmpty(saveDTO.getWaferCategory())) {
            booleanBuilder.and(qBatterySiliconWafer.waferCategory.eq(saveDTO.getWaferCategory()));
        }
        if (null!=saveDTO.getEffectiveStartDate()) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveStartDate.eq(saveDTO.getEffectiveStartDate()));
        }
        if (null!=saveDTO.getEffectiveEndDate()) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveEndDate.eq(saveDTO.getEffectiveEndDate()));
        }
//        if (StringUtils.isNotEmpty(saveDTO.getSiliconWaferProperties())) {
//            booleanBuilder.and(qBatterySiliconWafer.siliconWaferProperties.eq(saveDTO.getSiliconWaferProperties()));
//        }
//        if (StringUtils.isNotEmpty(saveDTO.getConditionItem())) {
//            booleanBuilder.and(qBatterySiliconWafer.conditionItem.eq(saveDTO.getConditionItem()));
//        } else {
//            booleanBuilder.and(qBatterySiliconWafer.conditionItem.isNull());
//        }
//        if (StringUtils.isNotEmpty(saveDTO.getBatteryValue())) {
//            booleanBuilder.and(qBatterySiliconWafer.batteryValue.eq(saveDTO.getBatteryValue()));
//        } else {
//            booleanBuilder.and(qBatterySiliconWafer.batteryValue.isNull());
//        }
//        if (StringUtils.isNotEmpty(saveDTO.getOldSiliconWaferProperties())) {
//            booleanBuilder.and(qBatterySiliconWafer.oldSiliconWaferProperties.eq(saveDTO.getOldSiliconWaferProperties()));
//        } else {
//            booleanBuilder.and(qBatterySiliconWafer.oldSiliconWaferProperties.isNull());
//        }
//        if (StringUtils.isNotEmpty(saveDTO.getOldConditionItem())) {
//            booleanBuilder.and(qBatterySiliconWafer.oldConditionItem.eq(saveDTO.getOldConditionItem()));
//        } else {
//            booleanBuilder.and(qBatterySiliconWafer.oldConditionItem.isNull());
//        }
//        if (StringUtils.isNotEmpty(saveDTO.getOldBatteryValue())) {
//            booleanBuilder.and(qBatterySiliconWafer.oldBatteryValue.eq(saveDTO.getOldBatteryValue()));
//        } else {
//            booleanBuilder.and(qBatterySiliconWafer.oldBatteryValue.isNull());
//        }

        List<BatterySiliconWafer> siliconWafers = IterableUtils.toList(repository.findAll(booleanBuilder));

        if (CollectionUtils.isNotEmpty(siliconWafers)) {
            String errorMsg = "";
            if(null!=saveDTO.getIndex()){
                errorMsg += "第"+saveDTO.getIndex()+"行数据重复,";
            }
            throw new BizException(errorMsg+"电池类型+生产基地+生产车间+生产单元+新硅片料号+硅片厚度+硅片品类+是否低碳+有效日期_起+有效日期止不允许重复");
            // 校验时间是否重复
//            for (BatterySiliconWafer siliconWafer : siliconWafers) {
//                if(null==siliconWafer.getEffectiveStartDate()){
//                    siliconWafer.setEffectiveStartDate(LocalDate.MIN);
//                }
//                if(null==siliconWafer.getEffectiveEndDate()){
//                    siliconWafer.setEffectiveEndDate(LocalDate.MIN);
//                }
//
////              //电池类型+生产基地+生产车间+生产单元+新硅片料号+硅片厚度+硅片品类+是否低碳+有效日期_起+有效日期止不允许重复
//                if (StringUtils.equals(siliconWafer.getBatteryCode(),saveDTO.getBatteryCode())
//                && StringUtils.equals(siliconWafer.getBatteryCode(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getBasePlace(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getWorkshop(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getWorkunit(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getItemCodeNew(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getWaferThickness(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getWaferCategory(),saveDTO.getBatteryCode())
//                        && StringUtils.equals(siliconWafer.getLowCarbonFlag(),saveDTO.getBatteryCode())
//                        && siliconWafer.getEffectiveEndDate().equals(saveDTO.getEffectiveEndDate())
//                        && siliconWafer.getEffectiveStartDate().equals(saveDTO.getEffectiveStartDate())) {
//                    throw new BizException("电池类型+生产基地+生产车间+生产单元+新硅片料号+硅片厚度+硅片品类+是否低碳+有效日期_起+有效日期止不允许重复");
//                }
//            }
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(BatterySiliconWaferQuery query, HttpServletResponse response) {
        List<BatterySiliconWaferDTO> dtos = queryByPage(query).getContent();
        dtos.forEach(item->{
            item.setLowCarbonFlag(item.getLowCarbonFlagName());
        });
        List<BatterySiliconWaferExcelDTO> exportDTOS = convert.toExcelDTO(dtos);

        ExcelUtils.setExportResponseHeader(response, "硅片切换信息_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "硅片切换信息").head(BatterySiliconWaferExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    @Override
    public List<BatterySiliconWaferDTO> listByMatchItem(String batteryType, String basePlace, String workshop, String workunit, LocalDateTime verifyDate) {
        List<BatterySiliconWaferDTO> all = batterySiliconWaferService.getAllByCache();
        return all.stream().filter(i -> {
            return i.getBatteryName().equals(batteryType)
                    && i.getBasePlace().equals(basePlace)
                    && i.getWorkshop().equals(workshop)
                    && i.getWorkunit().equals(workunit)
                    && DateOverlapChecker.isOverlapping(verifyDate.toLocalDate(), i.getEffectiveStartDate(), i.getEffectiveEndDate());
        }).collect(Collectors.toList());
    }

    @Override
    public List<BatterySiliconWaferDTO> listByMatchItem(MaterielMatchHeaderDTO headerDTO) {
        return this.listByMatchItem(headerDTO.getBatteryType(), headerDTO.getBasePlace(), headerDTO.getWorkshop(),
                headerDTO.getWorkunit(), headerDTO.getDemandDate().atStartOfDay());
    }

    @Override
    @Cacheable(cacheNames = "BatterySiliconWaferService_queryAllByCache")
    public List<BatterySiliconWaferDTO> getAllByCache() {
        List<BatterySiliconWafer> all = repository.findAll();
        return convert.toDto(all);
    }

    @Override
    @SneakyThrows
    public void importsEntity(MultipartFile file) {
        List<BatterySiliconWaferExcelDTO> excelDto = new LinkedList<>();
        EasyExcel.read(file.getInputStream(), BatterySiliconWaferExcelDTO.class, new ReadListener<BatterySiliconWaferExcelDTO>() {
            @Override
            public void invoke(BatterySiliconWaferExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        //校验数据是否重复
        Map<BatterySiliconWaferExcelDTO, Long> countMap = excelDto.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        List<Long> timsList = Lists.newCopyOnWriteArrayList();
        countMap.forEach((k, v) -> {
            if (v > 1) {
                timsList.add(v);
                String errorMsg = "";
                errorMsg += "第"+timsList.size()+"行数据重复,";
                throw new BizException(errorMsg+"电池类型+生产基地+生产车间+生产单元+新硅片料号+硅片厚度+硅片品类+是否低碳+有效日期_起+有效日期止不允许重复");
            }
        });

        importVerifyAndFill(excelDto);
        importDataSave(excelDto);
    }

    private void importDataSave(List<BatterySiliconWaferExcelDTO> excelDTOS) {
        List<BatterySiliconWafer> saves = new LinkedList<>();
        List<BatterySiliconWafer> dels = new LinkedList<>();
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        for(int i=1;i<=excelDTOS.size();i++) {
            BatterySiliconWaferExcelDTO item = excelDTOS.get(i-1);
            BatterySiliconWaferSaveDTO saveDTO = new BatterySiliconWaferSaveDTO();
            BeanUtils.copyProperties(item, saveDTO);
            saveDTO.setIndex(i);
            checkRepeat(saveDTO, booleanBuilder);
        }

        Map<String, List<BatterySiliconWaferExcelDTO>> groupKeyAndDataList = excelDTOS.stream().collect(Collectors.groupingBy(
                i -> groupKeyJoiner.join(
                        i.getBatteryCode(),
                        i.getBasePlace(),
                        i.getWorkshop(),
                        i.getWorkunit(),
                        i.getSiliconWaferProperties(),
                        i.getConditionItem(),
                        i.getBatteryValue(),
                        i.getOldSiliconWaferProperties(),
                        i.getOldConditionItem(),
                        i.getOldBatteryValue()
                )
        ));

        groupKeyAndDataList.forEach((key, list) -> {
            BatterySiliconWaferExcelDTO firstExcel = list.get(0);
            clearExcelData(firstExcel, dels);

            // 按开始日期排序,校验是否有重复的数据
//            List<BatterySiliconWaferExcelDTO> sortedByStartDate = list.stream().sorted((left, right) -> ComparisonChain
//                    .start()
//                    .compare(Optional.ofNullable(left.getEffectiveStartDate()).orElse(LocalDate.MIN),
//                            Optional.ofNullable(right.getEffectiveStartDate()).orElse(LocalDate.MIN))
//                    .result()).collect(Collectors.toList());
//            BatterySiliconWaferExcelDTO curDto = null;
//            for (BatterySiliconWaferExcelDTO excelDTO : sortedByStartDate) {
//                if (curDto == null) {
//                    curDto = excelDTO;
//                    continue;
//                }
//                if (DateOverlapChecker.isOverlapping(curDto.getEffectiveStartDate(), curDto.getEffectiveEndDate(), excelDTO.getEffectiveStartDate(), excelDTO.getEffectiveEndDate())) {
//                    throw new BizException("结束时间不能小于开始时间");
//                }
//                curDto = excelDTO;
//            }

            for (BatterySiliconWaferExcelDTO excelDTO : list) {
                BatterySiliconWaferSaveDTO saveDTO = convert.excelDTOtoSaveDTO(excelDTO);
                verify(saveDTO);
                BatterySiliconWafer newObj = new BatterySiliconWafer();
                convert.saveDTOtoEntity(saveDTO, newObj);
                saves.add(newObj);
            }
        });

        repository.deleteAll(dels);
        repository.saveAll(saves);
    }

    private void clearExcelData(BatterySiliconWaferExcelDTO firstExcel, List<BatterySiliconWafer> dels) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //调整为电池类型+生产基地+生产车间+生产单元+新硅片料号+硅片厚度+硅片品类+是否低碳+有效日期_起+有效日期止不允许重复
        if (StringUtils.isNotEmpty(firstExcel.getBatteryName())) {
            booleanBuilder.and(qBatterySiliconWafer.batteryName.eq(firstExcel.getBatteryName()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getBasePlace())) {
            booleanBuilder.and(qBatterySiliconWafer.basePlace.eq(firstExcel.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getWorkshop())) {
            booleanBuilder.and(qBatterySiliconWafer.workshop.eq(firstExcel.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getWorkunit())) {
            booleanBuilder.and(qBatterySiliconWafer.workunit.eq(firstExcel.getWorkunit()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getItemCodeNew())) {
            booleanBuilder.and(qBatterySiliconWafer.itemCodeNew.eq(firstExcel.getItemCodeNew()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getLowCarbonFlag())) {
            booleanBuilder.and(qBatterySiliconWafer.lowCarbonFlag.eq(firstExcel.getLowCarbonFlag()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getWaferThickness())) {
            booleanBuilder.and(qBatterySiliconWafer.waferThickness.eq(firstExcel.getWaferThickness()));
        }
        if (StringUtils.isNotEmpty(firstExcel.getWaferCategory())) {
            booleanBuilder.and(qBatterySiliconWafer.waferCategory.eq(firstExcel.getWaferCategory()));
        }
        if (null!=firstExcel.getEffectiveStartDate()) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveStartDate.eq(firstExcel.getEffectiveStartDate()));
        }
        if (null!=firstExcel.getEffectiveEndDate()) {
            booleanBuilder.and(qBatterySiliconWafer.effectiveEndDate.eq(firstExcel.getEffectiveEndDate()));
        }
//        if (StringUtils.isNotEmpty(firstExcel.getBatteryCode())) {
//            booleanBuilder.and(qBatterySiliconWafer.effectiveStartDate.isNull().or(qBatterySiliconWafer.effectiveStartDate.loe(LocalDate.now())));
//        }
//        if (StringUtils.isNotEmpty(firstExcel.getBatteryCode())) {
//            booleanBuilder.and(qBatterySiliconWafer.effectiveEndDate.isNull().or(qBatterySiliconWafer.effectiveEndDate.goe(LocalDate.now())));
//        }

        Iterable<BatterySiliconWafer> waitDels = repository.findAll(booleanBuilder);
        dels.addAll(IterableUtils.toList(waitDels));
    }

    private List<String> getEffectItemCodeNewList() {
        ItemsQuery query = new ItemsQuery();
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setCategorySegment5(ItemCategoryEnum.SILICON.getDesc());
        query.setItemStatus("Active");
        Page<ItemsDTO> itemsDTOS = itemsService.queryByItemCode(query);
        List<ItemsDTO> itemsDTOList = itemsDTOS.getContent();
        if (org.springframework.util.CollectionUtils.isEmpty(itemsDTOList)) {
            return Collections.emptyList();
        }
        return itemsDTOList.stream().map(ItemsDTO::getItemCode).distinct().collect(Collectors.toList());
    }

    private void importVerifyAndFill(List<BatterySiliconWaferExcelDTO> excelDto) {
        // 获取电池类型Code到电池类型名称的映射
        Map<String, String> batteryTypeNameToCode = batteryTypeMainService.queryBatteryCodeTypeAll().stream().collect(Collectors.toMap(BatteryTypeMainDTO::getBatteryName, BatteryTypeMainDTO::getBatteryCode));
        List<AttrTypeLineDTO> attrTypeLineDTOS = attrUtil.queryAttrTypeLinesByHeaderCode(ATTR_TYPE_018);
        Map<String, String> siliconWaferPropertiesNameToCode = attrTypeLineDTOS.stream().collect(Collectors.toMap(AttrTypeLineDTO::getAttrCnName, AttrTypeLineDTO::getAttrCode));
        Map<String, LovLineDTO> bomOperator = LovUtils.getAllByHeaderCode("bomOperator");
        List<String> effectItemCodeNewList = this.getEffectItemCodeNewList();

        for (BatterySiliconWaferExcelDTO batterySiliconWaferExcelDTO : excelDto) {
            if (StringUtils.isNotBlank(batterySiliconWaferExcelDTO.getItemCodeNew()) && !effectItemCodeNewList.contains(batterySiliconWaferExcelDTO.getItemCodeNew())) {
                throw new BizException("bbom_valid_itemCodeNew_illegal", batterySiliconWaferExcelDTO.getItemCodeNew());
            }
            if (StringUtils.isBlank(batterySiliconWaferExcelDTO.getBatteryName())) {
                throw new BizException("bbom_valid_batteryName_notBlank");
            }
            // 转换为电池类型Code
            if (batteryTypeNameToCode.containsKey(batterySiliconWaferExcelDTO.getBatteryName())) {
                batterySiliconWaferExcelDTO.setBatteryCode(batteryTypeNameToCode.get(batterySiliconWaferExcelDTO.getBatteryName()));
            } else {
                throw new BizException("bbom_valid_batteryName_illegal", batterySiliconWaferExcelDTO.getBatteryName());
            }

            if (StringUtils.isBlank(batterySiliconWaferExcelDTO.getBasePlaceName())) {
                throw new BizException("bbom_valid_basePlace_notBlank");
            } else {
                batterySiliconWaferExcelDTO.setBasePlace(batterySiliconWaferExcelDTO.getBasePlaceName());
            }
            if (StringUtils.isBlank(batterySiliconWaferExcelDTO.getWorkshopName())) {
                throw new BizException("bbom_valid_workshop_notBlank");
            } else {
                batterySiliconWaferExcelDTO.setWorkshop(batterySiliconWaferExcelDTO.getWorkshopName());
            }
            if (StringUtils.isBlank(batterySiliconWaferExcelDTO.getWorkunitName())) {
                throw new BizException("bbom_valid_workunit_notBlank");
            } else {
                batterySiliconWaferExcelDTO.setWorkunit(batterySiliconWaferExcelDTO.getWorkunitName());
            }
//            if (!siliconWaferPropertiesNameToCode.containsKey(batterySiliconWaferExcelDTO.getSiliconWaferPropertiesName())) {
//                throw new BizException("bbom_valid_newSiliconWaferProperties_illegal");
//            } else {
//                batterySiliconWaferExcelDTO.setSiliconWaferProperties(siliconWaferPropertiesNameToCode.get(batterySiliconWaferExcelDTO.getSiliconWaferPropertiesName()));
//            }
//            if (!bomOperator.containsKey(batterySiliconWaferExcelDTO.getConditionItemName())) {
//                throw new BizException("bbom_valid_newConditionItem_notBlank");
//            } else {
//                batterySiliconWaferExcelDTO.setConditionItem(bomOperator.get(batterySiliconWaferExcelDTO.getConditionItemName()).getLovValue());
//            }
//            if (StringUtils.isNotBlank(batterySiliconWaferExcelDTO.getBatteryValueName())) {
//                batterySiliconWaferExcelDTO.setBatteryValue(batterySiliconWaferExcelDTO.getBatteryValueName());
//            }
//            if (StringUtils.isNotBlank(batterySiliconWaferExcelDTO.getOldSiliconWaferPropertiesName())
//                    && batterySiliconWaferExcelDTO.getLineQty() == null) {
//                throw new BizException("bbom_valid_line_notBlank");
//            } else {
//                batterySiliconWaferExcelDTO.setLineQty(batterySiliconWaferExcelDTO.getLineQty());
//            }
//            if (StringUtils.isNotBlank(batterySiliconWaferExcelDTO.getOldSiliconWaferPropertiesName())) {
//                if (!siliconWaferPropertiesNameToCode.containsKey(batterySiliconWaferExcelDTO.getOldSiliconWaferPropertiesName())) {
//                    throw new BizException("bbom_valid_oldSiliconWaferProperties_illegal");
//                } else {
//                    batterySiliconWaferExcelDTO.setOldSiliconWaferProperties(siliconWaferPropertiesNameToCode.get(batterySiliconWaferExcelDTO.getOldSiliconWaferPropertiesName()));
//                }
//                if (!bomOperator.containsKey(batterySiliconWaferExcelDTO.getOldConditionItemName())) {
//                    throw new BizException("bbom_valid_oldConditionItem_notBlank");
//                } else {
//                    batterySiliconWaferExcelDTO.setOldConditionItem(bomOperator.get(batterySiliconWaferExcelDTO.getOldConditionItemName()).getLovValue());
//                }
//                if (StringUtils.isNotBlank(batterySiliconWaferExcelDTO.getOldBatteryValueName())) {
//                    batterySiliconWaferExcelDTO.setOldBatteryValue(batterySiliconWaferExcelDTO.getOldBatteryValueName());
//                }
//            }

            if (batterySiliconWaferExcelDTO.getEffectiveStartDate() != null && batterySiliconWaferExcelDTO.getEffectiveEndDate() != null
                    && batterySiliconWaferExcelDTO.getEffectiveStartDate().isAfter(batterySiliconWaferExcelDTO.getEffectiveEndDate())) {
                throw new BizException("有效日期_止不能小于有效日期_起");
            }
        }
    }
}
