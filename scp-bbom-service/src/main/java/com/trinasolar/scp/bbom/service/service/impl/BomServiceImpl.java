package com.trinasolar.scp.bbom.service.service.impl;

import com.trinasolar.scp.bbom.domain.dto.ConversionCoefficientMwDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpOperatRouteDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalAttrMapViewDTO;
import com.trinasolar.scp.bbom.domain.query.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.bbom.domain.query.ErpOperatRouteQuery;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.service.BomService;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/14
 */
@Slf4j
@Service("bomService")
@RequiredArgsConstructor
@CacheConfig(cacheNames = "BomService", cacheManager = "caffeineCacheManager")
public class BomServiceImpl implements BomService {
    private final BomFeign bomFeign;

    @Override
    public List<ExternalAttrMapViewDTO> findBySrcCategorySegment4(String srcCategorySegment4) {
        IdDTO idDTO = new IdDTO();
        idDTO.setId(srcCategorySegment4);
        ResponseEntity<Results<List<ExternalAttrMapViewDTO>>> bySrcCategorySegment4 = bomFeign.findBySrcCategorySegment4(idDTO);
        if (!bySrcCategorySegment4.getBody().isSuccess()) {
            throw new RuntimeException("调用BomFeign失败");
        }
        return bySrcCategorySegment4.getBody().getData();
    }

    /**
     * 电池MW系数 调用接口 MOCK数据
     *
     * @return
     */
    @Override
    public List<ConversionCoefficientMwDTO> getBatteryMWQty() {
        // Todo  2.电池MW系数 电池目标良率 调用2个接口 MOCK数据
        List<ConversionCoefficientMwDTO> list = new ArrayList<>();
        ConversionCoefficientMwDTO dto = new ConversionCoefficientMwDTO();
        BigDecimal batteryMWQty = new BigDecimal(2);
        dto.setBatteryMwQty(batteryMWQty);
        list.add(dto);
        return list;
    }

    /**
     * 电池目标良率 调用接口 MOCK数据
     *
     * @return
     */
    @Override
    public List<ConversionCoefficientMwDTO> getBatteryEfficiencyQty() {
        // Todo  2.电池MW系数 电池目标良率 调用2个接口 MOCK数据
        List<ConversionCoefficientMwDTO> list = new ArrayList<>();
        ConversionCoefficientMwDTO dto = new ConversionCoefficientMwDTO();
        BigDecimal batteryEfficiencyQty = new BigDecimal(3);
        dto.setBatteryEfficiencyQty(batteryEfficiencyQty);
        list.add(dto);
        return list;
    }

    @Override
    public ErpAlternateDesignatorDTO findErpAlternateDesignator(Long orgId, String designator) {
        ResponseEntity<Results<ErpAlternateDesignatorDTO>> resultsResponseEntity = bomFeign.queryByOrganizationIdAndDescription(new ErpAlternateDesignatorQuery().setOrganizationId(orgId).setDescription(designator));
        ErpAlternateDesignatorDTO data = resultsResponseEntity.getBody().getData();
        if (data == null) {
            throw new BizException("bbom_valid_alternateDesignator_notFound", orgId, designator);
        }
        return data;
    }

    @Override
    @Cacheable(cacheNames = "BomService_getErpAlternateDesignatorDTOByWorkshop", key = "#workshop", unless = "#result == null")
    public ErpAlternateDesignatorDTO getErpAlternateDesignatorDTOByWorkshop(String workshop) {
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        designatorQuery.setDescription(workshop);
        ErpAlternateDesignatorDTO designatorDTO = bomFeign.queryByOrgID(designatorQuery).getBody().getData();
        return designatorDTO;
    }

    @Override
    @Cacheable(cacheNames = "BomService_findBy5AAndAlternateRoutingDesignator", key = "#itemCode+'_'+#alternateBomDesignator")
    public ErpOperatRouteDTO findBy5AAndAlternateRoutingDesignator(String itemCode, String alternateBomDesignator) {
        ErpOperatRouteDTO erpOperatRouteDTO = bomFeign.findBy5AAndAlternateRoutingDesignator(new com.trinasolar.scp.bbom.domain.query.ErpOperatRouteQuery().setInventoryItemNo(itemCode).setAlternateRoutingDesignator(alternateBomDesignator)).getBody().getData();
        return erpOperatRouteDTO;
    }

    @Override
    @Cacheable(cacheNames = "BomService_getErpOperatRouteList", unless = "#result == null")
    public Map<String, ErpOperatRouteDTO> getErpOperatRouteList() {
        ErpOperatRouteQuery erpOperatRouteQuery = new ErpOperatRouteQuery();
        List<ErpOperatRouteDTO> routeDTOList = bomFeign.list(erpOperatRouteQuery).getBody().getData();
        Map<String, ErpOperatRouteDTO> erpOperatRouteDTOMap = routeDTOList.stream().collect(Collectors.toMap(ele -> String.format("%s%s%s", ele.getAlternateRoutingDesignator(), ele.getOrganizationId(), ele.getAssemblyItemId()),
                Function.identity(), (v1, v2) -> v1));
        return erpOperatRouteDTOMap;
    }
}
