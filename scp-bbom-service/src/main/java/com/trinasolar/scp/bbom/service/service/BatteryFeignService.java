package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.query.ItemCodesQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryFeignService
 * @Description
 * @Date 2023/12/13 14:34
 **/
public interface BatteryFeignService {

    /**
     * 查询校验基地车间生产单元是否合法
     *
     * @param basePlace
     * @param workshop
     * @param workunit
     * @return
     */
    List<ModuleBasePlaceDTO> findBasePlaceWorkshopWorkUnit(String basePlace, String workshop, String workunit);

    /**
     * 获取网版料号信息
     *
     * @param query
     * @return
     */
    Map<String, String> findItemDescByItemCodes(ItemCodesQuery query);
}
