package com.trinasolar.scp.bbom.service.service.impl;

import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.bbom.domain.convert.MaterielMatchLineMatchStatusDEConvert;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineMatchStatusDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLineMatchStatus;
import com.trinasolar.scp.bbom.domain.entity.QMaterielMatchLineMatchStatus;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchLineMatchStatusQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineMatchStatusSaveDTO;
import com.trinasolar.scp.bbom.service.repository.MaterielMatchLineMatchStatusRepository;
import com.trinasolar.scp.bbom.service.service.MaterielMatchLineMatchStatusService;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Slf4j
@Service("materielMatchLineMatchStatusService")
@RequiredArgsConstructor

public class MaterielMatchLineMatchStatusServiceImpl implements MaterielMatchLineMatchStatusService {
    private static final QMaterielMatchLineMatchStatus qMaterielMatchLineMatchStatus = QMaterielMatchLineMatchStatus.materielMatchLineMatchStatus;

    private final MaterielMatchLineMatchStatusDEConvert convert;

    private final MaterielMatchLineMatchStatusRepository repository;

    @Override
    public Page<MaterielMatchLineMatchStatusDTO> queryByPage(MaterielMatchLineMatchStatusQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<MaterielMatchLineMatchStatus> page = repository.findAll(booleanBuilder, pageable);
        return new PageImpl(convert.toDto(page.getContent()), page.getPageable(), page.getTotalElements());
    }

    private void buildWhere(BooleanBuilder booleanBuilder, MaterielMatchLineMatchStatusQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.id.eq(query.getId()));
        }
        if (query.getLineId() != null) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.lineId.eq(query.getLineId()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.itemCode.eq(query.getItemCode()));
        }
        if (StringUtils.isNotEmpty(query.getItemDesc())) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.itemDesc.eq(query.getItemDesc()));
        }
        if (StringUtils.isNotEmpty(query.getMatchStatus())) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.matchStatus.eq(query.getMatchStatus()));
        }
        if (StringUtils.isNotEmpty(query.getAlternateBomDesignator())) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.alternateBomDesignator.eq(query.getAlternateBomDesignator()));
        }
        if (StringUtils.isNotEmpty(query.getIsCatchProduction())) {
            booleanBuilder.and(qMaterielMatchLineMatchStatus.isCatchProduction.eq(query.getIsCatchProduction()));
        }
    }

    @Override
    public MaterielMatchLineMatchStatusDTO queryById(Long id) {
        MaterielMatchLineMatchStatus queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    public MaterielMatchLineMatchStatusDTO save(MaterielMatchLineMatchStatusSaveDTO saveDTO) {
        MaterielMatchLineMatchStatus newObj = Optional.ofNullable(saveDTO.getId())
                .flatMap(repository::findById)
                .orElse(new MaterielMatchLineMatchStatus());

        newObj = convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }


    @Override
    @SneakyThrows
    public void export(MaterielMatchLineMatchStatusQuery query, HttpServletResponse response) {
        List<MaterielMatchLineMatchStatusDTO> dtos = queryByPage(query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "电池料号匹配明细行匹配状态", "电池料号匹配明细行匹配状态", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByLineIds(List<Long> lineIds) {
        List<Long> delLineIds = lineIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delLineIds)) {
            repository.deleteAll(repository.selectByLineId(delLineIds));
        }
    }

    @Override
    public void saveByLine(MaterielMatchLine materielMatchLine, List<MaterielMatchLineMatchStatusDTO> matchLineMatchStatusDTOS) {
        if (CollectionUtils.isNotEmpty(matchLineMatchStatusDTOS)) {
            matchLineMatchStatusDTOS.forEach(i -> {
                i.setLineId(materielMatchLine.getId());
                i.setMatchStatus(materielMatchLine.getMatchStatus());
            });
            convert.dtoToSaveDTO(matchLineMatchStatusDTOS).forEach(this::save);
        }
    }

    @Override
    public List<MaterielMatchLineMatchStatusDTO> queryByLineId(Long lineId) {
        return convert.toDto(repository.findByLineId(lineId));
    }

    public List<MaterielMatchLineMatchStatusDTO> queryAll() {
        return convert.toDto(repository.findAll());
    }

    @Override
    public List<MaterielMatchLineMatchStatusDTO> queryByLineIds(List<Long> lineIdList) {
        Iterable<MaterielMatchLineMatchStatus> all = repository.findAll(
                qMaterielMatchLineMatchStatus.lineId.in(lineIdList)
        );

        return IterableUtils.toList(all).parallelStream().map(convert::toDto).collect(Collectors.toList());
    }

    @Override
    public void clearErrorData() {
        List<MaterielMatchLineMatchStatus> all = repository.findAll();
        all.stream().collect(Collectors.groupingBy(i -> i.getLineId() + "_" + i.getItemCode())).forEach((k, v) -> {
            if (v.size() > 1) {
                v.stream().sorted(Comparator.comparing(BasePO::getCreatedTime).reversed()).skip(1).forEach(repository::delete);
            }
        });
    }
}
