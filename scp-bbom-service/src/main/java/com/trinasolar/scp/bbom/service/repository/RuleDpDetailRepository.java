package com.trinasolar.scp.bbom.service.repository;

import com.trinasolar.scp.bbom.domain.entity.RuleDpDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * BOM规则DP因子
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Repository
public interface RuleDpDetailRepository extends JpaRepository<RuleDpDetail, Long>, QuerydslPredicateExecutor<RuleDpDetail> {

    @Query(value = "SELECT * FROM bbom_rule_dp_detail WHERE bbom_rule_dp_detail.rule_line_id = ?1 AND is_deleted=0",
            nativeQuery = true)
    List<RuleDpDetail> listByRuleLineId(Long ruleLineId);

    List<RuleDpDetail> findByRuleLineId(Long ruleLineId);
}
