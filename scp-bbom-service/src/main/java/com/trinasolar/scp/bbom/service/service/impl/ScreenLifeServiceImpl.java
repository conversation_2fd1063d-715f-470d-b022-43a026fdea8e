package com.trinasolar.scp.bbom.service.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.ScreenLifeDEConvert;
import com.trinasolar.scp.bbom.domain.dto.*;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.entity.*;
import com.trinasolar.scp.bbom.domain.enums.CodeEnum;
import com.trinasolar.scp.bbom.domain.excel.ScreenLifeExcelDTO;
import com.trinasolar.scp.bbom.domain.query.*;
import com.trinasolar.scp.bbom.domain.save.ScreenLifeSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.DateUtil;
import com.trinasolar.scp.bbom.domain.utils.FileUtil;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.BomFeign;
import com.trinasolar.scp.bbom.service.repository.ScreenLifeRepository;
import com.trinasolar.scp.bbom.service.service.*;
import com.trinasolar.scp.bbom.service.service.impl.scheduleEmail.SCPFileService;
import com.trinasolar.scp.bbom.service.util.CodeUtil;
import com.trinasolar.scp.bbom.service.util.LocalDateTimeUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网版寿命信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Slf4j
@Service("screenLifeService")
@RequiredArgsConstructor
public class ScreenLifeServiceImpl implements ScreenLifeService {
    private static final QScreenLife qScreenLife = QScreenLife.screenLife;

    private final static String BASE_PLACE = "base_place";

    private final static String WORK_SHOP = "work_shop";

    private final static String CELL_MACHINE = "CELL_MACHINE";

    private static final QItems qItems = QItems.items;

    private final ScreenLifeDEConvert convert;

    private final ScreenLifeRepository repository;

    private final BatteryTypeMainService batteryTypeMainService;

    private final BatteryFeignService batteryFeignService;

    private final StructuresService structuresService;

    private final JPAQueryFactory jpaQueryFactory;

    private final BomFeign bomFeign;

    private final ComponentsService componentsService;

    private final ItemsService itemsService;

    private final MailService mailService;

    private final SlurryInformationService informationService;

    @Autowired
    private SCPFileService scpFileService;

    private static void extracted(ScreenLifeSaveDTO dto, BooleanBuilder booleanBuilder) {
        if (null != dto.getId()) {
            booleanBuilder.and(qScreenLife.id.ne(dto.getId()));
        }
        //版本
//        if (StringUtils.isNotEmpty(dto.getVersion())) {
//            booleanBuilder.and(qScreenLife.version.eq(dto.getVersion()));
//        } else {
//            booleanBuilder.and(qScreenLife.version.isNull());
//        }
        //电池类型
        if (StringUtils.isNotEmpty(dto.getBatteryType())) {
            booleanBuilder.and(qScreenLife.batteryType.eq(dto.getBatteryType()));
        } else {
            booleanBuilder.and(qScreenLife.batteryType.isNull());
        }
        //网版料号
        if (StringUtils.isNotEmpty(dto.getItemCode())) {
            booleanBuilder.and(qScreenLife.itemCode.eq(dto.getItemCode()));
        } else {
            booleanBuilder.and(qScreenLife.itemCode.isNull());
        }
        //生产基地
        if (StringUtils.isNotEmpty(dto.getBasePlace())) {
            booleanBuilder.and(qScreenLife.basePlace.eq(dto.getBasePlace()));
        } else {
            booleanBuilder.and(qScreenLife.basePlace.isNull());
        }
        //生产车间
        if (StringUtils.isNotEmpty(dto.getWorkshop())) {
            booleanBuilder.and(qScreenLife.workshop.eq(dto.getWorkshop()));
        } else {
            booleanBuilder.and(qScreenLife.workshop.isNull());
        }
        //机台
        if (StringUtils.isNotEmpty(dto.getMachine())) {
            booleanBuilder.and(qScreenLife.machine.eq(dto.getMachine()));
        } else {
            booleanBuilder.and(qScreenLife.machine.isNull());
        }
        //主栅信息
        if (StringUtils.isNotEmpty(dto.getMainGridInfo())) {
            booleanBuilder.and(qScreenLife.mainGridInfo.eq(dto.getMainGridInfo()));
        } else {
            booleanBuilder.and(qScreenLife.mainGridInfo.isNull());
        }
        //主栅间距
        if (StringUtils.isNotEmpty(dto.getMainGridSpace())) {
            booleanBuilder.and(qScreenLife.mainGridSpace.eq(dto.getMainGridSpace()));
        } else {
            booleanBuilder.and(qScreenLife.mainGridSpace.isNull());
        }
        //单玻
        if (StringUtils.isNotEmpty(dto.getSingleGlassFlag())) {
            booleanBuilder.and(qScreenLife.singleGlassFlag.eq(dto.getSingleGlassFlag()));
        } else {
            booleanBuilder.and(qScreenLife.singleGlassFlag.isNull());
        }
    }

    /**
     * 保存新增校验
     *
     * @param saveDTO
     */
    private static void verifySave(ScreenLifeSaveDTO saveDTO) {
        // 新增修改校验
        if (StringUtils.isBlank(saveDTO.getBatteryType())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getMachine())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }

        // 公共部分
//        if (StringUtils.isBlank(saveDTO.getItemCode())) {
//            throw new BizException("bbom_valid_itemCode_notBlank");
//        }
        if (StringUtils.isBlank(saveDTO.getLifetime())) {
            throw new BizException("bbom_valid_lifetime_illegal");
        }
    }

    /**
     * 导入校验
     *
     * @param saveDTO
     */
    private static void verifyImport(ScreenLifeExcelDTO saveDTO) {
        // 导入校验
        if (StringUtils.isBlank(saveDTO.getBatteryTypeName())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlaceName())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshopName())) {
            throw new BizException("bbom_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getMachineName())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }

        // 公共部分
//        if (StringUtils.isBlank(saveDTO.getItemCode())) {
//            throw new BizException("bbom_valid_itemCode_notBlank");
//        }
        if (StringUtils.isBlank(saveDTO.getLifetime())) {
            throw new BizException("bbom_valid_lifetime_illegal");
        }
    }

    private String getCheckKey(ScreenLifeExcelDTO excelDTO) {
        StringBuilder builder = new StringBuilder();
        // 20250321 update ->电池类型+网版料号+生产基地+生产车间+机台+主栅信息+主栅间距+单玻的数据组合不允许重复
        builder.append(excelDTO.getBatteryType()).append("-").append(excelDTO.getItemCode()).append("-")
                .append(excelDTO.getBasePlace()).append("-").append(excelDTO.getWorkshop()).append("-")
                .append(excelDTO.getMachine()).append("-")
                .append(excelDTO.getMainGridInfo()).append("-")
                .append(excelDTO.getMainGridSpace()).append("-")
                .append(excelDTO.getSingleGlassFlagName());
        if (StringUtils.isNotEmpty(builder.toString())) {
            return builder.toString();
            //throw new BizException("bbom_valid_itemCode_repeat", excelDTO.getItemCode());
        }
        return null;
    }

    private String checkIsDuplicate(List<ScreenLifeExcelDTO> excelDTOList) {
        String errorMsg = null;
        Set<String> set = new HashSet();
        List<String> duplicates = new ArrayList();
        List<String> list = new ArrayList<>();

        for (ScreenLifeExcelDTO excelDTO : excelDTOList) {
            String element = getCheckKey(excelDTO);
            list.add(element);
            if (!set.add(element)) {
                // 如果无法成功添加到set，说明是重复元素
                if (!duplicates.contains(element)) {
                    duplicates.add(element);
                }
            }
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(duplicates)) {
            StringBuffer sbData = new StringBuffer();
            for (String duplicate : duplicates) {
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < excelDTOList.size(); i++) {
                    if (list.get(i).equals(duplicate)) {
                        sb.append("第" + (i + 1) + "行和");
                    }
                }
                if(org.springframework.util.StringUtils.hasText(sb.toString())){
                    String sbStr = sb.substring(0, sb.length() - 1);
                    sbData.append(new StringBuffer(sbStr).append("存在重复数据,").append("</br>"));
                }
            }
            if(sbData.toString() != null){
                sbData.append("电池类型+网版料号+生产基地+生产车间+机台+主栅信息+主栅间距+单玻不允许重复");

            }
            errorMsg = sbData.toString();
        }
        return errorMsg;
    }

    private static void extractedSave(ScreenLifeSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        if (null != saveDTO.getId()) {
            booleanBuilder.and(qScreenLife.id.eq(saveDTO.getId()));
        }
    }

    private static void extractedImport(ScreenLifeExcelDTO excelDTO, BooleanBuilder booleanBuilder) {
        if (StringUtils.isNotEmpty(excelDTO.getBatteryType())) {
            booleanBuilder.and(qScreenLife.batteryType.eq(excelDTO.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getItemCode())) {
            booleanBuilder.and(qScreenLife.itemCode.eq(excelDTO.getItemCode()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getBasePlace())) {
            booleanBuilder.and(qScreenLife.basePlace.eq(excelDTO.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getWorkshop())) {
            booleanBuilder.and(qScreenLife.workshop.eq(excelDTO.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getMachine())) {
            booleanBuilder.and(qScreenLife.machine.eq(excelDTO.getMachine()));
        }
    }

    @Override
    public Page<ScreenLifeDTO> queryByPage(String userId, ScreenLifeQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //查询用户数据权限
        List<DataPrivilegeDTO> privilegeDTOList = informationService.getDataPrivilegeDTOS(userId, LovHeaderCodeConstant.BASE_PLACE);
        if (CollectionUtils.isNotEmpty(privilegeDTOList)) {
            List<Long> ids = privilegeDTOList.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
            if (StringUtils.isEmpty(query.getBasePlace())) {
                if (!ids.contains(-1L)) {
                    booleanBuilder.and(qScreenLife.basePlaceId.in(ids));
                }
            }
        } else {
            booleanBuilder.and(qScreenLife.basePlaceId.in(-1L));
        }
        Page<ScreenLife> page = repository.findAll(booleanBuilder, pageable);
        List<ScreenLifeDTO> screenLifeList = convert.toDto(page.getContent());
        for (ScreenLifeDTO life : screenLifeList) {
            queryConvert(life);
        }
        return new PageImpl(screenLifeList, page.getPageable(), page.getTotalElements());
    }

    public List<ScreenLifeDTO> queryList() {
        Iterable<ScreenLife> lifeList = repository.findAll();
        return convert.toDto(IterableUtils.toList(lifeList));
    }

    public ScreenLifeDTO queryList(ScreenLifeQuery query) {
        if (StringUtils.isEmpty(query.getBatteryType())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isEmpty(query.getBasePlace())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isEmpty(query.getWorkshop())) {
            throw new BizException("bbom_workshop_notBlank");
        }
//        if (StringUtils.isEmpty(query.getItemCode())) {
//            throw new BizException("bbom_valid_itemCode_notBlank");
//        }
        JPAQuery<ScreenLife> screenLifeJPAQuery = jpaQueryFactory.select(qScreenLife).from(qScreenLife)
                .where(qScreenLife.batteryType.eq(query.getBatteryType())
                        .and(qScreenLife.basePlace.eq(query.getBasePlace()))
                        .and(qScreenLife.workshop.eq(query.getWorkshop()))
                        .and(qScreenLife.itemCode.eq(query.getItemCode()))).orderBy(qScreenLife.createdTime.desc());
        ScreenLife life = screenLifeJPAQuery.fetchFirst();
        if (null == life) {
            return null;
        }
        ScreenLifeDTO lifeDTO = convert.toDto(life);
        //1/(寿命*10000)
        BigDecimal lifeTotal = new BigDecimal(lifeDTO.getLifetime()).multiply(new BigDecimal("10000"));
        BigDecimal lifeTime = BigDecimal.ONE.divide(lifeTotal, 8, RoundingMode.HALF_UP);
        lifeDTO.setLifetime(lifeTime.toString());
        return lifeDTO;
    }

    private void queryConvert(ScreenLifeDTO excelDTO) {
        // 电池类型 id->name 转码用别名
        // 转换电池名称
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryCode(excelDTO.getBatteryType());
        if (StringUtils.isNotBlank(batteryTypeMainDTO.getBatteryName())) {
            excelDTO.setBatteryTypeName(batteryTypeMainDTO.getBatteryName());
        }
        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        if (StringUtils.isNumeric(excelDTO.getMachine())) {
            LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(excelDTO.getMachine()));
            if (lovLineDTO != null) {
                excelDTO.setMachineName(lovLineDTO.getLovName());
            }
        }
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));

        LovLineDTO yesOrNoLov = LovUtils.get("yes_or_no", excelDTO.getSingleGlassFlag());
        if(Objects.nonNull(yesOrNoLov)){
            excelDTO.setSingleGlassFlagName(yesOrNoLov.getLovName());
        }

        Map<String, LovLineDTO> mainGridInfoLov = LovUtils.getAllByHeaderCode("7A01500100117");
        if (Objects.nonNull(mainGridInfoLov)) {
            LovLineDTO mainGridInfoDtoLov = mainGridInfoLov.get(excelDTO.getMainGridInfo());
            if (Objects.nonNull(mainGridInfoDtoLov)) {
                excelDTO.setMainGridInfo(mainGridInfoDtoLov.getLovValue());
            }
        }

        Map<String, LovLineDTO> mainLov = LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
        LovLineDTO mainGridSpaceLov = mainLov.get(excelDTO.getMainGridSpace());
        if(Objects.nonNull(mainGridSpaceLov) && "main_grid_space".equals(mainGridSpaceLov.getAttribute1())){
            excelDTO.setMainGridSpace(mainGridSpaceLov.getLovValue());
        }

        //获取库存组织
        LovLineDTO workShop = LovUtils.get("work_shop", excelDTO.getWorkshop());
        //根据库存组织查询
        LovLineDTO organization = LovUtils.get("inventory_organization", workShop.getAttribute10());
        if (null != organization) {
            ItemsQuery itemsQuery = new ItemsQuery();
            itemsQuery.setItemCode(excelDTO.getItemCode());
            itemsQuery.setOrganizationId(Long.parseLong(organization.getAttribute1()));
            List<ItemsDTO> itemsDTOList = itemsService.queryByPage(itemsQuery).getContent();
            if (CollectionUtils.isNotEmpty(itemsDTOList)) {
                //如果状态为中大样的用前端淡橙色标注
                excelDTO.setLifecycleState(itemsDTOList.get(0).getLifecycleState());
            }
        }
    }

    private void convertExcelToKey(ScreenLifeExcelDTO excelDTO) {
        StringBuffer sb = new StringBuffer();
        if(org.springframework.util.StringUtils.hasText(excelDTO.getSingleGlassFlagName())){
            Map<String, LovLineDTO> yesOrNoLov = LovUtils.getAllByHeaderCode("yes_or_no");
            if(Objects.nonNull(yesOrNoLov) ){
                LovLineDTO singleGlassFlagDtoLov = yesOrNoLov.get(excelDTO.getSingleGlassFlagName());
                if (Objects.nonNull(singleGlassFlagDtoLov)) {
                    excelDTO.setSingleGlassFlag(singleGlassFlagDtoLov.getLovValue());
                }else {
                    sb.append("请填写正确的单玻!");
                }
            }
        }


        if(org.springframework.util.StringUtils.hasText(excelDTO.getMainGridInfo())){
            Map<String, LovLineDTO> mainGridInfoLov = LovUtils.getAllByHeaderCode("7A01500100117");
            if (Objects.nonNull(mainGridInfoLov) ) {
                LovLineDTO mainGridInfoDtoLov = mainGridInfoLov.get(excelDTO.getMainGridInfo());
                if (Objects.nonNull(mainGridInfoDtoLov)) {
                    excelDTO.setMainGridInfo(mainGridInfoDtoLov.getLovLineId().toString());
                }else {
                    sb.append("请填写正确的主栅信息!");
                }
            }
        }


        if(org.springframework.util.StringUtils.hasText(excelDTO.getMainGridSpace())){
            Map<String, LovLineDTO> mainLov = LovUtils.getAllByHeaderCode("BATTERY_UTILIZATION_RATE_SPECIAL_ATTRIBUTE_ANALYSIS");
            LovLineDTO mainGridSpaceLov = mainLov.get(excelDTO.getMainGridSpace());
            if(Objects.nonNull(mainGridSpaceLov)  && "main_grid_space".equals(mainGridSpaceLov.getAttribute1())){
                excelDTO.setMainGridSpace(mainGridSpaceLov.getLovName());
            }else {
                sb.append("请填写正确的主栅间距!");
            }
        }


        if(org.springframework.util.StringUtils.hasText(sb.toString())){
            throw new BizException(sb.toString());
        }
    }

    private void buildWhere(BooleanBuilder booleanBuilder, ScreenLifeQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qScreenLife.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qScreenLife.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            booleanBuilder.and(qScreenLife.itemCode.like("%" + query.getItemCode() + "%"));
        }
        if (StringUtils.isNotEmpty(query.getItemDesc())) {
            booleanBuilder.and(qScreenLife.itemDesc.eq(query.getItemDesc()));
        }
        if (StringUtils.isNotEmpty(query.getMaterialStatus())) {
            booleanBuilder.and(qScreenLife.materialStatus.eq(query.getMaterialStatus()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qScreenLife.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qScreenLife.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getMachine())) {
            booleanBuilder.and(qScreenLife.machine.eq(query.getMachine()));
        }
        if (StringUtils.isNotEmpty(query.getVersion())) {
            booleanBuilder.and(qScreenLife.version.eq(query.getVersion()));
        }
        if (StringUtils.isNotEmpty(query.getLifetime())) {
            booleanBuilder.and(qScreenLife.lifetime.eq(query.getLifetime()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qScreenLife.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qScreenLife.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }
    }

    @Override
    public ScreenLifeDTO queryById(Long id) {
        ScreenLife queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScreenLifeDTO save(ScreenLifeSaveDTO saveDTO) {
        // 校验为空
        verifySave(saveDTO);
        // 查询 区分新增 修改结果集合
        // 生效日期重叠 如何处理 唯一性校验
        // 拷贝处理copyProperties 规范区分 原生对象不允许操作 dto
        // 校验数据是否重复记录 save接口也要保持一致

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        checkRepeat(saveDTO, booleanBuilder);

        ScreenLife newObj = new ScreenLife();
        if (null != saveDTO.getId()) {
            booleanBuilder = new BooleanBuilder();
            extractedSave(saveDTO, booleanBuilder);
            newObj = repository.findOne(booleanBuilder).orElse(new ScreenLife());
        }
        saveDTO.setBasePlaceId(LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, saveDTO.getBasePlaceName()).getLovLineId());
        convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    /**
     * 清除老数据，实现覆盖
     * @param versionCode
     */
    private void clearOldData(String versionCode) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qScreenLife.version.eq(versionCode));
        List<ScreenLife> screenLifeList = IterableUtils.toList(repository.findAll(booleanBuilder));
        repository.deleteAll(screenLifeList);
    }

    /**
     * 判断网板料号是否在BOM中
     * @param itemCode
     */
    private void itemCodeDoseExistBom(Integer serialNumber,String itemCode) {
        JPAQuery<Long> where = jpaQueryFactory.select(QComponents.components.id)
                .from(QItems.items)
                .leftJoin(QComponents.components)
                .on(QItems.items.sourceItemId.eq(QComponents.components.componentItemId)
                        .and(QComponents.components.disableDate.isNull()
                        .or(QComponents.components.disableDate.gt(LocalDateTime.now())
                        .or(QComponents.components.disableDate.eq(LocalDateTime.now())))));
        // 拼接查询条件
        where.where(QItems.items.itemCode.eq(itemCode));
        List<Long> data = where.fetch();
        if(CollectionUtils.isEmpty(data)){
            String error = serialNumber==null?itemCode+"料号不在电池BOM中，请联系整合/技术添加"
                    :"第"+serialNumber+"行 "+itemCode+"料号不在电池BOM中，请联系整合/技术添加";
            throw new BizException(error);
        }
    }

    /**
     * 生产新的版本号
     * 生成的版本改为：SC+YYYYMMDD+基地（生产基地移除CMBU）+账套（生产车间对应的账套），如********盐城TYCT
     * @param basePlace
     * @param workshop
     */
    private String generateVersionCode(String basePlace,String workshop) {
        Map<String, LovLineDTO> workshopLovLineMap = LovUtils.getAllByHeaderCode("work_shop");
        String accountSet = workshopLovLineMap.get(workshop).getAttribute10();
        String dateFormatString = DateUtil.getFormatDate(new Date(),"YYYYMMdd");
        basePlace = basePlace.replace("CMBU","");
        String versionString = "SC"+dateFormatString+basePlace+accountSet;
        return  versionString;
    }

    /**
     * 网版寿命中，电池类型与网版料号做校验
     * @param itemCode
     * @param itemCode
     */
    private void batteryTypeCheck(Integer serialNumber,String itemCode,String batteryTypeName) {
        Items itemsCheck = jpaQueryFactory.select(qItems)
                .from(qItems).where(qItems.itemCode.eq(itemCode)
                        .and(qItems.organizationId.eq(82L))).fetchFirst();
        BatteryTypeMain batteryTypeCheck = jpaQueryFactory.select(QBatteryTypeMain.batteryTypeMain).from(QBatteryTypeMain.batteryTypeMain)
                .where(QBatteryTypeMain.batteryTypeMain.batteryName.eq(batteryTypeName)).fetchFirst();
        //先单独处理此情况，截取处理可能会造成其他数据混乱
        if(ObjectUtils.isNotEmpty(itemsCheck) && "二分片".equals(itemsCheck.getSegment16())){
            itemsCheck.setSegment16("二分");
        }else if(ObjectUtils.isNotEmpty(itemsCheck) && "三分片".equals(itemsCheck.getSegment16())){
            itemsCheck.setSegment16("三分");
        }
        if((ObjectUtils.isNotEmpty(itemsCheck) && ObjectUtils.isNotEmpty(batteryTypeCheck))
        && (
                !itemsCheck.getSegment3().equals(batteryTypeCheck.getCategory())
                || !itemsCheck.getSegment8().equals(batteryTypeCheck.getPOrN())
                || !itemsCheck.getSegment15().equals(batteryTypeCheck.getNumberMainGrids())
                || !itemsCheck.getSegment16().equals(batteryTypeCheck.getShardingMode())
        )){
            String error = serialNumber==null?itemCode+"料号不可用于电池类型"+batteryTypeName
                    :"第"+serialNumber+"行 "+itemCode+"料号不可用于电池类型"+batteryTypeName;
            throw new BizException(error);
        }

    }

    // 字段校验版本+电池类型+生产基地+生产车间+网版料号+机台唯一性
    // 20250321 update ->电池类型+网版料号+生产基地+生产车间+机台+主栅信息+主栅间距+单玻的数据组合不允许重复
    public void checkRepeat(ScreenLifeSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        extracted(saveDTO, booleanBuilder);
        ScreenLife newObj = repository.findOne(booleanBuilder).orElse(new ScreenLife());
        if (null != newObj.getId()) {
            throw new BizException("bbom_valid_screenLife_repeat");
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(ScreenLifeQuery query, HttpServletResponse response, String userId) {
        List<ScreenLifeDTO> dtos = queryByPage(userId, query).getContent();
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "网版寿命信息维护", "网版寿命信息维护", excelPara.getSimpleHeader(), excelData);
    }

    /**
     * 导出模版
     *
     * @param query
     * @param response
     */
    @Override
    @SneakyThrows
    public void queryByPageExport(ScreenLifeQuery query, HttpServletResponse response, String userId) {
        List<ScreenLifeDTO> dtos = queryByPage(userId, query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<ScreenLifeExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "网版寿命信息维护导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "网版寿命信息维护").head(ScreenLifeExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    /**
     * 导入模版数据
     *
     * @param file
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importsEntity(MultipartFile file) {
        List<ScreenLifeExcelDTO> excelDto = new LinkedList<>();
        List<String> itemCodeList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), ScreenLifeExcelDTO.class, new ReadListener<ScreenLifeExcelDTO>() {
            @Override
            public void invoke(ScreenLifeExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDto.add(data);
                // 汇总 网版料号
                if (StringUtils.isNotBlank(data.getItemCode())) {
                    itemCodeList.add(data.getItemCode());
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        importDataSave(excelDto, itemCodeList);
        log.info("网版寿命信息维护 数据导入成功");
    }

    private void importDataSave(List<ScreenLifeExcelDTO> excelDto, List<String> itemCodeList) {
        // 校验网版料号合法性 API接口 查询网版料号集合
        // 批量查询接口
        Map<String, String> itemDescByItemCodes = new HashMap<>();
        if(!org.springframework.util.CollectionUtils.isEmpty(itemCodeList)){
            ItemCodesQuery itemCode = new ItemCodesQuery();
            itemCode.setItemCodes(itemCodeList);
            itemDescByItemCodes = batteryFeignService.findItemDescByItemCodes(itemCode);
        }

        String codeVersion = CodeUtil.getCode(CodeEnum.SCREEN_LIFE);
        //校验通过
        Integer serialNumber = 1;
        for (ScreenLifeExcelDTO excelDTO : excelDto) {
            // 校验合法性和赋值
            checkImport(excelDTO, codeVersion);
            if(org.springframework.util.StringUtils.hasText(excelDTO.getItemCode())){
                //判断料号是否在bom中，如果不存在则报错
                itemCodeDoseExistBom(serialNumber,excelDTO.getItemCode());
                //在导入或维护网版料号时，对网版料号属性进行校验，若当前网版属性和电池类型不匹配，则返回报错
                batteryTypeCheck(serialNumber,excelDTO.getItemCode(),excelDTO.getBatteryTypeName());
                serialNumber++;
                if(!org.springframework.util.CollectionUtils.isEmpty(itemCodeList)) {
                    for (String item : itemCodeList) {
                        if (!itemDescByItemCodes.containsKey(item)) {
                            throw new BizException("bbom_valid_itemCode_illegal", item);
                        }
                    }
                }
            }
        }
        String duplicateStr = this.checkIsDuplicate(excelDto);
        if(!org.springframework.util.StringUtils.isEmpty(duplicateStr)){
            throw new BizException(duplicateStr);
        }
        for (ScreenLifeExcelDTO excelDTO : excelDto) {
            //10.15生成新的版本号ps_1082需求
            String versionCode = generateVersionCode(excelDTO.getBasePlace(),excelDTO.getWorkshop());
            //通过版本号清除老数据实现覆盖
            clearOldData(versionCode);
            excelDTO.setVersion(versionCode);
        }

        //保存
        for (ScreenLifeExcelDTO excelDTO : excelDto) {
            // 转换名字成为key 存到DB
            this.convertExcelToKey(excelDTO);
            //保留4位
            BigDecimal lifeTime = new BigDecimal(excelDTO.getLifetime()).setScale(4, RoundingMode.HALF_UP);
            if (lifeTime.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("bbom_valid_lifetime_illegal");
            }
            excelDTO.setLifetime(lifeTime.toString());
            ScreenLifeSaveDTO saveDTO = convert.toSaveDTO(excelDTO);
            save(saveDTO);
        }
    }

    private void checkImport(ScreenLifeExcelDTO excelDTO, String codeVersion) {
        verifyImport(excelDTO);
        // todo 批量调用 校验合法性 生产基地 生产车间 api接口
        List<ModuleBasePlaceDTO> moduleBasePlaceDTOList = batteryFeignService.
                findBasePlaceWorkshopWorkUnit(excelDTO.getBasePlaceName(), excelDTO.getWorkshopName(), null);
        if (CollectionUtils.isEmpty(moduleBasePlaceDTOList)) {
            throw new BizException("bbom_valid_basePlaceWorkshop_illegal", excelDTO.getBasePlaceName(), excelDTO.getWorkshopName());
        }
        // 获取基地
        excelDTO.setBasePlace(excelDTO.getBasePlaceName());
        // 获取车间
        excelDTO.setWorkshop(excelDTO.getWorkshopName());

        // 校验机台合法性 lov
        LovLineDTO workbench = Optional.ofNullable(LovUtils.getByName(CELL_MACHINE,
                excelDTO.getMachineName())).orElseThrow(() -> new BizException("bbom_valid_workbench_illegal", excelDTO.getMachineName()));
        // 获取机台信息
        excelDTO.setMachine(String.valueOf(workbench.getLovLineId()));

        // 补齐电池类型编码
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryName(excelDTO.getBatteryTypeName());
        Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
            excelDTO.setBatteryType(w.getBatteryCode());
        });
        excelDTO.setVersion(codeVersion);
        if(StringUtils.isNotEmpty(excelDTO.getItemCode())){

            Items item = (jpaQueryFactory.selectFrom(qItems)
                    .where(qItems.itemCode.eq(excelDTO.getItemCode()).and(qItems.isDeleted.eq(0)))
                    .fetchFirst());
            if (null == item) {
                throw new BizException("bbom_valid_itemCode_notExist", excelDTO.getItemCode());
            }
            excelDTO.setItemDesc(item.getItemDesc());
            excelDTO.setMaterialStatus(item.getItemStatus());
            if (StringUtils.isBlank(excelDTO.getBatteryType())) {
                throw new BizException("bbom_valid_batteryCode_illegal", excelDTO.getBatteryType());
            }
        }

    }

    @Override
    public void queryListBySendMail() {
        exportEmail();
    }

    public void exportEmail() {
        EmailDataResultDTO emailDataResultDTO = new EmailDataResultDTO();
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //减三个月
        booleanBuilder.and(qScreenLife.createdTime.after(LocalDateTimeUtil.getDateTimePlusMinusMonths(3)));
        Iterable<ScreenLife> screenLifeIterable = repository.findAll(booleanBuilder);
        //申请成功后发送邮件
        List<ScreenLifeDTO> dtos = convert.toDto(IterableUtils.toList(screenLifeIterable));
        dtos.stream().forEach(x -> {
            queryConvert(x);
        });
        //数据转换方法
        List<ScreenLifeExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        //网版切换
        String content = "网版寿命维护内容";
        JSONArray jsonArray = getObjects(emailDataResultDTO, exportDTOS, content);
        sendEmail(jsonArray, emailDataResultDTO);
    }

    public JSONArray getObjects(EmailDataResultDTO emailDataResultDTO, List<ScreenLifeExcelDTO> exportDTOS, String content) {
        File file = buildExcelFile(exportDTOS);
        String fileUrl = this.fileUpload(file);
        JSONArray jsonArray = FileUtil.getObjects(emailDataResultDTO, content, file, fileUrl, "网版寿命维护");
        return jsonArray;
    }

    @SneakyThrows
    private String fileUpload(File file) {
        MultipartFile multipartFile = FileUtil.getMultipartFile(file);
        return scpFileService.upload(multipartFile, false);
    }

    private File buildExcelFile(List<ScreenLifeExcelDTO> exportDTOS) {
        //创建目录
        File file = FileUtil.createLocalFile("网版寿命维护");
        // 文件输出位置
        ExcelWriter writer = EasyExcelFactory.write(file)
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build();


        WriteSheet sheet1 = EasyExcelFactory.writerSheet(0, "网版寿命维护导出信息").head(ScreenLifeExcelDTO.class).build();
        // 写数据
        writer.write(exportDTOS, sheet1);
        writer.finish();
        return file;
    }

    /**
     * 发送邮件
     */
    private void sendEmail(JSONArray jsonArray, EmailDataResultDTO emailDataResultDTO) {
        //查询所有收件人
        LovLineDTO lovLineDTO = LovUtils.getByName(LovHeaderCodeConstant.SEND_MAIL, LovHeaderCodeConstant.SCREEN_LIFE_MAIL);
        //发送邮件
        List<String> emails = Arrays.asList(StringUtils.split(lovLineDTO.getAttribute1(), ","));

        try {
            mailService.send(new ArrayList<>(emails), "battery_screen_plate.ftl", "网版寿命维护邮件提醒", MapUtil.of("Data", emailDataResultDTO), jsonArray.toJSONString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 前端使用
     * 获取网版寿命的版本集合
     */
    public List<String> getVersionList() {
        JPAQuery<String> where = jpaQueryFactory.selectDistinct(qScreenLife.version).from(qScreenLife);
        return where.fetch();
    }

    /**
     * 网版寿命批量更新bom寿命
     */
    public void batchUpdateBomLife() {
        Iterable<ScreenLife> lifeIterable = repository.findAll();
        List<ScreenLifeDTO> screenLifeDTOList = convert.toDto(IterableUtils.toList(lifeIterable));
        screenLifeDTOList.stream().forEach(screen -> {
            queryErpAlternateDesignator(screen);
        });
        repository.saveAll(convert.toEntity(screenLifeDTOList));
    }

    @Override
    public List<ScreenLifeDTO> queryListWithLifeTimeConvert() {
        List<ScreenLifeDTO> screenLifeDTOList = queryList();
        screenLifeDTOList.forEach(lifeDTO -> {
            //1/(寿命*10000)
            BigDecimal lifeTotal = new BigDecimal(lifeDTO.getLifetime()).multiply(new BigDecimal("10000"));
            BigDecimal lifeTime = BigDecimal.ONE.divide(lifeTotal, 8, RoundingMode.HALF_UP);
            lifeDTO.setLifetime(lifeTime.toString());
        });
        return screenLifeDTOList;
    }

    private void queryErpAlternateDesignator(ScreenLifeDTO excelDTO) {
        //获取bom替代项、库存组织根据车间
        ErpAlternateDesignatorQuery designatorQuery = new ErpAlternateDesignatorQuery();
        designatorQuery.setDescription(excelDTO.getWorkshop());
        ErpAlternateDesignatorDTO designatorDTO = bomFeign.queryByOrgID(designatorQuery).getBody().getData();
        if (null != designatorDTO.getOrganizationId()) {
            /*throw new BizException("该" + excelDTO.getWorkshop() + ":车间没有查到对应的bom替代项,无法查询单位用量");*/
            //根据电池编号、库存组织id查询
            List<Items> itemsList = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.segment60.eq(excelDTO.getBatteryType())
                            .and(qItems.organizationId.eq(82L).and(qItems.isDeleted.eq(0)))).fetch();
            //网版料号查询id 7A 唯一
            Items itemsDto = jpaQueryFactory.select(qItems).from(qItems)
                    .where(qItems.itemCode.eq(excelDTO.getItemCode()).and(qItems.organizationId.eq(82L).and(qItems.isDeleted.eq(0)))).fetchFirst();
            if (CollectionUtils.isNotEmpty(itemsList)) {
                for (Items items : itemsList) {
                    //bom替代项、组织id、5A料号id 唯一
                    StructuresQuery structuresQuery = new StructuresQuery();
                    structuresQuery.setAlternateBomDesignator(designatorDTO.getAlternateDesignatorCode());
                    structuresQuery.setOrganizationId(designatorDTO.getOrganizationId());
                    structuresQuery.setAssemblyItemId(items.getSourceItemId());
                    List<StructuresDTO> structuresDTOList = structuresService.queryByPage(structuresQuery).getContent();
                    if (CollectionUtils.isNotEmpty(structuresDTOList) && null != itemsDto) {
                        //根据7A料号+StructuresDTO主键查询行
                        ComponentsQuery componentsQuery = new ComponentsQuery();
                        componentsQuery.setBomId(structuresDTOList.get(0).getId());
                        //7A料号的id
                        componentsQuery.setComponentItemId(itemsDto.getSourceItemId());
                        List<ComponentsDTO> componentsDTOList = componentsService.queryByPage(componentsQuery).getContent();
                        if (CollectionUtils.isNotEmpty(componentsDTOList)) {
                            BigDecimal quantity = new BigDecimal("1").divide(new BigDecimal(componentsDTOList.get(0).getComponentQuantity()), 0, BigDecimal.ROUND_DOWN);
                            quantity = quantity.divide(new BigDecimal("10000"));
                            excelDTO.setBomLife(quantity.intValue());
                            break;
                        }
                    }
                }
            }
            if (null == excelDTO.getBomLife()) {
                excelDTO.setBomLife(0);
            }
        }
    }
}
