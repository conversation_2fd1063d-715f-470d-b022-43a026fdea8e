package com.trinasolar.scp.bbom.service.util.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.trinasolar.scp.bbom.domain.excel.ExcelHead;
import com.trinasolar.scp.bbom.domain.excel.ExcelMain;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import com.trinasolar.scp.common.api.util.LocalDateTimeConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * 动态表头Excel工具类
 *
 * <AUTHOR>
 * @date 2022年9月21日16:15:31
 */
public class ExcelUtil {
    /**
     * 导出
     *
     * @param response
     * @param excelMain
     * @throws IOException
     */
    public static void export(HttpServletResponse response, ExcelMain excelMain) throws IOException {
        EasyExcel.write(response.getOutputStream())
                .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
                .sheet("Sheet1")
                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .doWrite(ExcelUtil.buildData(excelMain));
    }

    /**
     * 导出到指定文件
     *
     * @param file
     * @param excelMain
     * @throws IOException
     */
    public static void export(File file, ExcelMain excelMain) throws IOException {
        EasyExcel.write(file)
                .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
                .sheet("Sheet1")
                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .doWrite(ExcelUtil.buildData(excelMain));
    }

    /**
     * 导出
     *
     * @param response
     * @param sheetMap
     * @throws IOException
     */
    public static void export(HttpServletResponse response, Map<String, ExcelMain> sheetMap) throws IOException {
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        sheetMap.forEach((sheetName, excelMain) -> {
            WriteSheet sheet = EasyExcel
                    .writerSheet(sheetName)
                    .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
                    .registerWriteHandler(new MergeStrategy(excelMain))
                    .registerConverter(new LocalDateConverter())
                    .registerConverter(new LocalDateTimeConverter())
                    .build();
            excelWriter.write(ExcelUtil.buildData(excelMain), sheet);
        });
        excelWriter.finish();

//        EasyExcel.write(response.getOutputStream())
//                .head(ExcelUtil.buildHead(excelMain.getExcelHeads()))
//                .sheet("Sheet1")
//                .registerWriteHandler(new MergeStrategy(excelMain))//自定义合并单元格
//                .registerConverter(new LocalDateConverter())
//                .registerConverter(new LocalDateTimeConverter())
//                .doWrite(ExcelUtil.buildData(excelMain));
    }

    private static void addMergedRegion(Sheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        if (firstRow < lastRow) {
            CellRangeAddress cra = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
            // 使用RegionUtil类为合并后的单元格添加边框
            RegionUtil.setBorderBottom(BorderStyle.THIN, cra, sheet); // 下边框
            RegionUtil.setBorderLeft(BorderStyle.THIN, cra, sheet); // 左边框
            RegionUtil.setBorderRight(BorderStyle.THIN, cra, sheet); // 有边框
            RegionUtil.setBorderTop(BorderStyle.THIN, cra, sheet); // 上边框
            sheet.addMergedRegion(cra);
        }
    }

    private static void setStyle(Sheet sheet, int startRow, int endRow, int startCell, int endCell, CellStyle cellStyle) {
        for (int i = startRow; i <= endRow; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = startCell; j <= endCell; j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        cell = row.createCell(j);
                    }
                    cell.setCellStyle(cellStyle);
                }
            }
        }
    }

    private static Cell createCell(Row row, int index, CellStyle style) {
        Cell cell = row.createCell(index, CellType.STRING);
        cell.setCellStyle(style);
        return cell;
    }

    public static void setExportResponseHeader(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        try {
            fileName = fileName + "_" + com.ibm.dpf.base.core.util.DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("UTF-8");
            String fileNameCode = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileNameCode + ".xlsx");
            response.setHeader("Access-Control-Allow-Origin", "Content-Disposition");
            response.setHeader("Access-Control-Allow-Origin", "Content-disposition");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        } catch (Throwable var3) {
            throw var3;
        }
    }

    public static void verifyFile(MultipartFile multipartFile) {
        if (multipartFile == null || multipartFile.isEmpty()) {
            throw new BizException("导入文件不能为空！");
        }
        //校验文件格式，必须为excel文件
        String fileName = multipartFile.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!"xlsx".equals(suffix)) {
            throw new BizException("导入文件只支持xlsx类型后缀！");
        }
    }

    /**
     * 构建导出表头
     */
    public static List<List<String>> buildHead(List<ExcelHead> heads) {
        boolean needMerge = false;
        for (ExcelHead headVO : heads) {
            if (CollectionUtils.isNotEmpty(headVO.getChildren())) {
                needMerge = true;
            }
        }
        List<List<String>> headList = new ArrayList<>();
        for (ExcelHead excelHead : heads) {
            if (CollectionUtils.isNotEmpty(excelHead.getChildren())) {
                excelHead.getChildren().forEach(child -> {
                    List<String> head = new ArrayList<>();
                    head.add(excelHead.getLabel());
                    head.add(child.getLabel());
                    headList.add(head);
                });
            } else {
                List<String> head = new ArrayList<>();
                head.add(excelHead.getLabel());
                if (needMerge) {
                    head.add(excelHead.getLabel());
                }
                headList.add(head);
            }
        }
        return headList;
    }

    /**
     * 构建导出数据
     */
    public static List<List<Object>> buildData(ExcelMain excelMain) {
        int maxExcel = 32767;
        List<List<Object>> list = new ArrayList<>();
        if (Objects.isNull(excelMain) || CollectionUtils.isEmpty(excelMain.getData())) {
            return list;
        }
        List<ExcelHead> heads = excelMain.getExcelHeads();
        for (Map<String, Object> map : excelMain.getData()) {
            List<Object> data = new ArrayList<>();
            for (ExcelHead head : heads) {
                if (CollectionUtils.isNotEmpty(head.getChildren())) {
                    head.getChildren().forEach(child -> {
                        Object value = map.get(child.getProp());
                        if ((value + "").length() >= maxExcel) {
                            data.add((value + "").substring(0, 255) + "字符过长");
                        } else {
                            data.add(value);
                        }
                    });
                } else {
                    Object value = map.get(head.getProp());
                    if ((value + "").length() >= maxExcel) {
                        data.add((value + "").substring(0, 255) + "字符过长");
                    } else {
                        data.add(value);
                    }
                }
            }
            list.add(data);
        }
        return list;
    }

}
