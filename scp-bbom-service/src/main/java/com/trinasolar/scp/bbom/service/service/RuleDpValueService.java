package com.trinasolar.scp.bbom.service.service;


import com.trinasolar.scp.bbom.domain.dto.RuleDpValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpValue;
import com.trinasolar.scp.bbom.domain.query.RuleDpValueQuery;
import com.trinasolar.scp.bbom.domain.save.RuleDpValueSaveDTO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * BOM规则DP因子明细值 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:01
 */
public interface RuleDpValueService {
    Page<RuleDpValue> queryByPage(RuleDpValueQuery query);

    RuleDpValueDTO queryById(Long id);

    RuleDpValueDTO save(RuleDpValueSaveDTO saveDTO);

    void deleteById(Long id);

    void saveValues(Long ruleDetailId, List<RuleDpValueSaveDTO> values);

    List<RuleDpValueDTO> listByRuleDetailId(Long ruleDetailId, String dpFiledName);
}

