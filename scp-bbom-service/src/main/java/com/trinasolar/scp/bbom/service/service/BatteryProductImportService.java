package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.BatteryProductImportDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryProductImport;
import com.trinasolar.scp.bbom.domain.query.BatteryProductImportQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryProductImportSaveDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电池产品导入表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
public interface BatteryProductImportService {
    /**
     * 分页获取电池产品导入表
     *
     * @param query 查询对象
     * @return 电池产品导入表分页对象
     */
    Page<BatteryProductImportDTO> queryByPage(BatteryProductImportQuery query);

    /**
     * 全量获取电池产品导入表
     *
     * @return
     */
    List<BatteryProductImportDTO> queryAll();

    /**
     * 根据主键获取电池产品导入表详情
     *
     * @param id 主键
     * @return 电池产品导入表详情
     */
    BatteryProductImportDTO queryById(Long id);

    /**
     * 保存或更新电池产品导入表
     *
     * @param saveDTO 电池产品导入表保存对象
     * @return 电池产品导入表对象
     */
    BatteryProductImportDTO save(BatteryProductImportSaveDTO saveDTO);

    /**
     * @param saveDTO
     * @return
     */
    BatteryProductImportDTO updateById(BatteryProductImport saveDTO);

    /**
     * 根据主键逻辑删除电池产品导入表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(BatteryProductImportQuery query, HttpServletResponse response);

    /**
     * 导入模版数据
     *
     * @param file
     */
    void importsEntity(@RequestParam("file") MultipartFile file);

    /**
     * 导出模版
     *
     * @param query
     * @param response
     */
    void queryByPageExport(BatteryProductImportQuery query, HttpServletResponse response);
}

