package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ComponentsDTO;
import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.query.ComponentsQuery;
import com.trinasolar.scp.bbom.domain.save.ComponentsSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * BOM行 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
public interface ComponentsService {
    /**
     * 分页获取BOM行
     *
     * @param query 查询对象
     * @return BOM行分页对象
     */
    Page<ComponentsDTO> queryByPage(ComponentsQuery query);

    /**
     * 获取BOM行
     *
     * @param query 查询对象
     * @return BOM行分页对象
     */
    List<ComponentsDTO> queryList();

    /**
     * 根据主键获取BOM行详情
     *
     * @param id 主键
     * @return BOM行详情
     */
    ComponentsDTO queryById(Long id);

    /**
     * 保存或更新BOM行
     *
     * @param saveDTO BOM行保存对象
     * @return BOM行对象
     */
    ComponentsDTO save(ComponentsSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除BOM行
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query    查询对象
     * @param response 响应对象
     */
    void export(ComponentsQuery query, HttpServletResponse response);

    List<ComponentsDTO> findByStructure(StructuresDTO structuresDTO);


    List<StructuresDTO> findBomStructureByComponentItemIdAndOrganizationId(Long sourceItemId, Long organizationId);

    ComponentsDTO findByStructureIdAndSourceItemId(Long structureId, Long sourceItemId);

    void updateComponentSlurrySubstituteFlag(String alternateBomDesignator, ComponentsDTO componentsDTO, String substituteFlag,String substituteEnableFlag);

    List<ComponentsDTO> getComponentsList(List<Long> componentItemId);

    Long findByStructureIdAndValidity(List<Long> structureIdList, Long sourceItemId);

    void updateComponentScreenSubstituteFlag(String alternateBomDesignator, ComponentsDTO componentsDTO, String substituteFlag,String substituteEnableFlag);
}

