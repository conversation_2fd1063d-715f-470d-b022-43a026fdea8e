package com.trinasolar.scp.bbom.service.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.bbom.domain.convert.SlurryInformationDEConvert;
import com.trinasolar.scp.bbom.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.dto.SlurryInformationDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.system.DataPrivilegeQuery;
import com.trinasolar.scp.bbom.domain.entity.QSlurryInformation;
import com.trinasolar.scp.bbom.domain.entity.SlurryInformation;
import com.trinasolar.scp.bbom.domain.enums.CodeEnum;
import com.trinasolar.scp.bbom.domain.excel.SlurryInformationExcelDTO;
import com.trinasolar.scp.bbom.domain.query.SlurryInformationQuery;
import com.trinasolar.scp.bbom.domain.save.SlurryInformationSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant;
import com.trinasolar.scp.bbom.service.feign.SystemFeign;
import com.trinasolar.scp.bbom.service.repository.SlurryInformationRepository;
import com.trinasolar.scp.bbom.service.service.BatteryFeignService;
import com.trinasolar.scp.bbom.service.service.BatteryTypeMainService;
import com.trinasolar.scp.bbom.service.service.SlurryInformationService;
import com.trinasolar.scp.bbom.service.util.CodeUtil;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.YesOrNoEnum;
import com.trinasolar.scp.common.api.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 浆料车间单耗及线数维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Slf4j
@Service("slurryInformationService")
@RequiredArgsConstructor
@CacheConfig(cacheManager = "caffeineCacheManager")
public class SlurryInformationServiceImpl implements SlurryInformationService {
    private static final QSlurryInformation qSlurryInformation = QSlurryInformation.slurryInformation;

    private final static String BASE_PLACE = "base_place";

    private final static String WORK_SHOP = "work_shop";

    private final static String CELL_MACHINE = "CELL_MACHINE";

    private final SlurryInformationDEConvert convert;

    private final SlurryInformationRepository repository;

    private final BatteryFeignService batteryFeignService;

    private final BatteryTypeMainService batteryTypeMainService;

    private final SystemFeign systemFeign;

    private final JPAQueryFactory jpaQueryFactory;

    private static void extracted(SlurryInformationSaveDTO dto, BooleanBuilder booleanBuilder) {
        if (null != dto.getId()) {
            booleanBuilder.and(qSlurryInformation.id.ne(dto.getId()));
        }
        //电池类型
        if (StringUtils.isNotEmpty(dto.getBatteryType())) {
            booleanBuilder.and(qSlurryInformation.batteryType.eq(dto.getBatteryType()));
        }
        //生产基地
        if (StringUtils.isNotEmpty(dto.getBasePlace())) {
            booleanBuilder.and(qSlurryInformation.basePlace.eq(dto.getBasePlace()));
        }
        //生产车间
        if (StringUtils.isNotEmpty(dto.getWorkshop())) {
            booleanBuilder.and(qSlurryInformation.workshop.eq(dto.getWorkshop()));
        }
        //机台
        if (StringUtils.isNotEmpty(dto.getWorkbench())) {
            booleanBuilder.and(qSlurryInformation.workbench.eq(dto.getWorkbench()));
        }

    }

    /**
     * 新增修改校验
     *
     * @param saveDTO
     */
    private static void verifySave(SlurryInformationSaveDTO saveDTO) {
        // 新增修改校验
        if (StringUtils.isBlank(saveDTO.getBatteryType())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlace())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshop())) {
            throw new BizException("bbom_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkbench())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }

        // 公共部分
        if (StringUtils.isBlank(saveDTO.getUnitConsumption())) {
            throw new BizException("bbom_valid_unitConsumption_notNumber");
        }
        if (StringUtils.isBlank(saveDTO.getLineNumber())) {
            throw new BizException("bbom_valid_line_notBlank");
        }
    }

    /**
     * 导入校验
     *
     * @param saveDTO
     */
    private static void verifyImport(SlurryInformationExcelDTO saveDTO) {
        // 导入校验
        if (StringUtils.isBlank(saveDTO.getBatteryTypeName())) {
            throw new BizException("bbom_batteryType_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getBasePlaceName())) {
            throw new BizException("bbom_basePlace_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkshopName())) {
            throw new BizException("bbom_workshop_notBlank");
        }
        if (StringUtils.isBlank(saveDTO.getWorkbenchName())) {
            throw new BizException("bbom_valid_workbench_notBlank");
        }

        // 公共部分
        if (StringUtils.isBlank(saveDTO.getUnitConsumption())) {
            throw new BizException("bbom_valid_unitConsumption_notNumber");
        } else if (new BigDecimal(saveDTO.getUnitConsumption()).compareTo(BigDecimal.ZERO) <= 0) {
            throw new BizException("bbom_valid_unitConsumption_notZero");
        }
        if (StringUtils.isBlank(saveDTO.getLineNumber())) {
            throw new BizException("bbom_valid_line_notBlank");
        }
    }

    private static StringBuilder getStringBuilder(SlurryInformationExcelDTO excelDTO, HashSet<String> hashSet) {
        StringBuilder builder = new StringBuilder();
        builder.append(excelDTO.getBatteryType()).append(excelDTO.getBasePlace())
                .append(excelDTO.getWorkshop()).append(excelDTO.getWorkbench());
        if (hashSet.contains(builder.toString())) {
            throw new BizException(builder + MessageHelper.getMessage("bbom_valid_importData_repeat").getDesc());
        }
        return builder;
    }

    private static void extractedSave(SlurryInformationSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        if (null != saveDTO.getId()) {
            booleanBuilder.and(qSlurryInformation.id.eq(saveDTO.getId()));
        }
    }

    private static void extractedImport(SlurryInformationExcelDTO excelDTO, BooleanBuilder booleanBuilder) {
        if (StringUtils.isNotEmpty(excelDTO.getBatteryType())) {
            booleanBuilder.and(qSlurryInformation.batteryType.eq(excelDTO.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getBasePlace())) {
            booleanBuilder.and(qSlurryInformation.basePlace.eq(excelDTO.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(excelDTO.getWorkshop())) {
            booleanBuilder.and(qSlurryInformation.workshop.eq(excelDTO.getWorkshop()));
        }
        /*if(StringUtils.isNotEmpty(excelDTO.getLineNumber())){
            booleanBuilder.and(qSlurryInformation.lineNumber.eq(excelDTO.getLineNumber()));
        }*/
        if (StringUtils.isNotEmpty(excelDTO.getWorkbench())) {
            booleanBuilder.and(qSlurryInformation.workbench.eq(excelDTO.getWorkbench()));
        }
    }

    @Override
    public Page<SlurryInformationDTO> queryByPage(String userId, SlurryInformationQuery query) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        buildWhere(booleanBuilder, query);
        //查询用户数据权限
        List<DataPrivilegeDTO> privilegeDTOList = getDataPrivilegeDTOS(userId, LovHeaderCodeConstant.BASE_PLACE);
        if (CollectionUtils.isNotEmpty(privilegeDTOList)) {
            List<Long> ids = privilegeDTOList.stream().map(DataPrivilegeDTO::getDataId).collect(Collectors.toList());
            if (StringUtils.isEmpty(query.getBasePlace())) {
                if (!ids.contains(-1L)) {
                    booleanBuilder.and(qSlurryInformation.basePlaceId.in(ids));
                }
            }
        }
        Page<SlurryInformation> page = repository.findAll(booleanBuilder, pageable);
        List<SlurryInformationDTO> slurryInformationList = convert.toDto(page.getContent());
        for (SlurryInformationDTO slurry : slurryInformationList) {
            queryConvert(slurry);
            fillUpdatedByName(slurry);
        }
        return new PageImpl(slurryInformationList, page.getPageable(), page.getTotalElements());
    }

    private void fillUpdatedByName(SlurryInformationDTO slurry) {
        slurry.setUpdatedByName(UserUtil.getUserNameById(slurry.getUpdatedBy()));
    }

    @Override
    @Cacheable(cacheNames = "getDataPrivilegeDTOS",
            key = "#p0+'_'+#p1", unless = "#result == null")
    public List<DataPrivilegeDTO> getDataPrivilegeDTOS(String userId, String privilegeType) {
        DataPrivilegeQuery dataPrivilegeQuery = new DataPrivilegeQuery();
        dataPrivilegeQuery.setUserId(userId);
        dataPrivilegeQuery.setPrivilegeType(privilegeType);
        List<DataPrivilegeDTO> privilegeDTOList = systemFeign.queryDataPrivilege(dataPrivilegeQuery).getBody().getData();
        return privilegeDTOList;
    }

    private void queryConvert(SlurryInformationDTO excelDTO) {
        // 电池类型 id->name 转码用别名
        // 转换电池名称
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryCode(excelDTO.getBatteryType());
        if (StringUtils.isNotBlank(batteryTypeMainDTO.getBatteryName())) {
            excelDTO.setBatteryTypeName(batteryTypeMainDTO.getBatteryName());
        }
        excelDTO.setBasePlaceName(excelDTO.getBasePlace());
        excelDTO.setWorkshopName(excelDTO.getWorkshop());
        if (StringUtils.isNumeric(excelDTO.getWorkbench())) {
            LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(excelDTO.getWorkbench()));
            if (lovLineDTO != null) {
                excelDTO.setWorkbenchName(lovLineDTO.getLovName());
            }
        }
        excelDTO.setCreatedBy(UserUtil.getUserNameById(excelDTO.getCreatedBy()));
    }

    public List<SlurryInformationDTO> queryList(SlurryInformationQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        query.setBatteryType(query.getBatteryType());
        LovLineDTO workbenchLov = LovUtils.getByName("CELL_MACHINE", query.getWorkbench());
        // 改为机台可能不传,就能返回所有的单耗信息
        if (workbenchLov != null) {
            query.setWorkbench(String.valueOf(workbenchLov.getLovLineId()));
        }
        // 只查询有效的数据
        query.setValid(YesOrNoEnum.YES.getCode());
        buildWhere(booleanBuilder, query);
        Iterable<SlurryInformation> informations = repository.findAll(booleanBuilder);
        List<SlurryInformationDTO> informationDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(IterableUtils.toList(informations))) {
            return informationDTOList;
        }
        informationDTOList = convert.toDto(IterableUtils.toList(informations));
        informationDTOList.stream().forEach(information -> {
            BigDecimal consumption = new BigDecimal(information.getUnitConsumption()).divide(new BigDecimal("1000"));
            information.setUnitConsumption(consumption.toString());
        });
        return informationDTOList;
    }

    private void buildWhere(BooleanBuilder booleanBuilder, SlurryInformationQuery query) {
        if (query.getId() != null) {
            booleanBuilder.and(qSlurryInformation.id.eq(query.getId()));
        }
        if (StringUtils.isNotEmpty(query.getPlanVersion())) {
            booleanBuilder.and(qSlurryInformation.planVersion.eq(query.getPlanVersion()));
        }
        if (StringUtils.isNotEmpty(query.getBatteryType())) {
            booleanBuilder.and(qSlurryInformation.batteryType.eq(query.getBatteryType()));
        }
        if (StringUtils.isNotEmpty(query.getBasePlace())) {
            booleanBuilder.and(qSlurryInformation.basePlace.eq(query.getBasePlace()));
        }
        if (StringUtils.isNotEmpty(query.getWorkshop())) {
            booleanBuilder.and(qSlurryInformation.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotEmpty(query.getLineNumber())) {
            booleanBuilder.and(qSlurryInformation.lineNumber.eq(query.getLineNumber()));
        }
        if (StringUtils.isNotEmpty(query.getWorkbench())) {
            booleanBuilder.and(qSlurryInformation.workbench.eq(query.getWorkbench()));
        }
        if (StringUtils.isNotEmpty(query.getUnitConsumption())) {
            booleanBuilder.and(qSlurryInformation.unitConsumption.eq(query.getUnitConsumption()));
        }
        if (query.getEffectiveStartDate() != null) {
            booleanBuilder.and(qSlurryInformation.effectiveStartDate.eq(query.getEffectiveStartDate()));
        }
        if (query.getEffectiveEndDate() != null) {
            booleanBuilder.and(qSlurryInformation.effectiveEndDate.eq(query.getEffectiveEndDate()));
        }
        if (StringUtils.isNotEmpty(query.getValid())) {
            booleanBuilder.and(qSlurryInformation.valid.eq(query.getValid()));
        }
    }

    @Override
    public SlurryInformationDTO queryById(Long id) {
        SlurryInformation queryObj = repository.findById(id).orElse(null);
        return convert.toDto(queryObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SlurryInformationDTO save(SlurryInformationSaveDTO saveDTO) {
        // 校验为空
        verifySave(saveDTO);
        // 查询 区分新增 修改结果集合
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        checkRepeat(saveDTO, booleanBuilder);
        SlurryInformation newObj = new SlurryInformation();
        if (null != saveDTO.getId()) {
            booleanBuilder = new BooleanBuilder();
            extractedSave(saveDTO, booleanBuilder);
            // 查询 区分新增 修改结果集合
            newObj = repository.findOne(booleanBuilder).orElse(new SlurryInformation());
        }
        saveDTO.setBasePlaceId(LovUtils.getByName(LovHeaderCodeConstant.BASE_PLACE, saveDTO.getBasePlaceName()).getLovLineId());
        convert.saveDTOtoEntity(saveDTO, newObj);
        repository.save(newObj);
        return this.queryById(newObj.getId());
    }

    //字段校验电池类型+生产基地+生产车间+机台 维度校验唯一
    public void checkRepeat(SlurryInformationSaveDTO saveDTO, BooleanBuilder booleanBuilder) {
        extracted(saveDTO, booleanBuilder);
        SlurryInformation newObj = repository.findOne(booleanBuilder).orElse(new SlurryInformation());
        if (null != newObj.getId()) {
            throw new BizException("bbom_valid_SlurryInformation_repeat", saveDTO.getBatteryTypeName(), saveDTO.getBasePlaceName(), saveDTO.getWorkshopName(), saveDTO.getWorkbenchName());
        }
    }

    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    @Override
    @SneakyThrows
    public void export(SlurryInformationQuery query, HttpServletResponse response, String userId) {
        List<SlurryInformationDTO> dtos = queryByPage(userId, query).getContent();

        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(dtos, excelPara);

        ExcelUtils.exportEx(response, "浆料车间单耗及线数维护", "浆料车间单耗及线数维护", excelPara.getSimpleHeader(), excelData);
    }

    @Override
    @SneakyThrows
    public void queryByPageExport(SlurryInformationQuery query, HttpServletResponse response, String userId) {
        List<SlurryInformationDTO> dtos = queryByPage(userId, query).getContent();
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<SlurryInformationExcelDTO> exportDTOS = convert.toExcelDTO(dtos);
        ExcelUtils.setExportResponseHeader(response, "浆料车间单耗及线数维护_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerConverter(new LocalDateConverter())
                .registerConverter(new LocalDateTimeConverter())
                .registerConverter(new LongStringConverter())
                .build()) {

            WriteSheet sheet = EasyExcel.writerSheet(0, "浆料车间单耗及线数维护").head(SlurryInformationExcelDTO.class).build();
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            excelWriter.write(exportDTOS, sheet);
        }
    }

    /**
     * 导入模版数据
     *
     * @param file
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importsEntity(MultipartFile file) {
        List<SlurryInformationExcelDTO> excelDtos = new LinkedList<>();
        EasyExcel.read(file.getInputStream(), SlurryInformationExcelDTO.class, new ReadListener<SlurryInformationExcelDTO>() {
            @Override
            public void invoke(SlurryInformationExcelDTO data, AnalysisContext context) {
                // Excel结果集
                excelDtos.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }
        }).sheet().doRead();
        importDataSave(excelDtos);
        log.info("浆料车间单耗及线数维护 数据导入成功");
    }

    @Override
    public List<String> versionList() {
        List<String> fetch = jpaQueryFactory.select(qSlurryInformation.planVersion)
                .from(qSlurryInformation)
                .groupBy(qSlurryInformation.planVersion)
                .orderBy(qSlurryInformation.planVersion.desc())
                .limit(50)
                .fetch()
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        return fetch;
    }

    private void importDataSave(List<SlurryInformationExcelDTO> excelDtos) {
        // 保存或更新数据 查询数据库是否存在数据（存在既更新）
        HashSet<String> hashSet = new HashSet<>();

        for (SlurryInformationExcelDTO excelDTO : excelDtos) {
            // 校验合法性和赋值
            checkImportAndFillAttr(excelDTO, hashSet);
        }
        if (excelDtos.isEmpty()) {
            return;
        }
        // 说明导入数据没问题,开始正式插入信息
        // 获取版本号
        String planVersion = CodeUtil.getCode(CodeEnum.BBOM_SLURRY);
        // 将数据分组
        Map<String, List<SlurryInformationExcelDTO>> collect = excelDtos.stream()
                .collect(Collectors.groupingBy(i -> i.getBatteryType() + "_" + i.getBasePlace() + "_" + i.getWorkshop()));
        for (Map.Entry<String, List<SlurryInformationExcelDTO>> entry : collect.entrySet()) {
            // 将同分组的最新的数据设置为无效,再插入本行
            Iterable<SlurryInformation> all = repository.findAll(
                    qSlurryInformation.batteryType.eq(entry.getValue().get(0).getBatteryType())
                            .and(qSlurryInformation.basePlace.eq(entry.getValue().get(0).getBasePlace()))
                            .and(qSlurryInformation.workshop.eq(entry.getValue().get(0).getWorkshop()))
                            .and(qSlurryInformation.valid.eq(YesOrNoEnum.YES.getCode()))
            );
            List<SlurryInformation> saves = new ArrayList<>();
            all.forEach(i -> {
                i.setValid(YesOrNoEnum.NO.getCode());
                saves.add(i);
            });
            entry.getValue().forEach(excelDTO -> {
                SlurryInformationSaveDTO saveDTO = convert.toSaveDTO(excelDTO);
                SlurryInformation newObj = new SlurryInformation();
                convert.saveDTOtoEntity(saveDTO, newObj);
                newObj.setPlanVersion(planVersion);
                newObj.setValid(YesOrNoEnum.YES.getCode());
                saves.add(newObj);
            });
            repository.saveAll(saves);
        }
    }

    private void checkImportAndFillAttr(SlurryInformationExcelDTO excelDTO, HashSet<String> hashSet) {
        verifyImport(excelDTO);
        // 校验合法性 生产基地 生产车间 api接口
        List<ModuleBasePlaceDTO> moduleBasePlaceDTOList = batteryFeignService.
                findBasePlaceWorkshopWorkUnit(excelDTO.getBasePlaceName(), excelDTO.getWorkshopName(), null);
        if (CollectionUtils.isEmpty(moduleBasePlaceDTOList)) {
            throw new BizException("bbom_valid_basePlaceWorkshop_illegal", excelDTO.getBasePlaceName(), excelDTO.getWorkshopName());

        }
        // 获取基地
        excelDTO.setBasePlace(excelDTO.getBasePlaceName());
        // 获取车间
        excelDTO.setWorkshop(excelDTO.getWorkshopName());

        // 校验机台合法性 lov
        LovLineDTO workbench = Optional.ofNullable(LovUtils.getByName(CELL_MACHINE,
                excelDTO.getWorkbenchName())).orElseThrow(() -> new BizException("bbom_valid_workbench_illegal", excelDTO.getWorkbenchName()));
        // 获取机台信息
        excelDTO.setWorkbench(String.valueOf(workbench.getLovLineId()));

        // 补齐电池类型编码
        BatteryTypeMainDTO batteryTypeMainDTO = batteryTypeMainService.queryByBatteryName(excelDTO.getBatteryTypeName());
        Optional.ofNullable(batteryTypeMainDTO).ifPresent(w -> {
            excelDTO.setBatteryType(w.getBatteryCode());
        });
        if (StringUtils.isBlank(excelDTO.getBatteryType())) {
            throw new BizException("bbom_valid_batteryCode_illegal", excelDTO.getBatteryType());
        }
        // 校验模版数据是否重复记录
        StringBuilder builder = getStringBuilder(excelDTO, hashSet);
        hashSet.add(builder.toString());
    }
}
