package com.trinasolar.scp.bbom.service.service;

import com.trinasolar.scp.bbom.domain.dto.ItemsLiftCycleDTO;
import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleDTO;
import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleSyncDTO;
import com.trinasolar.scp.bbom.domain.query.PdmItemLifecycleQuery;
import com.trinasolar.scp.bbom.domain.query.PdmItemLifecycleSyncQuery;
import com.trinasolar.scp.bbom.domain.save.PdmItemLifecycleSaveDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

/**
 * pdm物料生命周期原始表 服务接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
public interface PdmItemLifecycleService {
    /**
     * 分页获取pdm物料生命周期原始表
     *
     * @param query 查询对象
     * @return pdm物料生命周期原始表分页对象
     */
    Page<PdmItemLifecycleDTO> queryByPage(PdmItemLifecycleQuery query);

    /**
     * 根据主键获取pdm物料生命周期原始表详情
     *
     * @param id 主键
     * @return pdm物料生命周期原始表详情
     */
        PdmItemLifecycleDTO queryById(Long id);

    /**
     * 保存或更新pdm物料生命周期原始表
     *
     * @param saveDTO pdm物料生命周期原始表保存对象
     * @return pdm物料生命周期原始表对象
     */
    PdmItemLifecycleDTO save(PdmItemLifecycleSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除pdm物料生命周期原始表
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
      * 导出
      *
      * @param query 查询对象
      * @param response 响应对象
      */
    void export(PdmItemLifecycleQuery query, HttpServletResponse response);

    void saveLifeCycle(LocalDate date, List<ItemsLiftCycleDTO> filterList);

    PdmItemLifecycleSyncDTO queryByItemCodeAndOrgId(PdmItemLifecycleSyncQuery syncQuery);
}

