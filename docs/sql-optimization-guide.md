# bbom_materiel_match_line 表 SQL 优化指南

## 问题分析

原始SQL查询存在以下性能问题：

```sql
select * from bbom_materiel_match_line 
where match_status = 'APPOINT_MATCH' 
and item_code is not null 
and old_month >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y%m')
```

### 主要问题：
1. **缺少复合索引**：查询涉及多个条件但缺少对应的复合索引
2. **函数计算开销**：`DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y%m')` 每次查询都要计算
3. **SELECT * 开销**：查询所有字段增加IO负担
4. **数据量大**：几百万数据，每周增加几十万

## 优化方案

### 1. 索引优化

执行以下SQL创建复合索引：

```sql
-- 主要查询条件的复合索引
CREATE INDEX idx_match_status_item_code_old_month 
ON bbom_materiel_match_line (match_status, item_code, old_month) 
USING BTREE;

-- 时间范围查询优化索引
CREATE INDEX idx_old_month_match_status 
ON bbom_materiel_match_line (old_month, match_status, is_deleted) 
USING BTREE;

-- 分页查询优化索引
CREATE INDEX idx_match_status_time 
ON bbom_materiel_match_line (match_status, old_month, updated_time DESC) 
USING BTREE;
```

### 2. SQL查询优化

#### 优化前：
```sql
select * from bbom_materiel_match_line 
where match_status = 'APPOINT_MATCH' 
and item_code is not null 
and old_month >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y%m')
```

#### 优化后：
```sql
SELECT [具体字段列表] 
FROM bbom_materiel_match_line 
WHERE match_status = 'APPOINT_MATCH' 
AND item_code IS NOT NULL 
AND old_month >= ? 
AND is_deleted = 0 
ORDER BY old_month DESC, updated_time DESC
```

### 3. 代码层面优化

#### 使用QueryDSL替代原生SQL：
```java
public Map<String, List<MaterielMatchLine>> getAllLineMapOptimized() {
    LocalDate lastMonth = LocalDate.now().minusMonths(1);
    String lastMonthStr = lastMonth.format(DateTimeFormatter.ofPattern("yyyyMM"));
    
    BooleanBuilder booleanBuilder = new BooleanBuilder();
    booleanBuilder.and(QMaterielMatchLine.materielMatchLine.matchStatus.eq("APPOINT_MATCH"));
    booleanBuilder.and(QMaterielMatchLine.materielMatchLine.itemCode.isNotNull());
    booleanBuilder.and(QMaterielMatchLine.materielMatchLine.oldMonth.goe(lastMonthStr));
    booleanBuilder.and(QMaterielMatchLine.materielMatchLine.isDeleted.eq(DeleteEnum.NO.getCode()));
    
    return jpaQueryFactory
            .selectFrom(QMaterielMatchLine.materielMatchLine)
            .where(booleanBuilder)
            .orderBy(QMaterielMatchLine.materielMatchLine.oldMonth.desc(),
                    QMaterielMatchLine.materielMatchLine.updatedTime.desc())
            .fetch();
}
```

#### 分页查询避免大数据量加载：
```java
public Page<MaterielMatchLine> getAppointMatchLinesWithPaging(int pageNumber, int pageSize, int monthsBack) {
    // 实现分页查询，避免一次性加载大量数据
}
```

### 4. 性能监控建议

1. **监控查询执行时间**：
   ```sql
   EXPLAIN SELECT ... -- 查看执行计划
   ```

2. **监控索引使用情况**：
   ```sql
   SHOW INDEX FROM bbom_materiel_match_line;
   ```

3. **定期统计数据量**：
   ```java
   public long countAppointMatchLines(int monthsBack) {
       // 统计符合条件的记录数
   }
   ```

### 5. 数据库配置优化

在application.yml中添加以下配置：

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  jpa:
    properties:
      hibernate:
        jdbc:
          batch_size: 50
          fetch_size: 100
        order_inserts: true
        order_updates: true
```

## 预期性能提升

1. **查询时间**：从几秒降低到毫秒级
2. **CPU使用率**：减少函数计算开销
3. **内存使用**：分页查询减少内存占用
4. **并发能力**：索引优化提升并发查询能力

## 实施步骤

1. 在测试环境执行索引创建SQL
2. 部署优化后的代码
3. 监控性能指标
4. 逐步在生产环境实施
5. 持续监控和调优

## 注意事项

1. 索引创建可能需要较长时间，建议在业务低峰期执行
2. 新增索引会增加写操作的开销，需要权衡
3. 定期清理历史数据，避免表过大影响性能
4. 考虑分表策略，按月份分表存储历史数据
