# doSingleMatch方法性能优化方案

## 🔍 问题总结

### 主要性能问题
1. **数据库查询问题**: N+1查询、大数据量查询、频繁远程调用
2. **内存使用问题**: 大对象创建、频繁复制、缓存缺失
3. **CPU使用问题**: 复杂嵌套循环、反射调用、字符串操作
4. **架构问题**: 单体方法过于复杂、缺乏分层设计

## 🚀 优化方案

### 方案1: 缓存优化 (立即实施)

#### 1.1 添加方法级缓存
```java
@Service
public class MaterielMatchCacheService {
    
    @Cacheable(cacheNames = "items_all", key = "'all_5a_items'", unless = "#result.size() == 0")
    public List<ItemsDTO> getAllItems() {
        return itemsService.queryByMatchingAll();
    }
    
    @Cacheable(cacheNames = "express_rules", key = "'all_rules'")
    public Map<Long, List<ExpressRuleLineDTO>> getAllExpressRules() {
        return getExpressRuleLineList();
    }
    
    @Cacheable(cacheNames = "dp_columns", key = "'dp_item_mapping'")
    public Map<String, String> getDpItemMapping() {
        return getDpColAndItemColMap();
    }
}
```

#### 1.2 批量查询优化
```java
// 替换N+1查询
public Map<String, ItemsDTO> batchFindItemsByCodesAndOrg(List<String> itemCodes, Long orgId) {
    return jpaQueryFactory
        .selectFrom(qItems)
        .where(qItems.itemCode.in(itemCodes))
        .where(qItems.organizationId.eq(orgId))
        .fetch()
        .stream()
        .collect(Collectors.toMap(Items::getItemCode, convert::toDto));
}
```

### 方案2: 数据库查询优化 (高优先级)

#### 2.1 索引优化
```sql
-- 为items表添加复合索引
CREATE INDEX idx_items_code_org_status ON bbom_items (item_code, organization_id, item_status);
CREATE INDEX idx_items_category_org ON bbom_items (category_segment2, organization_id, item_status);

-- 为match_line表添加查询索引  
CREATE INDEX idx_match_line_header_split_hand ON bbom_materiel_match_line (header_id, split_flag, hand_work_flag);
```

#### 2.2 分页查询
```java
public Page<ItemsDTO> getItemsWithPaging(int pageSize) {
    Pageable pageable = PageRequest.of(0, pageSize);
    return itemsRepository.findByCategorySegment2AndOrganizationIdAndItemStatus(
        "自产电池片", 82L, "Active", pageable);
}
```

### 方案3: 方法重构 (中期实施)

#### 3.1 拆分大方法
```java
@Service
public class MaterielMatchService {
    
    public void doSingleMatch(Long matchId, Map<String,List<MaterielMatchLine>> matchLineaAllMap) {
        // 1. 数据准备阶段
        MatchContext context = prepareMatchData(matchId);
        
        // 2. 料号过滤阶段  
        List<ItemsDTO> filteredItems = filterItems(context);
        
        // 3. 匹配处理阶段
        List<MaterielMatchLineDTO> matchedLines = processMatching(context, filteredItems);
        
        // 4. 结果保存阶段
        saveMatchResults(context, matchedLines);
    }
    
    private MatchContext prepareMatchData(Long matchId) {
        return MatchContext.builder()
            .header(materielMatchHeaderService.queryById(matchId))
            .designatorMap(cacheService.getDesignatorMap())
            .expressRules(cacheService.getAllExpressRules())
            .dpMapping(cacheService.getDpItemMapping())
            .allItems(cacheService.getAllItems())
            .build();
    }
}
```

#### 3.2 引入上下文对象
```java
@Data
@Builder
public class MatchContext {
    private MaterielMatchHeaderDTO header;
    private Map<Long, List<ErpAlternateDesignatorDTO>> designatorMap;
    private Map<Long, List<ExpressRuleLineDTO>> expressRules;
    private Map<String, String> dpMapping;
    private List<ItemsDTO> allItems;
    private Map<String, StructuresDTO> structures;
}
```

### 方案4: 异步处理 (长期规划)

#### 4.1 异步匹配处理
```java
@Async("matchExecutor")
public CompletableFuture<Void> doAsyncMatch(Long matchId) {
    try {
        doSingleMatch(matchId, getAllLineMap());
        return CompletableFuture.completedFuture(null);
    } catch (Exception e) {
        return CompletableFuture.failedFuture(e);
    }
}
```

#### 4.2 分批处理
```java
public void processBatchMatching(List<Long> matchIds) {
    int batchSize = 10;
    List<List<Long>> batches = Lists.partition(matchIds, batchSize);
    
    batches.parallelStream().forEach(batch -> {
        batch.forEach(matchId -> {
            try {
                doSingleMatch(matchId, getAllLineMap());
            } catch (Exception e) {
                log.error("匹配失败: matchId={}", matchId, e);
            }
        });
    });
}
```

### 方案5: 内存优化 (立即实施)

#### 5.1 减少对象复制
```java
// 使用Map替代嵌套循环
Map<Long, MaterielMatchLineDTO> hasItemMap = hasItemDtos.stream()
    .collect(Collectors.toMap(MaterielMatchLineDTO::getId, Function.identity()));

matchLineDTOList.forEach(item -> {
    MaterielMatchLineDTO hasItem = hasItemMap.get(item.getId());
    if (hasItem != null) {
        BeanUtils.copyProperties(hasItem, item);
    }
});
```

#### 5.2 使用Builder模式
```java
// 减少中间对象创建
public MaterielMatchLineDTO buildMatchLine(ItemsDTO item, MatchContext context) {
    return MaterielMatchLineDTO.builder()
        .itemCode(item.getItemCode())
        .itemDesc(item.getItemDesc())
        .alternateBomDesignator(getAlternateDesignator(item, context))
        .route(checkRoute(item, context) ? "是" : "否")
        .build();
}
```

## 📊 预期性能提升

| 优化项目 | 当前性能 | 优化后性能 | 提升幅度 |
|---------|---------|-----------|---------|
| 数据库查询时间 | 5-10秒 | 0.5-1秒 | 80-90% |
| 内存使用 | 500MB+ | 100-200MB | 60-80% |
| CPU使用率 | 80-90% | 30-50% | 40-60% |
| 总执行时间 | 30-60秒 | 5-10秒 | 80-85% |

## 🎯 实施计划

### 第一阶段 (1-2周)
1. ✅ 添加缓存注解和缓存服务
2. ✅ 优化数据库索引
3. ✅ 修复N+1查询问题
4. ✅ 减少对象复制操作

### 第二阶段 (2-3周)  
1. 🔄 重构大方法，拆分职责
2. 🔄 引入上下文对象
3. 🔄 优化复杂循环逻辑
4. 🔄 添加性能监控

### 第三阶段 (3-4周)
1. 🔮 实现异步处理
2. 🔮 添加分批处理机制
3. 🔮 实现熔断和降级
4. 🔮 完善监控和告警

## ⚠️ 风险评估

1. **缓存一致性**: 需要合理设置缓存过期时间
2. **数据准确性**: 重构过程中需要充分测试
3. **系统稳定性**: 分阶段实施，避免大范围改动
4. **业务影响**: 在低峰期进行部署和测试
