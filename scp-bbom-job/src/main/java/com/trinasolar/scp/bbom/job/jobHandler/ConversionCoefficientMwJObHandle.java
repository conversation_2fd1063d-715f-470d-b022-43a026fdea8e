package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.domain.save.ConversionCoefficientMwSaveDTO;
import com.trinasolar.scp.bbom.service.service.ConversionCoefficientMwService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MatchLineItemJObHandle
 * @description: TODO
 * @date 2024/3/11 19:04
 */
@Component
@Slf4j
public class ConversionCoefficientMwJObHandle {
    @Autowired
    ConversionCoefficientMwService conversionCoefficientMwService;

    /**
     * 每隔1小时跑
     *
     * @return
     */
    @XxlJob("ConversionCoefficientMwComputeSave")
    public ReturnT<String> runMatchLine() {
        XxlJobHelper.log("开始定时任务-兆瓦转换系数计算：{}", LocalDateTime.now());
        ConversionCoefficientMwSaveDTO saveDTO = new ConversionCoefficientMwSaveDTO();
        conversionCoefficientMwService.computeSave(saveDTO);
        XxlJobHelper.log("结束定时任务-兆瓦转换系数计算：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}