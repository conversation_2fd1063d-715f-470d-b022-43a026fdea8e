package com.trinasolar.scp.bbom.job.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 物料生命周期接口：在BBOM_ITEM表中基于ITEM_CODE写入lifecycle_state字段中
 * @date 202/01/19
 */
@Component
@Slf4j
public class ItemLifecycleStateSyncJobHandler {
    @Autowired
    ItemsService itemsService;

    @XxlJob("ItemLifecycleStateSyncJobHandler")
    public ReturnT<String> ItemLifecycleStateSyncJobHandler() {
        XxlJobHelper.log("开始同步物料生命周期：{}", LocalDateTime.now());
        LocalDate now = LocalDate.now();
        LocalDate startDate = now.minusDays(2L);
        while (startDate.isBefore(now) || startDate.isEqual(now)) {
            itemsService.ItemLifecycleStateSync(startDate);
            startDate = startDate.plusDays(1);
        }
        XxlJobHelper.log("结束同步物料生命周期：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
