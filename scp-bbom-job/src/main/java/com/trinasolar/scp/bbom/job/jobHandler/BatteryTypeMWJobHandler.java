package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.service.service.ConversionCoefficientMwService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryTypeMWJobHandler
 * @Description 定时任务-兆瓦转换系数的定时计算
 * @Date 2024-03-27
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class BatteryTypeMWJobHandler {

    @Autowired
    ConversionCoefficientMwService conversionCoefficientMwService;


    /**
     * 定时任务-兆瓦系数计算
     * 每天晚上11点执行定时任务
     *
     * @return
     */
    @XxlJob("computeSaveWithJob")
    public ReturnT<String> computeSaveWithJob() {
        XxlJobHelper.log("开始同步computeSaveWithJob：{}", LocalDateTime.now());
        conversionCoefficientMwService.computeSaveWithJob();
        XxlJobHelper.log("结束同步computeSaveWithJob：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
