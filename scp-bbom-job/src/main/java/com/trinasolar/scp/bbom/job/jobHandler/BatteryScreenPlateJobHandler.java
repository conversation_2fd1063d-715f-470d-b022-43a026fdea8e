package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateQuery;
import com.trinasolar.scp.bbom.service.service.BatteryScreenPlateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/5/1
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BatteryScreenPlateJobHandler {
    private final BatteryScreenPlateService batteryScreenPlateService;

    /**
     * 定时任务-网版切换-批量更新
     */
    @XxlJob("batteryScreenPlateBatchUpdate")
    public ReturnT<String> batteryScreenPlateBatchUpdate() {
        XxlJobHelper.log("开始 网版切换-批量更新：{}", LocalDateTime.now());
        batteryScreenPlateService.batchUpdate(new BatteryScreenPlateQuery());
        XxlJobHelper.log("结束 网版切换-批量更新：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
