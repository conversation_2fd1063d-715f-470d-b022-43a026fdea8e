package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.service.service.BatteryTypeJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryProductJobHandler
 * @Description 定时任务-电池类型静态属性-解析-main表
 * @Date 2023/12/11 10:32
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class BatteryProductJobHandler {

    private final BatteryTypeJobService batteryTypeJobService;


    /**
     * 定时任务-同步电池类型编码名称到lov
     */
    @XxlJob("batteryTypeJobHandler")
    public ReturnT<String> batteryTypeJobHandler() {
        XxlJobHelper.log("开始同步batteryTypeJobHandler：{}", LocalDateTime.now());
        batteryTypeJobService.batteryTypeJobHandler();
        XxlJobHelper.log("结束同步batteryTypeJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 定时任务-电池类型静态属性主表写数据
     * 每天晚上8点执行定时任务
     *
     * @return
     */
    @XxlJob("batteryJobHandler")
    public ReturnT<String> batteryJobHandler() {
        XxlJobHelper.log("开始同步batteryJobHandler：{}", LocalDateTime.now());
        batteryTypeJobService.batteryJobHandler();
        XxlJobHelper.log("结束同步batteryJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 定时任务-电池类型静态属性value字段更新物料表bbom_items segment60
     * 每天晚上9点执行定时任务
     *
     * @return
     */
    @XxlJob("batteryUpdateItmesJobHandler")
    public ReturnT<String> batteryUpdateItmesJobHandler() {
        XxlJobHelper.log("开始同步batteryUpdateItmesJobHandler：{}", LocalDateTime.now());
        batteryTypeJobService.batteryUpdateItmesJobHandler();
        XxlJobHelper.log("结束同步batteryUpdateItmesJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
