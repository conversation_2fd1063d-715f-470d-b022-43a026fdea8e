package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.bbom.domain.utils.DateUtil;
import com.trinasolar.scp.bbom.service.service.MaterielMatchHeaderService;
import com.trinasolar.scp.common.api.util.MyThreadLocal;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName MatchLineItemJObHandle
 * @description: TODO
 * @date 2024/3/11 19:04
 */
@Component
@Slf4j
public class MatchLineItemJObHandle {
    @Autowired
    MaterielMatchHeaderService materielMatchHeaderService;

    /**
     * 每隔1小时跑 不超过三个月的数据
     *
     * @return
     */
    @XxlJob("runMatchLine")
    public ReturnT<String> runMatchLine() {
        XxlJobHelper.log("开始定时任务-料号匹配runMatchLine：{}", LocalDateTime.now());
        MaterielMatchHeaderQuery query = new MaterielMatchHeaderQuery();
        query.setUserId("-1");
        query.setMonth(DateUtil.getMonth(LocalDate.now()));
        materielMatchHeaderService.allMatchItemPage(query);

        // 增加匹配下个月的数据
        MaterielMatchHeaderQuery nextQuery = new MaterielMatchHeaderQuery();
        nextQuery.setUserId("-1");
        nextQuery.setMonth(DateUtil.getMonth(LocalDate.now().plusMonths(1)));
        materielMatchHeaderService.allMatchItemPage(nextQuery);
        XxlJobHelper.log("结束定时任务-料号匹配runMatchLine：{}", LocalDateTime.now());

        // 增加匹配下下个月的数据
        MaterielMatchHeaderQuery nextNextQuery = new MaterielMatchHeaderQuery();
        nextNextQuery.setUserId("-1");
        nextNextQuery.setMonth(DateUtil.getMonth(LocalDate.now().plusMonths(2)));
        materielMatchHeaderService.allMatchItemPage(nextNextQuery);
        XxlJobHelper.log("结束定时任务-料号匹配runMatchLine：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}