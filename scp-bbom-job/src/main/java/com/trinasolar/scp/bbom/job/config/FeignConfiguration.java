package com.trinasolar.scp.bbom.job.config;

import com.trinasolar.scp.common.api.util.MyThreadLocal;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class FeignConfiguration {

    @Value("${scp.super.token}")
    public String superToken;

    /**
     * Set the Feign specific log level to log client REST requests.
     */
    @Bean
    feign.Logger.Level feignLoggerLevel() {
        return feign.Logger.Level.NONE;
    }

    /**
     * 创建Feign请求拦截器，在发送请求前设置认证的token,各个微服务将token设置到环境变量中来达到通用
     *
     * @return
     */
    @Bean
    public FeignBasicAuthRequestInterceptor basicAuthRequestInterceptor() {
        return new FeignBasicAuthRequestInterceptor();
    }

    /**
     * Feign请求拦截器
     *
     * <AUTHOR>
     * @create 2017-11-10 17:25
     **/
    public class FeignBasicAuthRequestInterceptor implements RequestInterceptor {
        public FeignBasicAuthRequestInterceptor() {
        }

        @Override
        public void apply(RequestTemplate template) {
            template.header("lang", MyThreadLocal.get().getLang());
            // 默认给token
            /*if(StringUtils.isBlank(MyThreadLocal.get().getToken())){
                template.header("accesstoken", superToken);
            }else{
                template.header("accesstoken", superToken);
            }*/
//            template.header("accesstoken", "!QAZ@WSX#EDC$RFV%TGB^YHN");
            template.header("accesstoken", superToken);
            template.header("applicationId", MyThreadLocal.get().getApplicationId());
        }
    }
}
