package com.trinasolar.scp.bbom.job.config;

import com.ibm.dpf.common.config.FastConverter;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@Configuration
public class EntityManagerConfig {
    @PersistenceContext
    EntityManager entityManager;

    @Primary
    @Bean
    @Scope("prototype")
    public JPAQueryFactory jpaQueryFactory() {
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        return jpaQueryFactory;
    }

    @Bean
    @Qualifier("ipassRestTemplate")
    public RestTemplate ipassRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(2 * 60 * 1000);
        factory.setReadTimeout(2 * 60 * 1000);
        RestTemplate template = new RestTemplate(factory);
        template.setMessageConverters(Collections.singletonList(new FastConverter()));
        template.setRequestFactory(factory);
        return template;
    }
}
