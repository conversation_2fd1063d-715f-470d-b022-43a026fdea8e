package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.service.service.ItemAttrLovService;
import com.trinasolar.scp.bbom.service.service.ItemAttrService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/27
 */
@Component
@Slf4j
public class ItemAttrSyncJobHandler {
    @Autowired
    ItemAttrService itemAttrService;

    @Autowired
    ItemAttrLovService itemAttrLovService;

    @XxlJob("itemAttrSyncJobHandler")
    public ReturnT<String> itemAttrSyncJobHandler() {
        XxlJobHelper.log("开始同步itemAttrSyncJobHandler：{}", LocalDateTime.now());
        itemAttrService.sync();
        itemAttrLovService.sync();
        itemAttrLovService.transToLov();
        XxlJobHelper.log("结束同步itemAttrSyncJobHandler：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

}
