package com.trinasolar.scp.bbom.job.jobHandler;

import com.trinasolar.scp.bbom.service.service.BatteryScreenPlateService;
import com.trinasolar.scp.bbom.service.service.BatterySlurryService;
import com.trinasolar.scp.bbom.service.service.MaterielMatchLineService;
import com.trinasolar.scp.bbom.service.service.ScreenLifeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 邮件推送
 *
 * <AUTHOR>
 * @date 2024/02/26
 */
@Component
@Slf4j
public class sendMailJobHandler {
    @Autowired
    BatteryScreenPlateService screenPlateService;
    @Autowired
    ScreenLifeService screenLifeService;

    @Autowired
    BatterySlurryService batterySlurryService;
    @Autowired
    MaterielMatchLineService matchLineService;

    /**
     * 每周一 早上9:00推送
     *
     * @return
     */
    @XxlJob("sendBatteryScreenPlate")
    public ReturnT<String> sendBatteryScreenPlate() {
        XxlJobHelper.log("开始定时任务-网版切换邮件推送sendBatteryScreenPlate：{}", LocalDateTime.now());
        screenPlateService.queryListBySendMail();
        XxlJobHelper.log("结束定时任务-网版切换邮件推送sendBatteryScreenPlate：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 每周一 早上9:00推送
     *
     * @return
     */
    @XxlJob("sendBatterySlurry")
    public ReturnT<String> sendBatterySlurry() {
        XxlJobHelper.log("开始定时任务-浆料切换邮件推送sendBatterySlurry：{}", LocalDateTime.now());
        batterySlurryService.queryListBySendMail();
        XxlJobHelper.log("结束定时任务-浆料切换邮件推送sendBatterySlurry：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 每周一 早上9:00推送
     *
     * @return
     */
    @XxlJob("sendScreenLife")
    public ReturnT<String> sendScreenLife() {
        XxlJobHelper.log("开始定时任务-网版寿命邮件推送sendScreenLife：{}", LocalDateTime.now());
        screenLifeService.queryListBySendMail();
        XxlJobHelper.log("结束定时任务-网版寿命邮件推送sendScreenLife：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }

    /**
     * 每隔1小时跑
     *
     * @return
     */
    @XxlJob("sendMatchLine")
    public ReturnT<String> sendMatchLine() {
        XxlJobHelper.log("开始定时任务-料号匹配邮件推送sendMatchLine：{}", LocalDateTime.now());
        matchLineService.sendMailByqueryList();
        XxlJobHelper.log("结束定时任务-料号匹配邮件推送sendMatchLine：{}", LocalDateTime.now());
        return ReturnT.SUCCESS;
    }
}
