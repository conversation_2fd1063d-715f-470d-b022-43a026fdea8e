package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleDetailDTO", description = "DTO对象")
public class RuleDetailDTO extends BaseDTO {

    @ApiModelProperty(value = "序号")
    private Integer no;


    @ApiModelProperty(value = "规则分类名称")
    private String ruleCategoryName;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    private String ruleNumber;

    /**
     * 行规则编码
     */
    @ApiModelProperty(value = "行规则编码")
    private String lineRuleNumber;
    /**
     * 电池片料号
     */
    @ApiModelProperty(value = "电池片料号")
    private String itemCode;
    /**
     * 监造限制
     */
    @ApiModelProperty(value = "监造限制")
    private String supervisionRestriction;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    private String batteryWorkshop;
    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    private String componentWorkshop;
    /**
     * 特殊单限制
     */
    @ApiModelProperty(value = "特殊单限制")
    private String specialOrderRestriction;
    /**
     * 单/双玻
     */
    @ApiModelProperty(value = "单/双玻")
    private String SingleOrDoubleGlass;
    /**
     * 机台限制
     */
    @ApiModelProperty(value = "机台限制")
    private String machineLimit;
    /**
     * 测试限制
     */
    @ApiModelProperty(value = "测试限制")
    private String testLimitation;
    /**
     * 包装限制
     */
    @ApiModelProperty(value = "包装限制")
    private String packagingRestriction;
    /**
     * 认证限制
     */
    @ApiModelProperty(value = "认证限制")
    private String authenticationRestriction;
    /**
     * 材料限制
     */
    @ApiModelProperty(value = "材料限制")
    private String materialLimitation;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;

}
