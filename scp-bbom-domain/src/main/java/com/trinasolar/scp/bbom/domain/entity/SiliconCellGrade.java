package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 硅片等级与电池等级映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Entity
@ToString
@Data
@Table(name = "bbom_silicon_cell_grade")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_silicon_cell_grade SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_silicon_cell_grade SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class SiliconCellGrade extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    @Column(name = "silicon_wafer_properties")
    private String siliconWaferProperties;

    /**
     * 硅片属性条件项
     */
    @ApiModelProperty(value = "硅片属性条件项")
    @Column(name = "silicon_wafer_conditional_item")
    private String siliconWaferConditionalItem;

    /**
     * 硅片属性值
     */
    @ApiModelProperty(value = "硅片属性值")
    @Column(name = "silicon_wafer_value")
    private String siliconWaferValue;

    /**
     * 电池属性
     */
    @ApiModelProperty(value = "电池属性")
    @Column(name = "battery_properties")
    private String batteryProperties;

    /**
     * 电池属性条件项
     */
    @ApiModelProperty(value = "电池属性条件项")
    @Column(name = "battery_conditional_item")
    private String batteryConditionalItem;

    /**
     * 电池属性值
     */
    @ApiModelProperty(value = "电池属性值")
    @Column(name = "battery_value")
    private String batteryValue;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;


}
