package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatteryTypeProduceDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryTypeProduce;
import com.trinasolar.scp.bbom.domain.excel.BatteryTypeProduceExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型动态属性-产出电池类型 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryTypeProduceDEConvert extends BaseDEConvert<BatteryTypeProduceDTO, BatteryTypeProduce> {

    BatteryTypeProduceDEConvert INSTANCE = Mappers.getMapper(BatteryTypeProduceDEConvert.class);

    List<BatteryTypeProduceExcelDTO> toExcelDTO(List<BatteryTypeProduceDTO> dtos);

    BatteryTypeProduceExcelDTO toExcelDTO(BatteryTypeProduceDTO dto);
}
