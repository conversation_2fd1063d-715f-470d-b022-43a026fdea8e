package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.bbom.domain.entity.LowEfficiencyCellPercent;
import com.trinasolar.scp.bbom.domain.excel.LowEfficiencyCellPercentExportExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.LowEfficiencyCellPercentImportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LowEfficiencyCellPercentDEConvert extends BaseDEConvert<LowEfficiencyCellPercentDTO, LowEfficiencyCellPercent> {

    LowEfficiencyCellPercent INSTANCE = Mappers.getMapper(LowEfficiencyCellPercent.class);

    List<LowEfficiencyCellPercentDTO> excelToDTO(List<LowEfficiencyCellPercentImportExcelDTO> excelDTOS);

    List<LowEfficiencyCellPercentExportExcelDTO> dtoToExcel(List<LowEfficiencyCellPercentDTO> dtoList);
}
