package com.trinasolar.scp.bbom.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/7/8
 */
@AllArgsConstructor
@Getter
public enum AttrOperatorEnum {
    /**
     * AttrOperator的关系
     */
    INCLUDE("1", "包含", "=="),
    EXCLUDE("2", "排除", "!="),
    EQUAL("3", "等于", "==");

    private String value;

    private String name;

    private String connector;

    public static AttrOperatorEnum getEnumByValue(String value) {
        for (AttrOperatorEnum attrOperatorEnum : AttrOperatorEnum.values()) {
            if (attrOperatorEnum.getValue().equals(value)) {
                return attrOperatorEnum;
            }
        }
        return null;
    }
}
