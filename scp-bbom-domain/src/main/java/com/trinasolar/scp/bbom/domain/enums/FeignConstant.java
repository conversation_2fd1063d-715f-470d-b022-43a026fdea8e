package com.trinasolar.scp.bbom.domain.enums;

/**
 * 系统常量
 *
 * <AUTHOR>
 * @date 2022年4月26日10:03:33
 */
public interface FeignConstant {
    String PDM_SCP = "PDM";

    /**
     * 系统服务
     */

    String SCP_SYSTEM_API = "scp-system-api";

    String SCP_DP_API = "scp-dp-api";

    String SCP_BOM_API = "scp-bom-api";

    String SCP_CERT_API = "scp-cert-api";

    String SCP_APS_API = "scp-aps-api";

    String SCP_AOP_API = "scp-aop-api";

    String SCP_BATTERY_APS_API = "scp-battery-aps-api";

    String SCP_BATTERY_DM_API = "scp-battery-dm-api";

    String SCP_BATTERY_MRP_API = "scp-battery-mrp-api";

    String SCP_BATTERY_BOM_API = "scp-battery-bom-api";
    public static final String DPA_MESSAGE_SERVICE = "scp-dpa-message-api";

}
