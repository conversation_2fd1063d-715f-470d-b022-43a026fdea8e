package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ERP工艺路线
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 20:00:01
 */
@Data
@ApiModel(value = "ErpOperatRoute查询条件", description = "查询条件")
@Accessors(chain = true)
public class ErpOperatRouteQuery extends PageDTO {
    /**
     * organization_id
     */
    @ApiModelProperty(value = "organization_id")
    private Long organizationId;
    /**
     * 组件料号
     */
    @ApiModelProperty(value = "组件料号")
    private String inventoryItemNo;
    /**
     * 备用路由指示符
     */
    @ApiModelProperty(value = "备用路由指示符")
    private String alternateRoutingDesignator;
    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料ID")
    private Long assemblyItemId;
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
