package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryTypeMWQuey
 * @Description
 * @Date 2023/12/20 11:33
 **/
@Data
@ApiModel(value = "BatteryTypeMW查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatteryTypeMWQuery extends PageDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    private String classify;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
}
