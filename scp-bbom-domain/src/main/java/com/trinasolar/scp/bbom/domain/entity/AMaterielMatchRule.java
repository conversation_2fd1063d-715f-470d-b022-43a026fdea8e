package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 特殊片源A-料号匹配规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Entity
@ToString
@Data
@Table(name = "bbom_a_materiel_match_rule")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_a_materiel_match_rule SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_a_materiel_match_rule SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class AMaterielMatchRule extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cells_type")
    private String cellsType;

    /**
     * 实际片厚
     */
    @ApiModelProperty(value = "实际片厚")
    @Column(name = "actual_piece_thickness")
    private String actualPieceThickness;

    /**
     * 主栅两端形状
     */
    @ApiModelProperty(value = "主栅两端形状")
    @Column(name = "main_grid_both_shape")
    private String mainGridBothShape;

    /**
     * A-料号
     */
    @ApiModelProperty(value = "A-料号")
    @Column(name = "a_item_code")
    private String aItemCode;
}
