package com.trinasolar.scp.bbom.domain.dto.feign;

import com.trinasolar.scp.bbom.domain.dto.ItemsLiftCycleDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ItemLifeCycleReturnDto
 * @description: TODO
 * @date 2024/1/23 14:19
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ItemLifeCycleReturnDto {

    public Integer totalSize;
    public Integer totalPage;
    public Integer pageSize;
    public Integer currentPage;
    public List<ItemsLiftCycleDTO> partInfos;
}