package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/3
 */
@Data
@ApiModel(value = "Item查询接口", description = "查询条件")
@Accessors(chain = true)
public class ItemQuery implements Serializable {

    private static final long serialVersionUID = 1077581523528572259L;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;


    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

}
