package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


/**
 * pdm物料生命周期原始表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "pdm物料生命周期原始表DTO对象", description = "DTO对象")
public class PdmItemLifecycleDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 同步日期
     */
    @ApiModelProperty(value = "同步日期")
    private LocalDate syncDate;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemCode;

    /**
     * name
     */
    @ApiModelProperty(value = "name")
    private String name;

    /**
     * 生命周期
     */
    @ApiModelProperty(value = "生命周期")
    private String lifecycle;

    /**
     * stage
     */
    @ApiModelProperty(value = "stage")
    private String stage;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String itemDesc;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private Long orgId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
}
