package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryWaferPropertyDTO
 * @Description
 * @Date 2023/12/5 11:31
 **/
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池类型动态属性-硅片属性DTO对象", description = "硅片属性DTO对象")
public class BatteryWaferPropertyDTO {
    @ApiModelProperty(value = "ID主键")
    private Long id;
    @ApiModelProperty(value = "lov名称")
    private String name;
    @ApiModelProperty(value = "lovId主键")
    private String lovId;
}
