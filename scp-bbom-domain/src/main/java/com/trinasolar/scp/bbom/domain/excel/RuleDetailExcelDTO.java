package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * BOM规则头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleDetailExcelDTO {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号")
    private Integer no;


    @ApiModelProperty(value = "规则分类名称")
    @ExcelProperty(value = "规则分类名称")
    private String ruleCategoryName;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    @ExcelProperty(value = "规则名称")
    private String ruleName;
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    @ExcelProperty(value = "规则编码")
    private String ruleNumber;

    /**
     * 行规则编码
     */
    @ApiModelProperty(value = "行规则编码")
    @ExcelProperty(value = "行规则编码")
    private String lineRuleNumber;
    /**
     * 电池片料号
     */
    @ApiModelProperty(value = "电池片料号")
    @ExcelProperty(value = "电池片料号")
    private String itemCode;
    /**
     * 监造限制
     */
    @ApiModelProperty(value = "监造限制")
    @ExcelProperty(value = "监造限制")
    private String supervisionRestriction;
    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    @ExcelProperty(value = "电池车间")
    private String batteryWorkshop;
    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    @ExcelProperty(value = "组件车间")
    private String componentWorkshop;
    /**
     * 特殊单限制
     */
    @ApiModelProperty(value = "特殊单限制")
    @ExcelProperty(value = "特殊单限制")
    private String specialOrderRestriction;
    /**
     * 单/双玻
     */
    @ApiModelProperty(value = "单/双玻")
    @ExcelProperty(value = "单/双玻")
    private String SingleOrDoubleGlass;
    /**
     * 机台限制
     */
    @ApiModelProperty(value = "机台限制")
    @ExcelProperty(value = "机台限制")
    private String machineLimit;
    /**
     * 测试限制
     */
    @ApiModelProperty(value = "测试限制")
    @ExcelProperty(value = "测试限制")
    private String testLimitation;
    /**
     * 包装限制
     */
    @ApiModelProperty(value = "包装限制")
    @ExcelProperty(value = "包装限制")
    private String packagingRestriction;
    /**
     * 认证限制
     */
    @ApiModelProperty(value = "认证限制")
    @ExcelProperty(value = "认证限制")
    private String authenticationRestriction;
    /**
     * 材料限制
     */
    @ApiModelProperty(value = "材料限制")
    @ExcelProperty(value = "材料限制")
    private String materialLimitation;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;


}
