package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LowEfficiencyCellPercentImportExcelDTO {

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外lovName")
    @ExcelProperty(value = "国内/海外")
    private String countryFlagName;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份")
    private String year;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地lovName")
    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "生产车间lovName")
    @ExcelProperty(value = "生产车间")
    private String workshopName;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型lovName")
    @ExcelProperty(value = "电池类型")
    private String cellTypeName;

    /**
     * 1月占比
     */
    @ApiModelProperty(value = "1月占比")
    @ExcelProperty(value = "JAN")
    private String m1Percent;

    /**
     * 2月占比
     */
    @ApiModelProperty(value = "2月占比")
    @ExcelProperty(value = "FEB")
    private String m2Percent;

    /**
     * 3月占比
     */
    @ApiModelProperty(value = "3月占比")
    @ExcelProperty(value = "MAR")
    private String m3Percent;

    /**
     * 4月占比
     */
    @ApiModelProperty(value = "4月占比")
    @ExcelProperty(value = "APR")
    private String m4Percent;

    /**
     * 5月占比
     */
    @ApiModelProperty(value = "5月占比")
    @ExcelProperty(value = "MAY")
    private String m5Percent;

    /**
     * 6月占比
     */
    @ApiModelProperty(value = "6月占比")
    @ExcelProperty(value = "JUN")
    private String m6Percent;

    /**
     * 7月占比
     */
    @ApiModelProperty(value = "7月占比")
    @ExcelProperty(value = "JUL")
    private String m7Percent;

    /**
     * 8月占比
     */
    @ApiModelProperty(value = "8月占比")
    @ExcelProperty(value = "AUG")
    private String m8Percent;

    /**
     * 9月占比
     */
    @ApiModelProperty(value = "9月占比")
    @ExcelProperty(value = "SEP")
    private String m9Percent;

    /**
     * 10月占比
     */
    @ApiModelProperty(value = "10月占比")
    @ExcelProperty(value = "OCT")
    private String m10Percent;

    /**
     * 11月占比
     */
    @ApiModelProperty(value = "11月占比")
    @ExcelProperty(value = "NOV")
    private String m11Percent;

    /**
     * 12月占比
     */
    @ApiModelProperty(value = "12月占比")
    @ExcelProperty(value = "DEC")
    private String m12Percent;
}
