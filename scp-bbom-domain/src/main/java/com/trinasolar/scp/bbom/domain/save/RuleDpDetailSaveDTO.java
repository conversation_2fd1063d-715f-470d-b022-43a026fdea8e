package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * BOM规则DP因子
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleDpDetail保存参数", description = "保存参数")
public class RuleDpDetailSaveDTO extends TokenDTO implements Serializable {

    /**
     * 规则行ID
     */
    @ApiModelProperty(value = "规则行ID")
    private Long ruleLineId;
    /**
     * 规则明细ID，序列号生成
     */
    @ApiModelProperty(value = "规则明细ID，序列号生成")
    private Long ruleDetailId;
    @ApiModelProperty(value = "DP字段Id,AttrLineId")
    private Long dpFiledId;
    /**
     * DP字段
     */
    @ApiModelProperty(value = "DP字段名称")
    private String dpFiledName;
    /**
     * 运算符 1：包含  2：排除  3：等于
     */
    @ApiModelProperty(value = "运算符 1：包含  2：排除  3：等于")
    private String attrOperator;

    @ApiModelProperty(value = "值，可能是多个")
    private List<RuleDpValueSaveDTO> values;
}
