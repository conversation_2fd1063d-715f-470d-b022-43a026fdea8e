package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ScreenLifeDTO;
import com.trinasolar.scp.bbom.domain.entity.ScreenLife;
import com.trinasolar.scp.bbom.domain.excel.ScreenLifeExcelDTO;
import com.trinasolar.scp.bbom.domain.save.ScreenLifeSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 网版寿命信息维护 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ScreenLifeDEConvert extends BaseDEConvert<ScreenLifeDTO, ScreenLife> {

    ScreenLifeDEConvert INSTANCE = Mappers.getMapper(ScreenLifeDEConvert.class);

    List<ScreenLifeExcelDTO> toExcelDTO(List<ScreenLifeDTO> dtos);

    ScreenLifeExcelDTO toExcelDTO(ScreenLifeDTO dto);

    ScreenLifeSaveDTO toSaveDTO(ScreenLifeExcelDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    ScreenLife saveDTOtoEntity(ScreenLifeSaveDTO saveDTO, @MappingTarget ScreenLife entity);

    List<ScreenLifeDTO> toEntityDTO(List<ScreenLifeExcelDTO> dto);

    List<ScreenLifeDTO> toDto(List<ScreenLife> dto);
}
