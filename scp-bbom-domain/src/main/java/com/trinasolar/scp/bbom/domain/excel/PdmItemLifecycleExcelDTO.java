package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * pdm物料生命周期原始表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PdmItemLifecycleExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 同步日期
     */
    @ExcelProperty(value = "同步日期")
    private LocalDate syncDate;
    /**
     * 料号
     */
    @ExcelProperty(value = "料号")
    private String itemCode;
    /**
     * name
     */
    @ExcelProperty(value = "name")
    private String name;
    /**
     * 生命周期
     */
    @ExcelProperty(value = "生命周期")
    private String lifecycle;
    /**
     * stage
     */
    @ExcelProperty(value = "stage")
    private String stage;
    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String itemDesc;
    /**
     * 组织id
     */
    @ExcelProperty(value = "组织id")
    private Long orgId;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
}
