package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchHeader;
import com.trinasolar.scp.bbom.domain.excel.MaterielMatchHeaderExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池物料号匹配 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MaterielMatchHeaderDEConvert extends BaseDEConvert<MaterielMatchHeaderDTO, MaterielMatchHeader> {

    MaterielMatchHeaderDEConvert INSTANCE = Mappers.getMapper(MaterielMatchHeaderDEConvert.class);

    List<MaterielMatchHeaderExcelDTO> toExcelDTO(List<MaterielMatchHeaderDTO> dtos);

    MaterielMatchHeaderExcelDTO toExcelDTO(MaterielMatchHeaderDTO dto);

    MaterielMatchHeaderDTO toCopyDTO(MaterielMatchHeaderDTO dto);

    MaterielMatchHeader saveDTOtoEntity(MaterielMatchHeaderDTO saveDTO, @MappingTarget MaterielMatchHeader entity);

    MaterielMatchLineDTO toLineDTO(MaterielMatchHeaderDTO headerDTO);

    @Mappings(
            {
                @Mapping(target = "ratioCode" ),
                @Mapping(target = "ecsCode" ),
                @Mapping(target = "supplyModeName" ),
            }
    )
    @Override
    MaterielMatchHeader toEntity(MaterielMatchHeaderDTO headerDTO );
}
