package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ReplaceDpFieldQuery
 * @Description
 * @Date 2024/1/1 10:07
 **/
@Data
@ApiModel(value = "ReplaceDpFieldQuery查询条件", description = "查询条件")
@Accessors(chain = true)
public class ReplaceDpFieldQuery {

    List<MaterielMatchHeaderDTO> demandLinesList;
}
