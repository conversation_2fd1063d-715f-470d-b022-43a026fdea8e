package com.trinasolar.scp.bbom.domain.dto.feign.bmrp;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 合格供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-29 07:44:20
 */
@Data
@ApiModel(value = "ApprovedVendor查询条件", description = "查询条件")
@Accessors(chain = true)
public class ApprovedVendorQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    private String itemCategory;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;
    /**
     * 正背面
     */
    @ApiModelProperty(value = "正背面")
    private String frontBack;
    /**
     * 工位
     */
    @ApiModelProperty(value = "工位")
    private String opPosition;
    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;
    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemDesc;
    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;
    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;
    /**
     * 供应商(简称)
     */
    @ApiModelProperty(value = "供应商(简称)")
    private String vendorNameAlt;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    @ApiModelProperty(value = "规格")
    private String itemSpec;

    @ApiModelProperty(value = "型号")
    private String itemModel;
}
