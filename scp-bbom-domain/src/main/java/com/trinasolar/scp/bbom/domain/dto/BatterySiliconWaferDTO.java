package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.time.LocalDate;

/**
 * 电池类型动态属性-硅片
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池类型动态属性-硅片DTO对象", description = "DTO对象")
public class BatterySiliconWaferDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 基地 页面展示使用
     */
    @ApiModelProperty(value = "基地 页面展示使用")
    @Transient
    private String basePlaceName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间 页面展示使用
     */
    @ApiModelProperty(value = "车间 页面展示使用")
    @Transient
    private String workshopName;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元 页面展示使用
     */
    @ApiModelProperty(value = "生产单元 页面展示使用")
    @Transient
    private String workunitName;
    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "新硅片属性")
    private String siliconWaferProperties;
    /**
     * 硅片属性 页面展示使用
     */
    @ApiModelProperty(value = "新硅片属性 页面展示使用")
    @Transient
    private String siliconWaferPropertiesName;
    /**
     * 条件项
     */
    @ApiModelProperty(value = "新条件项")
    private String conditionItem;
    /**
     * 条件项 页面展示使用
     */
    @ApiModelProperty(value = "新条件项 页面展示使用")
    @Transient
    private String conditionItemName;
    /**
     * 值
     */
    @ApiModelProperty(value = "新值")
    private String batteryValue;
    /**
     * 值 页面展示使用
     */
    @ApiModelProperty(value = "新值 页面展示使用")
    @Transient
    private String batteryValueName;
    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDate effectiveStartDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDate effectiveEndDate;

    /**
     * 旧硅片属性
     */
    @ApiModelProperty(value = "旧硅片属性")
    private String oldSiliconWaferProperties;

    /**
     * 旧硅片属性
     */
    @ApiModelProperty(value = "旧硅片属性名称")
    private String oldSiliconWaferPropertiesName;

    /**
     * 旧条件项
     */
    @ApiModelProperty(value = "旧条件项")
    private String oldConditionItem;

    /**
     * 旧条件项
     */
    @ApiModelProperty(value = "旧条件项名称")
    private String oldConditionItemName;

    /**
     * 旧值
     */
    @ApiModelProperty(value = "旧值")
    private String oldBatteryValue;

    /**
     * 旧值
     */
    @ApiModelProperty(value = "旧值名称")
    private String oldBatteryValueName;

    /**
     * 新线体数量
     */
    @ApiModelProperty(value = "新线体数量")
    private Integer lineQty;

    /**
     * 新硅片料号
     */
    @ApiModelProperty(value = "新硅片料号")
    private String itemCodeNew;

    /**
     * 是否低碳
     */
    @ApiModelProperty(value = "是否低碳")
    private String lowCarbonFlag;

    @ApiModelProperty(value = "是否低碳名称")
    private String lowCarbonFlagName;

    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    private String waferThickness;

    /**
     * 硅片品类
     */
    @ApiModelProperty(value = "硅片品类")
    private String waferCategory;

    @ApiModelProperty(value = "硅片品类名称")
    private String waferCategoryName;

    @ApiModelProperty(value = "硅片ECS CODE")
    private String waferEcsCode;
}
