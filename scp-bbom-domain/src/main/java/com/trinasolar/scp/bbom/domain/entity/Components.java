package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BOM行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Entity
@ToString
@Data
@Table(name = "bbom_components")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_components SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_components SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class Components extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * bom构件id，自增序列
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "bom构件id，自增序列")
    @Column(name = "id")
    private Long id;

    /**
     * bom id，从结构上取
     */
    @ApiModelProperty(value = "bom id，从结构上取")
    @Column(name = "bom_id")
    private Long bomId;

    /**
     * 操作序列号
     */
    @ApiModelProperty(value = "操作序列号")
    @Column(name = "operation_seq_num")
    private Integer operationSeqNum;

    /**
     * 构件物料id
     */
    @ApiModelProperty(value = "构件物料id")
    @Column(name = "component_item_id")
    private Long componentItemId;

    /**
     * BOM结构
     */
    @ApiModelProperty(value = "BOM结构")
    @Column(name = "bom_structure")
    private String bomStructure;

    /**
     * 替代项标志
     */
    @ApiModelProperty(value = "替代项标志")
    @Column(name = "substitute_flag")
    private String substituteFlag;

    /**
     * 浆料替代项标志
     */
    @ApiModelProperty(value = "浆料替代项标志")
    @Column(name = "slurry_substitute_flag")
    private String slurrySubstituteFlag;


    /**
     * 网版替代项标志
     */
    @ApiModelProperty(value = "网版替代项标志")
    @Column(name = "screen_substitute_flag")
    private String screenSubstituteFlag;


    @ApiModelProperty(value = "网版替代项是否启用")
    @Column(name = "screen_substitute_enable_flag")
    public String screenSubstituteEnableFlag;

    @ApiModelProperty(value = "浆料替代项是否启用")
    @Column(name = "slurry_substitute_enable_flag")
    public String slurrySubstituteEnableFlag;

    /**
     * 最后更新日期
     */
    @ApiModelProperty(value = "最后更新日期")
    @Column(name = "last_update_date")
    private LocalDateTime lastUpdateDate;

    /**
     * 项目序列号
     */
    @ApiModelProperty(value = "项目序列号")
    @Column(name = "item_num")
    private Integer itemNum;

    /**
     * 构件数量
     */
    @ApiModelProperty(value = "构件数量")
    @Column(name = "component_quantity")
    private String componentQuantity;

    /**
     * 产出因子
     */
    @ApiModelProperty(value = "产出因子")
    @Column(name = "component_yield_factor")
    private Integer componentYieldFactor;

    /**
     * 构件备注
     */
    @ApiModelProperty(value = "构件备注")
    @Column(name = "component_remarks")
    private String componentRemarks;

    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    @Column(name = "effectivity_date")
    private LocalDateTime effectivityDate;

    /**
     * 变更备注
     */
    @ApiModelProperty(value = "变更备注")
    @Column(name = "change_notice")
    private String changeNotice;

    /**
     * 实施日期
     */
    @ApiModelProperty(value = "实施日期")
    @Column(name = "implementation_date")
    private LocalDateTime implementationDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    @Column(name = "disable_date")
    private LocalDateTime disableDate;

    /**
     * 属性类别
     */
    @ApiModelProperty(value = "属性类别")
    @Column(name = "attribute_category")
    private String attributeCategory;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute1")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute2")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute3")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute4")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute5")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute6")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute7")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute8")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute9")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute10")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute11")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute12")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute13")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute14")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute15")
    private String attribute15;

    /**
     * 计划百分比
     */
    @ApiModelProperty(value = "计划百分比")
    @Column(name = "planning_factor")
    private Integer planningFactor;

    /**
     * 相关数量
     */
    @ApiModelProperty(value = "相关数量")
    @Column(name = "quantity_related")
    private Integer quantityRelated;

    /**
     * ACD类型
     */
    @ApiModelProperty(value = "ACD类型")
    @Column(name = "acd_type")
    private Integer acdType;

    /**
     * 构件序号
     */
    @ApiModelProperty(value = "构件序号")
    @Column(name = "component_sequence_id")
    private Long componentSequenceId;

    /**
     * 替代项Id
     */
    @ApiModelProperty(value = "替代项Id")
    @Column(name = "substitute_component_id")
    private Long substituteComponentId;

    /**
     * 清单序号（关键字）
     */
    @ApiModelProperty(value = "清单序号（关键字）")
    @Column(name = "bill_sequence_id")
    private Long billSequenceId;

    /**
     * 车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟
     */
    @ApiModelProperty(value = "车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟")
    @Column(name = "wip_supply_type")
    private Integer wipSupplyType;

    /**
     * 供应子库
     */
    @ApiModelProperty(value = "供应子库")
    @Column(name = "supply_subinventory")
    private String supplySubinventory;

    /**
     * 供应库位
     */
    @ApiModelProperty(value = "供应库位")
    @Column(name = "supply_locator_id")
    private Long supplyLocatorId;

    /**
     * 清单项目类型1.模型.选项类.计划中.标准
     */
    @ApiModelProperty(value = "清单项目类型1.模型.选项类.计划中.标准")
    @Column(name = "bom_item_type")
    private Integer bomItemType;

    /**
     * 基础类型
     */
    @ApiModelProperty(value = "基础类型")
    @Column(name = "basis_type")
    private Integer basisType;


}
