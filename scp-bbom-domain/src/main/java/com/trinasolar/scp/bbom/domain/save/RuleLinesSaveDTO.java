package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * BOM规则行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 09:15:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleLines保存参数", description = "保存参数")
public class RuleLinesSaveDTO extends TokenDTO implements Serializable {

    /**
     * 规则头ID
     */
    @ApiModelProperty(value = "规则头ID ")
    private Long ruleHeaderId;


    @ApiModelProperty(value = "本次保存的行列表")
    private List<RuleLineSaveDTO> lines;
}
