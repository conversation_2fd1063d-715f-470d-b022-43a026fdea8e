package com.trinasolar.scp.bbom.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SlurryMainReplacementVO {
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String mainItemCode;

    /**
     * 电池物料描述
     */
    @ApiModelProperty(value = "电池物料描述")
    private String mainItemDesc;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    private String workshop;

    @ApiModelProperty(value = "正背面")
    private String frontOrBack;

    @ApiModelProperty(value = "工位")
    private String opPosition;

    /**
     * 浆料料号
     */
    @ApiModelProperty(value = "浆料料号")
    private String itemCode;

    /**
     * 浆料物料描述
     */
    @ApiModelProperty(value = "浆料物料描述")
    private String itemDesc;

    /**
     * 分组Id
     */
    @ApiModelProperty(value = "分组Id")
    private Long componentSequenceId;

    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    private String vendorAltName;

    /**
     * 是否主料
     */
    @ApiModelProperty(value = "是否主料")
    private String substituteFlag;

    /**
     * 替代料是否启用
     */
    @ApiModelProperty(value = "替代料是否启用")
    private String substituteEnableFlag;
}
