package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * pdm物料生命周期原始表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Entity
@ToString
@Data
@Table(name = "bbom_pdm_item_lifecycle")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_pdm_item_lifecycle SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_pdm_item_lifecycle SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class PdmItemLifecycle extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * 同步日期
     */
    @ApiModelProperty(value = "同步日期")
    @Column(name = "sync_date")
    private LocalDate syncDate;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * name
     */
    @ApiModelProperty(value = "name")
    @Column(name = "name")
    private String name;

    /**
     * 生命周期
     */
    @ApiModelProperty(value = "生命周期")
    @Column(name = "lifecycle")
    private String lifecycle;

    /**
     * stage
     */
    @ApiModelProperty(value = "stage")
    @Column(name = "stage")
    private String stage;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    @Column(name = "org_id")
    private Long orgId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;

    @ApiModelProperty(value = "临时量产标识")
    @Column(name = "is_temporary_output")
    private String isTemporaryOutput;
}
