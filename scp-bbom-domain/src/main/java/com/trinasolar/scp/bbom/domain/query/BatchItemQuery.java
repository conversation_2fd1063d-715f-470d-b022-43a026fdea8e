package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/3
 */
@Data
@ApiModel(value = "批量Item查询接口", description = "查询条件")
@Accessors(chain = true)
public class BatchItemQuery implements Serializable {
    private static final long serialVersionUID = 861092832046161557L;

    @ApiModelProperty(value = "批量查询接口")
    private List<ItemQuery> itemQueryList;
}
