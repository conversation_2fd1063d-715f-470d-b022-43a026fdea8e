/**
 * @Function: ComponentItemIdItemCodeDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niu<PERSON><PERSON>
 * @date: 2024年1月3日 14:54:28
 */
package com.trinasolar.scp.bbom.domain.dto;

import lombok.Data;

/**
 *
 * @Function: ComponentItemIdItemCodeDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 14:54:28
 */

/**
 *
 * @Function: ComponentItemIdItemCodeDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 14:54:28
 */
@Data
public class ComponentItemIdItemCodeDTO {

    private Long componentSequenceId;
    private String itemCode;
}
