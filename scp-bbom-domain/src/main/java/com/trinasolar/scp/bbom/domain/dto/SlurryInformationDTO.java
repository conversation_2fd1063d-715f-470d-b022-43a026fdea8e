package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Transient;
import java.time.LocalDateTime;


/**
 * 浆料车间单耗及线数维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "浆料车间单耗及线数维护DTO对象", description = "DTO对象")
public class SlurryInformationDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String planVersion;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 电池类型 页面展示名称取值
     */
    @Transient
    @ApiModelProperty(value = "电池类型 页面展示名称取值")
    private String batteryTypeName;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Transient
    private String basePlaceName;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Transient
    private String workshopName;
    /**
     * 线数
     */
    @ApiModelProperty(value = "线数")
    private String lineNumber;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workbench;
    /**
     * 机台 页面展示使用
     */
    @ApiModelProperty(value = "机台 页面展示使用")
    @Transient
    private String workbenchName;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    private String unitConsumption;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "更新人名称")
    private String updatedByName;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效 Y 有效")
    private String valid;

}
