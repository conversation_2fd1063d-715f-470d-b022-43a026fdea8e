package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BBOM结构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Entity
@ToString
@Data
@Table(name = "bbom_structures")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_structures SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_structures SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class Structures extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * bom id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "bom id")
    @Column(name = "id")
    private Long id;

    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料ID")
    @Column(name = "assembly_item_id")
    private Long assemblyItemId;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * alternateBomDesignator
     */
    @ApiModelProperty(value = "alternateBomDesignator")
    @Column(name = "alternate_bom_designator")
    private String alternateBomDesignator;

    /**
     * ERP最后更新日期
     */
    @ApiModelProperty(value = "ERP最后更新日期")
    @Column(name = "last_update_date")
    private LocalDateTime lastUpdateDate;

    /**
     * 公共项目内码
     */
    @ApiModelProperty(value = "公共项目内码")
    @Column(name = "common_assembly_item_id")
    private Long commonAssemblyItemId;

    /**
     * specificAssemblyComment
     */
    @ApiModelProperty(value = "specificAssemblyComment")
    @Column(name = "specific_assembly_comment")
    private String specificAssemblyComment;

    /**
     * pendingFromEcn
     */
    @ApiModelProperty(value = "pendingFromEcn")
    @Column(name = "pending_from_ecn")
    private String pendingFromEcn;

    /**
     * 属性类别
     */
    @ApiModelProperty(value = "属性类别")
    @Column(name = "attribute_category")
    private String attributeCategory;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute1")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute2")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute3")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute4")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute5")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute6")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute7")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute8")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute9")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute10")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute11")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute12")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute13")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute14")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute15")
    private String attribute15;

    /**
     * 装配类别
     */
    @ApiModelProperty(value = "装配类别")
    @Column(name = "assembly_type")
    private Long assemblyType;

    /**
     * 公共序号
     */
    @ApiModelProperty(value = "公共序号")
    @Column(name = "common_bill_sequence_id")
    private Long commonBillSequenceId;

    /**
     * 清单序号（关键字）
     */
    @ApiModelProperty(value = "清单序号（关键字）")
    @Column(name = "bill_sequence_id")
    private Long billSequenceId;

    /**
     * 请求id
     */
    @ApiModelProperty(value = "请求id")
    @Column(name = "request_id")
    private Long requestId;

    /**
     * programApplicationId
     */
    @ApiModelProperty(value = "programApplicationId")
    @Column(name = "program_application_id")
    private Long programApplicationId;

    /**
     * programId
     */
    @ApiModelProperty(value = "programId")
    @Column(name = "program_id")
    private Long programId;

    /**
     * programUpdateDate
     */
    @ApiModelProperty(value = "programUpdateDate")
    @Column(name = "program_update_date")
    private LocalDateTime programUpdateDate;

    /**
     * 公共组织
     */
    @ApiModelProperty(value = "公共组织")
    @Column(name = "common_organization_id")
    private Long commonOrganizationId;

    /**
     * nextExplodeDate
     */
    @ApiModelProperty(value = "nextExplodeDate")
    @Column(name = "next_explode_date")
    private LocalDateTime nextExplodeDate;

    /**
     * projectId
     */
    @ApiModelProperty(value = "projectId")
    @Column(name = "project_id")
    private Long projectId;

    /**
     * taskId
     */
    @ApiModelProperty(value = "taskId")
    @Column(name = "task_id")
    private Long taskId;

    /**
     * originalSystemReference
     */
    @ApiModelProperty(value = "originalSystemReference")
    @Column(name = "original_system_reference")
    private String originalSystemReference;

    /**
     * structureTypeId
     */
    @ApiModelProperty(value = "structureTypeId")
    @Column(name = "structure_type_id")
    private Long structureTypeId;

    /**
     * implementationDate
     */
    @ApiModelProperty(value = "implementationDate")
    @Column(name = "implementation_date")
    private LocalDateTime implementationDate;

    /**
     * objName
     */
    @ApiModelProperty(value = "objName")
    @Column(name = "obj_name")
    private String objName;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    @Column(name = "pk1_value")
    private String pk1Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    @Column(name = "pk2_value")
    private String pk2Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    @Column(name = "pk3_value")
    private String pk3Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    @Column(name = "pk4_value")
    private String pk4Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    @Column(name = "pk5_value")
    private String pk5Value;

    /**
     * effectivityControl
     */
    @ApiModelProperty(value = "effectivityControl")
    @Column(name = "effectivity_control")
    private Integer effectivityControl;

    /**
     * isPreferred
     */
    @ApiModelProperty(value = "isPreferred")
    @Column(name = "is_preferred")
    private String isPreferred;

    /**
     * sourceBillSequenceId
     */
    @ApiModelProperty(value = "sourceBillSequenceId")
    @Column(name = "source_bill_sequence_id")
    private Long sourceBillSequenceId;


}
