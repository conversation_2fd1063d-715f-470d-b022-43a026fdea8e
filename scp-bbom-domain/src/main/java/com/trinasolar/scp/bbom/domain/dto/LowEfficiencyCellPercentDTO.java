package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.bbom.domain.validation.group.AddGroup;
import com.trinasolar.scp.bbom.domain.validation.group.DefaultGroup;
import com.trinasolar.scp.bbom.domain.validation.group.UpdateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Pattern;

@Data
@ApiModel(value = "LowEfficiencyCellPercentDTO对象", description = "DTO对象")
public class LowEfficiencyCellPercentDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(groups = UpdateGroup.class, message = "id不能为空")
    @Null(groups = AddGroup.class, message = "id必须为空")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外lovValue")
    @NotBlank(groups = UpdateGroup.class, message = "国内/海外列未填写")
    private String countryFlag;


    @ApiModelProperty(value = "国内/海外lovName")
    @NotBlank(groups = AddGroup.class, message = "国内/海外列未填写")
    private String countryFlagName;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @NotBlank(groups = DefaultGroup.class, message = "年份列未填写")
    @Pattern(groups = DefaultGroup.class, regexp = "^\\d{4}$", message = "年份只能为四位整数")
    private String year;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间lovValue")
    @NotBlank(groups = UpdateGroup.class, message = "车间列未填写")
    private String workshop;

    @ApiModelProperty(value = "车间lovName")
    @NotBlank(groups = AddGroup.class, message = "车间列未填写")
    private String workshopName;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地lovValue")
    @NotBlank(groups = UpdateGroup.class, message = "生产基地列未填写")
    private String basePlace;

    @ApiModelProperty(value = "生产基地lovName")
    @NotBlank(groups = AddGroup.class, message = "生产基地列未填写")
    private String basePlaceName;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型lovValue")
    @NotBlank(groups = UpdateGroup.class, message = "电池类型列未填写")
    private String cellType;

    @ApiModelProperty(value = "电池类型lovName")
    @NotBlank(groups = AddGroup.class, message = "电池类型列未填写")
    private String cellTypeName;

    /**
     * 1月占比
     */
    @ApiModelProperty(value = "1月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "1月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m1Percent;

    /**
     * 2月占比
     */
    @ApiModelProperty(value = "2月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "2月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m2Percent;

    /**
     * 3月占比
     */
    @ApiModelProperty(value = "3月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "3月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m3Percent;

    /**
     * 4月占比
     */
    @ApiModelProperty(value = "4月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "4月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m4Percent;

    /**
     * 5月占比
     */
    @ApiModelProperty(value = "5月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "5月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m5Percent;

    /**
     * 6月占比
     */
    @ApiModelProperty(value = "6月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "6月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m6Percent;

    /**
     * 7月占比
     */
    @ApiModelProperty(value = "7月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "7月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m7Percent;

    /**
     * 8月占比
     */
    @ApiModelProperty(value = "8月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "8月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m8Percent;

    /**
     * 9月占比
     */
    @ApiModelProperty(value = "9月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "9月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m9Percent;

    /**
     * 10月占比
     */
    @ApiModelProperty(value = "10月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "10月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m10Percent;

    /**
     * 11月占比
     */
    @ApiModelProperty(value = "11月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "11月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m11Percent;

    /**
     * 12月占比
     */
    @ApiModelProperty(value = "12月占比")
    @Pattern(groups = DefaultGroup.class, regexp = "^([1-9]?\\d(\\.\\d{1,2})?|100)%$", message = "12月占比必须%结尾，数字部分大于等于0小于等于100且最多两位小数")
    private String m12Percent;


}
