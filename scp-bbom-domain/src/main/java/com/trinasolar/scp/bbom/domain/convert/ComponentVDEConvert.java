package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ComponentVDTO;
import com.trinasolar.scp.bbom.domain.entity.ComponentV;
import com.trinasolar.scp.bbom.domain.excel.ComponentVExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 同步cux3_bbom_component_v DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentVDEConvert extends BaseDEConvert<ComponentVDTO, ComponentV> {

    ComponentVDEConvert INSTANCE = Mappers.getMapper(ComponentVDEConvert.class);

    List<ComponentVExcelDTO> toExcelDTO(List<ComponentVDTO> dtos);

    ComponentVExcelDTO toExcelDTO(ComponentVDTO dto);
}
