package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * pdm物料生命周期原始表  组织ID + 物料编码 从中间表bbom_pdm_item_lifecycle获取
 */
@Data
@ApiModel(value = "PdmItemLifecycleSyncQuery查询条件", description = "查询条件")
@Accessors(chain = true)
@Builder
public class PdmItemLifecycleSyncDTO implements Serializable {

    @ApiModelProperty(value = "料号")
    private String itemCode;

    @ApiModelProperty(value = "组织id")
    private Long orgId;

    @ApiModelProperty(value = "生命周期状态")
    private String lifecycleState;

    @ApiModelProperty(value = "临时量产标识")
    private String isTemporaryOutput;
}
