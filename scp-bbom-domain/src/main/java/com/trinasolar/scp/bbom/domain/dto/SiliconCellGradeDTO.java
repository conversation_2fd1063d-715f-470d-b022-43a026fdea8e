package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Transient;
import java.time.LocalDateTime;


/**
 * 硅片等级与电池等级映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "硅片等级与电池等级映射DTO对象", description = "DTO对象")
public class SiliconCellGradeDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferProperties;
    /**
     * 硅片属性 页面展示使用
     */
    @Transient
    @ApiModelProperty(value = "硅片属性 页面展示使用")
    private String siliconWaferPropertiesName;
    /**
     * 硅片属性条件项
     */
    @ApiModelProperty(value = "硅片属性条件项")
    private String siliconWaferConditionalItem;
    /**
     * 硅片属性值
     */
    @ApiModelProperty(value = "硅片属性值")
    private String siliconWaferValue;
    /**
     * 电池属性
     */
    @ApiModelProperty(value = "电池属性")
    private String batteryProperties;
    /**
     * 电池属性 页面展示使用
     */
    @Transient
    @ApiModelProperty(value = "电池属性 页面展示使用")
    private String batteryPropertiesName;
    /**
     * 电池属性条件项
     */
    @ApiModelProperty(value = "电池属性条件项")
    private String batteryConditionalItem;
    /**
     * 电池属性值
     */
    @ApiModelProperty(value = "电池属性值")
    private String batteryValue;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
}
