package com.trinasolar.scp.bbom.domain.utils;

public final class LovHeaderCodeConstant {
    //邮件推送LOV
    public static final String SEND_MAIL = "BBOM_EMAIL_REMINDER";
    //邮件推送LOV 网版寿命
    public static final String SCREEN_LIFE_MAIL = "网版寿命";

    public static final String MATERIEL_MATCH_CONFIRM = "料号匹配确认";
    //邮件推送LOV 网版切换
    public static final String SCREEN_PLATE_MAIL = "网版切换";
    //邮件推送LOV 浆料切换
    public static final String SLURRY_MAIL = "浆料切换";
    //邮件推送LOV 料号匹配
    public static final String MATCH_HEADER_MAIL = "料号匹配";
    //电池类型
    public static final String BATTERY_TYPE = "BATTERY_TYPE";
    /**
     * APS电池类型
     */
    public static final String APS_BATTERY_TYPE = "aps_power_cell_type";
    //国内国外
    public static final String DOMESTIC_OVERSEA = "Domestic/Oversea";
    //生产基地
    public static final String BASE_PLACE = "base_place";
    //生产车间
    public static final String WORK_SHOP = "work_shop";
    //生成单元
    public static final String WORK_UNIT = "work_unit";
    //车间类型
    public static final String WORKSHOP_TYPE = "WORKSHOP_TYPE";
    //库存组织代码对照关系
    public static final String INVENTORY_ORGANIZATION = "inventory_organization";
    //生成单元
    public static final String SHIPMENT_LEADTIME_TYPE = "SHIPMENT_LEADTIME_TYPE";
    //电池片_硅料厂商
    public static final String SI_MFRS = "5A00100100121";
    //硅片等级
    public static final String WAFER_GRADE = "5A00100100122";
    //电池片_加工类别
    public static final String CELL_PROCESS_CATEGORY = "5A00100100111";
    //硅片_加工方式
    public static final String SILICON_PROCESS_CATEGORY = "4A00100100105";
    //研发/量产
    public static final String ENG_MFG = "ENG/MFG";
    //网版切换类型
    public static final String NETWORK_VERSION_SWITCHING_TYPE = "NETWORK_VERSION_SWITCHING_TYPE";
    //浆料切换维护类型
    public static final String LEAD_TYPE = "LEAD_TYPE";
    public static final String YF = "研发";
    public static final String LC = "量产";
    //电池片料号
    public static final String DCPLH = "电池片料号";
    //监造限制
    public static final String JZXZ = "监造限制";
    //电池车间
    public static final String DCCJ = "电池车间";
    //组件车间
    public static final String ZJCJ = "组件车间";
    //特殊单限制
    public static final String TSDXZ = "特殊单限制";
    //单双玻限制
    public static final String DSBXZ = "单双玻限制";
    //机台限制
    public static final String JTXZ = "机台限制";
    //测试限制
    public static final String CSXZ = "测试限制";
    //包装限制
    public static final String BZXZ = "包装限制";
    //认证限制
    public static final String RZXZ = "认证限制";
    //材料限制
    public static final String CLXZ = "材料限制";

    //正电极网版
    public static final String ZDJWB = "正电极网版";
    //背电极网版
    public static final String FDJWB = "背电极网版";

    //P型
    public static final String P_MODEL = "P型";
    //N型
    public static final String N_MODEL = "N型";
    //主栅
    public static final String ZS = "主栅";
    //细栅
    public static final String XS = "细栅";
    public static final String none = "无";

    public static final String item_segment18 = "5膜优化";

    public static final String ITEM_CATEGORY = "ITEM_CATEGORY";

    public final static String AOP_CELL_SERIES = "aop_cell_series";
    
    public final static String AOP_COUNTRY_FLAG = "AOP_COUNTRY_FLAG";

    /**
     * 物料小区域指定
     */
    public final static String BDM_MATERIAL_AREA = "BDM_MATERIAL_AREA";

    public final static String CELL_MACHINE = "CELL_MACHINE";

    public final static String ACTUAL_PIECE_THICKNESS = "5A00100100118";

    public final static String MAIN_GRID_BOTH_SHAPE = "5A00100100108";

    public final static String AOP_COUNTRY = "country";

    /**
     * 电池片_H追溯
     */
    public static  final String H_TRACE ="5A00100100135";
    /**
     * 电池片_美学
     */
    public static  final String AESTHETICS ="5A00100100124";
    /**
     * 透明双玻
     */
    public static  final String TRANSPARENT_DOUBLE_GLASS="5A00100100123";
    /**
     * 片源种类
     */
    public static final String PCS_SOURCE_TYPE = "6A00100100124";
    /**
     * 产品等级
     */
    public static final String PRODUCT_GRADE = "5A00100100103";
    /**
     * 语言中文
     */
    public  static final String LANGUAGE_CN="zh_CN";
    public static final String LANGUAGE_EN="en_US";
}
