package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 电池类型动态属性-浆料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatterySlurryExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 切换类型
     */
    @ExcelIgnore
    private String switchingType;
    /**
     * 切换类型名称
     */
    @ExcelIgnore
    private String switchingTypeName;
    /**
     * 维护类型
     */
    @ExcelIgnore
    private String leadType;
    /**
     * 维护类型名称
     */
    @ExcelProperty(value = "维护类型")
    private String leadTypeName;
    /**
     * 电池类型编码
     */
    @ExcelIgnore
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ExcelIgnore
    private String singleGlassFlag;

    @ExcelProperty(value = "单玻")
    private String singleGlassFlagName;

    @ExcelIgnore
    private String workBench;

    @ExcelProperty(value = "机台")
    private String workBenchName;
    /**
     * 基地
     */
    @ExcelIgnore
    private String basePlace;
    /**
     * 基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 生产单元
     */
    @ExcelIgnore
    private String workunit;
    /**
     * 生产单元
     */
    @ExcelIgnore
    private String workunitName;

//    /**
//     * 物料料号-旧
//     */
//    @ExcelProperty(value = "物料料号-旧")
//    private String itemCodeOld;
//    /**
//     * 物料-旧说明
//     */
//    @ExcelProperty(value = "物料-旧说明")
//    private String itemDescOld;
//
//    @ExcelProperty(value = "物料状态")
//    private String lifecycleStateOld;

    /**
     * 物料料号-新
     */
    @ExcelProperty(value = "物料料号-新")
    private String itemCodeNew;

    /**
     * 物料-新说明
     */
    @ExcelProperty(value = "物料-新说明")
    private String itemDescNew;

    @ExcelProperty(value = "物料状态")
    private String lifecycleStateNew;

//    @ExcelProperty(value = "数量")
//    private BigDecimal quantity;

//    /**
//     * 线体-旧
//     */
//    @ExcelProperty(value = "线体数量-旧")
//    private String lineOld;
    /**
     * 线体-新
     */
    @ExcelProperty(value = "线体数量-新")
    private String lineNew;

    @ExcelProperty(value = "物料编码-新是否存在BOM中")
    private String isExistBom;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String vendorName;

    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效期起
     */
    @ExcelProperty(value = "有效期起")
    private String effectiveStartDates;
    /**
     * 有效期止
     */
    @ExcelProperty(value = "有效期止")
    private String effectiveEndDates;

    /**
     * 供应商id
     */
    @ExcelIgnore
    private Long vendorId;

}
