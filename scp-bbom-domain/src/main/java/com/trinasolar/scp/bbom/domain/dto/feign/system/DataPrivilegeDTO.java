package com.trinasolar.scp.bbom.domain.dto.feign.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * 数据权限表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-17 15:39:12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DataPrivilegeDTO对象", description = "DTO对象")
public class DataPrivilegeDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 权限类型，一般大写为表名
     */
    @ApiModelProperty(value = "权限类型，一般大写为表名")
    private String privilegeType;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID")
    private Long dataId;
    /**
     * 数据编码
     */
    @ApiModelProperty(value = "数据编码")
    private String dataCode;
}
