package com.trinasolar.scp.bbom.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@AllArgsConstructor
@Getter
public enum TimeSlotEnum {
    /**
     * 时间段: present 当前, presentAndFuture 现在和未来
     */
    PRESENT("present", "当前"),
    PRESENT_AND_FUTURE("presentAndFuture", "现在和未来");
    private String code;
    private String name;

    public static TimeSlotEnum getByValue(String value) {
        for (TimeSlotEnum anEnum : TimeSlotEnum.values()) {
            if (Objects.equals(anEnum.getCode(), value)) {
                return anEnum;
            }
        }
        return null;
    }
}
