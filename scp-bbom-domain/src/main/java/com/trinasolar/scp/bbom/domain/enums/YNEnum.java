package com.trinasolar.scp.bbom.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/6/10
 */
@Getter
@AllArgsConstructor
public enum YNEnum {
    /*
     * Y
     */
    Y("Y"),
    /*
     * N
     */
    N("N");

    private String value;

    public static YNEnum getByValue(String value) {
        for (YNEnum anEnum : YNEnum.values()) {
            if (Objects.equals(anEnum.getValue(), value)) {
                return anEnum;
            }
        }
        return null;
    }
}
