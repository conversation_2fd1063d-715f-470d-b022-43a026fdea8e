package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.bbom.domain.vo.StructureItemVO;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * BBOM结构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BBOM结构DTO对象", description = "DTO对象")
public class StructuresDTO extends BaseDTO {

    private static final long serialVersionUID = -1582363620822862999L;

    /**
     * bom id
     */
    @ApiModelProperty(value = "bom id")
    private Long id;

    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料ID")
    private Long assemblyItemId;

    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料Code")
    private String assemblyItemCode;

    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料描述")
    private String assemblyItemDesc;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    private Long organizationId;

    /**
     * alternateBomDesignator
     */
    @ApiModelProperty(value = "alternateBomDesignator")
    private String alternateBomDesignator;

    /**
     * ERP最后更新日期
     */
    @ApiModelProperty(value = "ERP最后更新日期")
    private LocalDateTime lastUpdateDate;

    /**
     * 公共项目内码
     */
    @ApiModelProperty(value = "公共项目内码")
    private Long commonAssemblyItemId;

    /**
     * specificAssemblyComment
     */
    @ApiModelProperty(value = "specificAssemblyComment")
    private String specificAssemblyComment;

    /**
     * pendingFromEcn
     */
    @ApiModelProperty(value = "pendingFromEcn")
    private String pendingFromEcn;

    /**
     * 属性类别
     */
    @ApiModelProperty(value = "属性类别")
    private String attributeCategory;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute15;

    /**
     * 装配类别
     */
    @ApiModelProperty(value = "装配类别")
    private Long assemblyType;

    /**
     * 公共序号
     */
    @ApiModelProperty(value = "公共序号")
    private Long commonBillSequenceId;

    /**
     * 清单序号（关键字）
     */
    @ApiModelProperty(value = "清单序号（关键字）")
    private Long billSequenceId;

    /**
     * 请求id
     */
    @ApiModelProperty(value = "请求id")
    private Long requestId;

    /**
     * programApplicationId
     */
    @ApiModelProperty(value = "programApplicationId")
    private Long programApplicationId;

    /**
     * programId
     */
    @ApiModelProperty(value = "programId")
    private Long programId;

    /**
     * programUpdateDate
     */
    @ApiModelProperty(value = "programUpdateDate")
    private LocalDateTime programUpdateDate;

    /**
     * 公共组织
     */
    @ApiModelProperty(value = "公共组织")
    private Long commonOrganizationId;

    /**
     * nextExplodeDate
     */
    @ApiModelProperty(value = "nextExplodeDate")
    private LocalDateTime nextExplodeDate;

    /**
     * projectId
     */
    @ApiModelProperty(value = "projectId")
    private Long projectId;

    /**
     * taskId
     */
    @ApiModelProperty(value = "taskId")
    private Long taskId;

    /**
     * originalSystemReference
     */
    @ApiModelProperty(value = "originalSystemReference")
    private String originalSystemReference;

    /**
     * structureTypeId
     */
    @ApiModelProperty(value = "structureTypeId")
    private Long structureTypeId;

    /**
     * implementationDate
     */
    @ApiModelProperty(value = "implementationDate")
    private LocalDateTime implementationDate;

    /**
     * objName
     */
    @ApiModelProperty(value = "objName")
    private String objName;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    private String pk1Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    private String pk2Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    private String pk3Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    private String pk4Value;

    /**
     * pkValue
     */
    @ApiModelProperty(value = "pkValue")
    private String pk5Value;

    /**
     * effectivityControl
     */
    @ApiModelProperty(value = "effectivityControl")
    private Integer effectivityControl;

    /**
     * isPreferred
     */
    @ApiModelProperty(value = "isPreferred")
    private String isPreferred;

    /**
     * sourceBillSequenceId
     */
    @ApiModelProperty(value = "sourceBillSequenceId")
    private Long sourceBillSequenceId;


    @ApiModelProperty(value = "子料列表")
    private List<ComponentsDTO> components;

    @ApiModelProperty(value = "物料code子料列表")
    private Map<String, List<ComponentsDTO>> itemCodeAndComponents;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment3;


    @ApiModelProperty(value = "物料信息")
    private StructureItemVO item;
}
