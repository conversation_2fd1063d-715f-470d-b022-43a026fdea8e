package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 浆料车间单耗及线数维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@ApiModel(value = "SlurryInformation查询条件", description = "查询条件")
@Accessors(chain = true)
public class SlurryInformationQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "版本号")
    private String planVersion;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 线数
     */
    @ApiModelProperty(value = "线数")
    private String lineNumber;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workbench;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    private String unitConsumption;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效 Y 有效")
    private String valid;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
