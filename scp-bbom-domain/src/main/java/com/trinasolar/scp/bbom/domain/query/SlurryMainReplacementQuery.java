package com.trinasolar.scp.bbom.domain.query;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 浆料主替信息维护查询
 *
 */
@Data
@ApiModel(value = "浆料主替信息维护查询", description = "查询条件")
@Accessors(chain = true)
public class SlurryMainReplacementQuery extends PageDTO implements Serializable {
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String mainItemCode;


    /**
     * BOM替代项
     */
    @ExcelProperty(value = "BOM替代项")
    private String alternateBomDesignator;


    /**
     * 浆料料号
     */
    @ApiModelProperty(value = "浆料料号")
    private String itemCode;
}
