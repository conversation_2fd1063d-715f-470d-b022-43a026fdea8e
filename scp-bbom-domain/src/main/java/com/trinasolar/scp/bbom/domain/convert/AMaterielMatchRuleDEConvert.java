package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.AMaterielMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.entity.AMaterielMatchRule;
import com.trinasolar.scp.bbom.domain.excel.AMaterielMatchRuleExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 制造工艺切换计划维护 DTO与实体转换器
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AMaterielMatchRuleDEConvert extends BaseDEConvert<AMaterielMatchRuleDTO, AMaterielMatchRule> {

    AMaterielMatchRuleDEConvert INSTANCE = Mappers.getMapper(AMaterielMatchRuleDEConvert.class);

    List<AMaterielMatchRuleExcelDTO> toExcelDTO(List<AMaterielMatchRuleExcelDTO> dtos);

    AMaterielMatchRuleExcelDTO toExcelDTO(AMaterielMatchRuleDTO dto);
    @Mappings(
            {
                    @Mapping(target = "isOverseaName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.DOMESTIC_OVERSEA, aMaterielMatchRule.getIsOversea()))"),
                    @Mapping(target = "cellsTypeName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.APS_BATTERY_TYPE, aMaterielMatchRule.getCellsType()))"),
                    @Mapping(target = "actualPieceThicknessName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.ACTUAL_PIECE_THICKNESS, aMaterielMatchRule.getActualPieceThickness()))"),
                    @Mapping(target = "mainGridBothShapeName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.MAIN_GRID_BOTH_SHAPE, aMaterielMatchRule.getMainGridBothShape()))")
            }
    )
    AMaterielMatchRuleDTO toDto(AMaterielMatchRule aMaterielMatchRule);

    AMaterielMatchRule excelDtoToSaveDto(AMaterielMatchRuleExcelDTO excelDTO);

    List<AMaterielMatchRule> excelDtoToSaveDto(List<AMaterielMatchRuleExcelDTO> dto);
}
