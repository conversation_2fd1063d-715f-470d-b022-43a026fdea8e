package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * BOM规则行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 09:15:32
 */
@Data
@ApiModel(value = "RuleLine查询条件", description = "查询条件")
public class RuleLineQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "料号")
    private String itemCode;
}
