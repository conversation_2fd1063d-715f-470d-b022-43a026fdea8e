package com.trinasolar.scp.bbom.domain.dto.feign.bmrp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TjOnHandDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long tjOnHandLineId;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String organizationCode;

    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    private String subInventoryCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemDesc;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String uom;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 库存日期
     */
    @ApiModelProperty(value = "库存日期")
    private LocalDateTime inventoryDate;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
