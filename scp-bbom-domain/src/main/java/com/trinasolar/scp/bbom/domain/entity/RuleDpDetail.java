package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * BOM规则DP因子
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:08
 */
@Entity
@ToString
@Data
@Table(name = "bbom_rule_dp_detail")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_rule_dp_detail SET is_deleted = 1 WHERE rule_detail_id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_rule_dp_detail SET is_deleted = 1 WHERE rule_detail_id = ?")
public class RuleDpDetail extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 规则行ID
     */
    @ApiModelProperty(value = "规则行ID")
    private Long ruleLineId;

    /**
     * 规则明细ID，序列号生成
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "规则明细ID，序列号生成")
    private Long ruleDetailId;

    @ApiModelProperty(value = "DP字段Id,AttrLineId")
    private Long dpFiledId;

    /**
     * DP字段
     */
    @ApiModelProperty(value = "DP字段名称")
    private String dpFiledName;

    /**
     * 运算符 1：包含  2：排除  3：等于
     */
    @ApiModelProperty(value = "运算符 1：包含  2：排除  3：等于")
    private String attrOperator;


}
