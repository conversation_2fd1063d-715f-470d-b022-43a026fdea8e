package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 网版寿命信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ScreenLifeExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型
     */
    @ExcelIgnore
    private String batteryType;
    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String batteryTypeName;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ExcelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ExcelIgnore
    private String singleGlassFlag;

    @ExcelProperty(value = "单玻")
    private String singleGlassFlagName;

    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlaceName;

    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 网版料号
     */
    @ExcelProperty(value = "网版料号")
    private String itemCode;
    /**
     * 料号说明
     */
    @ExcelProperty(value = "料号说明")
    private String itemDesc;
    /**
     * 物料状态
     */
    @ExcelProperty(value = "物料状态")
    private String materialStatus;
    /**
     * 生产基地
     */
    @ExcelIgnore
    private String basePlace;
    /**
     * 生产车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 机台
     */
    @ExcelIgnore
    private String machine;
    /**
     * 机台
     */
    @ExcelProperty(value = "机台")
    private String machineName;
    /**
     * 寿命（万）
     */
    @ExcelProperty(value = "寿命（万）")
    private String lifetime;
    /**
     * 有效日期_起
     */
    /*@ExcelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;*/
    /**
     * 有效日期_止
     */
    /*@ExcelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;*/
    /**
     * BOM寿命
     */
    @ExcelProperty(value = "BOM寿命(万)")
    private Integer bomLife;

    @ExcelProperty("创建人")
    private String createdBy;
    @ExcelProperty("创建时间")
    private LocalDateTime createdTime;

}
