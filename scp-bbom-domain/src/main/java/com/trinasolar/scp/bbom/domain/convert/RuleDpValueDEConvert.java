package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleDpValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpValue;
import com.trinasolar.scp.bbom.domain.excel.RuleDpValueExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * BOM规则DP因子明细值 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleDpValueDEConvert extends BaseDEConvert<RuleDpValueDTO, RuleDpValue> {

    RuleDpValueDEConvert INSTANCE = Mappers.getMapper(RuleDpValueDEConvert.class);

    List<RuleDpValueExcelDTO> toExcelDTO(List<RuleDpValueDTO> dtos);

    RuleDpValueExcelDTO toExcelDTO(RuleDpValueDTO dto);
}
