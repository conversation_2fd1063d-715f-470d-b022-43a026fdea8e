package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ItemAttrLovDTO;
import com.trinasolar.scp.bbom.domain.entity.ItemAttrLov;
import com.trinasolar.scp.bbom.domain.excel.ItemAttrLovExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 物料属性字段Lov DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ItemAttrLovDEConvert extends BaseDEConvert<ItemAttrLovDTO, ItemAttrLov> {

    ItemAttrLovDEConvert INSTANCE = Mappers.getMapper(ItemAttrLovDEConvert.class);

    List<ItemAttrLovExcelDTO> toExcelDTO(List<ItemAttrLovDTO> dtos);

    ItemAttrLovExcelDTO toExcelDTO(ItemAttrLovDTO dto);
}
