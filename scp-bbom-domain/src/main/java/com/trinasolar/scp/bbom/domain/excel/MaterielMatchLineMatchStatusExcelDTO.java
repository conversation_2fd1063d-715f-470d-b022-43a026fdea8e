package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterielMatchLineMatchStatusExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 电池料号匹配明细行ID
     */
    @ExcelProperty(value = "电池料号匹配明细行ID")
    private Long lineId;

    /**
     * 物料Code
     */
    @ExcelProperty(value = "物料Code")
    private String itemCode;

    /**
     * 物料描述
     */
    @ExcelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 匹配状态
     */
    @ExcelProperty(value = "匹配状态")
    private String matchStatus;

    /**
     * BOM替代项
     */
    @ExcelProperty(value = "BOM替代项")
    private String alternateBomDesignator;

    /**
     * 研发/量产
     */
    @ExcelProperty(value = "研发/量产")
    private String isCatchProduction;
}
