package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * BBOM结构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Data
@ApiModel(value = "StructuresMrp查询条件", description = "查询条件")
@Accessors(chain = true)
public class StructuresMrpQuery extends TokenDTO implements Serializable {

    private static final long serialVersionUID = -5850399240280644256L;

    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料ID")
    private Long assemblyItemId;

    /**
     * alternateBomDesignator
     */
    @ApiModelProperty(value = "alternateBomDesignator")
    private String alternateBomDesignator;
}
