package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BatteryScreenPlateWorkshop保存参数", description = "保存参数")
public class BatteryScreenPlateWorkshopSaveDTO extends TokenDTO implements Serializable {

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;

    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;

    @ApiModelProperty(value = "单玻")
    private String singleGlassFlag;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "生产车间")
    private String workshop;

    @ApiModelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGrid;

    @ApiModelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGrid;

    @ApiModelProperty(value = "有效期起")
    private LocalDate effectiveStartDate;

    @ApiModelProperty(value = "有效期止")
    private LocalDate effectiveEndDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "备注")
    private String singleGlassFlagName;

    @ApiModelProperty(value = "提醒下标")
    private Integer index;

}
