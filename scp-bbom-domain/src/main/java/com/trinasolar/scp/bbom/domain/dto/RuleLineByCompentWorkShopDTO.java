package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 接口返回实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleLineByCompentWorkShopDTO对象", description = "DTO对象")
public class RuleLineByCompentWorkShopDTO extends BaseDTO {
    /**
     * 物料料号
     */
    @ApiModelProperty(value = "物料料号")
    private String itemCode;
    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    private List<String> compentWorkShopList;
    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    private List<String> batteryWorkShopList;

}
