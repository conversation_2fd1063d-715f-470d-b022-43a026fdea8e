package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 电池产品导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@ApiModel(value = "BatteryProductImport查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatteryProductImportQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 1646781489299623823L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 电池产品编码
     */
    @ApiModelProperty(value = "电池产品编码")
    private String batteryCode;

    /**
     * 电池产品名称
     */
    @ApiModelProperty(value = "电池产品名称")
    private String batteryName;

    /**
     * 电池片晶体类型
     */
    @ApiModelProperty(value = "电池片晶体类型")
    private String batteryCrystalType;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 电池片尺寸编码
     */
    @ApiModelProperty(value = "电池片尺寸编码")
    private String batteryDimensionCode;
    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    private String numberMainGrids;
    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private String shardingNumber;
    /**
     * 预警提示
     */
    @ApiModelProperty(value = "预警提示")
    private String warningReason;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
