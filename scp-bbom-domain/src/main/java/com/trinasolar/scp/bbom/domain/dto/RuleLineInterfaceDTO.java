package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


/**
 * 接口返回实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleLineDTO对象", description = "DTO对象")
public class RuleLineInterfaceDTO extends BaseDTO {
    /**
     * 物料料号制判断符
     */
    @ApiModelProperty(value = "物料料号")
    private String itemCodeLimit;
    /**
     * 物料料号制判断符
     */
    @ApiModelProperty(value = "物料料号")
    private String itemCodeLimitOperator;
    /**
     * 监造限制
     */
    @ApiModelProperty(value = "监造限制")
    private String supervisionRestriction;
    /**
     * 监造限制判断符
     */
    @ApiModelProperty(value = "监造限制判断符")
    private String supervisionRestrictionOperator;
    /**
     * 机台限制
     */
    @ApiModelProperty(value = "机台限制")
    private String machineLimit;
    /**
     * 机台限制判断符
     */
    @ApiModelProperty(value = "机台限制判断符")
    private String machineLimitOperator;
    /**
     * 认证限制
     */
    @ApiModelProperty(value = "认证限制")
    private String certificationLimitation;
    /**
     * 认证限制判断符
     */
    @ApiModelProperty(value = "认证限制判断符")
    private String certificationLimitationOperator;
    /**
     * 批次限制
     */
    @ApiModelProperty(value = "批次限制")
    private String batchLimit;
    /**
     * 批次限制判断符
     */
    @ApiModelProperty(value = "批次限制判断符")
    private String batchLimitOperator;
    /**
     * dp限制
     */
    @ApiModelProperty(value = "dp限制")
    private String dpLimit;
    /**
     * dp限制判断符
     */
    @ApiModelProperty(value = "dp限制判断符")
    private String dpLimitOperator;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;
}
