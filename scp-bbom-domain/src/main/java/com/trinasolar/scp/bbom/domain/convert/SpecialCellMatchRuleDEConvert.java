package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.SpecialCellMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.entity.SpecialCellMatchRule;
import com.trinasolar.scp.bbom.domain.excel.SpecialCellMatchRuleExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 特殊片源匹配规则 DTO与实体转换器
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SpecialCellMatchRuleDEConvert extends BaseDEConvert<SpecialCellMatchRuleDTO, SpecialCellMatchRule> {

    SpecialCellMatchRuleDEConvert INSTANCE = Mappers.getMapper(SpecialCellMatchRuleDEConvert.class);

    List<SpecialCellMatchRuleDTO> toExcelDTO(List<SpecialCellMatchRuleDTO> dtos);

    SpecialCellMatchRuleDTO toExcelDTO(SpecialCellMatchRuleDTO dto);
    @Mappings(
            {
                    @Mapping(target = "cellsTypeName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.APS_BATTERY_TYPE, rule.getCellsType()))"),
                    @Mapping(target = "regionalCountryName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.AOP_COUNTRY, rule.getRegionalCountry()))"),
                    @Mapping(target = "HTraceName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.H_TRACE, rule.getHTrace()))"),
                    @Mapping(target = "cellSourceName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.PCS_SOURCE_TYPE, rule.getCellSource()))"),
                    @Mapping(target = "aestheticsName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.AESTHETICS, rule.getAesthetics()))"),
                    @Mapping(target = "transparentDoubleGlassName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.TRANSPARENT_DOUBLE_GLASS, rule.getTransparentDoubleGlass()))"),
                    @Mapping(target = "productionGradeName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.PRODUCT_GRADE, rule.getProductionGrade()))"),
                    @Mapping(target = "commonCellsTypeName" ,expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getNameByValue(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.APS_BATTERY_TYPE, rule.getCommonCellsType()))")
            }
    )
    SpecialCellMatchRuleDTO toDto(SpecialCellMatchRule rule);

    SpecialCellMatchRule excelDtoToSaveDto(SpecialCellMatchRuleExcelDTO excelDTO);

    List<SpecialCellMatchRule> excelDtoToSaveDto(List<SpecialCellMatchRuleExcelDTO> dto);
}
