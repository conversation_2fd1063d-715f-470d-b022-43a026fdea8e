package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class BatteryTypeMainScreenPlateExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型编码
     */
    @ExcelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 切换类型
     */
    @ExcelIgnore
    private Long switchType;
    /**
     * 切换类型
     */
    @ExcelProperty(value = "类型")
    private String switchTypeName;
    /**
     * 基地
     */
    @ExcelIgnore
    private String basePlace;

    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 生产单元
     */
//    @ExcelIgnore
//    private String workunit;
    /**
     * 生产单元
     */
//    @ExcelProperty(value = "生产单元")
//    private String workunitName;
    /**
     * 线体
     */
    @ExcelProperty(value = "线体数量")
    private String line;
    /**
     * 机台
     */
    @ExcelIgnore
    private String workbench;
    /**
     * 机台
     */
    @ExcelProperty(value = "机台")
    private String workbenchName;
    /**
     * 物料料号-旧
     */
//    @ExcelProperty(value = "物料料号-旧")
//    private String itemCodeOld;
    /**
     * 物料料号-旧说明
     */
//    @ExcelProperty(value = "物料描述-旧")
//    private String itemDescOld;
    /**
     * 物料料号-新
     */
    @ExcelProperty(value = "物料料号-新")
    private String itemCodeNew;
    /**
     * 物料料号-新说明
     */
    @ExcelProperty(value = "物料描述-新")
    private String itemDescNew;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ExcelProperty(value = "单玻")
    private String singleGlassFlag;

    @ExcelProperty(value = "网版类别")
    private String screenPlateVersionCategoryNew;

    @ExcelProperty(value = "栅线数量")
    private String gridsNumber;
    /**
     * 数量-旧
     */
//    @ExcelProperty(value = "旧物料料号数量")
//    private String numberOld;
    /**
     * 数量-新
     */
    @ExcelProperty(value = "新物料料号数量")
    private String numberNew;
    /**
     * 旧网版库存
     */
//    @ExcelIgnore
//    private String screenPlateInventoryOld;

    /**
     * 旧网板在途数量
     */
//    @ExcelIgnore
//    private String screenPlateNumberOld;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remarks;

    /**
     * 是否存在bom
     */
    @ExcelIgnore
    private String isExistBom;
    /**
     * 有效期起
     */
    @ExcelProperty(value = "有效期起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效期止
     */
    @ExcelProperty(value = "有效期止")
    private LocalDateTime effectiveEndDate;

    /**
     * 供应商名称
     */
    @ExcelIgnore
    private String vendorName;
    /**
     * 供应商id
     */
    @ExcelIgnore
    private Long vendorId;


    /**
     * 网版类别旧
     */
//    @ExcelIgnore
//    private String screenPlateVersionCategoryOld;
}
