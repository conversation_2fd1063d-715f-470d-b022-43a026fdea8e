package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "特殊片源A-料号匹配规则保存参数", description = "保存参数")
public class AMaterielMatchRuleSaveDTO extends TokenDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    @ApiModelProperty(value = "电池车间")
    private String workshop;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "实际片厚")
    private String actualPieceThickness;

    @ApiModelProperty(value = "主栅两端形状")
    private String mainGridBothShape;

    @ApiModelProperty(value = "A-料号")
    private String aItemCode;
}
