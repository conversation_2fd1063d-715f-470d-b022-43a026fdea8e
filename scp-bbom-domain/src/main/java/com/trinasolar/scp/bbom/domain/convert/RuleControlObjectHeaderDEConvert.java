package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectHeaderDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectHeader;
import com.trinasolar.scp.bbom.domain.excel.RuleControlObjectHeaderExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 规则管控对象头 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleControlObjectHeaderDEConvert extends BaseDEConvert<RuleControlObjectHeaderDTO, RuleControlObjectHeader> {

    RuleControlObjectHeaderDEConvert INSTANCE = Mappers.getMapper(RuleControlObjectHeaderDEConvert.class);

    List<RuleControlObjectHeaderExcelDTO> toExcelDTO(List<RuleControlObjectHeaderDTO> dtos);

    RuleControlObjectHeaderExcelDTO toExcelDTO(RuleControlObjectHeaderDTO dto);
}
