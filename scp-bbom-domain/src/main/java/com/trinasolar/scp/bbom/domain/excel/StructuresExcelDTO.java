package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * BBOM结构
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StructuresExcelDTO {

    /**
     * bom id
     */
    @ExcelProperty(value = "bom id")
    private Long id;

    /**
     * 装配件物料ID
     */
    @ExcelProperty(value = "装配件物料ID")
    private Long assemblyItemId;

    /**
     * 组织代码
     */
    @ExcelProperty(value = "组织代码")
    private Long organizationId;

    /**
     * alternateBomDesignator
     */
    @ExcelProperty(value = "alternateBomDesignator")
    private String alternateBomDesignator;

    /**
     * ERP最后更新日期
     */
    @ExcelProperty(value = "ERP最后更新日期")
    private LocalDateTime lastUpdateDate;

    /**
     * 公共项目内码
     */
    @ExcelProperty(value = "公共项目内码")
    private Long commonAssemblyItemId;

    /**
     * specificAssemblyComment
     */
    @ExcelProperty(value = "specificAssemblyComment")
    private String specificAssemblyComment;

    /**
     * pendingFromEcn
     */
    @ExcelProperty(value = "pendingFromEcn")
    private String pendingFromEcn;

    /**
     * 属性类别
     */
    @ExcelProperty(value = "属性类别")
    private String attributeCategory;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute15;

    /**
     * 装配类别
     */
    @ExcelProperty(value = "装配类别")
    private Long assemblyType;

    /**
     * 公共序号
     */
    @ExcelProperty(value = "公共序号")
    private Long commonBillSequenceId;

    /**
     * 清单序号（关键字）
     */
    @ExcelProperty(value = "清单序号（关键字）")
    private Long billSequenceId;

    /**
     * 请求id
     */
    @ExcelProperty(value = "请求id")
    private Long requestId;

    /**
     * programApplicationId
     */
    @ExcelProperty(value = "programApplicationId")
    private Long programApplicationId;

    /**
     * programId
     */
    @ExcelProperty(value = "programId")
    private Long programId;

    /**
     * programUpdateDate
     */
    @ExcelProperty(value = "programUpdateDate")
    private LocalDateTime programUpdateDate;

    /**
     * 公共组织
     */
    @ExcelProperty(value = "公共组织")
    private Long commonOrganizationId;

    /**
     * nextExplodeDate
     */
    @ExcelProperty(value = "nextExplodeDate")
    private LocalDateTime nextExplodeDate;

    /**
     * projectId
     */
    @ExcelProperty(value = "projectId")
    private Long projectId;

    /**
     * taskId
     */
    @ExcelProperty(value = "taskId")
    private Long taskId;

    /**
     * originalSystemReference
     */
    @ExcelProperty(value = "originalSystemReference")
    private String originalSystemReference;

    /**
     * structureTypeId
     */
    @ExcelProperty(value = "structureTypeId")
    private Long structureTypeId;

    /**
     * implementationDate
     */
    @ExcelProperty(value = "implementationDate")
    private LocalDateTime implementationDate;

    /**
     * objName
     */
    @ExcelProperty(value = "objName")
    private String objName;

    /**
     * pkValue
     */
    @ExcelProperty(value = "pkValue")
    private String pk1Value;

    /**
     * pkValue
     */
    @ExcelProperty(value = "pkValue")
    private String pk2Value;

    /**
     * pkValue
     */
    @ExcelProperty(value = "pkValue")
    private String pk3Value;

    /**
     * pkValue
     */
    @ExcelProperty(value = "pkValue")
    private String pk4Value;

    /**
     * pkValue
     */
    @ExcelProperty(value = "pkValue")
    private String pk5Value;

    /**
     * effectivityControl
     */
    @ExcelProperty(value = "effectivityControl")
    private Integer effectivityControl;

    /**
     * isPreferred
     */
    @ExcelProperty(value = "isPreferred")
    private String isPreferred;

    /**
     * sourceBillSequenceId
     */
    @ExcelProperty(value = "sourceBillSequenceId")
    private Long sourceBillSequenceId;
}
