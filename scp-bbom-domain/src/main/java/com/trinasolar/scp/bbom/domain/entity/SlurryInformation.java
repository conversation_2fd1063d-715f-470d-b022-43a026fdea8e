package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 浆料车间单耗及线数维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Entity
@ToString
@Data
@Table(name = "bbom_slurry_information")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_slurry_information SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_slurry_information SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class SlurryInformation extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Column(name = "plan_version")
    private String planVersion;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "battery_type")
    private String batteryType;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 线数
     */
    @ApiModelProperty(value = "线数")
    @Column(name = "line_number")
    private String lineNumber;

    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    @Column(name = "workbench")
    private String workbench;

    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    @Column(name = "unit_consumption")
    private String unitConsumption;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效 Y 有效")
    @Column(name = "valid")
    private String valid;
}
