package com.trinasolar.scp.bbom.domain.utils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2022年6月21日19:24:50
 */
@Slf4j
public class DateUtil {
    public final static String yyyyMMFormat = "yyyyMM";

    public final static String yyyy_MMformat = "yyyy-MM";

    public final static String yyyyMMddFormat = "yyyy-MM-dd";

    public static final String format_DateTime = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取星期几
     *
     * @param localDate
     * @return
     */
    public static String getWeek(LocalDate localDate) {
        int dayOfWeek = localDate.getDayOfWeek().getValue();
        String weekStr = null;
        switch (dayOfWeek) {
            case 1:
                weekStr = "星期一";
                break;
            case 2:
                weekStr = "星期二";
                break;
            case 3:
                weekStr = "星期三";
                break;
            case 4:
                weekStr = "星期四";
                break;
            case 5:
                weekStr = "星期五";
                break;
            case 6:
                weekStr = "星期六";
                break;
            case 7:
                weekStr = "星期日";
                break;
            default:
                break;
        }

        return weekStr;
    }

    /**
     * 获取是第几周
     *
     * @param localDate
     * @return
     */
    public static String getWeekNum(LocalDate localDate) {
        WeekFields weekFields = WeekFields.ISO;
        int weekNumber = LocalDate.now().get(weekFields.weekOfYear());
        return localDate.format(DateTimeFormatter.ofPattern("yyyy")) + weekNumber;
    }

    /**
     * 获取两个日期天数差
     */
    public static int getDiffDays(LocalDate startDate, LocalDate endDate) {
        Period next = Period.between(startDate, endDate);
        return next.getDays();//相差天数
    }

    /**
     * 根据年 月 获取对应的月份 天数
     */
    public static int getDaysByYearMonth(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        return a.get(Calendar.DATE);
    }

    /**
     * 转换日期
     *
     * @param date   日期
     * @param format 日期格式
     * @return
     */
    public static String getFormatDate(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 获取月份最后一天
     * 传入  月份 202202
     */
    public static String getMonthLastDay(String month) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMFormat);
        Date date = null;
        try {
            date = sdf.parse(month);
        } catch (ParseException e) {
            log.warn("[DateUtil.getMonthLastDay]");
        }
        if (date == null) {
            return null;
        }
        Calendar a = Calendar.getInstance();
        a.setTime(date);
        a.add(Calendar.MONTH, 1);//月增加1天
        a.add(Calendar.DAY_OF_MONTH, -1);//日期倒数一日,既得到本月最后一天

        return getFormatDate(a.getTime(), yyyyMMddFormat);
    }

    /**
     * 获取月份第一天
     * 传入  月份 202202
     */
    public static String getMonthFirstDay(String month) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMFormat);
        Date date = null;
        try {
            date = sdf.parse(month);
        } catch (ParseException e) {
            log.warn("[DateUtil.getMonthLastDay]");
        }
        if (date == null) {
            return null;
        }
        Calendar a = Calendar.getInstance();
        a.setTime(date);
//        a.add(Calendar.MONTH, -1);//月增加1天
//        a.add(Calendar.DAY_OF_MONTH, 1);//日期倒数一日,既得到本月最后一天

        return getFormatDate(a.getTime(), yyyyMMddFormat);
    }

    /**
     * 获取某年所有月份数据
     *
     * @param year
     * @return
     */
    public static List<String> getMonthsByYear(Integer year) {
        return IntStream.rangeClosed(1, 12).mapToObj(i -> year + StringUtils.leftPad(i + "", 2, "0"))
                .collect(Collectors.toList());
    }

    /**
     * 获取月份对应的天数
     */
    public static int getDaysForMonth(String month) {
        LocalDate localDate = month2LocalDate(month);
        return getDaysByYearMonth(localDate.getYear(), localDate.getMonthValue());
    }

    /**
     * 获取指定月份增减后的新月份
     */
    public static String getNewMonth(String month, int n) {
        LocalDate localDate = month2LocalDate(month).minusMonths(-n);
        return localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }

    /**
     * 将月+日转换为yyyy-MM-dd日期
     *
     * @param month yyyyMM月
     * @param day   日
     */
    public static LocalDate getLocalDate(String month, Integer day) {
        try {
            return LocalDate.parse(month.substring(0, 4)
                            .concat("-").concat(month.substring(4))
                            .concat("-").concat(StringUtils.leftPad(day + "", 2, "0")),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取增减天数后的月份
     *
     * @param yyyyMMddDate 日期串
     * @param day          增减天数
     * @return yyyyMM月份
     */
    public static String getMonth(String yyyyMMddDate, int day) {
        LocalDate date = LocalDate.parse(yyyyMMddDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate localDate = date.minusDays(-day);
        return localDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }

    /**
     * localDate转Date
     *
     * @param localDate localdate日期
     * @return date日期
     */
    public static Date localDate2Date(LocalDate localDate) {
        if (Objects.isNull(localDate)) {
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * localDate转Date
     *
     * @param localDateTime localdate日期
     * @return date日期
     */
    public static Date localDate2Time(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }


    /**
     * 获取增减天数后的天数值
     *
     * @param yyyyMMddDate 日期串
     * @param day          增减天数
     * @return 天数
     */
    public static int getDay(String yyyyMMddDate, int day) {
        LocalDate date = LocalDate.parse(yyyyMMddDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate localDate = date.minusDays(-day);
        return localDate.getDayOfMonth();
    }

    /**
     * 获取指定日期增减后的新日期
     */
    public static LocalDate getNewLocalDate(LocalDate localDate, int day) {
        return localDate.minusDays(-day);
    }

    /**
     * 获取两个月份间所有月份
     *
     * @param dateStart
     * @param dateEnd
     * @return
     * @throws ParseException
     */
    @SneakyThrows
    public static List<String> getMonthList(String dateStart, String dateEnd) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMFormat);
        Date startDate = sdf.parse(dateStart);
        Date endDate = sdf.parse(dateEnd);
        Calendar c = Calendar.getInstance();
        List<String> list = new ArrayList<>();
        SimpleDateFormat df = new SimpleDateFormat(yyyyMMFormat);
        while (startDate.getTime() <= endDate.getTime()) {
            String newStartDate = df.format(startDate);
            list.add(newStartDate);
            c.setTime(startDate);
            //加一个月
            c.add(Calendar.MONTH, 1);
            startDate = c.getTime();
        }
        return list;
    }

    @SneakyThrows
    public static List<LocalDate> getLastDateList(String dateStart, String dateEnd) {
        return getMonthList(dateStart, dateEnd).stream().map(m -> DateUtil.month2EndLocalDate(m))
                .collect(Collectors.toList());
    }

    /**
     * 获取上月、本月、下月
     *
     * @param month 本月
     * @return 月度集合
     */
    public static List<String> getThreeMonth(String month) {
        LocalDate currentLocalDate = LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));

        return ImmutableList.of(
                currentLocalDate.plusMonths(-1).format(DateTimeFormatter.ofPattern(yyyyMMFormat)),
                month,
                currentLocalDate.plusMonths(1).format(DateTimeFormatter.ofPattern(yyyyMMFormat)
                ));
    }

    /**
     * 校验月份是否在区间内
     *
     * @param month      月份
     * @param beginMonth 开始月份
     * @param endMonth   结束月份
     * @return 是否在区间内
     */
    public static boolean validMonth(String month, String beginMonth, String endMonth) {
        LocalDate currentDate = LocalDate.parse(month.concat("01"), DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate beginDate = month2BeginLocalDate(beginMonth);
        LocalDate endDate = month2EndLocalDate(endMonth);

        return !currentDate.isBefore(beginDate) && !currentDate.isAfter(endDate);
    }

    /**
     * 获取两个日期间所有日期
     *
     * @param dateStart 开始日期
     * @param dateEnd   结束日期
     * @return
     * @throws ParseException
     */
    @SneakyThrows
    public static List<LocalDate> getDayList(LocalDate dateStart, LocalDate dateEnd) {
        List<LocalDate> days = Lists.newArrayList();
        if (dateEnd.compareTo(dateStart) < 0) {
            return days;
        }

        while (true) {
            days.add(dateEnd);
            if (dateEnd.compareTo(dateStart) <= 0) {
                break;
            }
            dateEnd = dateEnd.plusDays(-1);
        }
        return days;
    }

    /**
     * 获取某月开始和结束日期
     *
     * @param month 月份，格式：yyyyMM
     * @return yyyyMMdd L：开始日期，R：结束日期
     */
    public static Pair<LocalDate, LocalDate> getFirstDayAndLastDay(String month) {
        LocalDate localDate = LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate firstDay = localDate.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());

        return Pair.of(firstDay, lastDay);
    }

    /**
     * 获取月份数据
     *
     * @param localDate 日期
     * @return yyyyMM格式月份
     */
    public static String getMonth(LocalDate localDate) {
        if (Objects.isNull(localDate)) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(yyyyMMFormat));
    }

    /**
     * 获取月份数据
     *
     * @param localDate 日期
     * @return yyyyMM格式月份
     */
    public static String getMonth(LocalDateTime localDate) {
        if (Objects.isNull(localDate)) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(yyyyMMFormat));
    }

    /**
     * 月份到天的数据
     *
     * @param localDate 日期
     * @return yyyyMM格式月份
     */
    public static String getMonthDay(LocalDateTime localDate) {
        if (Objects.isNull(localDate)) {
            return null;
        }
        return localDate.format(DateTimeFormatter.ofPattern(yyyyMMddFormat));
    }

    /**
     * 获取最大天数
     *
     * @param months
     * @return
     */
    public static int getMaxDayOfMonth(Set<String> months) {
        if (CollectionUtils.isEmpty(months)) {
            return 0;
        }

        AtomicInteger maxDays = new AtomicInteger();
        months.forEach(m -> {
            int daysForMonth = getDaysForMonth(m);
            if (daysForMonth > maxDays.get()) {
                maxDays.set(daysForMonth);
            }
        });

        return maxDays.get();
    }

    /**
     * 获取月份数据
     *
     * @param date 日期
     * @return yyyyMM格式月份
     */
    public static String getMonthStr(Date date) {
        return new SimpleDateFormat(yyyyMMFormat).format(date);
    }

    /**
     * 从传入时间date 获取下 num 个月份
     *
     * @param date 日期
     * @param num  下num个月
     * @return yyyyMM格式月份
     */
    public static String getNextMonth(Date date, int num) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, num);
        return new SimpleDateFormat(yyyyMMFormat).format(c.getTime());
    }

    //转化日期  yyyyMM
    public static Date getMoth(String date) throws ParseException {
        return new SimpleDateFormat(yyyyMMFormat).parse(date);
    }

    //转化日期  yyyy-MM-dd
    public static Date getDay(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return new SimpleDateFormat(yyyyMMddFormat).parse(date);
        } catch (ParseException e) {
            log.warn("【DateUtil.getDay】", e);
        }
        return null;
    }

    public static Date getDay(String date, String format) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return new SimpleDateFormat(format).parse(date);
        } catch (ParseException e) {
            log.warn("【DateUtil.getDay】", e);
        }
        return null;
    }

    /**
     * 日期格式转换为yyyy-MM-dd格式日期
     */
    public static LocalDate formatLocalDate(LocalDate localDate) {
        String localDateStr = localDate.getYear() + "-" +
                StringUtils.leftPad(localDate.getMonthValue() + "", 2, "0") + "-" +
                StringUtils.leftPad(localDate.getDayOfMonth() + "", 2, "0");
        return LocalDate.parse(localDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 月转为1号的日期
     *
     * @param month yyyyMM格式的月份
     * @return yyyy-MM-dd
     */
    public static LocalDate month2LocalDate(String month) {
        return LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 获取指定月开始日期
     *
     * @param month yyyyMM格式的月份
     * @return yyyy-MM-dd
     */
    public static LocalDate month2BeginLocalDate(String month) {
        if (StringUtils.isBlank(month)) {
            return null;
        }
        return LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 获取指定月结束日期
     *
     * @param month yyyyMM格式的月份
     * @return yyyy-MM-dd
     */
    public static LocalDate month2EndLocalDate(String month) {
        if (StringUtils.isBlank(month)) {
            return null;
        }

        return LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd")).with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 月转为1号的日期
     *
     * @param month yyyyMM格式的月份
     * @return yyyy-MM-dd
     */
    public static LocalDate monthLocalDate(String month) {
        return LocalDate.parse(month + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 字符串转日期
     *
     * @param day yyyy-MM-dd 格式的日期
     * @return yyyy-MM-dd
     */
    public static LocalDate dayLocalDate(String day) {
        return LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 日期转时间
     *
     * @param day yyyy-MM-dd 格式的日期
     * @return yyyy-MM-dd
     */
    public static Long dateStrToLong(String day) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMddFormat);
        try {
            return sdf.parse(day).getTime();
        } catch (ParseException e) {
            log.warn("DateUtil.dateStrToLong", e);
//            e.printStackTrace();
        }

        return null;
    }


    public static String dateStrFormat(Date day) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        return sdf.format(day);
    }

    /**
     * 获取月份范围内的值
     *
     * @param m1 2021-12-01
     * @param m2 2022-03-04
     * @return
     */
    public static List<Map<String, String>> monthScope(String m1, String m2, String year) {
        if (StringUtils.isBlank(m1) || StringUtils.isBlank(m2)) {
            return null;
        }
        List<Map<String, String>> list = null;
        try {
            Date d1 = new SimpleDateFormat(yyyyMMddFormat).parse(m1);//定义起始日期

            Date d2 = new SimpleDateFormat(yyyyMMddFormat).parse(m2);//定义结束日期

            Calendar dd = Calendar.getInstance();//定义日期实例

            dd.setTime(d1);//设置日期起始时间
            list = new ArrayList<>();
            //判断是否到结束日期
            while (dd.getTime().before(d2) || dd.getTime().equals(d2)) {
                HashMap<String, String> map = new HashMap<>();
                //开始时间
                String beginDay = new SimpleDateFormat(yyyyMMddFormat).format(dd.getTime());


                map.put("beginDay", beginDay);
                //结束时间
                dd.set(Calendar.DAY_OF_MONTH, dd.getActualMaximum(Calendar.DAY_OF_MONTH));
                if (dd.getTime().before(d2)) {
                    map.put("endDay", new SimpleDateFormat(yyyyMMddFormat).format(dd.getTime()));
                } else {
                    map.put("endDay", new SimpleDateFormat(yyyyMMddFormat).format(d2.getTime()));
                }

                dd.add(Calendar.MONTH, 1);//进行当前日期月份加1
                dd.set(Calendar.DAY_OF_MONTH, 1); //设置成一号

                if (StringUtils.isNotBlank(year)) {
                    if (!beginDay.substring(0, 4).equals(year)) {
                        continue;
                    }
                }
                list.add(map);
            }
        } catch (ParseException e) {
            log.warn("【DateUtil.monthScope】", e);
        }


        return list;
    }
    /**
     * 获取年份范围内的值
     *
     * @param m1 2021  起始年份
     * @param m2 2023  结束年份
     * @return [2021, 2022, 2023]
     */
    public static List<Integer> yearRange(String m1, String m2) {
        Date d1 = null;//定义起始日期
        Date d2 = null;//定义结束日期
        try {
            d1 = new SimpleDateFormat("yyyy").parse(m1);

            d2 = new SimpleDateFormat("yyyy").parse(m2);
        } catch (ParseException e) {
            log.warn("【DateUtil.yearRange】", e);
        }

        Calendar dd = Calendar.getInstance();//定义日期实例
        if (d1 == null || d2 == null) {
            return null;
        }
        dd.setTime(d1);//设置日期起始时间
        List<Integer> list = new ArrayList<>();
        while (dd.getTime().before(d2) || dd.getTime().equals(d2)) {//判断是否到结束日期

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");

            String str = sdf.format(dd.getTime());

            list.add(Integer.parseInt(str));

            dd.add(Calendar.YEAR, 1);//进行当前日期月份加1

        }
        return list;
    }

    /**
     * 计算两个时间内所有日期
     *
     * @param date1
     * @param date2
     * @return
     */
    public static List<String> getDays(String date1, String date2, String dateFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Long startTime;
        Long endTime;
        try {
            startTime = sdf.parse(date1).getTime();
            endTime = sdf.parse(date2).getTime();
        } catch (ParseException e) {
            log.warn(String.format("[DateUtil.getDays] 转换日期失败 [%s],[%s]", date1, date2));
            return null;
        }

        List<String> dateList = new ArrayList<String>();
        Long oneDay = 1000 * 60 * 60 * 24L;

        Long time = startTime;
        while (time <= endTime) {
            Date d = new Date(time);
            DateFormat df = new SimpleDateFormat(dateFormat);
            String date = df.format(d);
            System.out.println(date);
            dateList.add(date);
            time += oneDay;
        }
        return dateList;
    }


    /**
     * 获取添加过后的月份数据
     *
     * @param month 202202
     * @param num   2
     * @return 202402
     */
    public static String getAddYear(String month, int num) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyyMMFormat);

        Date date = null;
        try {
            date = sdf.parse(month);
        } catch (ParseException e) {
            log.warn("[DateUtil.getAddYear]");
        }

        Calendar dd = Calendar.getInstance();//定义日期实例

        if (date == null) {
            return "";
        }
        dd.setTime(date);

        dd.add(Calendar.YEAR, num);

        return sdf.format(dd.getTime());
    }

    public static int localDateCompare(LocalDate start, LocalDate end) {

        Period next = Period.between(start, end);
//        next.getDays();//相差天数
//        next.getMonths();//相差月份
//        next.getYears();//相差年份
        return next.getDays();
    }

    public static List<String> getContinuousTime(List<String> dates) {
        // 将日期字符串转换为LocalDate对象，并按照日期进行排序
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<LocalDate> dateList = dates.stream()
                .map(str -> LocalDate.parse(str, formatter))
                .sorted()
                .collect(Collectors.toList());
        // 使用循环遍历LocalDate对象列表，判断相邻两个日期对象的差是否为1天，如果是则将它们加入当前日期范围，如果不是则输出当前日期范围并重新开始一个新的日期范围。
        List<String> rangeList = new ArrayList<>();
        LocalDate start = null;
        LocalDate end = null;
        for (LocalDate date : dateList) {
            if (start == null) {
                start = date;
                end = date;
            } else if (date.minusDays(1).equals(end)) {
                end = date;
            } else {
                String rangeStr = DateUtil.dateStrFormat(DateUtil.getDay(start.format(formatter))) + "-" + DateUtil.dateStrFormat(DateUtil.getDay(end.format(formatter)));
                rangeList.add(rangeStr);
                start = date;
                end = date;

            }
        }
        if (start != null) {
            String rangeStr = DateUtil.dateStrFormat(DateUtil.getDay(start.format(formatter))) + "-" + DateUtil.dateStrFormat(DateUtil.getDay(end.format(formatter)));
            rangeList.add(rangeStr);
        }
        return rangeList;
    }

    public static List<String> convertDate(List<String> yearMonths) {
        yearMonths.sort(Comparator.naturalOrder());
        List<String> resultList = Lists.newLinkedList();
        yearMonths.forEach(date -> {
            Integer year = Integer.valueOf(date.substring(0, 4));
            Integer month = Integer.valueOf(date.substring(4));
            LocalDate localDate = LocalDate.of(year, month, 1);
            Month firstMonthOfQuarter = localDate.getMonth().firstMonthOfQuarter();
            Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
            resultList.add(year + "-" + month);
            if (endMonthOfQuarter.getValue() == month) {
                DateTimeFormatter pattern = DateTimeFormatter.ofPattern("qqq");
                String format = localDate.format(pattern);
                resultList.add(year + "Q" + format + "季度");
            }
        });
        return resultList;
    }

    public static String convertDate(String yearMonth) {
        Integer year = Integer.valueOf(yearMonth.substring(0, 4));
        Integer month = Integer.valueOf(yearMonth.substring(4));
        return year + "-" + month;
    }

    public static String getEndMonthOfQuarter(String date) {
        Integer year = Integer.valueOf(date.substring(0, 4));
        Integer month = Integer.valueOf(date.substring(4));
        LocalDate localDate = LocalDate.of(year, month, 1);
        Month firstMonthOfQuarter = localDate.getMonth().firstMonthOfQuarter();
        Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
        if (endMonthOfQuarter.getValue() == month) {
            DateTimeFormatter pattern = DateTimeFormatter.ofPattern("qqq");
            String format = localDate.format(pattern);
            return year + "Q" + format + "季度";
        }
        return null;
    }

    public static List<Integer> getTwoOrThreeYearsByNow(LocalDate localDate) {
        List<Integer> years = new ArrayList<>();
        //当前日期的年中
        LocalDate localDateMid = LocalDate.of(localDate.getYear(), 6, 30);
        if (localDate.isAfter(localDateMid)) {
            //取两年
            years.add(localDate.getYear());
            years.add(localDate.getYear() - 1);
        } else {
            //取三年
            years.add(localDate.getYear());
            years.add(localDate.getYear() - 1);
            years.add(localDate.getYear() - 2);
        }
        return years;
    }

    /**
     * 获取月份年的部分
     *
     * @param month
     * @return
     */
    public static Integer getYear(String month) {
        if (StringUtils.isNotEmpty(month)) {
            return Integer.parseInt(month.substring(0, 4));
        }
        return null;
    }

    public static LocalDateTime getMonthFirstDay() {
        LocalDate currentDate = LocalDate.now(); // 获取当前日期
        //int year = currentDate.getYear(); // 年份
        //int monthValue = currentDate.getMonthValue(); // 月份（1-12）
        LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1); // 设置日期为本月第一天
        LocalDateTime localDateTime = firstDayOfMonth.atStartOfDay(); // 转换为LocalDateTime对象
        return localDateTime;
    }

    /**
     * 获取当前月的上个月 对应的第一天
     *
     * @return
     */
    public static LocalDateTime getLastMonthFirstDay() {
        LocalDate currentDate = LocalDate.now(); // 获取当前日期
        // 计算上个月的日期
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        LocalDate firstDayOfMonth = lastMonthDate.withDayOfMonth(1); // 设置日期为本月第一天
        LocalDateTime localDateTime = firstDayOfMonth.atStartOfDay(); // 转换为LocalDateTime对象
        return localDateTime;
    }

    /**
     * date转为LocalDateTime
     * 步骤:
     * 1.拿到要转换的Date对象
     * 2.将Date对象转换成为Instant对象
     * 方法:
     * Date对象.toInstant()
     * 3.将瞬时对象转换成为LocalDateTime对象
     * 方法:
     * LocalDateTime.ofInstant(瞬时对象,时区);
     *
     * @param date
     * @return
     */
    public static LocalDateTime convertToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return localDateTime;
    }

    //java日期集合中只保留当前月份的 其他月份的数据排除
    public static boolean isSameMonth(Date date, int month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.MONTH) == month;
    }

    public static void printDates(List<Date> dates) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (Date date : dates) {
            String formattedDate = sdf.format(date);
            System.out.println(formattedDate);
        }
    }

    /**
     * 获取当月的前一个月
     *
     * @return
     */
    public static LocalDateTime getLastMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算上个月的日期
        LocalDate lastMonthDate = currentDate.minusMonths(1);
        // 创建上个月的LocalDateTime对象
        LocalDateTime lastMonthDateTime = LocalDateTime.of(lastMonthDate, LocalTime.MIN);
        return lastMonthDateTime;
    }

    /**
     * 获取当月
     *
     * @return
     */
    public static LocalDateTime getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 创建本月的LocalDateTime对象
        LocalDateTime lastMonthDateTime = LocalDateTime.of(currentDate, LocalTime.MIN);
        return lastMonthDateTime;
    }
}
