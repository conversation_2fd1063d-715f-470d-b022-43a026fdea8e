package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleDetailDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleHeaderDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleHeader;
import com.trinasolar.scp.bbom.domain.excel.RuleDetailExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.RuleHeaderExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * BOM规则头表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleHeaderDEConvert extends BaseDEConvert<RuleHeaderDTO, RuleHeader> {

    RuleHeaderDEConvert INSTANCE = Mappers.getMapper(RuleHeaderDEConvert.class);

    List<RuleHeaderExcelDTO> toExcelDTO(List<RuleHeaderDTO> dtos);

    RuleHeaderExcelDTO toExcelDTO(RuleHeaderDTO dto);

    List<RuleDetailExcelDTO> toExcelDTOByDetail(List<RuleDetailDTO> dtos);
}
