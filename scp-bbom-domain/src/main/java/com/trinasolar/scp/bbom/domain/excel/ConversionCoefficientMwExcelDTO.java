package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 兆瓦转换系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ConversionCoefficientMwExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型编码
     */
    @ExcelIgnore
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 品类
     */
    @ExcelProperty(value = "品类")
    private String category;
    /**
     * 物料分类
     */
    @ExcelProperty(value = "物料分类")
    private String classify;
    /**
     * 基地
     */
    @ExcelIgnore
    private String basePlace;
    /**
     * 基地
     */
    @ExcelProperty(value = "基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 车间
     */
    @ExcelProperty(value = "车间")
    private String workshopName;
    /**
     * 电池MW系数
     */
    @ExcelProperty(value = "电池MW系数")
    private BigDecimal batteryMwQty;
    /**
     * 电池目标良率
     */
    @ExcelProperty(value = "电池目标良率")
    private BigDecimal batteryEfficiencyQty;
    /**
     * 单耗
     */
    @ExcelProperty(value = "寿命/单耗")
    private BigDecimal unitConsumption;
    /**
     * 物料MW系数
     */
    @ExcelProperty(value = "物料MW系数")
    private BigDecimal materielMwQty;
    /**
     * 预警
     */
    @ExcelProperty(value = "预警")
    private String warningReason;
}
