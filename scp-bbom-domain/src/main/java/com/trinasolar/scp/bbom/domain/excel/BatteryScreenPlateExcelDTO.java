package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class BatteryScreenPlateExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 切换类型
     */
    @ExcelProperty(value = "切换类型")
    private String switchTypeName;

    /**
     * 物料料号-新
     */
    @ExcelProperty(value = "网版料号-新")
    private String itemCodeNew;

    /**
     * 物料料号-新说明
     */
    @ExcelProperty(value = "物料说明")
    private String itemDescNew;

    /**
     * 物料状态
     */
    @ExcelProperty(value = "物料状态")
    private String itemStatusNew;

    /**
     * 电池类型编码
     */
    @ExcelProperty(value = "电池类型编码")
    @ExcelIgnore
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型")
    private String batteryName;
    /**
     * 切换类型
     */
    @ExcelIgnore
    private Long switchType;

    @ExcelProperty(value = "机台")
    private String workbenchName;

    /**
     * 基地
     */
    @ExcelIgnore
    private String basePlace;

    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 生产单元
     */
    @ExcelIgnore
    private String workunit;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunitName;
    /**
     * 线体
     */
    @ExcelProperty(value = "线体数量")
    private String line;
    /**
     * 机台
     */
    @ExcelIgnore
    private String workbench;

    /**
     * 是否存在bom
     */
    @ExcelProperty(value = "是否存在bom")
    private String isExistBom;


    /**
     * 物料料号-旧
     */
//    @ExcelProperty(value = "网版料号-旧")
//    @ExcelIgnore
//    private String itemCodeOld;
    /**
     * 物料料号-旧说明
     */
//    @ExcelProperty(value = "网版料号-旧说明")
//    @ExcelIgnore
//    private String itemDescOld;


    /**
     * 数量-旧
     */
    @ExcelProperty(value = "旧网板消耗数量")
    @ExcelIgnore
    private String numberOld;
    /**
     * 数量-新
     */
    @ExcelProperty(value = "新网板消耗数量")
    @ExcelIgnore
    private String numberNew;
    /**
     * 旧网版库存
     */
//    @ExcelProperty(value = "旧网版库存")
//    @ExcelIgnore
//    private String screenPlateInventoryOld;

    /**
     * 旧网板在途数量
     */
//    @ExcelProperty(value = "旧网板在途数量")
//    @ExcelIgnore
//    private String screenPlateNumberOld;
    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商")
    private String vendorName;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;


    /**
     * 有效期起
     */
    @ExcelProperty(value = "有效期起")
    private String effectiveStartDates;
    /**
     * 有效期止
     */
    @ExcelProperty(value = "有效期止")
    private String effectiveEndDates;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ExcelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ExcelProperty(value = "网版类型")
    private String screenPlateVersionCategoryNew;

    @ExcelIgnore
    private String singleGlassFlag;

    @ExcelProperty(value = "单玻")
    private String singleGlassFlagName;

    @ExcelProperty(value = "栅线数量")
    private String gridsNumber;


    /**
     * 供应商id
     */
    @ExcelIgnore
    private Long vendorId;
    /**
     * 网版类别旧
     */
    @ExcelIgnore
    private String screenPlateVersionCategoryOld;



    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    @ExcelIgnore
    private LocalDateTime effectiveStartDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    @ExcelIgnore
    private LocalDateTime effectiveEndDate;


}
