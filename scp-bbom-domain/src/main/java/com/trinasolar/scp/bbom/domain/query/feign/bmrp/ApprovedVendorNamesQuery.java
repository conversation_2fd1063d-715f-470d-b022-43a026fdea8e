package com.trinasolar.scp.bbom.domain.query.feign.bmrp;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 合格供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-29 07:44:20
 */
@Data
@ApiModel(value = "ApprovedVendorNamesQuery查询条件", description = "查询条件")
@Accessors(chain = true)
public class ApprovedVendorNamesQuery extends TokenDTO implements Serializable {
    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商名称")
    private List<String> vendorNames;

}
