package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * ERP工艺路线
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 20:00:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpOperatRouteDTO对象", description = "DTO对象")
public class ErpOperatRouteDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 工艺路线序列号
     */
    @ApiModelProperty(value = "工艺路线序列号")
    private Long routingSequenceId;
    /**
     * 装配件物料ID
     */
    @ApiModelProperty(value = "装配件物料ID")
    private Long assemblyItemId;
    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private Long organizationId;
    /**
     * 备用路由指示符
     */
    @ApiModelProperty(value = "备用路由指示符")
    private String alternateRoutingDesignator;
}
