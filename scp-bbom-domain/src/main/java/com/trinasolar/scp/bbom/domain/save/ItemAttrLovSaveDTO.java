package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 物料属性字段Lov
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ItemAttrLov保存参数", description = "保存参数")
public class ItemAttrLovSaveDTO extends TokenDTO implements Serializable {

    private static final long serialVersionUID = 4639134019877777011L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * lovId
     */
    @ApiModelProperty(value = "lovId")
    private String lovId;

    /**
     * lovName
     */
    @ApiModelProperty(value = "lovName")
    private String lovName;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrName
     */
    @ApiModelProperty(value = "srcAttrName")
    private String srcAttrName;

    /**
     * lovLineId
     */
    @ApiModelProperty(value = "lovLineId")
    private String lovLineId;

    /**
     * lovLineValue
     */
    @ApiModelProperty(value = "lovLineValue")
    private String lovLineValue;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    private String language;

    /**
     * isRequired
     */
    @ApiModelProperty(value = "isRequired")
    private String isRequired;
}
