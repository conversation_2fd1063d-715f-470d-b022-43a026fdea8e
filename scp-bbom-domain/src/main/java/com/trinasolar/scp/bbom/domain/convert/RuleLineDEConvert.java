package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleLineDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleLine;
import com.trinasolar.scp.bbom.domain.excel.RuleLineExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * BOM规则行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleLineDEConvert extends BaseDEConvert<RuleLineDTO, RuleLine> {

    RuleLineDEConvert INSTANCE = Mappers.getMapper(RuleLineDEConvert.class);

    List<RuleLineExcelDTO> toExcelDTO(List<RuleLineDTO> dtos);

    RuleLineExcelDTO toExcelDTO(RuleLineDTO dto);
}
