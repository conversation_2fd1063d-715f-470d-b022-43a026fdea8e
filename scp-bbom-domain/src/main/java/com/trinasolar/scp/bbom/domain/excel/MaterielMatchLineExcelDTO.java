package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterielMatchLineExcelDTO {


    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String batteryType;

    /**
     * 国内/海外
     */
    @ExcelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;

    @ExcelProperty(value = "计划类型")
    private String planType;

    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 主栅间距
     */
    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 特殊区域
     */
    @ExcelProperty(value = "特殊区域")
    private String specialArea;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String pcsSourceType;

    @ExcelProperty(value = "法碳配比")
    private String ratioCode;

    @ExcelProperty(value = "ECS_CODE")
    private String ecsCode;

    /**
     * 供应方式名称
     */
    @ExcelProperty(value = "供应方式")
    private String supplyModeName;

    /**
     * 电池厂家
     */
    @ExcelProperty(value = "电池厂家")
    private String batteryManufacturer;
    /**
     * 是否有特殊要求
     */
    @ExcelProperty(value = "是否有特殊要求")
    private String isSpecialRequirements;
    /**
     * 硅料厂家
     */
    @ExcelProperty(value = "硅料厂家")
    private String siliconMaterialManufacturer;
    /**
     * 网版厂家
     */
    @ExcelProperty(value = "网版厂家")
    private String screenManufacturer;
    /**
     * 银浆厂家
     */
    @ExcelProperty(value = "银浆厂家")
    private String silverSlurryManufacturer;
    /**
     * 低阻
     */
    @ExcelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 需求地
     */
    @ExcelProperty(value = "需求地")
    private String demandPlace;
    /**
     * 硅片等级
     */
    @ExcelProperty(value = "硅片等级")
    private String pcsSourceLevel;

    /**
     * 硅片厚度
     */
    @ExcelProperty(value = "新硅片厚度")
    private String siliconWaferValue;
    /**
     * 硅片品类
     */
    @ExcelProperty(value = "硅片品类")
    private String waferCategory;


    /**
     * 加工类别
     */
    @ExcelProperty(value = "加工类别")
    private String processCategory;
    /**
     * 排产基地
     */
    @ExcelProperty(value = "排产基地")
    private String basePlace;
    /**
     * 排产车间
     */
    @ExcelProperty(value = "排产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 线体数量
     */
    @ExcelProperty(value = "线体数量")
    private BigDecimal line;
    /**
     * 排产时间
     */
    @ExcelProperty(value = "排产时间")
    private String month;

    @ExcelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGridName;

    @ExcelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGridName;

    @ExcelProperty(value = "主栅信息")
    private String mainGridInfo;

    /**
     * 网版切换料号
     */
    @ExcelProperty(value = "网版切换料号1")
    private String screenPlateItemCode;
    /**
     * 网版物料说明
     */
    @ExcelProperty(value = "网版物料说明1")
    private String screenPlateItemCodeDesc;
    /**
     * 网版切换料号
     */
    @ExcelProperty(value = "网版切换料号2")
    private String screenPlateCodeFilter;
    /**
     * 网版物料说明
     */
    @ExcelProperty(value = "网版物料说明2")
    private String screenPlateCodeDescFilter;
    /**
     * 网版切换料号
     */
    @ExcelProperty(value = "网版切换料号3")
    private String screenPlateItemCodeFineGrid;
    /**
     * 网版物料说明
     */
    @ExcelProperty(value = "网版物料说明3")
    private String screenPlateItemCodeDescFineGrid;
    /**
     * 网版切换料号
     */
    @ExcelProperty(value = "网版切换料号4")
    private String screenPlateCodeFilterFineGrid;
    /**
     * 网版物料说明
     */
    @ExcelProperty(value = "网版物料说明4")
    private String screenPlateCodeDescFilterFineGrid;
    /**
     * 电池料号
     */
    @ExcelProperty(value = "电池料号")
    private String itemCode;
    /**
     * 电池料号说明
     */
    @ExcelProperty(value = "电池料号说明")
    private String itemDesc;
    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal cellQty;
    /**
     * 研发/量产
     */
    @ExcelProperty(value = "研发/量产")
    private String isCatchProduction;
    /**
     * BOM替代项
     */
    @ExcelProperty(value = "BOM替代项")
    private String alternateBomDesignator;
    /**
     * 切换开始时间
     */
    @ExcelProperty(value = "切换开始时间")
    private LocalDateTime switchStartDate;
    /**
     * 切换完成时间
     */
    @ExcelProperty(value = "切换完成时间")
    private LocalDateTime switchEndDate;
    /**
     * 排产开始时间
     */
    @ExcelProperty(value = "排产开始时间")
    private LocalDate startTimeStart;
    /**
     * 排产完成时间
     */
    @ExcelProperty(value = "排产完成时间")
    private LocalDate startTimeEnd;
    /**
     * 工艺路线
     */
    @ExcelProperty(value = "工艺路线")
    private String route;
    /**
     * 认证型号
     */
    @ExcelProperty(value = "认证型号")
    private String certifiedModels;
    /**
     * 匹配状态
     */
    @ExcelProperty(value = "匹配状态")
    private String matchStatusName;

    /**
     * 匹配时间
     */
    @ExcelProperty(value = "匹配时间")
    private LocalDateTime updatedTime;

    @ExcelProperty(value = "料号匹配结果")
    private String itemMatchStatusName;
    /**
     * 问题点
     */
    @ExcelProperty(value = "问题点")
    private String remark;


}
