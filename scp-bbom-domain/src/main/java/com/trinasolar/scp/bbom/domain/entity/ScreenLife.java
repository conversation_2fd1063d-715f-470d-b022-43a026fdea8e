package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 网版寿命信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Entity
@ToString
@Data
@Table(name = "bbom_screen_life")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_screen_life SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_screen_life SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ScreenLife extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型code")
    @Column(name = "battery_type")
    private String batteryType;

    /**
     * 网版料号
     */
    @ApiModelProperty(value = "网版料号")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 料号说明
     */
    @ApiModelProperty(value = "料号说明")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    @Column(name = "material_status")
    private String materialStatus;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    @Column(name = "base_place_id")
    private Long basePlaceId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 机台
     */
    @ApiModelProperty(value = "机台code")
    @Column(name = "machine")
    private String machine;

    /**
     * 寿命（万）
     */
    @ApiModelProperty(value = "寿命（万）")
    @Column(name = "lifetime")
    private String lifetime;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;

    /**
     * BOM寿命
     */
    @ApiModelProperty(value = "BOM寿命")
    @Column(name = "bom_life")
    private Integer bomLife;

    @ApiModelProperty(value = "主栅信息")
    @Column(name = "main_grid_info")
    private String mainGridInfo;

    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    @ApiModelProperty(value = "单玻")
    @Column(name = "single_glass_flag")
    private String singleGlassFlag;
}
