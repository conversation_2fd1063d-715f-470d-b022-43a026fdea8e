package com.trinasolar.scp.bbom.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 超级BOM模型
 *
 * <AUTHOR>
 * @date 2022/6/15 15:31
 **/
@Data
@NoArgsConstructor
@ApiModel("BillOfMaterial")
public class BillOfMaterialVO implements Serializable {

    private static final long serialVersionUID = 8989358018131680066L;

    @ApiModelProperty("库存组织")
    @JSONField(name = "organization_code")
    private String organizationCode;

    @JSONField(name = "bill_sequence_id")
    private String billSequenceId;

    @ApiModelProperty("装配件物料id")
    @JSONField(name = "assembly_item_id")
    private String assemblyItemId;

    @ApiModelProperty("装配件")
    @JSONField(name = "item_number")
    private String itemNumber;


    @ApiModelProperty("装配件描述")
    @JSONField(name = "item_description")
    private String itemDescription;

    @ApiModelProperty("装配件单位")
    @JSONField(name = "uom_code")
    private String uomCode;

    @ApiModelProperty("特殊需求单ID")
    @JSONField(name = "attribute10")
    private String attribute10;

    @ApiModelProperty("替代项")
    @JSONField(name = "alternate_bom_designator")
    private String alternateBomDesignator;

    @ApiModelProperty("替代项")
    @JSONField(name = "bm_attribute_category")
    private String bmAttributeCategory;

    @ApiModelProperty("BOM主料弹性域1")
    @JSONField(name = "bm_attribute1")
    private String bmAttribute1;

    @ApiModelProperty("BOM所属部门")
    @JSONField(name = "attribute2")
    private String attribute2;

    @ApiModelProperty("BOM主料弹性域3")
    @JSONField(name = "bm_attribute3")
    private String bmAttribute3;

    @ApiModelProperty("BOM主料弹性域4")
    @JSONField(name = "bm_attribute4")
    private String bmAttribute4;

    @ApiModelProperty("BOM主料弹性域5")
    @JSONField(name = "bm_attribute5")
    private String bmAttribute5;

    @ApiModelProperty("Q2料号")
    @JSONField(name = "attribute6")
    private String attribute6;

    @ApiModelProperty("Q3料号")
    @JSONField(name = "attribute7")
    private String attribute7;

    @ApiModelProperty("Qb料号")
    @JSONField(name = "attribute8")
    private String attribute8;

    @ApiModelProperty("联产品Q1料号")
    @JSONField(name = "attribute9")
    private String attribute9;

    @ApiModelProperty("BOM主料弹性域10")
    @JSONField(name = "bm_attribute10")
    private String bmAttribute10;

    @ApiModelProperty("BOM主料弹性域11")
    @JSONField(name = "bm_attribute11")
    private String bmAttribute11;

    @ApiModelProperty("BOM主料弹性域12")
    @JSONField(name = "bm_attribute12")
    private String bmAttribute12;

    @ApiModelProperty("BOM主料弹性域13")
    @JSONField(name = "bm_attribute13")
    private String bmAttribute13;

    @ApiModelProperty("BOM主料弹性域14")
    @JSONField(name = "bm_attribute14")
    private String bmAttribute14;

    @ApiModelProperty("BOM主料弹性域15")
    @JSONField(name = "bm_attribute15")
    private String bmAttribute15;

    @ApiModelProperty("最后更新时间")
    @JSONField(name = "last_update_date")
    private String lastUpdateDate;

    @ApiModelProperty("2是研发BOM，1是制造")
    @JSONField(name = "assembly_type")
    private String assemblyType;

    @ApiModelProperty("BOM组件唯一性ID")
    @JSONField(name = "bomponent_Sequence_Id")
    private String bomComponentSequenceId;

    @ApiModelProperty("BOM组件替代料ID")
    @JSONField(name = "substitute_component_id")
    private String substituteComponentId;

    @ApiModelProperty("主/替")
    @JSONField(name = "bsc_flag")
    private String bscFlag;

    @ApiModelProperty("结构")
    @JSONField(name = "bom_structures")
    private String bomStructures;

    @ApiModelProperty("序号")
    @JSONField(name = "item_num")
    private String itemNum;

    @ApiModelProperty("工序序号")
    @JSONField(name = "operation_seq_num")
    private String operationSeqNum;

    @ApiModelProperty("子件物料ID")
    @JSONField(name = "omponent_item_id")
    private String bomComponentItemId;

    @ApiModelProperty("子件")
    @JSONField(name = "component_item")
    private String componentItem;

    @ApiModelProperty("子件描述")
    @JSONField(name = "component_item_description")
    private String componentItemDescription;

    @ApiModelProperty("子件单位")
    @JSONField(name = "bic_item_uom")
    private String bicItemUom;

    @ApiModelProperty("单位用量")
    @JSONField(name = "component_quantity")
    private String componentQuantity;

    @ApiModelProperty("计划")
    @JSONField(name = "planning_factor")
    private String planningFactor;

    @ApiModelProperty("产出率")
    @JSONField(name = "component_yield_factor")
    private String componentYieldFactor;

    @ApiModelProperty("供应类型")
    @JSONField(name = "supply_type")
    private String supplyType;

    @ApiModelProperty("生效日期")
    @JSONField(name = "effectivity_date")
    private String effectivityDate;

    @ApiModelProperty("失效日期")
    @JSONField(name = "disable_date")
    private String disableDate;

    @ApiModelProperty("ECO")
    @JSONField(name = "change_notice")
    private String changeNotice;

    @ApiModelProperty("")
    @JSONField(name = "bc_attribute_category")
    private String bcAttributeCategory;

    @ApiModelProperty("基准 1/2 物料/批次")
    @JSONField(name = "basis_type")
    private String basisType;

    @ApiModelProperty("自动请求物料")
    @JSONField(name = "auto_request_material")
    private String autoRequestMaterial;

    @ApiModelProperty("自动请求物料")
    @JSONField(name = "ENFORCE_INT_REQUIREMENTS")
    private String enforceIntRequirements;


    @ApiModelProperty("包括在累计成本中")
    @JSONField(name = "include_in_cost_rollup")
    private String includeInCostRollup;

    @ApiModelProperty("组件料号类型")
    @JSONField(name = "bic_item_type")
    private String bicItemType;

    @ApiModelProperty("组件料号状态")
    @JSONField(name = "bic_item_status")
    private String bicItemStatus;

    @ApiModelProperty("供应类型代码")
    @JSONField(name = "wip_supply_type")
    private String wipSupplyType;

    @ApiModelProperty("组件弹性域1")
    @JSONField(name = "bc_attribute1")
    private String bcAttribute1;

    @ApiModelProperty("组件弹性域2")
    @JSONField(name = "bc_attribute2")
    private String bcAttribute2;

    @ApiModelProperty("组件弹性域3")
    @JSONField(name = "bc_attribute3")
    private String bcAttribute3;

    @ApiModelProperty("组件弹性域4")
    @JSONField(name = "bc_attribute4")
    private String bcAttribute4;

    @ApiModelProperty("组件弹性域5")
    @JSONField(name = "bc_attribute5")
    private String bcAttribute5;

    @ApiModelProperty("组件弹性域6")
    @JSONField(name = "bc_attribute6")
    private String bcAttribute6;

    @ApiModelProperty("组件弹性域7")
    @JSONField(name = "bc_attribute7")
    private String bcAttribute7;

    @ApiModelProperty("组件弹性域8")
    @JSONField(name = "bc_attribute8")
    private String bcAttribute8;

    @ApiModelProperty("组件弹性域9")
    @JSONField(name = "bc_attribute9")
    private String bcAttribute9;

    @ApiModelProperty("组件弹性域10")
    @JSONField(name = "bc_attribute10")
    private String bcAttribute10;

    @ApiModelProperty("组件弹性域11")
    @JSONField(name = "bc_attribute11")
    private String bcAttribute11;

    @ApiModelProperty("组件弹性域12")
    @JSONField(name = "bc_attribute12")
    private String bcAttribute12;

    @ApiModelProperty("组件弹性域13")
    @JSONField(name = "bc_attribute13")
    private String bcAttribute13;

    @ApiModelProperty("组件弹性域14")
    @JSONField(name = "bc_attribute14")
    private String bcAttribute14;

    @ApiModelProperty("组件弹性域15")
    @JSONField(name = "bc_attribute15")
    private String bcAttribute15;
}
