package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 兆瓦转换系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@Entity
@ToString
@Data
@Table(name = "bbom_conversion_coefficient_mw")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_conversion_coefficient_mw SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_conversion_coefficient_mw SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ConversionCoefficientMw extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    @Column(name = "battery_code")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    @Column(name = "category")
    private String category;

    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    @Column(name = "classify")
    private String classify;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 电池MW系数
     */
    @ApiModelProperty(value = "电池MW系数")
    @Column(name = "battery_mw_qty")
    private BigDecimal batteryMwQty;

    /**
     * 电池目标良率
     */
    @ApiModelProperty(value = "电池目标良率")
    @Column(name = "battery_efficiency_qty")
    private BigDecimal batteryEfficiencyQty;

    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    @Column(name = "unit_consumption")
    private BigDecimal unitConsumption;

    /**
     * 物料MW系数
     */
    @ApiModelProperty(value = "物料MW系数")
    @Column(name = "materiel_mw_qty")
    private BigDecimal materielMwQty;

    /**
     * 预警
     */
    @ApiModelProperty(value = "预警")
    @Column(name = "warning_reason")
    private String warningReason;
    /**
     * 库存组织id
     */
    @ApiModelProperty(value = "库存组织id")
    @Column(name = "organization_id")
    private Long organizationId;

}
