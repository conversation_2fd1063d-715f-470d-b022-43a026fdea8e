package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 电池主栅间距规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@Entity
@ToString
@Data
@Table(name = "bbom_main_grid_spacing_rule")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_main_grid_spacing_rule SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_main_grid_spacing_rule SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class MainGridSpacingRule extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "battery_type")
    private String batteryType;

    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    @Column(name = "item_workshop")
    private String itemWorkshop;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    @Column(name = "battery_workshop")
    private String batteryWorkshop;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_spacing")
    private String mainGridSpacing;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDate effectiveEndDate;
}
