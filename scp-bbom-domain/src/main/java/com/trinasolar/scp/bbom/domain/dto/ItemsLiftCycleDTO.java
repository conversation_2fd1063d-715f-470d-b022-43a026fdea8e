package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 物料生命周期
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "物料生命周期DTO对象", description = "DTO对象")
public class ItemsLiftCycleDTO extends BaseDTO {

    public static final long serialVersionUID = 8877092908004132252L;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String number;

    private String name;

    private String lifecycle;

    private String stage;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private Long view;

    private String version;

    @ApiModelProperty(value = "临时量产标识")
    private String isTemporaryOutput;
}
