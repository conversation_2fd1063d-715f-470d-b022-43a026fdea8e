package com.trinasolar.scp.bbom.domain.save;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BatteryScreenPlate保存参数", description = "保存参数")
public class BatteryScreenPlateSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 切换类型
     */
    @ApiModelProperty(value = "切换类型")
    private Long switchType;
    /**
     * 切换类型
     */
    @ApiModelProperty(value = "切换类型")
    private String switchTypeName;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshopName;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunitName;
    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    private String line;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workbench;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workbenchName;
    /**
     * 物料料号-新
     */
    @ApiModelProperty(value = "物料料号-新")
    private String itemCodeNew;
    /**
     * 物料料号-新说明
     */
    @ApiModelProperty(value = "物料料号-新说明")
    private String itemDescNew;
    /**
     * 物料料号-旧
     */
//    @ApiModelProperty(value = "物料料号-旧")
//    private String itemCodeOld;
    /**
     * 物料料号-旧说明
     */
//    @ApiModelProperty(value = "物料料号-旧说明")
//    private String itemDescOld;
    /**
     * 数量-新
     */
    @ApiModelProperty(value = "数量-新")
    private String numberNew;
    /**
     * 数量-旧
     */
    @ApiModelProperty(value = "数量-旧")
    private String numberOld;
    /**
     * 目标
     */
    @ApiModelProperty(value = "目标")
    private String target;
    /**
     * 旧网版库存
     */
//    @ApiModelProperty(value = "旧网版库存")
//    private String screenPlateInventoryOld;
    /**
     * 旧网板在途数量
     */
//    @ApiModelProperty(value = "旧网板在途数量")
//    private String screenPlateNumberOld;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 是否存在bom
     */
    @ApiModelProperty(value = "是否存在bom")
    private String isExistBom;
    /**
     * 网版类别新
     */
    @ApiModelProperty(value = "网版类别新")
    private String screenPlateVersionCategoryNew;
    /**
     * 网版类别旧
     */
    @ApiModelProperty(value = "网版类别旧")
    private String screenPlateVersionCategoryOld;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDateTime effectiveEndDate;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private String effectiveStartDates;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private String effectiveEndDates;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ApiModelProperty(value = "单玻")
    private String singleGlassFlag;

    @ApiModelProperty(value = "单玻名称")
    private String singleGlassFlagName;

    @ApiModelProperty(value = "栅线数量")
    private String gridsNumber;
}
