package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * ERP替代项表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 20:00:20
 */
@Data
@ApiModel(value = "ErpAlternateDesignator查询条件", description = "查询条件")
@Accessors(chain = true)
public class ErpAlternateDesignatorQuery extends PageDTO {
    /**
     * organization_id
     */
    @ApiModelProperty(value = "organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "description")
    private String description;
    /**
     * organization_ids
     */
    @ApiModelProperty(value = "organization_ids")
    private List<Long> organizationIds;
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
