package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 电池产品导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BatteryProductImport保存参数", description = "保存参数")
public class BatteryProductImportSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池产品编码
     */
    @ApiModelProperty(value = "电池产品编码")
    private String batteryCode;
    /**
     * 电池产品名称
     */
    @ApiModelProperty(value = "电池产品名称")
    private String batteryName;
    /**
     * 电池片晶体类型
     */
    @ApiModelProperty(value = "电池片晶体类型")
    private String batteryCrystalType;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 电池片尺寸编码
     */
    @ApiModelProperty(value = "电池片尺寸编码")
    private String batteryDimensionCode;
    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    private String numberMainGrids;
    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private String shardingNumber;
    /**
     * 预警提示
     */
    @ApiModelProperty(value = "预警提示")
    private String warningReason;
}
