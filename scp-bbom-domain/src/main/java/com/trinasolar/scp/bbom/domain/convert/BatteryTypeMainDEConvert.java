package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryTypeMain;
import com.trinasolar.scp.bbom.domain.excel.BatteryTypeMainExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型静态属性 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryTypeMainDEConvert extends BaseDEConvert<BatteryTypeMainDTO, BatteryTypeMain> {

    BatteryTypeMainDEConvert INSTANCE = Mappers.getMapper(BatteryTypeMainDEConvert.class);

    List<BatteryTypeMainExcelDTO> toExcelDTO(List<BatteryTypeMainDTO> dtos);

    BatteryTypeMainExcelDTO toExcelDTO(BatteryTypeMainDTO dto);

    List<BatteryTypeMainDTO> toDto(List<BatteryTypeMain> dtos);

    BatteryTypeMainDTO deepCopy(BatteryTypeMainDTO batteryTypeMainDTO);
}
