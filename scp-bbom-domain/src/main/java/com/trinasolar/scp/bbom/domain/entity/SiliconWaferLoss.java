package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 硅片损耗率信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Entity
@ToString
@Data
@Table(name = "bbom_silicon_wafer_loss")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_silicon_wafer_loss SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_silicon_wafer_loss SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class SiliconWaferLoss extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "battery_type")
    private String batteryType;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 硅片良率
     */
    @ApiModelProperty(value = "硅片良率")
    @Column(name = "silicon_wafer_yield")
    private String siliconWaferYield;

    /**
     * 硅片隔离率
     */
    @ApiModelProperty(value = "硅片隔离率")
    @Column(name = "silicon_isolation_rate")
    private String siliconIsolationRate;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;


}
