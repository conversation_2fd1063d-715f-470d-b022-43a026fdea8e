package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 电池车间
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "BatteryWorkShopQuery查询条件", description = "查询条件")
public class BatteryWorkShopQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "品类")
    private String segment3;
    @ApiModelProperty(value = "P/N型")
    private String segment1;
    @ApiModelProperty(value = "组件车间")
    private String compentWorkShop;


}
