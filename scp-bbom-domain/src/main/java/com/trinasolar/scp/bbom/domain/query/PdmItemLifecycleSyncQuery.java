package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * pdm物料生命周期原始表  组织ID + 物料编码 从中间表bbom_pdm_item_lifecycle获取
 */
@Data
@ApiModel(value = "PdmItemLifecycleSyncQuery查询条件", description = "查询条件")
@Accessors(chain = true)
public class PdmItemLifecycleSyncQuery implements Serializable {

    @ApiModelProperty(value = "料号")
    private String itemCode;

    @ApiModelProperty(value = "组织id")
    private Long orgId;
}
