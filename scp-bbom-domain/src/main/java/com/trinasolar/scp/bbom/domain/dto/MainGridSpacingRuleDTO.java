package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


/**
 * 电池主栅间距规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池主栅间距规则DTO对象", description = "DTO对象")
public class MainGridSpacingRuleDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;

    @ApiModelProperty(value = "电池类型")
    private String batteryTypeName;

    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    private String itemWorkshop;

    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    private String itemWorkshopName;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    private String batteryWorkshop;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    private String batteryWorkshopName;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpacing;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpacingName;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;
}
