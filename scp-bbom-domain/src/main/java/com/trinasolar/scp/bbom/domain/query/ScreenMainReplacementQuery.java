package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 网版主替查询条件
 *
 */
@Data
@ApiModel(value = "网版主替查询条件", description = "网版主替查询条件")
@Accessors(chain = true)
public class ScreenMainReplacementQuery extends PageDTO implements Serializable {
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String mainItemCode;


    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;


    /**
     * 网版料号
     */
    @ApiModelProperty(value = "网版料号")
    private String itemCode;
}
