package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/8/17
 */
@Data
@ApiModel(value = "时间查询条件")
@Accessors(chain = true)
public class DateTimeQuery {
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime fromDateTime;
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime toDateTime;
}
