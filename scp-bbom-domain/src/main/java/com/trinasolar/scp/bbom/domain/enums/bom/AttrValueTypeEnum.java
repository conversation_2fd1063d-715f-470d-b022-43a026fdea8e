package com.trinasolar.scp.bbom.domain.enums.bom;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@AllArgsConstructor
@Getter
public enum AttrValueTypeEnum {
    /**
     * 属性值类型id 1-文本 2-数字  3-值列表 4-日期
     */
    TEXT(1),
    NUMBER(2),
    LIST_VALUE(3),
    DATE_VALUE(4);
    private Integer value;

    public static AttrValueTypeEnum getByValue(Integer value) {
        for (AttrValueTypeEnum anEnum : AttrValueTypeEnum.values()) {
            if (Objects.equals(anEnum.getValue(), value)) {
                return anEnum;
            }
        }
        return null;
    }
}
