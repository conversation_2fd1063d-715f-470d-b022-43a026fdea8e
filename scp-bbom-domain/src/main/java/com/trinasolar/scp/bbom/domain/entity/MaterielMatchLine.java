package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Entity
@ToString
@Data
@Table(name = "bbom_materiel_match_line")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_materiel_match_line SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_materiel_match_line SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class MaterielMatchLine extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池物料号匹配ID
     */
    @ApiModelProperty(value = "电池物料号匹配ID")
    @Column(name = "header_id")
    private Long headerId;

    /**
     * 排产日期
     */
    @ApiModelProperty(value = "排产日期")
    @Column(name = "schedule_date")
    private LocalDate scheduleDate;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    @Column(name = "schedule_qty")
    private BigDecimal scheduleQty;

    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    @Column(name = "match_status")
    private String matchStatus;

    /**
     * 料号匹配状态
     */
    @ApiModelProperty(value = "料号匹配状态（匹配到多个料号或无匹配料号或匹配到一个料号）")
    @Column(name = "item_match_status")
    private String itemMatchStatus;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    @Column(name = "is_catch_production")
    private String isCatchProduction;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产Id")
    @Column(name = "is_catch_production_id")
    private Long isCatchProductionId;

    /**
     * 切换网版料号
     */
    @ApiModelProperty(value = "切换网版料号")
    @Column(name = "screen_plate_item_code")
    private String screenPlateItemCode;

    /**
     * 切换结束数据
     */
    @ApiModelProperty(value = "切换结束数据")
    @Column(name = "switch_end_date")
    private LocalDateTime switchEndDate;

    /**
     * 切换开始时间
     */
    @ApiModelProperty(value = "切换开始时间")
    @Column(name = "switch_start_date")
    private LocalDateTime switchStartDate;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "line")
    private BigDecimal line;

    /**
     * 电池片数量
     */
    @ApiModelProperty(value = "电池片数量")
    @Column(name = "cell_qty")
    private BigDecimal cellQty;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    @Column(name = "alternate_bom_designator")
    private String alternateBomDesignator;
    /**
     * 排产明细表主键
     */
    @ApiModelProperty(value = "排产明细表主键")
    @Column(name = "cell_production_plan_id")
    private Long cellProductionPlanId;
    /**
     * 手工指定标识(Y/N)
     */
    @ApiModelProperty(value = "手工指定标识(Y/N)")
    @Column(name = "hand_work_flag")
    private String handWorkFlag;
    /**
     * 网版物料说明
     */
    @ApiModelProperty(value = "网版物料说明")
    @Column(name = "screen_plate_item_code_desc")
    private String screenPlateItemCodeDesc;

    /**
     * 拆分标识(Y/空)
     */
    @ApiModelProperty(value = "拆分标识(Y/空)")
    @Column(name = "split_flag")
    private String splitFlag;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "final_version")
    private String finalVersion;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "battery_type")
    private String batteryType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "battery_type_id")
    private Long batteryTypeId;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;

    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    @Column(name = "special_area")
    private String specialArea;

    /**
     * 特殊区域Id
     */
    @ApiModelProperty(value = "特殊区域Id")
    @Column(name = "special_area_id")
    private Long specialAreaId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    @Column(name = "pcs_source_type")
    private String pcsSourceType;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    @Column(name = "pcs_source_level")
    private String pcsSourceLevel;

    /**
     * 是否有特殊要求
     */
    @ApiModelProperty(value = "是否有特殊要求")
    @Column(name = "is_special_requirements")
    private String isSpecialRequirements;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    @Column(name = "screen_manufacturer")
    private String screenManufacturer;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    @Column(name = "silicon_material_manufacturer")
    private String siliconMaterialManufacturer;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    @Column(name = "battery_manufacturer")
    private String batteryManufacturer;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    @Column(name = "silver_slurry_manufacturer")
    private String silverSlurryManufacturer;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    @Column(name = "low_resistance")
    private String lowResistance;

    /**
     * 硅片购买方式
     */
    @ApiModelProperty(value = "硅片购买方式")
    @Column(name = "silicon_wafer_purchase_method")
    private String siliconWaferPurchaseMethod;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    @Column(name = "demand_place")
    private String demandPlace;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;
    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    @Column(name = "process_category")
    private String processCategory;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    @Column(name = "production_grade")
    private String productionGrade;
    /**
     * 总线体数量
     */
    @ApiModelProperty(value = "总线体数量")
    @Column(name = "total_line")
    private BigDecimal totalLine;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    @Column(name = "old_month")
    private String oldMonth;

    /**
     * 新旧网版料号标识(N/O)
     */
    @ApiModelProperty(value = "新旧网版料号标识(N/O)")
    @Column(name = "new_or_old_item_flag")
    private String newOrOldItemFlag;

    /**
     * 网版料号为正电极的时候
     * 找背电极网版料号赋值
     */
    @ApiModelProperty(value = "存在的目的为了过滤5A料号")
    @Column(name = "screen_plate_code_filter")
    private String screenPlateCodeFilter;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 工艺路线  取手工指定的5A料号+对应的替代项在表bom_erp_operat_route是否存在，存在显示是，不存在显示否
     */
    @ApiModelProperty(value = "工艺路线", notes = "取手工指定的5A料号+对应的替代项在表bom_erp_operat_route是否存在，存在显示是，不存在显示否")
    @Column(name = "route")
    private String route;

    /**
     * 手工指定的5A对应的segment28
     */
    @ApiModelProperty(value = "手工指定的5A对应的segment28")
    @Column(name = "certified_models")
    private String certifiedModels;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    @Column(name = "silicon_wafer_properties")
    private String siliconWaferProperties;

    /**
     * 硅片属性 条件项
     */
    @ApiModelProperty(value = "条件项")
    @Column(name = "silicon_wafer_condition")
    private String siliconWaferCondition;

    /**
     * 硅片属性 值
     */
    @ApiModelProperty(value = "值")
    @Column(name = "silicon_wafer_value")
    private String siliconWaferValue;

    /**
     * 网版料号为正电极的时候
     * 找背电极网版料号赋值
     */
    @ApiModelProperty(value = "存在的目的为了过滤5A料号描述")
    @Column(name = "screen_plate_code_desc_filter")
    private String screenPlateCodeDescFilter;

    /**
     * 切换网版料号细栅
     */
    @ApiModelProperty(value = "切换网版料号细栅")
    @Column(name = "screen_plate_item_code_fine_grid")
    private String screenPlateItemCodeFineGrid;
    /**
     * 切换网版料号细栅
     */
    @ApiModelProperty(value = "切换网版料号细栅")
    @Column(name = "screen_plate_item_code_desc_fine_grid")
    private String screenPlateItemCodeDescFineGrid;
    /**
     * 背电极细栅
     */
    @ApiModelProperty(value = "背电极细栅")
    @Column(name = "screen_plate_code_filter_fine_grid")
    private String screenPlateCodeFilterFineGrid;
    /**
     * 背电极细栅
     */
    @ApiModelProperty(value = "背电极细栅")
    @Column(name = "screen_plate_code_desc_filter_fine_grid")
    private String screenPlateCodeDescFilterFineGrid;

    /**
     * 正电极网版细栅
     */
    @ApiModelProperty(value = "正电极网版细栅")
    @Column(name = "positive_electrode_screen_fine_grid")
    private String positiveElectrodeScreenFineGrid;

    /**
     * 背电极网版细栅
     */
    @ApiModelProperty(value = "背电极网版细栅")
    @Column(name = "negative_electrode_screen_fine_grid")
    private String negativeElectrodeScreenFineGrid;

    /**
     * 硅片品类
     */
    @Column(name = "wafer_category")
    @ApiModelProperty(value = "硅片品类")
    private String waferCategory;

    @ApiModelProperty(value = "法碳配比")
    @Column(name = "ratio_code")
    private String ratioCode;

    @ApiModelProperty(value = "ECS_CODE")
    @Column(name = "ecs_code")
    private String ecsCode;

    /**
     * 供应方式名称
     */
    @ApiModelProperty(value = "供应方式名称")
    @Column(name = "supply_mode_name")
    private String supplyModeName;

    /**
     * 主栅信息
     */
    @Column(name = "main_grid_info")
    @ApiModelProperty(value = "主栅信息")
    private String mainGridInfo;

    /**
     * 特殊订单
     */
    @Column(name = "special_order")
    @ApiModelProperty(value = "特殊订单")
    private String specialOrder;

    /**
     * 制造工艺
     */
    @Column(name = "manufacture_process")
    @ApiModelProperty(value = "制造工艺")
    private String manufactureProcess;
}
