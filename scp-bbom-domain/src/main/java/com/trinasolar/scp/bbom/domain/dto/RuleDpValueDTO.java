package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * BOM规则DP因子明细值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleDpValueDTO对象", description = "DTO对象")
public class RuleDpValueDTO extends BaseDTO {

    /**
     * 规则明细ID
     */
    @ApiModelProperty(value = "规则明细ID")
    private Long ruleDetailId;
    /**
     * 属性值ID，序列号生成
     */
    @ApiModelProperty(value = "属性值ID，序列号生成")
    private Long id;
    /**
     * 值类型，1-值  2-范围
     */
    @ApiModelProperty(value = "值类型，1-值  2-范围")
    private String valueType;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值Id")
    private Long attrValueId;
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止Id")
    private Long attrValueToId;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止")
    private String attrValueTo;

    @ApiModelProperty(value = "属性值名称")
    private String attrValueName;

    @ApiModelProperty(value = "属性值_止名称")
    private String attrValueToName;
}
