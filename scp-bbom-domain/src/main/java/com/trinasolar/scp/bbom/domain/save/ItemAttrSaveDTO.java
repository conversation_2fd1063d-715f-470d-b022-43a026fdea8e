package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ItemAttr保存参数", description = "保存参数")
public class ItemAttrSaveDTO extends TokenDTO implements Serializable {

    private static final long serialVersionUID = 6039407602817717573L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrAlias
     */
    @ApiModelProperty(value = "srcAttrAlias")
    private String srcAttrAlias;

    /**
     * srcCategorySegment4Id
     */
    @ApiModelProperty(value = "srcCategorySegment4Id")
    private String srcCategorySegment4Id;

    /**
     * srcCategorySegment4
     */
    @ApiModelProperty(value = "srcCategorySegment4")
    private String srcCategorySegment4;

    /**
     * srcAttrType
     */
    @ApiModelProperty(value = "srcAttrType")
    private String srcAttrType;

    /**
     * srcOptionFlag
     */
    @ApiModelProperty(value = "srcOptionFlag")
    private String srcOptionFlag;

    /**
     * srcAttrColumn
     */
    @ApiModelProperty(value = "srcAttrColumn")
    private String srcAttrColumn;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    private String language;
}
