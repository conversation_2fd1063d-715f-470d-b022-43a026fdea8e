package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物料属性字段Lov
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@ApiModel(value = "ItemAttrLov查询条件", description = "查询条件")
@Accessors(chain = true)
public class ItemAttrLovQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = -279185887704105892L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * lovId
     */
    @ApiModelProperty(value = "lovId")
    private String lovId;

    /**
     * lovName
     */
    @ApiModelProperty(value = "lovName")
    private String lovName;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrName
     */
    @ApiModelProperty(value = "srcAttrName")
    private String srcAttrName;

    /**
     * lovLineId
     */
    @ApiModelProperty(value = "lovLineId")
    private String lovLineId;

    /**
     * lovLineValue
     */
    @ApiModelProperty(value = "lovLineValue")
    private String lovLineValue;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    private String language;

    /**
     * isRequired
     */
    @ApiModelProperty(value = "isRequired")
    private String isRequired;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
