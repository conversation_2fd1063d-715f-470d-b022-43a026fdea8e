package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ItemsNewQuery
 * @Description 物料基础数据表
 * @Date 2023/12/22 14:08
 **/
@Data
@ApiModel(value = "Items查询条件", description = "查询条件")
@Accessors(chain = true)
public class ItemsNewQuery implements Serializable {

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private List<String> itemCodes;

}
