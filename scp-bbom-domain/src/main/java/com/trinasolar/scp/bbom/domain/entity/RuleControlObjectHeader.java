package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 规则管控对象头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:32
 */
@Entity
@ToString
@Data
@Table(name = "bbom_rule_control_object_header")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_rule_control_object_header SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_rule_control_object_header SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
public class RuleControlObjectHeader extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 规则行ID
     */
    @ApiModelProperty(value = "规则行ID")
    private Long ruleLineId;
    /**
     * 结构对象ID
     */
    @ApiModelProperty(value = "结构对象ID")
    private Long structObjectId;
    /**
     * 结构对象
     */
    @ApiModelProperty(value = "结构对象")
    private String structObject;
    /**
     * 管控对象ID
     */
    @ApiModelProperty(value = "管控对象ID")
    private Long controlObjectId;

    /**
     * 管控对象
     */
    @ApiModelProperty(value = "管控对象")
    private String controlObject;

    /**
     * BOM提示：是（Y）否（N）
     */
    @ApiModelProperty(value = "BOM提示：是（Y）否（N）")
    private String bomPrompt;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
