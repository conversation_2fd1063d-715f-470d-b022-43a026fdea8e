package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@ApiModel(value = "AMaterielMatchRule查询条件", description = "查询条件")
@Accessors(chain = true)
public class AMaterielMatchRuleQuery extends PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "电池车间")
    private String workshop;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
