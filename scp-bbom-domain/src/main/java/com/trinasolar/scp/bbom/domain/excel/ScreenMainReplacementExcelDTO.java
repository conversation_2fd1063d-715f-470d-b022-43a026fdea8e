package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class ScreenMainReplacementExcelDTO {

    /**
     * 电池车间
     */
    @ExcelProperty(value = "电池车间")
    private String workshop;

    /**
     * 网版料号
     */
    @ExcelProperty(value = "网版料号")
    private String itemCode;

    /**
     * 网版物料描述
     */
    @ExcelProperty(value = "网版物料描述")
    private String itemDesc;

    /**
     * 供应商简称
     */
    @ExcelProperty(value = "供应商简称")
    private String vendorAltName;

    @ExcelProperty(value = "正背面")
    private String frontOrBack;

    @ExcelProperty(value = "工位")
    private String opPosition;

    /**
     * 是否主料
     */
    @ExcelProperty(value = "是否主料")
    private String substituteFlag;

    /**
     * 是否启用
     */
    @ExcelProperty(value = "是否启用")
    private String substituteEnableFlag;

    /**
     * 电池料号
     */
    @ExcelProperty(value = "电池料号")
    private String mainItemCode;

    /**
     * 电池物料描述
     */
    @ExcelProperty(value = "电池物料描述")
    private String mainItemDesc;

    /**
     * BOM替代项
     */
    @ExcelProperty(value = "BOM替代项")
    private String alternateBomDesignator;
}
