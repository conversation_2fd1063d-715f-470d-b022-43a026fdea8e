package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 网版寿命信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@ApiModel(value = "ScreenLife查询条件", description = "查询条件")
@Accessors(chain = true)
public class ScreenLifeQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 网版料号
     */
    @ApiModelProperty(value = "网版料号")
    private String itemCode;
    /**
     * 料号说明
     */
    @ApiModelProperty(value = "料号说明")
    private String itemDesc;
    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    private String materialStatus;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String machine;
    /**
     * 寿命（万）
     */
    @ApiModelProperty(value = "寿命（万）")
    private String lifetime;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * BOM寿命
     */
    @ApiModelProperty(value = "BOM寿命")
    private Integer bomLife;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
