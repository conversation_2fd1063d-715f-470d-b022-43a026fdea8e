package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * BOM规则头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 15:49:10
 */
@Data
@ApiModel(value = "RuleHeader查询条件", description = "查询条件")
public class RuleHeaderQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "规则分类ID，从LOV取LOV行ID")
    private Long ruleCategoryId;

    @ApiModelProperty(value = "规则编号")
    private String ruleNumber;

    @ApiModelProperty(value = "管控主体ID,取对应LOV行ID")
    private Long controlSubjectId;

    @ApiModelProperty(value = "管控主体名称")
    private String controlSubjectName;

    @ApiModelProperty(value = "管控对象，取组件分料号动态配置标识下的组件分料号属性ID")
    private Long controlObjectId;

    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    @ApiModelProperty(value = "时间段: present 当前, presentAndFuture 现在和未来")
    private String timeSlot;

    @ApiModelProperty("规则编码id")
    private List<String> ruleNumberList;

    @ApiModelProperty(value = "料号")
    private String itemCode;
    @ApiModelProperty(value = "料号ID")
    private Long itemCodeId;
    @ApiModelProperty(value = "导出名称")
    private String exportName;
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
