package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 客户;
 *
 * <AUTHOR> darke
 * @date : 2022-4-24
 */
@ApiModel(value = "客户DTO", description = "客户DTO")
@Data
public class CustomerDTO extends PageDTO implements Serializable {
    /**
     * 主键
     */
    // @ApiModelProperty(value = "主键")
    // private Long id;

    @ApiModelProperty(value = "ERP客户ID")
    private Long customerId;
    /**
     * 客户编码
     */
    @NotBlank(groups = {ValidGroups.Save.class, ValidGroups.Submit.class}, message = "{scp-dp-api.customer.customerCode.NotBlank}")
    @ApiModelProperty(value = "客户编码")
    private String customerCode;
    /**
     * 客户名称
     */
    @NotBlank(groups = {ValidGroups.Submit.class}, message = "{scp-dp-api.customer.customerName.NotBlank}")
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 客户简称
     */
    @NotBlank(groups = {ValidGroups.Submit.class}, message = "{scp-dp-api.customer.customerName.NotBlank}")
    @ApiModelProperty(value = "客户简称")
    private String customerNm;

    @ApiModelProperty(value = "客户账户编号/MDM代码")
    private String accountNumber;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String areaNo;
    /**
     * 区域细分
     */
    @ApiModelProperty(value = "区域细分")
    private String areaSubNo;
    /**
     * 业务员
     */
    @ApiModelProperty(value = "业务员")
    private String salesmanId;
    /**
     * 客户类别编码
     */
    @ApiModelProperty(value = "客户类别编码")
    private String customerCategoryCode;
    /**
     * 客户类型编码
     */
    @ApiModelProperty(value = "客户类型编码")
    private String customerTypeCode;
    /**
     * 客户等级编码
     */
    @ApiModelProperty(value = "客户等级编码")
    private String customerGradingCode;
    /**
     * 是否生效
     */
    @ApiModelProperty(value = "是否生效")
    private Long enableFlag;
    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    private Date startDate;
    /**
     * 截止日期
     */
    @ApiModelProperty(value = "截止日期")
    private Date endDate;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "ERP最后更新时间")
    private LocalDateTime erpLastUpdateDate;

    @ApiModelProperty(value = "是否分页查询")
    private Boolean pageFlag = false;
}
