package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 电池类型静态属性
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatteryTypeMainExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型编码
     */
    @ExcelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 晶体类型
     */
    @ExcelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * PN型
     */
    @ExcelProperty(value = "PN型")
    private String pOrN;
    /**
     * 品类
     */
    @ExcelProperty(value = "品类")
    private String category;
    /**
     * 单双面
     */
    @ExcelProperty(value = "单双面")
    private String singleDoubleFace;
    /**
     * 主栅数
     */
    @ExcelProperty(value = "主栅数")
    private String numberMainGrids;
    /**
     * 分片方式
     */
    @ExcelProperty(value = "分片方式")
    private String shardingMode;
    /**
     * 产品分类
     */
    @ExcelProperty(value = "产品分类")
    private String productClassification;
}
