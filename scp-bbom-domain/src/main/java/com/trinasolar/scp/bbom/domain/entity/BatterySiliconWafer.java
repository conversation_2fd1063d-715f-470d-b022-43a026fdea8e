package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 电池类型动态属性-硅片
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Entity
@ToString
@Data
@Table(name = "bbom_battery_silicon_wafer")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_battery_silicon_wafer SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_battery_silicon_wafer SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class BatterySiliconWafer extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    @Column(name = "battery_code")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;

    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    @Column(name = "silicon_wafer_properties")
    private String siliconWaferProperties;

    /**
     * 条件项
     */
    @ApiModelProperty(value = "条件项")
    @Column(name = "condition_item")
    private String conditionItem;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    @Column(name = "battery_value")
    private String batteryValue;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    @Column(name = "effective_start_date")
    private LocalDate effectiveStartDate;

    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    @Column(name = "effective_end_date")
    private LocalDate effectiveEndDate;

    /**
     * 旧硅片属性
     */
    @ApiModelProperty(value = "旧硅片属性")
    @Column(name = "old_silicon_wafer_properties")
    private String oldSiliconWaferProperties;

    /**
     * 旧条件项
     */
    @ApiModelProperty(value = "旧条件项")
    @Column(name = "old_condition_item")
    private String oldConditionItem;

    /**
     * 旧值
     */
    @ApiModelProperty(value = "旧值")
    @Column(name = "old_battery_value")
    private String oldBatteryValue;

    /**
     * 新线体数量
     */
    @ApiModelProperty(value = "新线体数量")
    @Column(name = "line_qty")
    private Integer lineQty;

    /**
     * 新硅片料号
     */
    @ApiModelProperty(value = "新硅片料号")
    @Column(name = "item_code_new")
    private String itemCodeNew;

    /**
     * 是否低碳
     */
    @ApiModelProperty(value = "是否低碳")
    @Column(name = "low_carbon_flag")
    private String lowCarbonFlag;

    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    @Column(name = "wafer_thickness")
    private String waferThickness;

    /**
     * 硅片品类
     */
    @ApiModelProperty(value = "硅片品类")
    @Column(name = "wafer_category")
    private String waferCategory;

    @ApiModelProperty(value = "硅片ECS CODE")
    @Column(name = "wafer_ecs_code")
    private String waferEcsCode;
}
