package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 硅片损耗率信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SiliconWaferLossExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型
     */
    @ExcelIgnore
    private String batteryType;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String batteryTypeName;
    /**
     * 生产基地
     */
    @ExcelIgnore
    private String basePlace;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 生产车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 硅片良率
     */
    @ExcelProperty(value = "电池良率")
    private String siliconWaferYield;
    /**
     * 硅片隔离率
     */
    @ExcelProperty(value = "硅片隔离率")
    private String siliconIsolationRate;
    @ExcelProperty("创建人")
    private String createdBy;
    @ExcelProperty("创建时间")
    private LocalDateTime createdTime;
}
