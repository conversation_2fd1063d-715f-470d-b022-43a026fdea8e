package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 物料属性字段Lov
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemAttrLovExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * lovId
     */
    @ExcelProperty(value = "lovId")
    private String lovId;

    /**
     * lovName
     */
    @ExcelProperty(value = "lovName")
    private String lovName;

    /**
     * srcAttrId
     */
    @ExcelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrName
     */
    @ExcelProperty(value = "srcAttrName")
    private String srcAttrName;

    /**
     * lovLineId
     */
    @ExcelProperty(value = "lovLineId")
    private String lovLineId;

    /**
     * lovLineValue
     */
    @ExcelProperty(value = "lovLineValue")
    private String lovLineValue;

    /**
     * language
     */
    @ExcelProperty(value = "language")
    private String language;

    /**
     * isRequired
     */
    @ExcelProperty(value = "isRequired")
    private String isRequired;
}
