package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 规则管控对象详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExpressRuleDetailDTO {
    /**
     * 来源栏位
     */
    private String sourceColumn;
    /**
     * 运算符 1：包含  2：排除  3：等于
     */
    @ApiModelProperty(value = "运算符")
    private String attrOperator;

    @ApiModelProperty(value = "1：材料因子，2：DP因子")
    private String detailType;

    @ApiModelProperty(value = "值")
    private List<ExpressRuleValueDTO> values;
}
