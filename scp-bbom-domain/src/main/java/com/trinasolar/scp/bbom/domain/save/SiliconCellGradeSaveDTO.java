package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 硅片等级与电池等级映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SiliconCellGrade保存参数", description = "保存参数")
public class SiliconCellGradeSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferProperties;
    /**
     * 硅片属性条件项
     */
    @ApiModelProperty(value = "硅片属性条件项")
    private String siliconWaferConditionalItem;
    /**
     * 硅片属性值
     */
    @ApiModelProperty(value = "硅片属性值")
    private String siliconWaferValue;
    /**
     * 电池属性
     */
    @ApiModelProperty(value = "电池属性")
    private String batteryProperties;
    /**
     * 电池属性条件项
     */
    @ApiModelProperty(value = "电池属性条件项")
    private String batteryConditionalItem;
    /**
     * 电池属性值
     */
    @ApiModelProperty(value = "电池属性值")
    private String batteryValue;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
}
