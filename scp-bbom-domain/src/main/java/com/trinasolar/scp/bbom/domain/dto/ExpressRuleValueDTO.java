package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExpressRuleValueDTO {
    /**
     * 值类型，1-值  2-范围
     */
    @ApiModelProperty(value = "属性值类型id 1-文本 2-数字  3-值列表 4-日期")
    private Integer valueType;
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止")
    private String attrValueTo;
}
