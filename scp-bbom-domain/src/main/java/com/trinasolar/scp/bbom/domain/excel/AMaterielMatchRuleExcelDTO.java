package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AMaterielMatchRuleExcelDTO {
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "国内/海外")
    @ExcelIgnore
    private String isOversea;

    @ExcelProperty(value = "国内/海外")
    private String isOverseaName;

    @ExcelProperty(value = "生产车间")
    @ExcelIgnore
    private String workshop;

    @ExcelProperty(value = "电池类型")
    @ExcelIgnore
    private String cellsType;

    @ExcelProperty(value = "电池类型")
    private String cellsTypeName;

    @ExcelProperty(value = "实际片厚")
    @ExcelIgnore
    private String actualPieceThickness;

    @ExcelProperty(value = "实际片厚")
    private String actualPieceThicknessName;

    @ExcelProperty(value = "主栅两端形状")
    @ExcelIgnore
    private String mainGridBothShape;

    @ExcelProperty(value = "主栅两端形状")
    private String mainGridBothShapeName;

    @ExcelProperty(value = "A-料号")
    private String aItemCode;
}
