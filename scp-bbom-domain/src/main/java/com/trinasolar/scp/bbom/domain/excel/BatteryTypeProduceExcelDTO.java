package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 电池类型动态属性-产出电池类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatteryTypeProduceExcelDTO {

    /**
     * 电池类型编码
     */
    @ExcelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 名称
     */
    @ExcelProperty(value = "产出电池类型")
    private String name;


}
