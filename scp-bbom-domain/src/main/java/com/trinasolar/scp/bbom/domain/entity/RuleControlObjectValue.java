package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
@Entity
@ToString
@Data
@Table(name = "bbom_rule_control_object_value")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_rule_control_object_value SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_rule_control_object_value SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
public class RuleControlObjectValue extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 规则管控对象详情ID
     */
    @ApiModelProperty(value = "规则管控对象详情ID")
    private Long ruleControlObjectDetailId;

    /**
     * 值类型，1-值  2-范围
     */
    @ApiModelProperty(value = "值类型，1-值  2-范围")
    private String valueType;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值Id")
    private Long attrValueId;
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止Id")
    private Long attrValueToId;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止")
    private String attrValueTo;


}
