package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * BOM规则头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleHeaderExcelDTO {

    /**
     * 规则头ID 序列号生成
     */
    @ExcelProperty(value = "规则头ID 序列号生成")
    private Long ruleHeaderId;
    /**
     * 规则分类ID，从LOV取LOV行ID
     */
    @ExcelProperty(value = "规则分类ID，从LOV取LOV行ID")
    private Long ruleCategoryId;
    /**
     * 规则编号
     */
    @ExcelProperty(value = "规则编号")
    private String ruleNumber;
    /**
     * 规则名
     */
    @ExcelProperty(value = "规则名")
    private String ruleName;
    /**
     * 管控主体ID,取对应LOV行ID
     */
    @ExcelProperty(value = "管控主体ID,取对应LOV行ID")
    private Long controlSubjectId;
    /**
     * 管控主体名称
     */
    @ExcelProperty(value = "管控主体名称")
    private String controlSubjectName;
    /**
     * 管控对象，取组件分料号动态配置标识下的组件分料号属性ID
     */
    @ExcelProperty(value = "管控对象，取组件分料号动态配置标识下的组件分料号属性ID")
    private Long controlObjectId;
    /**
     * 管控对象名称
     */
    @ExcelProperty(value = "管控对象名称")
    private String controlObjectName;
    /**
     * 管控目的,取对应LOV行ID
     */
    @ExcelProperty(value = "管控目的,取对应LOV行ID")
    private Long controlPurposeId;
    /**
     * 管控目的
     */
    @ExcelProperty(value = "管控目的")
    private String controlPurposeName;
    /**
     * 有效标识
     */
    @ExcelProperty(value = "有效标识")
    private String enableFlag;
    /**
     * 有效日期_起
     */
    @ExcelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ExcelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute1;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute2;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute3;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute4;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute5;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute6;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute7;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute8;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute9;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute10;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute11;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute12;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute13;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute14;
    /**
     * 扩展属性
     */
    @ExcelProperty(value = "扩展属性")
    private String attribute15;
}
