package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 自产低效电池比例
 */
@Entity
@ToString
@Data
@Table(name = "bbom_low_efficiency_cell_percent")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_low_efficiency_cell_percent SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_low_efficiency_cell_percent SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class LowEfficiencyCellPercent extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private String year;


    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type")
    private String cellType;


    /**
     * 1月占比
     */
    @ApiModelProperty(value = "1月占比")
    @Column(name = "m1_percent")
    private String m1Percent;

    /**
     * 2月占比
     */
    @ApiModelProperty(value = "2月占比")
    @Column(name = "m2_percent")
    private String m2Percent;

    /**
     * 3月占比
     */
    @ApiModelProperty(value = "3月占比")
    @Column(name = "m3_percent")
    private String m3Percent;

    /**
     * 4月占比
     */
    @ApiModelProperty(value = "4月占比")
    @Column(name = "m4_percent")
    private String m4Percent;

    /**
     * 5月占比
     */
    @ApiModelProperty(value = "5月占比")
    @Column(name = "m5_percent")
    private String m5Percent;

    /**
     * 6月占比
     */
    @ApiModelProperty(value = "6月占比")
    @Column(name = "m6_percent")
    private String m6Percent;

    /**
     * 7月占比
     */
    @ApiModelProperty(value = "7月占比")
    @Column(name = "m7_percent")
    private String m7Percent;

    /**
     * 8月占比
     */
    @ApiModelProperty(value = "8月占比")
    @Column(name = "m8_percent")
    private String m8Percent;

    /**
     * 9月占比
     */
    @ApiModelProperty(value = "9月占比")
    @Column(name = "m9_percent")
    private String m9Percent;

    /**
     * 10月占比
     */
    @ApiModelProperty(value = "10月占比")
    @Column(name = "m10_percent")
    private String m10Percent;

    /**
     * 11月占比
     */
    @ApiModelProperty(value = "11月占比")
    @Column(name = "m11_percent")
    private String m11Percent;

    /**
     * 12月占比
     */
    @ApiModelProperty(value = "12月占比")
    @Column(name = "m12_percent")
    private String m12Percent;


}
