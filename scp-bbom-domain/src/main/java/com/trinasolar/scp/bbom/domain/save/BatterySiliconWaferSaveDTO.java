package com.trinasolar.scp.bbom.domain.save;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * 电池类型动态属性-硅片
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BatterySiliconWafer保存参数", description = "保存参数")
public class BatterySiliconWaferSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshopName;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunitName;
    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferProperties;
    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferPropertiesName;
    /**
     * 条件项
     */
    @ApiModelProperty(value = "条件项")
    private String conditionItem;
    /**
     * 条件项
     */
    @ApiModelProperty(value = "条件项")
    private String conditionItemName;
    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String batteryValue;
    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String batteryValueName;
    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDate effectiveStartDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDate effectiveEndDate;

    /**
     * 旧硅片属性
     */
    @ApiModelProperty(value = "旧硅片属性")
    private String oldSiliconWaferProperties;

    /**
     * 旧条件项
     */
    @ApiModelProperty(value = "旧条件项")
    private String oldConditionItem;

    /**
     * 旧值
     */
    @ApiModelProperty(value = "旧值")
    private String oldBatteryValue;

    /**
     * 新线体数量
     */
    @ApiModelProperty(value = "新线体数量")
    private Integer lineQty;

    /**
     * 新硅片料号
     */
    @ExcelProperty(value = "新硅片料号")
    private String itemCodeNew;

    /**
     * 是否低碳
     */
    @ApiModelProperty(value = "是否低碳")
    private String lowCarbonFlag;

    /**
     * 硅片厚度
     */
    @ApiModelProperty(value = "硅片厚度")
    private String waferThickness;

    /**
     * 硅片品类
     */
    @ApiModelProperty(value = "硅片品类")
    private String waferCategory;

    @ApiModelProperty(value = "硅片ECS CODE")
    private String waferEcsCode;

    @ApiModelProperty(value = "数据下标")
    private Integer index;
}
