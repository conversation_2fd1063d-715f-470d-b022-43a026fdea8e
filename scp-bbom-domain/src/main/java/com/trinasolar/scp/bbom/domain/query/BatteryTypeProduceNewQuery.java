package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryTypeProduceNewQuery
 * @Description
 * @Date 2023/12/22 15:12
 **/
@Data
@ApiModel(value = "BatteryTypeProduce查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatteryTypeProduceNewQuery implements Serializable {

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private List<String> batteryType;

}
