package com.trinasolar.scp.bbom.domain.excel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * excel头对象
 *
 * <AUTHOR>
 * @date 2022年9月21日15:25:25
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("表头")
@Data
public class ExcelHead {
    @ApiModelProperty("是否合并")
    private boolean merge = true;
    @ApiModelProperty("表格的字段")
    private String prop;
    @ApiModelProperty("表格字段名称")
    private String label;
    @ApiModelProperty("字段类型：input,textarea,number,radio,checkbox,time,date,rate,color,select,switch,slider,text,link,imgupload,fileupload,table,grid,report,divider")
    private String type;
    @ApiModelProperty("是否为导出excel字段")
    private Boolean excel;
    @ApiModelProperty("宽度")
    private String width;
    @ApiModelProperty("后台自定义")
    private Boolean custom = false;
    @ApiModelProperty("子表单")
    private List<ExcelHead> children;

    public ExcelHead(String prop, String label) {
        this.prop = prop;
        this.label = label;
        this.type = "input";
        this.excel = true;
        this.width = "auto";
    }
}

