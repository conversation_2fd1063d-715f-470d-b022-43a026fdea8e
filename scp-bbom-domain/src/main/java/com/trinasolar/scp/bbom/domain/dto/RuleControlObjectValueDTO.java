package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleControlObjectValueDTO对象", description = "DTO对象")
public class RuleControlObjectValueDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 规则管控对象详情ID
     */
    @ApiModelProperty(value = "规则管控对象详情ID")
    private Long ruleControlObjectDetailId;
    /**
     * 值类型，1-值  2-范围
     */
    @ApiModelProperty(value = "值类型，1-值  2-范围")
    private String valueType;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值Id")
    private Long attrValueId;
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止Id")
    private Long attrValueToId;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止")
    private String attrValueTo;

    @ApiModelProperty(value = "属性值名称")
    private String attrValueName;

    @ApiModelProperty(value = "属性值_止名称")
    private String attrValueToName;
}
