package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ApiModel(value = "MainReplacementQuery查询条件", description = "查询条件")
@Accessors(chain = true)
public class MainReplacementQuery extends PageDTO implements Serializable {
    @ApiModelProperty(value = "电池型号")
    private String batteryType;

    @ApiModelProperty(value = "机台")
    private String workbench;

    @ApiModelProperty(value = "主替标识")
    private String subFlag;

    @ApiModelProperty(value = "网版料号")
    private String itemCode;

    @ApiModelProperty(value = "车间")
    private String workShop;
}
