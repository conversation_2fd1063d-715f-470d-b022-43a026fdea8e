package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.SiliconCellGradeDTO;
import com.trinasolar.scp.bbom.domain.entity.SiliconCellGrade;
import com.trinasolar.scp.bbom.domain.excel.SiliconCellGradeExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 硅片等级与电池等级映射 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SiliconCellGradeDEConvert extends BaseDEConvert<SiliconCellGradeDTO, SiliconCellGrade> {

    SiliconCellGradeDEConvert INSTANCE = Mappers.getMapper(SiliconCellGradeDEConvert.class);

    List<SiliconCellGradeExcelDTO> toExcelDTO(List<SiliconCellGradeDTO> dtos);

    SiliconCellGradeExcelDTO toExcelDTO(SiliconCellGradeDTO dto);
}
