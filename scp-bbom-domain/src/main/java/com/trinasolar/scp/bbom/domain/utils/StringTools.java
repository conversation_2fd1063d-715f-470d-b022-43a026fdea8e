package com.trinasolar.scp.bbom.domain.utils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Optional;

public class StringTools {
    public static String joinWith(String separator, String... values) {

        if (values == null || values.length == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        Arrays.stream(values).forEach(value -> {
            value = Optional.ofNullable(value).orElse("");
            sb.append(separator);
            sb.append(value);
        });
        return sb.substring(1);
    }

    public static boolean isInRange(BigDecimal num, BigDecimal min, BigDecimal max) {
        //(num >= min) && (num <= max)
        return num.compareTo(min) >= 0 && num.compareTo(max) <= 0;
    }

}
