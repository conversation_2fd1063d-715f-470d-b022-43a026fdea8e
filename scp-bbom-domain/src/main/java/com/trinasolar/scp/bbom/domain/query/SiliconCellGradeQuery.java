package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 硅片等级与电池等级映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Data
@ApiModel(value = "SiliconCellGrade查询条件", description = "查询条件")
@Accessors(chain = true)
public class SiliconCellGradeQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferProperties;
    /**
     * 硅片属性条件项
     */
    @ApiModelProperty(value = "硅片属性条件项")
    private String siliconWaferConditionalItem;
    /**
     * 硅片属性值
     */
    @ApiModelProperty(value = "硅片属性值")
    private String siliconWaferValue;
    /**
     * 电池属性
     */
    @ApiModelProperty(value = "电池属性")
    private String batteryProperties;
    /**
     * 电池属性条件项
     */
    @ApiModelProperty(value = "电池属性条件项")
    private String batteryConditionalItem;
    /**
     * 电池属性值
     */
    @ApiModelProperty(value = "电池属性值")
    private String batteryValue;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
