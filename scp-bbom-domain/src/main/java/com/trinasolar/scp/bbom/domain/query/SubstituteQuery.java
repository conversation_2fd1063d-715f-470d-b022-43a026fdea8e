/**
 * @Function: SubstituteQuery.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 16:57:45
 */
package com.trinasolar.scp.bbom.domain.query;

import lombok.Data;

/**
 * @Function: SubstituteQuery.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 16:57:45
 */
@Data
public class SubstituteQuery {

    private Long organizationId;

    private String itemCode;

    private String alternateBomDesignator;
}
