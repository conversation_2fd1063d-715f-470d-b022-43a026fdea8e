package com.trinasolar.scp.bbom.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Pair;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/1
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ScreenMainReplacementDTO {
    @ApiModelProperty(value = "电池型号")
    private String batteryType;

    @ApiModelProperty(value = "分片方式")
    private String shardingMode;

    @ApiModelProperty(value = "小区域")
    private String regionalCountry;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "单玻")
    private String singleGlass;

    @ApiModelProperty(value = "机台")
    private String workbench;

    @ApiModelProperty(value = "主替标识")
    private String subFlag;

    @ApiModelProperty(value = "规格")
    private String model;

    @ApiModelProperty(value = "网版料号")
    private String itemCode;

    @ApiModelProperty(value = "料号描述")
    private String itemDescription;

    @ApiModelProperty(value = "厂商")
    private String vendor;

    @ApiModelProperty(value = "车间明细")
    private List<MainReplacementDetailDTO> detailDTOS;

    /**
     * 动态列
     */
    @ApiModelProperty(value = "动态列")
    private List<Pair<String, String>> subList;
    /**
     * 动态列
     */
    @ApiModelProperty(value = "动态列")
    private Map<String, String> subMap;

    public Map<String, Object> convertMap() {
        Map<String, Object> objectMap = BeanUtil.beanToMap(this);
        if (MapUtils.isNotEmpty(this.subMap)) {
            Map<String, Object> newMap = Maps.newHashMap();
            this.subList.forEach(item -> {
                String key = StringUtils.join("subMap_", item.getKey(), "_", item.getValue());
                newMap.put(key, this.subMap.get(key));
            });
            objectMap.putAll(newMap);
        }
        return objectMap;
    }
}
