package com.trinasolar.scp.bbom.domain.dto.feign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * VIEW
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 10:47:29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ExternalAttrMapViewDTO对象", description = "DTO对象")
public class ExternalAttrMapViewDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 源系统属性ID
     */
    @ApiModelProperty(value = "源系统属性ID")
    private String srcAttrId;

    /**
     * 属性类型中文名
     */
    @ApiModelProperty(value = "属性类型中文名")
    private String srcAttrValue;

    /**
     * 源系统属性别名
     */
    @ApiModelProperty(value = "源系统属性别名")
    private String srcAttrAlias;

    /**
     * 源系统属性四级分类ID
     */
    @ApiModelProperty(value = "源系统属性四级分类ID")
    private String srcCategorySegment4Id;

    /**
     * 源系统属性四级分类
     */
    @ApiModelProperty(value = "源系统属性四级分类")
    private String srcCategorySegment4;

    /**
     * 源系统属性类型
     */
    @ApiModelProperty(value = "源系统属性类型")
    private String srcAttrType;

    /**
     * 对应源系统属性字段名
     */
    @ApiModelProperty(value = "对应源系统属性字段名")
    private String srcAttrColumn;

    /**
     * 属性行id
     */
    @ApiModelProperty(value = "属性行id")
    private Long attrLineId;

    /**
     * 来源字段名
     */
    @ApiModelProperty(value = "来源字段名")
    private String sourceColumn;
}
