package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 电池类型动态属性-网版
 * 邮件推送使用
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatteryScreenPlateExcelMailDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型编码
     */
    @ExcelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 切换类型
     */
    @ExcelIgnore
    private Long switchType;
    /**
     * 切换类型
     */
    @ExcelProperty(value = "切换类型")
    private String switchTypeName;
    /**
     * 基地
     */
    @ExcelIgnore
    private String basePlace;

    @ExcelProperty(value = "基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 车间
     */
    @ExcelProperty(value = "车间")
    private String workshopName;
    /**
     * 生产单元
     */
    @ExcelIgnore
    private String workunit;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunitName;
    /**
     * 线体
     */
    @ExcelProperty(value = "线体数量")
    private String line;
    /**
     * 机台
     */
    @ExcelIgnore
    private String workbench;
    /**
     * 机台
     */
    @ExcelProperty(value = "机台")
    private String workbenchName;
    /**
     * 物料料号-旧
     */
    @ExcelProperty(value = "网版料号-旧")
    private String itemCodeOld;
    /**
     * 物料料号-旧说明
     */
    @ExcelProperty(value = "网版料号-旧说明")
    private String itemDescOld;
    /**
     * 物料料号-新
     */
    @ExcelProperty(value = "网版料号-新")
    private String itemCodeNew;
    /**
     * 物料料号-新说明
     */
    @ExcelProperty(value = "网版料号-新说明")
    private String itemDescNew;
    /**
     * 数量-旧
     */
    @ExcelProperty(value = "旧网板消耗数量")
    private String numberOld;
    /**
     * 数量-新
     */
    @ExcelProperty(value = "新网板消耗数量")
    private String numberNew;
    /**
     * 旧网版库存
     */
    @ExcelProperty(value = "旧网版库存")
    private String screenPlateInventoryOld;

    /**
     * 旧网板在途数量
     */
    @ExcelProperty(value = "旧网板在途数量")
    private String screenPlateNumberOld;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 有效期起
     */
    @ExcelProperty(value = "有效期起")
    private String effectiveStartDates;
    /**
     * 有效期止
     */
    @ExcelProperty(value = "有效期止")
    private String effectiveEndDates;

    /**
     * 切换目标
     */
    @ExcelProperty(value = "切换目标")
    private String target;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String vendorName;

}
