package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@ApiModel(value = "BatteryScreenPlate查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatteryScreenPlateQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = -6897046869568720203L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;

    /**
     * 切换类型
     */
    @ApiModelProperty(value = "切换类型")
    private Long switchType;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    private String line;

    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workbench;

    /**
     * 物料料号-新
     */
    @ApiModelProperty(value = "物料料号-新")
    private String itemCodeNew;

    /**
     * 物料料号-新说明
     */
    @ApiModelProperty(value = "物料料号-新说明")
    private String itemDescNew;

    /**
     * 物料料号-旧
     */
    @ApiModelProperty(value = "物料料号-旧")
    private String itemCodeOld;

    /**
     * 物料料号-旧说明
     */
    @ApiModelProperty(value = "物料料号-旧说明")
    private String itemDescOld;

    /**
     * 数量-新
     */
    @ApiModelProperty(value = "数量-新")
    private String numberNew;

    /**
     * 数量-旧
     */
    @ApiModelProperty(value = "数量-旧")
    private String numberOld;

    /**
     * 目标
     */
    @ApiModelProperty(value = "切换目标")
    private String target;

    /**
     * 旧网版库存
     */
    @ApiModelProperty(value = "旧网版库存")
    private String screenPlateInventoryOld;

    /**
     * 旧网板在途数量
     */
    @ApiModelProperty(value = "旧网板在途数量")
    private String screenPlateNumberOld;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 是否存在bom
     */
    @ApiModelProperty(value = "是否存在bom")
    private String isExistBom;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDateTime effectiveEndDate;

    /**
     * 时间校验
     */
    @ApiModelProperty(value = "时间校验")
    private LocalDateTime veirfyDate;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
