package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Entity
@ToString
@Data
@Table(name = "bbom_item_attr")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_item_attr SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_item_attr SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ItemAttr extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    @Column(name = "src_attr_id")
    private String srcAttrId;

    /**
     * srcAttrAlias
     */
    @ApiModelProperty(value = "srcAttrAlias")
    @Column(name = "src_attr_alias")
    private String srcAttrAlias;

    /**
     * srcCategorySegment4Id
     */
    @ApiModelProperty(value = "srcCategorySegment4Id")
    @Column(name = "src_category_segment4_id")
    private String srcCategorySegment4Id;

    /**
     * srcCategorySegment4
     */
    @ApiModelProperty(value = "srcCategorySegment4")
    @Column(name = "src_category_segment4")
    private String srcCategorySegment4;

    /**
     * srcAttrType
     */
    @ApiModelProperty(value = "srcAttrType")
    @Column(name = "src_attr_type")
    private String srcAttrType;

    /**
     * srcOptionFlag
     */
    @ApiModelProperty(value = "srcOptionFlag")
    @Column(name = "src_option_flag")
    private String srcOptionFlag;

    /**
     * srcAttrColumn
     */
    @ApiModelProperty(value = "srcAttrColumn")
    @Column(name = "src_attr_column")
    private String srcAttrColumn;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    @Column(name = "language")
    private String language;


}
