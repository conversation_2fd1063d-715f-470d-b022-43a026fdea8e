package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.SlurryInformationDTO;
import com.trinasolar.scp.bbom.domain.entity.SlurryInformation;
import com.trinasolar.scp.bbom.domain.excel.SlurryInformationExcelDTO;
import com.trinasolar.scp.bbom.domain.save.SlurryInformationSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 浆料车间单耗及线数维护 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SlurryInformationDEConvert extends BaseDEConvert<SlurryInformationDTO, SlurryInformation> {

    SlurryInformationDEConvert INSTANCE = Mappers.getMapper(SlurryInformationDEConvert.class);

    List<SlurryInformationExcelDTO> toExcelDTO(List<SlurryInformationDTO> dtos);

    SlurryInformationExcelDTO toExcelDTO(SlurryInformationDTO dto);

    @Mappings(
            {
                    @Mapping(target = "basePlaceId", expression = "java(com.trinasolar.scp.common.api.util.LovUtils.getByName(com.trinasolar.scp.bbom.domain.utils.LovHeaderCodeConstant.BASE_PLACE,dto.getBasePlaceName()).getLovLineId())"),
            }
    )
    SlurryInformationSaveDTO toSaveDTO(SlurryInformationExcelDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    SlurryInformation saveDTOtoEntity(SlurryInformationSaveDTO saveDTO, @MappingTarget SlurryInformation entity);

    List<SlurryInformationDTO> toDto(List<SlurryInformation> dtos);
}
