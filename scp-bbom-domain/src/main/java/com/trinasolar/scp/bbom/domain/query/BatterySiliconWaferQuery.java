package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 电池类型动态属性-硅片
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@ApiModel(value = "BatterySiliconWafer查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatterySiliconWaferQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 6571878889480960922L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferProperties;

    /**
     * 条件项
     */
    @ApiModelProperty(value = "条件项")
    private String conditionItem;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String batteryValue;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDate effectiveStartDate;

    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDate effectiveEndDate;

    /**
     * 时间校验
     */
    @ApiModelProperty(value = "时间校验")
    private LocalDate veirfyDate;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    /**
     * 新硅片料号
     */
    @ApiModelProperty(value = "新硅片料号")
    private String itemCodeNew;
}
