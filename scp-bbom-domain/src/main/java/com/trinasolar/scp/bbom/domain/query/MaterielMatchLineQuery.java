package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Data
@ApiModel(value = "MaterielMatchLine查询条件", description = "查询条件")
@Accessors(chain = true)
public class MaterielMatchLineQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = -3104696074162899339L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 排产明细表主键
     */
    @ApiModelProperty(value = "排产明细表主键")
    private Long cellProductionPlanId;

    /**
     * 电池物料号匹配ID
     */
    @ApiModelProperty(value = "电池物料号匹配ID")
    private Long headerId;

    /**
     * 排产日期
     */
    @ApiModelProperty(value = "排产日期")
    private LocalDate scheduleDate;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;

    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String itemCode;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;

    @ApiModelProperty(value = "匹配状态列表")
    private List<String> matchStatusList;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String isCatchProduction;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产Id")
    private Long isCatchProductionId;

    /**
     * 切换网版料号
     */
    @ApiModelProperty(value = "切换网版料号")
    private String screenPlateItemCode;

    /**
     * 切换结束数据
     */
    @ApiModelProperty(value = "切换结束数据")
    private LocalDateTime switchEndDate;

    /**
     * 切换开始时间
     */
    @ApiModelProperty(value = "切换开始时间")
    private LocalDateTime switchStartDate;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal line;

    /**
     * 电池片数量
     */
    @ApiModelProperty(value = "电池片数量")
    private BigDecimal cellQty;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;
    /**
     * 网版物料说明
     */
    @ApiModelProperty(value = "网版物料说明")
    private String screenPlateItemCodeDesc;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;


    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long batteryTypeId;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    private String specialArea;

    /**
     * 特殊区域Id
     */
    @ApiModelProperty(value = "特殊区域Id")
    private Long specialAreaId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String pcsSourceLevel;

    /**
     * 是否有特殊要求
     */
    @ApiModelProperty(value = "是否有特殊要求")
    private String isSpecialRequirements;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenManufacturer;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siliconMaterialManufacturer;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String batteryManufacturer;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverSlurryManufacturer;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;

    /**
     * 硅片购买方式
     */
    @ApiModelProperty(value = "硅片购买方式")
    private String siliconWaferPurchaseMethod;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandPlace;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    private String processCategory;
    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String basePlace;

    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 排产日期
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String oldMonth;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    @ApiModelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGrid;

    @ApiModelProperty(value = "正电极网版细栅名称")
    private String positiveElectrodeScreenFineGridName;

    @ApiModelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGrid;

    @ApiModelProperty(value = "背电极网版细栅名称")
    private String negativeElectrodeScreenFineGridName;

    @ApiModelProperty(value = "计划类型")
    private String planType;

    @ApiModelProperty(value = "用户ID")
    private String userId;
}
