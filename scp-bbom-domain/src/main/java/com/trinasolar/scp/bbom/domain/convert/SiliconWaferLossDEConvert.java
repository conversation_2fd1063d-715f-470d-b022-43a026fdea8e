package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.SiliconWaferLossDTO;
import com.trinasolar.scp.bbom.domain.entity.SiliconWaferLoss;
import com.trinasolar.scp.bbom.domain.excel.SiliconWaferLossExcelDTO;
import com.trinasolar.scp.bbom.domain.save.SiliconWaferLossSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 硅片损耗率信息维护 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SiliconWaferLossDEConvert extends BaseDEConvert<SiliconWaferLossDTO, SiliconWaferLoss> {

    SiliconWaferLossDEConvert INSTANCE = Mappers.getMapper(SiliconWaferLossDEConvert.class);

    List<SiliconWaferLossExcelDTO> toExcelDTO(List<SiliconWaferLossDTO> dtos);

    SiliconWaferLossExcelDTO toExcelDTO(SiliconWaferLossDTO dto);

    SiliconWaferLossSaveDTO toSaveDTO(SiliconWaferLossExcelDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    SiliconWaferLoss saveDTOtoEntity(SiliconWaferLossSaveDTO saveDTO, @MappingTarget SiliconWaferLoss entity);
}
