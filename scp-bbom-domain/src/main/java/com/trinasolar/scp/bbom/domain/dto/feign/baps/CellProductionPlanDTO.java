package com.trinasolar.scp.bbom.domain.dto.feign.baps;

import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateDTO;
import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * 投产计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "投产计划DTO对象", description = "DTO对象")
public class CellProductionPlanDTO extends BaseDTO {

    private static final long serialVersionUID = -1142345528364755287L;

    // 待匹配的物料
    List<ItemsDTO> itemsDTOS;

    /**
     * 网版切换的数据
     */
    List<BatteryScreenPlateDTO> screenPlateSwitch;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 生产基地Id
     */
    @ApiModelProperty(value = "生产基地Id")
    private Long basePlaceId;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;

    /**
     * 生产车间Id
     */
    @ApiModelProperty(value = "生产车间Id")
    private Long workshopId;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 生产单元Id
     */
    @ApiModelProperty(value = "生产单元Id")
    private Long workunitId;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long cellsTypeId;

    /**
     * 线体数量
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal numberLine;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String cellSource;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * MV
     */
    @ApiModelProperty(value = "MV")
    private BigDecimal cellMv;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 5A料号
     */
    @ApiModelProperty(value = "5A料号")
    private String itemCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 数量（片）
     */
    @ApiModelProperty(value = "数量（片）")
    private BigDecimal qtyPc;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 投产计划汇总版本号
     */
    @ApiModelProperty(value = "投产计划汇总版本号")
    private String finalVersion;

    /**
     * 生产线体
     */
    @ApiModelProperty(value = "生产线体")
    private String lineName;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;

    /**
     * 汇总明细行id
     */
    @ApiModelProperty(value = "汇总明细行id")
    private Long demandSummarylinesId;
    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siMfrs;
    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenPlateMfrs;
    /**
     * 起始效率
     */
    @ApiModelProperty(value = "起始效率")
    private String startEfficiency;
    /**
     * 最大分布效率
     */
    @ApiModelProperty(value = "最大分布效率")
    private String maxEfficiency;
    /**
     * 电池特殊单号
     */
    @ApiModelProperty(value = "电池特殊单号")
    private String specialOrderNo;
    /**
     * 需求日期
     */
    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String waferGrade;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String processCategory;
    /**
     * 需求说明
     */
    @ApiModelProperty(value = "需求说明")
    private String demandRemark;
    /**
     * 分档规则
     */
    @ApiModelProperty(value = "分档规则")
    private String gradeRule;
    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderCode;

    /**
     * 需求版本号
     */
    @ApiModelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 小区域国家
     */
    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandBasePlace;
    /**
     * 是否电池特殊要求
     */
    @ApiModelProperty(value = "是否电池特殊要求")
    private String isSpecialRequirement;
    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String cellMfrs;
    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverPulpMfrs;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private String demandQty;
    /**
     * 验证标识
     */
    @ApiModelProperty(value = "验证标识")
    private String verificationMark;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String verson;
    /**
     * bbom中对应的Id
     */
    @ApiModelProperty(value = "bbom中对应的Id")
    private Long bbomId;
    /**
     * 入库计划中对应的fromId
     */
    @ApiModelProperty(value = "入库计划中对应的fromId")
    private Long fromId;

    public String getStartTimeStr() {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return fmt.format(this.startTime);
    }
}
