package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Entity
@ToString
@Data
@Table(name = "bbom_battery_screen_plate_workshop")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_battery_screen_plate_workshop SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_battery_screen_plate_workshop SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class BatteryScreenPlateWorkshop extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    @Column(name = "battery_code")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 单玻
     */
    @ApiModelProperty(value = "单玻")
    @Column(name = "single_glass_flag")
    private String singleGlassFlag;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    @ApiModelProperty(value = "主栅信息")
    @Column(name = "main_grid_info")
    private String mainGridInfo;


    /**
     * 基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "生产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 正电极网版细栅
     */
    @ApiModelProperty(value = "正电极网版细栅")
    @Column(name = "positive_electrode_screen_fine_grid")
    private String positiveElectrodeScreenFineGrid;

    /**
     * 背电极网版细栅
     */
    @ApiModelProperty(value = "背电极网版细栅")
    @Column(name = "negative_electrode_screen_fine_grid")
    private String negativeElectrodeScreenFineGrid;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    @Column(name = "effective_start_date")
    private LocalDate effectiveStartDate;

    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    @Column(name = "effective_end_date")
    private LocalDate effectiveEndDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


}
