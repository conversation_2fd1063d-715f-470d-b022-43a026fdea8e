package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectValue;
import com.trinasolar.scp.bbom.domain.excel.RuleControlObjectValueExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 规则管控对象值 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleControlObjectValueDEConvert extends BaseDEConvert<RuleControlObjectValueDTO, RuleControlObjectValue> {

    RuleControlObjectValueDEConvert INSTANCE = Mappers.getMapper(RuleControlObjectValueDEConvert.class);

    List<RuleControlObjectValueExcelDTO> toExcelDTO(List<RuleControlObjectValueDTO> dtos);

    RuleControlObjectValueExcelDTO toExcelDTO(RuleControlObjectValueDTO dto);
}
