package com.trinasolar.scp.bbom.domain.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.EmailDataResultDTO;
import com.trinasolar.scp.common.api.util.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2022年6月21日19:24:50
 */
@Slf4j
public class FileUtil {
    static final String EMAIL_FILE_LOCAL_DIR_NAME = "./email_file";

    public static MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName());
        try (InputStream input = new FileInputStream(file);
             OutputStream os = item.getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }
        return new CommonsMultipartFile(item);
    }

    public static File createLocalFile(String content) {
        File dir = new File(getLocalDir());
        if (!dir.exists()) {
            boolean mkdirs = dir.mkdirs();
            if (!mkdirs) {
                throw new BizException(String.format("创建文件夹{%s}失败", dir.getPath()));
            }
        }
        String fileName = content + "信息导出_" + LocalDate.now();
        File file = new File(dir.getPath() + "/" + fileName + ".xlsx");
        if (file.exists()) {
            boolean delete = file.delete();
            if(!delete){
                throw new BizException(String.format("删除文件名{%s}文件失败", fileName));
            }
        }
        try {
            boolean newFile = file.createNewFile();
            if (!newFile) {
                throw new BizException(String.format("创建文件名{%s}文件失败", fileName));
            }
        } catch (IOException e) {
            throw new BizException(String.format("创建文件名{%s}文件失败", fileName));
        }
        return file;
    }

    public static String getLocalDir() {
        return EMAIL_FILE_LOCAL_DIR_NAME + "/";
    }

    public static JSONArray getObjects(EmailDataResultDTO emailDataResultDTO, String content, File file, String fileUrl, String exportName) {
        emailDataResultDTO.setFileName(file.getName());
        emailDataResultDTO.setFileUrl(fileUrl);
        //组合json数据
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fileName", emailDataResultDTO.getFileName());
        jsonObject.put("fileUrl", emailDataResultDTO.getFileUrl());
        jsonArray.add(jsonObject);
        emailDataResultDTO.setYear(String.valueOf(LocalDateTime.now().getYear()));
        emailDataResultDTO.setMonth(String.valueOf(LocalDateTime.now().getMonthValue()));
        emailDataResultDTO.setContent(content);
        emailDataResultDTO.setExportName(exportName);
        return jsonArray;
    }
}
