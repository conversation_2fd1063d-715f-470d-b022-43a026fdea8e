package com.trinasolar.scp.bbom.domain.event;

import com.trinasolar.scp.bbom.domain.entity.RuleLine;
import org.springframework.context.ApplicationEvent;

public class CollocationRulesEvent extends ApplicationEvent {
    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public CollocationRulesEvent(Object source) {
        super(source);
    }

    @Override
    public RuleLine getSource() {
        return (RuleLine) source;
    }

}
