package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 物料属性字段Lov
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Entity
@ToString
@Data
@Table(name = "bbom_item_attr_lov")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_item_attr_lov SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_item_attr_lov SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ItemAttrLov extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * lovId
     */
    @ApiModelProperty(value = "lovId")
    @Column(name = "lov_id")
    private String lovId;

    /**
     * lovName
     */
    @ApiModelProperty(value = "lovName")
    @Column(name = "lov_name")
    private String lovName;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    @Column(name = "src_attr_id")
    private String srcAttrId;

    /**
     * srcAttrName
     */
    @ApiModelProperty(value = "srcAttrName")
    @Column(name = "src_attr_name")
    private String srcAttrName;

    /**
     * lovLineId
     */
    @ApiModelProperty(value = "lovLineId")
    @Column(name = "lov_line_id")
    private String lovLineId;

    /**
     * lovLineValue
     */
    @ApiModelProperty(value = "lovLineValue")
    @Column(name = "lov_line_value")
    private String lovLineValue;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    @Column(name = "language")
    private String language;

    /**
     * isRequired
     */
    @ApiModelProperty(value = "isRequired")
    @Column(name = "is_required")
    private String isRequired;


}
