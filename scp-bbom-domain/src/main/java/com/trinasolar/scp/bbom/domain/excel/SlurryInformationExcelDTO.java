package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 浆料车间单耗及线数维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SlurryInformationExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型
     */
    @ExcelIgnore
    private String batteryType;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    private String planVersion;

    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String batteryTypeName;
    /**
     * 生产基地
     */
    @ExcelIgnore
    private String basePlace;
    /**
     * 生产基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 生产车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 生产车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 线数
     */
    @ExcelProperty(value = "线数")
    private String lineNumber;
    /**
     * 机台
     */
    @ExcelIgnore
    private String workbench;
    /**
     * 机台
     */
    @ExcelProperty(value = "机台")
    private String workbenchName;
    /**
     * 单耗
     */
    @ExcelProperty(value = "单耗")
    private String unitConsumption;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态")
    private String valid;

    @ExcelProperty("创建人")
    private String updatedByName;
    
    @ExcelProperty("创建时间")
    private LocalDateTime updatedTime;
}
