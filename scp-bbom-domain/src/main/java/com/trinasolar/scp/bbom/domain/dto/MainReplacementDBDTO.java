package com.trinasolar.scp.bbom.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/10/2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MainReplacementDBDTO {
    private String materialNo;

    private String segment1;

    private String segment2;

    private String segment3;

    private String segment4;

    private String segment6;

    private String segment7;

    private String segment8;

    private String segment9;

    private String segment11;

    private String segment13;

    private String segment23;

    private String itemCode;

    private String itemDesc;

    private String alternateBomDesignator;

    private String substituteFlag;
}