package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * BOM规则行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleLineExcelDTO {

    /**
     * 规则头ID
     */
    @ExcelProperty(value = "规则头ID ")
    private Long ruleHeaderId;
    /**
     * 规则行ID，序列号生成
     */
    @ExcelProperty(value = "规则行ID，序列号生成")
    private Long ruleLineId;
    /**
     * 规则编码
     */
    @ExcelProperty(value = "规则编码")
    private String code;
    /**
     * 管控主体ID,取对应LOV行ID
     */
    @ExcelProperty(value = "管控主体ID,取对应LOV行ID")
    private Long controlSubjectId;
    /**
     * 管控主体
     */
    @ExcelProperty(value = "管控主体")
    private String controlSubject;
    /**
     * 管控对象，取组件分料号动态配置标识下的组件分料号属性ID
     */
    @ExcelProperty(value = "管控对象，取组件分料号动态配置标识下的组件分料号属性ID")
    private Long controlObjectId;
    /**
     * 管控对象
     */
    @ExcelProperty(value = "管控对象")
    private String controlObject;
    /**
     * 管控目的,取对应LOV行ID
     */
    @ExcelProperty(value = "管控目的,取对应LOV行ID")
    private Long controlPurposeId;
    /**
     * 管控目的
     */
    @ExcelProperty(value = "管控目的")
    private String controlPurpose;
    /**
     * 默认标识
     */
    @ExcelProperty(value = "默认标识")
    private String defaultFlag;
    /**
     * 属性值
     */
    @ExcelProperty(value = "属性值")
    private String attrValue;
    /**
     * lovId
     */
    @ExcelProperty(value = "lovId")
    private Long attrValueId;
    /**
     * 有效日期_起
     */
    @ExcelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ExcelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute1;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute2;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute3;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute4;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute5;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute6;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute7;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute8;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute9;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute10;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute11;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute12;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute13;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute14;
    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段 ")
    private String attribute15;
    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Integer no;
}
