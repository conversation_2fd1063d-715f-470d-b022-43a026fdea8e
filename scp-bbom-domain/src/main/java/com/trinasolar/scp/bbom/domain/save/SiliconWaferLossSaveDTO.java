package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 硅片损耗率信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SiliconWaferLoss保存参数", description = "保存参数")
public class SiliconWaferLossSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryTypeName;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 硅片良率
     */
    @ApiModelProperty(value = "电池良率")
    private String siliconWaferYield;
    /**
     * 硅片隔离率
     */
    @ApiModelProperty(value = "硅片隔离率")
    private String siliconIsolationRate;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
}
