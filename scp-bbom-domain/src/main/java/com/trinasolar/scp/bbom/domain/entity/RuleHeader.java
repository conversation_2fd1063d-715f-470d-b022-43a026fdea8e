package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * BOM规则头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 15:49:10
 */
@Entity
@ToString
@Data
@Table(name = "bbom_rule_header")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_rule_header SET is_deleted = 1 WHERE rule_header_id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_rule_header SET is_deleted = 1 WHERE rule_header_id = ?")
public class RuleHeader extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 规则头ID 序列号生成
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "规则头ID 序列号生成")
    private Long ruleHeaderId;

    /**
     * 规则分类ID，从LOV取LOV行ID
     */
    @ApiModelProperty(value = "规则分类ID，从LOV取LOV行ID")
    private Long ruleCategoryId;

    @Transient
    @ApiModelProperty(value = "规则分类名称")
    private String ruleCategoryName;

    /**
     * 规则编号
     */
    @ApiModelProperty(value = "规则编号")
    private String ruleNumber;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 管控主体ID,取对应LOV行ID
     */
    @ApiModelProperty(value = "管控主体ID,取对应LOV行ID")
    private Long controlSubjectId;

    @ApiModelProperty(value = "管控主体名称")
    private String controlSubjectName;
    /**
     * 管控对象，取组件分料号动态配置标识下的组件分料号属性ID
     */
    @ApiModelProperty(value = "管控对象，取组件分料号动态配置标识下的组件分料号属性ID")
    private Long controlObjectId;

    @ApiModelProperty(value = "管控对象名称")
    private String controlObjectName;

    /**
     * 管控目的,取对应LOV行ID
     */
    @ApiModelProperty(value = "管控目的,取对应LOV行ID")
    private Long controlPurposeId;

    @ApiModelProperty(value = "管控目的名称")
    private String controlPurposeName;

    /**
     * 有效标识
     */
    @ApiModelProperty(value = "有效标识")
    private String enableFlag;

    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute15;


}
