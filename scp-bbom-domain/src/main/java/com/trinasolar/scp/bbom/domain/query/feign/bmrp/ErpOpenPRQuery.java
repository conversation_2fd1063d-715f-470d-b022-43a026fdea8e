package com.trinasolar.scp.bbom.domain.query.feign.bmrp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PR查询条件", description = "PR查询条件")
public class ErpOpenPRQuery {
    @ApiModelProperty("组织Id")
    private Long organizationId;
    @ApiModelProperty("物料编号")
    private String itemCode;
    @ApiModelProperty("行取消标识")
    private String lineCancelFlag;
    @ApiModelProperty("头关闭标识")
    private String headerClosedFlag;
}
