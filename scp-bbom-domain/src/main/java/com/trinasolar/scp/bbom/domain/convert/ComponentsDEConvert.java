package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ComponentsDTO;
import com.trinasolar.scp.bbom.domain.entity.Components;
import com.trinasolar.scp.bbom.domain.excel.ComponentsExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * BOM行 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentsDEConvert extends BaseDEConvert<ComponentsDTO, Components> {

    ComponentsDEConvert INSTANCE = Mappers.getMapper(ComponentsDEConvert.class);

    List<ComponentsExcelDTO> toExcelDTO(List<ComponentsDTO> dtos);

    ComponentsExcelDTO toExcelDTO(ComponentsDTO dto);
}
