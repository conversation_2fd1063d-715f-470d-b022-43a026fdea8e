package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "AttrTypeLineQuery", description = "查询条件")
public class AttrTypeLineQuery extends PageDTO {
    /**
     * 属性类型编码
     */
    @ApiModelProperty(value = "属性类型编码")
    private String code;
}
