package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MaterielMatchLineAppointItemSaveDTO保存参数", description = "保存参数")
public class MaterielMatchLineAppointItemSaveDTO extends TokenDTO implements Serializable {
    private static final long serialVersionUID = 8120595556992833077L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "料号")
    private String itemCode;
    @ApiModelProperty(value = "bom替代项")
    private String alternateBomDesignator;
    ;
}
