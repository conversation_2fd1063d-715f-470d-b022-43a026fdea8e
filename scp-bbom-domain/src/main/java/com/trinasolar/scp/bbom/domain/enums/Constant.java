package com.trinasolar.scp.bbom.domain.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17
 */
public final class Constant {
    public static final List<String> CATEGORYS;

    static {
        CATEGORYS = new ArrayList<>();
        CATEGORYS.add("4A001001001");
        CATEGORYS.add("4A002001001");
        CATEGORYS.add("4A003001001");
        CATEGORYS.add("5A001001001");
        CATEGORYS.add("5A001002001");
        CATEGORYS.add("5A002001001");
        CATEGORYS.add("5A002002001");
        CATEGORYS.add("5A002003001");
        CATEGORYS.add("7A015001001");
        CATEGORYS.add("7A009001001");

    }


    private Constant() {
    }
}
