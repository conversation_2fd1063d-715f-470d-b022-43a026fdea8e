package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 兆瓦转换系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ConversionCoefficientMw保存参数", description = "保存参数")
public class ConversionCoefficientMwSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    private String classify;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 电池MW系数
     */
    @ApiModelProperty(value = "电池MW系数")
    private BigDecimal batteryMwQty;
    /**
     * 电池目标良率
     */
    @ApiModelProperty(value = "电池目标良率")
    private BigDecimal batteryEfficiencyQty;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    private BigDecimal unitConsumption;
    /**
     * 物料MW系数
     */
    @ApiModelProperty(value = "物料MW系数")
    private BigDecimal materielMwQty;
    /**
     * 预警
     */
    @ApiModelProperty(value = "预警")
    private String warningReason;
    /**
     * 库存组织id
     */
    @ApiModelProperty(value = "库存组织id")
    private Long organizationId;
}
