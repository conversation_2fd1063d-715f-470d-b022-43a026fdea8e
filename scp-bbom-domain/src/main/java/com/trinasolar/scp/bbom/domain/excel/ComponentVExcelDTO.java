package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 同步cux3_bbom_component_v
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ComponentVExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * lastUpdateDate
     */
    @ExcelProperty(value = "lastUpdateDate")
    private LocalDateTime lastUpdateDate;

    /**
     * billSequenceId
     */
    @ExcelProperty(value = "billSequenceId")
    private String billSequenceId;

    /**
     * assemblyItemId
     */
    @ExcelProperty(value = "assemblyItemId")
    private String assemblyItemId;

    /**
     * organizationId
     */
    @ExcelProperty(value = "organizationId")
    private String organizationId;

    /**
     * assemblyType
     */
    @ExcelProperty(value = "assemblyType")
    private String assemblyType;

    /**
     * assItemNum
     */
    @ExcelProperty(value = "assItemNum")
    private String assItemNum;

    /**
     * assItemDes
     */
    @ExcelProperty(value = "assItemDes")
    private String assItemDes;

    /**
     * assItemUom
     */
    @ExcelProperty(value = "assItemUom")
    private String assItemUom;

    /**
     * alternateBomDesignator
     */
    @ExcelProperty(value = "alternateBomDesignator")
    private String alternateBomDesignator;

    /**
     * bmAttributeCategory
     */
    @ExcelProperty(value = "bmAttributeCategory")
    private String bmAttributeCategory;

    /**
     * bmAttribute1
     */
    @ExcelProperty(value = "bmAttribute1")
    private String bmAttribute1;

    /**
     * bmAttribute2
     */
    @ExcelProperty(value = "bmAttribute2")
    private String bmAttribute2;

    /**
     * bmAttribute3
     */
    @ExcelProperty(value = "bmAttribute3")
    private String bmAttribute3;

    /**
     * bmAttribute4
     */
    @ExcelProperty(value = "bmAttribute4")
    private String bmAttribute4;

    /**
     * bmAttribute5
     */
    @ExcelProperty(value = "bmAttribute5")
    private String bmAttribute5;

    /**
     * bmAttribute6
     */
    @ExcelProperty(value = "bmAttribute6")
    private String bmAttribute6;

    /**
     * bmAttribute7
     */
    @ExcelProperty(value = "bmAttribute7")
    private String bmAttribute7;

    /**
     * bmAttribute8
     */
    @ExcelProperty(value = "bmAttribute8")
    private String bmAttribute8;

    /**
     * bmAttribute9
     */
    @ExcelProperty(value = "bmAttribute9")
    private String bmAttribute9;

    /**
     * bmAttribute10
     */
    @ExcelProperty(value = "bmAttribute10")
    private String bmAttribute10;

    /**
     * bmAttribute11
     */
    @ExcelProperty(value = "bmAttribute11")
    private String bmAttribute11;

    /**
     * bmAttribute12
     */
    @ExcelProperty(value = "bmAttribute12")
    private String bmAttribute12;

    /**
     * bmAttribute13
     */
    @ExcelProperty(value = "bmAttribute13")
    private String bmAttribute13;

    /**
     * bmAttribute14
     */
    @ExcelProperty(value = "bmAttribute14")
    private String bmAttribute14;

    /**
     * bmAttribute15
     */
    @ExcelProperty(value = "bmAttribute15")
    private String bmAttribute15;

    /**
     * componentSequenceId
     */
    @ExcelProperty(value = "componentSequenceId")
    private String componentSequenceId;

    /**
     * substituteComponentId
     */
    @ExcelProperty(value = "substituteComponentId")
    private String substituteComponentId;

    /**
     * componentItemId
     */
    @ExcelProperty(value = "componentItemId")
    private String componentItemId;

    /**
     * itemNum
     */
    @ExcelProperty(value = "itemNum")
    private String itemNum;

    /**
     * operationSeqNum
     */
    @ExcelProperty(value = "operationSeqNum")
    private String operationSeqNum;

    /**
     * bicItemNum
     */
    @ExcelProperty(value = "bicItemNum")
    private String bicItemNum;

    /**
     * bicItemDes
     */
    @ExcelProperty(value = "bicItemDes")
    private String bicItemDes;

    /**
     * bicItemUom
     */
    @ExcelProperty(value = "bicItemUom")
    private String bicItemUom;

    /**
     * basisType
     */
    @ExcelProperty(value = "basisType")
    private String basisType;

    /**
     * componentQuantity
     */
    @ExcelProperty(value = "componentQuantity")
    private String componentQuantity;

    /**
     * autoRequestMaterial
     */
    @ExcelProperty(value = "autoRequestMaterial")
    private String autoRequestMaterial;

    /**
     * effectivityDate
     */
    @ExcelProperty(value = "effectivityDate")
    private String effectivityDate;

    /**
     * disableDate
     */
    @ExcelProperty(value = "disableDate")
    private String disableDate;

    /**
     * changeNotice
     */
    @ExcelProperty(value = "changeNotice")
    private String changeNotice;

    /**
     * planningFactor
     */
    @ExcelProperty(value = "planningFactor")
    private String planningFactor;

    /**
     * componentYieldFactor
     */
    @ExcelProperty(value = "componentYieldFactor")
    private String componentYieldFactor;

    /**
     * enforceIntRequirements
     */
    @ExcelProperty(value = "enforceIntRequirements")
    private String enforceIntRequirements;

    /**
     * includeInCostRollup
     */
    @ExcelProperty(value = "includeInCostRollup")
    private String includeInCostRollup;

    /**
     * bicItemType
     */
    @ExcelProperty(value = "bicItemType")
    private String bicItemType;

    /**
     * bicItemStatus
     */
    @ExcelProperty(value = "bicItemStatus")
    private String bicItemStatus;

    /**
     * wipSupplyType
     */
    @ExcelProperty(value = "wipSupplyType")
    private String wipSupplyType;

    /**
     * supplyType
     */
    @ExcelProperty(value = "supplyType")
    private String supplyType;

    /**
     * bcAttributeCategory
     */
    @ExcelProperty(value = "bcAttributeCategory")
    private String bcAttributeCategory;

    /**
     * bcAttribute1
     */
    @ExcelProperty(value = "bcAttribute1")
    private String bcAttribute1;

    /**
     * bcAttribute2
     */
    @ExcelProperty(value = "bcAttribute2")
    private String bcAttribute2;

    /**
     * bcAttribute3
     */
    @ExcelProperty(value = "bcAttribute3")
    private String bcAttribute3;

    /**
     * bcAttribute4
     */
    @ExcelProperty(value = "bcAttribute4")
    private String bcAttribute4;

    /**
     * bcAttribute5
     */
    @ExcelProperty(value = "bcAttribute5")
    private String bcAttribute5;

    /**
     * bcAttribute6
     */
    @ExcelProperty(value = "bcAttribute6")
    private String bcAttribute6;

    /**
     * bcAttribute7
     */
    @ExcelProperty(value = "bcAttribute7")
    private String bcAttribute7;

    /**
     * bcAttribute8
     */
    @ExcelProperty(value = "bcAttribute8")
    private String bcAttribute8;

    /**
     * bcAttribute9
     */
    @ExcelProperty(value = "bcAttribute9")
    private String bcAttribute9;

    /**
     * bcAttribute10
     */
    @ExcelProperty(value = "bcAttribute10")
    private String bcAttribute10;

    /**
     * bcAttribute11
     */
    @ExcelProperty(value = "bcAttribute11")
    private String bcAttribute11;

    /**
     * bcAttribute12
     */
    @ExcelProperty(value = "bcAttribute12")
    private String bcAttribute12;

    /**
     * bcAttribute13
     */
    @ExcelProperty(value = "bcAttribute13")
    private String bcAttribute13;

    /**
     * bcAttribute14
     */
    @ExcelProperty(value = "bcAttribute14")
    private String bcAttribute14;

    /**
     * bcAttribute15
     */
    @ExcelProperty(value = "bcAttribute15")
    private String bcAttribute15;

    /**
     * bscFlag
     */
    @ExcelProperty(value = "bscFlag")
    private String bscFlag;

    /**
     * 是否处理
     */
    @ExcelProperty(value = "是否处理")
    private Integer isProcess;
}
