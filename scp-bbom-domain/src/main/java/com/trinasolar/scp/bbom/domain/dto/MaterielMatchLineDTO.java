package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import com.trinasolar.scp.common.api.util.BizException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;


/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池料号匹配明细行DTO对象", description = "DTO对象")
public class MaterielMatchLineDTO extends BaseDTO {

    private static final long serialVersionUID = -4145515235277745476L;
    // 待匹配的物料
    List<ItemsDTO> itemsDTOS;
    /**
     * 网版切换的数据
     */
    List<BatteryScreenPlateDTO> screenPlateSwitch;
    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 排产明细表主键
     */
    @ApiModelProperty(value = "排产明细表主键")
    private Long cellProductionPlanId;
    /**
     * 电池物料号匹配ID
     */
    @ApiModelProperty(value = "电池物料号匹配ID")
    private Long headerId;
    /**
     * 排产日期
     */
    @ApiModelProperty(value = "排产日期")
    private LocalDate scheduleDate;
    /**
     * 行开始日期
     */
    @ApiModelProperty(value = "行开始日期")
    private LocalDate startTimeStart;
    /**
     * 行结束日期
     */
    @ApiModelProperty(value = "行结束日期")
    private LocalDate startTimeEnd;
    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String itemCode;
    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String itemDesc;
    /**
     * 电池料号多匹配
     */
    @ApiModelProperty(value = "电池料号多匹配")
    private List<MaterielMatchLineMatchStatusDTO> matchCodes;
    /**
     * 工艺路线
     */
    @ApiModelProperty(value = "工艺路线")
    private String route;
    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;
    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatusName;

    /**
     * 料号匹配状态
     */
    @ApiModelProperty(value = "料号匹配状态（匹配到多个料号或无匹配料号或匹配到一个料号）")
    private String itemMatchStatus;

    /**
     * 料号匹配状态
     */
    @ApiModelProperty(value = "料号匹配状态（匹配到多个料号或无匹配料号或匹配到一个料号）")
    private String itemMatchStatusName;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String isCatchProduction;
    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产Id")
    private Long isCatchProductionId;
    /**
     * 切换网版料号 正电极主栅
     */
    @ApiModelProperty(value = "切换网版料号")
    private String screenPlateItemCode;
    /**
     * 切换结束数据
     */
    @ApiModelProperty(value = "切换结束数据")
    private LocalDateTime switchEndDate;
    /**
     * 切换开始时间
     */
    @ApiModelProperty(value = "切换开始时间")
    private LocalDateTime switchStartDate;
    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal line;
    /**
     * 电池片数量
     */
    @ApiModelProperty(value = "电池片数量")
    private BigDecimal cellQty;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;
    /**
     * 备注
     */
    @ApiModelProperty(value = "年月格式日期")
    private String month;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long batteryTypeId;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String basePlace;
    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    private String specialArea;

    /**
     * 特殊区域Id
     */
    @ApiModelProperty(value = "特殊区域Id")
    private Long specialAreaId;

    /**
     * 手工指定标识(Y/N)
     */
    @ApiModelProperty(value = "手工指定标识(Y/N)")
    private String handWorkFlag;
    /**
     * 网版物料说明
     */
    @ApiModelProperty(value = "网版物料说明")
    private String screenPlateItemCodeDesc;
    /**
     * 拆分标识(Y/空)
     */
    @ApiModelProperty(value = "拆分标识(Y/空)")
    private String splitFlag;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String finalVersion;


    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;


    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String pcsSourceLevel;

    /**
     * 是否有特殊要求
     */
    @ApiModelProperty(value = "是否有特殊要求")
    private String isSpecialRequirements;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenManufacturer;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siliconMaterialManufacturer;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String batteryManufacturer;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverSlurryManufacturer;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;

    /**
     * 硅片购买方式
     */
    @ApiModelProperty(value = "硅片购买方式")
    private String siliconWaferPurchaseMethod;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandPlace;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    private String processCategory;

    @ApiModelProperty(value = "detailLines")
    private List<MaterielMatchLineDTO> detailLines;
    /**
     * 总线体数量
     */
    @ApiModelProperty(value = "总线体数量")
    private BigDecimal totalLine;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 认证型号
     */
    @ApiModelProperty(value = "认证型号")
    private String certifiedModels;

    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String oldMonth;
    /**
     * 新旧网版料号标识(N/O)
     */
    @ApiModelProperty(value = "新旧网版料号标识(N/O)")
    private String newOrOldItemFlag;

    /**
     * 背电极网版主栅
     */
    @ApiModelProperty(value = "背电极网版主栅")
    private String screenPlateCodeFilter;
    /**
     * 网版类别 ：正电极、背电极
     */
    @ApiModelProperty(value = "网版类别[正电极-Z、背电极-F]")
    private String categoryFlag;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    /**
     * 硅片属性
     */
    @ApiModelProperty(value = "硅片属性")
    private String siliconWaferProperties;

    /**
     * 硅片属性 条件项
     */
    @ApiModelProperty(value = "条件项")
    private String siliconWaferCondition;

    /**
     * 硅片属性 值
     */
    @ApiModelProperty(value = "值")
    private String siliconWaferValue;

    private List<BatterySiliconWaferDTO> siliconWaferList;
    /**
     * 网版料号为正电极的时候
     * 找背电极网版料号赋值
     */
    @ApiModelProperty(value = "存在的目的为了过滤5A料号描述")
    private String screenPlateCodeDescFilter;

    /**
     * 切换网版料号细栅
     */
    @ApiModelProperty(value = "切换网版料号细栅")
    private String screenPlateItemCodeFineGrid;
    /**
     * 切换网版料号细栅
     */
    @ApiModelProperty(value = "切换网版料号细栅描述")
    private String screenPlateItemCodeDescFineGrid;
    /**
     * 背电极细栅
     */
    @ApiModelProperty(value = "背电极细栅")
    private String screenPlateCodeFilterFineGrid;
    /**
     * 背电极细栅
     */
    @ApiModelProperty(value = "背电极细栅")
    private String screenPlateCodeDescFilterFineGrid;

    @ApiModelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGrid;

    @ApiModelProperty(value = "正电极网版细栅名称")
    private String positiveElectrodeScreenFineGridName;

    @ApiModelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGrid;

    @ApiModelProperty(value = "背电极网版细栅名称")
    private String negativeElectrodeScreenFineGridName;

    @ApiModelProperty(value = "硅片品类")
    private String waferCategory;

    @ApiModelProperty(value = "计划类型")
    private String planType;

    @ApiModelProperty(value = "法碳配比")
    private String ratioCode;

    @ApiModelProperty(value = "ECS_CODE")
    private String ecsCode;

    /**
     * 供应方式名称
     */
    @ApiModelProperty(value = "供应方式名称")
    private String supplyModeName;

    @ApiModelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ApiModelProperty(value = "特殊订单")
    private String specialOrder;

    @ApiModelProperty(value = "制造工艺")
    private String manufactureProcess;

    //料号维度
    public String getGroupFiled() {
        return this.batteryType + "/"
                + this.aesthetics + "/"
                + this.transparentDoubleGlass + "/"
                + this.specialArea + "/"
                + this.hTrace + "/"
                + this.pcsSourceType + "/"
                + this.pcsSourceLevel + "/"
                + this.isSpecialRequirements + "/"
                + this.screenManufacturer + "/"
                + this.siliconMaterialManufacturer + "/"
                + this.batteryManufacturer + "/"
                + this.silverSlurryManufacturer + "/"
                + this.lowResistance + "/"
                + this.siliconWaferPurchaseMethod + "/"
                + this.demandPlace + "/"
                + this.basePlace + "/"
                + this.workshop + "/"
                + this.workunit + "/"
                + this.productionGrade + "/"
                + this.processCategory + "/"
                + this.screenPlateItemCode + "/"
                + this.cellProductionPlanId + "/"
                + this.oldMonth + "/"
                + this.siliconWaferValue + "/"
                + this.waferCategory + "/"
                + this.supplyModeName + "/"
                + this.ratioCode + "/"
                + this.ecsCode + "/"
                + this.isOversea;
    }

    //料号维度
    public String getGroupFiledByQuery() {
        return this.batteryType + "/"
                + this.aesthetics + "/"
                + this.transparentDoubleGlass + "/"
                + this.specialArea + "/"
                + this.hTrace + "/"
                + this.pcsSourceType + "/"
                + this.pcsSourceLevel + "/"
                + this.isSpecialRequirements + "/"
                + this.screenManufacturer + "/"
                + this.siliconMaterialManufacturer + "/"
                + this.batteryManufacturer + "/"
                + this.silverSlurryManufacturer + "/"
                + this.lowResistance + "/"
                + this.siliconWaferPurchaseMethod + "/"
                + this.demandPlace + "/"
                + this.basePlace + "/"
                + this.workshop + "/"
                + this.workunit + "/"
                + this.productionGrade + "/"
                + this.processCategory + "/"
                + this.screenPlateItemCode + "/"
                + this.itemCode + "/"
                + this.month + "/"
                + this.line + "/"
                + this.siliconWaferValue + "/"
                + this.waferCategory + "/"
                + this.supplyModeName + "/"
                + this.ratioCode + "/"
                + this.ecsCode + "/"
                + this.isOversea;
    }

    //头id+7料号
    public String getGroupFiledByQuery2() {
        return this.headerId + "/" + this.screenPlateItemCode;
    }

    @Getter
    @AllArgsConstructor
    public enum MatchStatus {
        FAIL_MATCH("FAIL_MATCH", "7A匹配失败"),
        ATTR_NON_MATCH("ATTR_NON_MATCH", "属性未匹配"),
        NON_MATCH("NON_MATCH", "未匹配"),
        APPOINT_MATCH("APPOINT_MATCH", "手工指定"),
        MATCHED("MATCHED", "系统匹配"),
        MULTI_MATCH("MULTI_MATCH", "多个匹配"),
        APPOINT_BY_CELL_PLAN("APPOINT_BY_CELL_PLAN", "投产指定"),
        SINGLE_MATCH("SINGLE_MATCH", "单个匹配");

        String code;

        String name;

        public static String getNameByCode(String code) {
            if (StringUtils.isBlank(code)) {
                return "";
            }
            return Arrays.stream(MatchStatus.values()).filter(i -> i.getCode().equals(code)).findFirst().orElseThrow(() -> new BizException("不存在")).getName();
        }
    }
}
