package com.trinasolar.scp.bbom.domain.dto.feign.bmrp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "DTO对象", description = "DTO对象")
public class ErpOpenPODTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 采购订单行ID
     */
    @ApiModelProperty(value = "采购订单行ID")
    private Long poLineId;

    /**
     * 采购订头行ID
     */
    @ApiModelProperty(value = "采购订头行ID")
    private Long poHeaderId;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value = "采购订单号")
    private String poHeaderNum;

    /**
     * 业务实体
     */
    @ApiModelProperty(value = "业务实体")
    private String orgName;

    /**
     * 头取消标识
     */
    @ApiModelProperty(value = "头取消标识")
    private String headerCancelFlag;

    /**
     * 头关闭标识
     */
    @ApiModelProperty(value = "头关闭标识")
    private String headerClosedFlag;

    /**
     * 行取消标识
     */
    @ApiModelProperty(value = "行取消标识")
    private String lineCancelFlag;

    /**
     * 行关闭标识
     */
    @ApiModelProperty(value = "行关闭标识")
    private String lineClosedFlag;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemDesc;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String uom;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String organizationCode;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    /**
     * 供应商地点
     */
    @ApiModelProperty(value = "供应商地点")
    private String vendorSite;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
