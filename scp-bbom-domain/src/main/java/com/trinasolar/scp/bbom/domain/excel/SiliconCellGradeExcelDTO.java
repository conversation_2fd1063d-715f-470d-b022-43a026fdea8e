package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 硅片等级与电池等级映射
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SiliconCellGradeExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 硅片属性
     */
    @ExcelProperty(value = "硅片属性")
    private String siliconWaferProperties;
    /**
     * 硅片属性条件项
     */
    @ExcelProperty(value = "硅片属性条件项")
    private String siliconWaferConditionalItem;
    /**
     * 硅片属性值
     */
    @ExcelProperty(value = "硅片属性值")
    private String siliconWaferValue;
    /**
     * 电池属性
     */
    @ExcelProperty(value = "电池属性")
    private String batteryProperties;
    /**
     * 电池属性条件项
     */
    @ExcelProperty(value = "电池属性条件项")
    private String batteryConditionalItem;
    /**
     * 电池属性值
     */
    @ExcelProperty(value = "电池属性值")
    private String batteryValue;
    /**
     * 有效日期_起
     */
    @ExcelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ExcelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
}
