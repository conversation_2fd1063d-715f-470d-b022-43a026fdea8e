package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleDTO;
import com.trinasolar.scp.bbom.domain.entity.PdmItemLifecycle;
import com.trinasolar.scp.bbom.domain.excel.PdmItemLifecycleExcelDTO;
import com.trinasolar.scp.bbom.domain.save.PdmItemLifecycleSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * pdm物料生命周期原始表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PdmItemLifecycleDEConvert extends BaseDEConvert<PdmItemLifecycleDTO, PdmItemLifecycle> {

    PdmItemLifecycleDEConvert INSTANCE = Mappers.getMapper(PdmItemLifecycleDEConvert.class);

    List<PdmItemLifecycleExcelDTO> toExcelDTO(List<PdmItemLifecycleDTO> dtos);

    PdmItemLifecycleExcelDTO toExcelDTO(PdmItemLifecycleDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    PdmItemLifecycle saveDTOtoEntity(PdmItemLifecycleSaveDTO saveDTO, @MappingTarget PdmItemLifecycle entity);
}
