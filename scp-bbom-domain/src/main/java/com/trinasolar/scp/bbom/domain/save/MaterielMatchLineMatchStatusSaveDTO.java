package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MaterielMatchLineMatchStatus保存参数", description = "保存参数")
public class MaterielMatchLineMatchStatusSaveDTO extends TokenDTO implements Serializable {

    private static final long serialVersionUID = -6006032589712136406L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 电池料号匹配明细行ID
     */
    @ApiModelProperty(value = "电池料号匹配明细行ID")
    private Long lineId;

    /**
     * 物料Code
     */
    @ApiModelProperty(value = "物料Code")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String isCatchProduction;
    /**
     * 工艺路线 yes or no
     */
    @ApiModelProperty(value = "工艺路线")
    private String route;
    /**
     * 认证型号 取值5A料号的segment28
     */
    @ApiModelProperty(value = "认证型号")
    private String certifiedModels;
}
