package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 规则管控对象头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:32
 */
@Data
@ApiModel(value = "RuleControlObjectHeader查询条件", description = "查询条件")
public class RuleControlObjectHeaderQuery extends PageDTO implements Serializable {

}
