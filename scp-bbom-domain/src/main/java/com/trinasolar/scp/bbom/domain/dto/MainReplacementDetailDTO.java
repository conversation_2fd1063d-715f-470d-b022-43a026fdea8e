package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/10/1
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MainReplacementDetailDTO {
    @ApiModelProperty(value = "车间")
    private String workShop;

    @ApiModelProperty(value = "生产区域")
    private String productionArea;

    @ApiModelProperty(value = "存在标记")
    private String existFlagDesc;
}
