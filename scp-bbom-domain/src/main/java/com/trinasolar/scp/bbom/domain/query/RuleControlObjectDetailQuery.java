package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 规则管控对象详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
@Data
@ApiModel(value = "RuleControlObjectDetail查询条件", description = "查询条件")
public class RuleControlObjectDetailQuery extends PageDTO implements Serializable {

}
