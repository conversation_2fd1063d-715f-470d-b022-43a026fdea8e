package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectDetail;
import com.trinasolar.scp.bbom.domain.excel.RuleControlObjectDetailExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 规则管控对象详情 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleControlObjectDetailDEConvert extends BaseDEConvert<RuleControlObjectDetailDTO, RuleControlObjectDetail> {

    RuleControlObjectDetailDEConvert INSTANCE = Mappers.getMapper(RuleControlObjectDetailDEConvert.class);

    List<RuleControlObjectDetailExcelDTO> toExcelDTO(List<RuleControlObjectDetailDTO> dtos);

    RuleControlObjectDetailExcelDTO toExcelDTO(RuleControlObjectDetailDTO dto);
}
