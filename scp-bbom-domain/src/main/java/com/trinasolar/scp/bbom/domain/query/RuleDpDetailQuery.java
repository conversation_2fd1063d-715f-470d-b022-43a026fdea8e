package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * BOM规则DP因子
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:08
 */
@Data
@ApiModel(value = "RuleDpDetail查询条件", description = "查询条件")
public class RuleDpDetailQuery extends PageDTO implements Serializable {

}
