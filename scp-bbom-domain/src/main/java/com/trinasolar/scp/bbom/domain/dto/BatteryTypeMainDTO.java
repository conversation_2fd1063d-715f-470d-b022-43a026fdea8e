package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 电池类型静态属性
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池类型静态属性DTO对象", description = "DTO对象")
public class BatteryTypeMainDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "单双晶")
    private String crystalType;
    /**
     * PN型
     */
    @ApiModelProperty(value = "PN型")
    private String pOrN;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;
    /**
     * 单双面
     */
    @ApiModelProperty(value = "单双面")
    private String singleDoubleFace;
    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅类别")
    private String numberMainGrids;
    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    private String shardingMode;
    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    private String productClassification;
}
