package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Data
@ApiModel(value = "MaterielMatchLineMatchStatus查询条件", description = "查询条件")
@Accessors(chain = true)
public class MaterielMatchLineMatchStatusQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = -1316615946322490751L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 电池料号匹配明细行ID
     */
    @ApiModelProperty(value = "电池料号匹配明细行ID")
    private Long lineId;

    /**
     * 物料Code
     */
    @ApiModelProperty(value = "物料Code")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String isCatchProduction;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
