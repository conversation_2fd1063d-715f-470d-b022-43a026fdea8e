package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * BOM规则DP因子
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleDpDetailExcelDTO {

    /**
     * 规则行ID
     */
    @ExcelProperty(value = "规则行ID")
    private Long ruleLineId;
    /**
     * 规则明细ID，序列号生成
     */
    @ExcelProperty(value = "规则明细ID，序列号生成")
    private Long ruleDetailId;
    /**
     * DP字段Id,AttrLineId
     */
    @ExcelProperty(value = "DP字段Id,AttrLineId")
    private Long dpFiledId;
    /**
     * DP属性字段
     */
    @ExcelProperty(value = "DP属性字段")
    private String dpFiledName;
    /**
     * 运算符
     */
    @ExcelProperty(value = "运算符")
    private String attrOperator;
}
