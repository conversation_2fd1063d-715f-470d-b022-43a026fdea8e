package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 物料基础数据表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "物料基础数据表DTO对象", description = "DTO对象")
public class ItemsDTO extends BaseDTO {

    private static final long serialVersionUID = 8877092908004132252L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 来源系统代码
     */
    @ApiModelProperty(value = "来源系统代码")
    private String sourceSystemCode;

    /**
     * 源系统物料ID
     */
    @ApiModelProperty(value = "源系统物料ID")
    private Long sourceItemId;

    /**
     * 源系统库存组织ID
     */
    @ApiModelProperty(value = "源系统库存组织ID")
    private Long sourceInvOrgId;

    /**
     * 第一分类
     */
    @ApiModelProperty(value = "第一分类")
    private String categorySegment1;

    /**
     * 第二分类
     */
    @ApiModelProperty(value = "第二分类")
    private String categorySegment2;

    /**
     * 第三分类
     */
    @ApiModelProperty(value = "第三分类")
    private String categorySegment3;

    /**
     * 第四分类
     */
    @ApiModelProperty(value = "第四分类")
    private String categorySegment4;

    /**
     * 第五分类
     */
    @ApiModelProperty(value = "第五分类")
    private String categorySegment5;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    private String itemStatus;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String priUom;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment1;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment2;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment3;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment4;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment5;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment6;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment7;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment8;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment9;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment10;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment11;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment12;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment13;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment14;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment15;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment16;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment17;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment18;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment19;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment20;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment21;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment22;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment23;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment24;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment25;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment26;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment27;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment28;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment29;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment30;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment31;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment32;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment33;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment34;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment35;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment36;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment37;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment38;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment39;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment40;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment41;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment42;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment43;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment44;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment45;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment46;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment47;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment48;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment49;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment50;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment51;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment52;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment53;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment54;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment55;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment56;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment57;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment58;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment59;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment60;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute15;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute16;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute17;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute18;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute19;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute20;

    /**
     * 语种
     */
    @ApiModelProperty(value = "语种")
    private String language;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    private String itemType;

    /**
     * 可售标识
     */
    @ApiModelProperty(value = "可售标识")
    private String customerOrderFlag;

    /**
     * 物料小类
     */
    @ApiModelProperty(value = "物料小类")
    private String itemSubcategory;

    /**
     * 生命周期状态
     */
    @ApiModelProperty(value = "生命周期状态")
    private String lifecycleState;

    /**
     * 临时量产标识
     */
    @ApiModelProperty(value = "临时量产标识")
    private String isTemporaryOutput;
}
