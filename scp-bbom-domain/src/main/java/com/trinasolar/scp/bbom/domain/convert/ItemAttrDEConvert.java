package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ItemAttrDTO;
import com.trinasolar.scp.bbom.domain.entity.ItemAttr;
import com.trinasolar.scp.bbom.domain.excel.ItemAttrExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 物料属性字段别名 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ItemAttrDEConvert extends BaseDEConvert<ItemAttrDTO, ItemAttr> {

    ItemAttrDEConvert INSTANCE = Mappers.getMapper(ItemAttrDEConvert.class);

    List<ItemAttrExcelDTO> toExcelDTO(List<ItemAttrDTO> dtos);

    ItemAttrExcelDTO toExcelDTO(ItemAttrDTO dto);
}
