package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.bbom.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "特殊片源A-料号匹配规则", description = "DTO对象")
public class AMaterielMatchRuleDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "国内海外")
    private String isOversea;

    @ApiModelProperty(value = "国内海外")
    private String isOverseaName;

    @ApiModelProperty(value = "电池车间")
    private String workshop;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "电池类型")
    private String cellsTypeName;

    @ApiModelProperty(value = "实际片厚")
    private String actualPieceThickness;

    @ApiModelProperty(value = "实际片厚")
    private String actualPieceThicknessName;

    @ApiModelProperty(value = "主栅两端形状")
    private String mainGridBothShape;

    @ApiModelProperty(value = "主栅两端形状")
    private String mainGridBothShapeName;

    @ApiModelProperty(value = "A-料号")
    private String aItemCode;

    public String filedGroup() {
        return StringTools.joinWith("", this.isOverseaName, this.workshop, this.cellsTypeName, this.actualPieceThicknessName,
                this.mainGridBothShapeName);
    }
}
