package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemAttrExcelDTO {

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * srcAttrId
     */
    @ExcelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrAlias
     */
    @ExcelProperty(value = "srcAttrAlias")
    private String srcAttrAlias;

    /**
     * srcCategorySegment4Id
     */
    @ExcelProperty(value = "srcCategorySegment4Id")
    private String srcCategorySegment4Id;

    /**
     * srcCategorySegment4
     */
    @ExcelProperty(value = "srcCategorySegment4")
    private String srcCategorySegment4;

    /**
     * srcAttrType
     */
    @ExcelProperty(value = "srcAttrType")
    private String srcAttrType;

    /**
     * srcOptionFlag
     */
    @ExcelProperty(value = "srcOptionFlag")
    private String srcOptionFlag;

    /**
     * srcAttrColumn
     */
    @ExcelProperty(value = "srcAttrColumn")
    private String srcAttrColumn;

    /**
     * language
     */
    @ExcelProperty(value = "language")
    private String language;
}
