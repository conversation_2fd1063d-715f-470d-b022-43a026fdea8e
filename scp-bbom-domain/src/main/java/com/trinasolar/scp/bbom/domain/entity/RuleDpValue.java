package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * BOM规则DP因子明细值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:01
 */
@Entity
@ToString
@Data
@Table(name = "bbom_rule_dp_value")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_rule_dp_value SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_rule_dp_value SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
public class RuleDpValue extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 规则明细ID
     */
    @ApiModelProperty(value = "规则明细ID")
    private Long ruleDetailId;

    /**
     * 属性值ID，序列号生成
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "属性值ID，序列号生成")
    private Long id;

    /**
     * 值类型，1-值  2-范围
     */
    @ApiModelProperty(value = "值类型，1-值  2-范围")
    private String valueType;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值Id")
    private Long attrValueId;
    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止Id")
    private Long attrValueToId;
    /**
     * 属性值_止
     */
    @ApiModelProperty(value = "属性值_止")
    private String attrValueTo;


}
