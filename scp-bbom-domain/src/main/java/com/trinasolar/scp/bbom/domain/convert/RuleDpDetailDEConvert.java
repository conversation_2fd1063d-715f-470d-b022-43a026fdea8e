package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.RuleDpDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpDetail;
import com.trinasolar.scp.bbom.domain.excel.RuleDpDetailExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * BOM规则DP因子 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RuleDpDetailDEConvert extends BaseDEConvert<RuleDpDetailDTO, RuleDpDetail> {

    RuleDpDetailDEConvert INSTANCE = Mappers.getMapper(RuleDpDetailDEConvert.class);

    List<RuleDpDetailExcelDTO> toExcelDTO(List<RuleDpDetailDTO> dtos);

    RuleDpDetailExcelDTO toExcelDTO(RuleDpDetailDTO dto);
}
