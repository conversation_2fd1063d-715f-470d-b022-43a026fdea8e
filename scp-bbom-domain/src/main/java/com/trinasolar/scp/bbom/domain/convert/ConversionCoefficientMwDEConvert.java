package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ConversionCoefficientMwDTO;
import com.trinasolar.scp.bbom.domain.entity.ConversionCoefficientMw;
import com.trinasolar.scp.bbom.domain.excel.ConversionCoefficientMwExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 兆瓦转换系数 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConversionCoefficientMwDEConvert extends BaseDEConvert<ConversionCoefficientMwDTO, ConversionCoefficientMw> {

    ConversionCoefficientMwDEConvert INSTANCE = Mappers.getMapper(ConversionCoefficientMwDEConvert.class);

    List<ConversionCoefficientMwExcelDTO> toExcelDTO(List<ConversionCoefficientMwDTO> dtos);

    ConversionCoefficientMwExcelDTO toExcelDTO(ConversionCoefficientMwDTO dto);
}
