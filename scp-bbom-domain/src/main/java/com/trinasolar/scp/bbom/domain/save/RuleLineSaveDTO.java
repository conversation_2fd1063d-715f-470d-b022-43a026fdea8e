package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * BOM规则行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 09:15:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleLine保存参数", description = "保存参数")
public class RuleLineSaveDTO extends TokenDTO implements Serializable {

    @ApiModelProperty(value = "规则行名称")
    private String name;

    /**
     * 规则头ID
     */
    @ApiModelProperty(value = "规则头ID ")
    private Long ruleHeaderId;
    /**
     * 规则行ID，序列号生成
     */
    @ApiModelProperty(value = "规则行ID，序列号生成")
    private Long ruleLineId;
    /**
     * 管控主体ID,取对应LOV行ID
     */
    @ApiModelProperty(value = "管控主体ID,取对应LOV行ID")
    private Long controlSubjectId;

    @ApiModelProperty(value = "管控主体")
    private String controlSubject;

    /**
     * 管控对象，取组件分料号动态配置标识下的组件分料号属性ID
     */
    @ApiModelProperty(value = "管控对象，取组件分料号动态配置标识下的组件分料号属性ID")
    private Long controlObjectId;

    @ApiModelProperty(value = "管控对象")
    private String controlObject;


    /**
     * 管控目的,取对应LOV行ID
     */
    @ApiModelProperty(value = "管控目的,取对应LOV行ID")
    private Long controlPurposeId;

    @ApiModelProperty(value = "管控目的")
    private String controlPurpose;
    /**
     * 值LovId
     */
    @ApiModelProperty(value = "值LovId")
    private Long attrValueId;
    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String attrValue;
    /**
     * 推荐标识
     */
    @ApiModelProperty(value = "推荐标识")
    private String defaultFlag;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 扩展属性1
     */
    @ApiModelProperty(value = "扩展属性1")
    private String attribute1;
    /**
     * 扩展属性2
     */
    @ApiModelProperty(value = "扩展属性2")
    private String attribute2;
    /**
     * 扩展属性3
     */
    @ApiModelProperty(value = "扩展属性3")
    private String attribute3;
    /**
     * 扩展属性4
     */
    @ApiModelProperty(value = "扩展属性4")
    private String attribute4;
    /**
     * 扩展属性5
     */
    @ApiModelProperty(value = "扩展属性5")
    private String attribute5;
    /**
     * 扩展属性6
     */
    @ApiModelProperty(value = "扩展属性6")
    private String attribute6;
    /**
     * 扩展属性7
     */
    @ApiModelProperty(value = "扩展属性7")
    private String attribute7;
    /**
     * 扩展属性8
     */
    @ApiModelProperty(value = "扩展属性8")
    private String attribute8;
    /**
     * 扩展属性9
     */
    @ApiModelProperty(value = "扩展属性9")
    private String attribute9;
    /**
     * 扩展属性10
     */
    @ApiModelProperty(value = "扩展属性10")
    private String attribute10;
    /**
     * 扩展属性11
     */
    @ApiModelProperty(value = "扩展属性11")
    private String attribute11;
    /**
     * 扩展属性12
     */
    @ApiModelProperty(value = "扩展属性12")
    private String attribute12;
    /**
     * 扩展属性13
     */
    @ApiModelProperty(value = "扩展属性13")
    private String attribute13;
    /**
     * 扩展属性14
     */
    @ApiModelProperty(value = "扩展属性14")
    private String attribute14;
    /**
     * 扩展属性15
     */
    @ApiModelProperty(value = "扩展属性15")
    private String attribute15;

    @ApiModelProperty(value = "RuleDpDetail保存参数")
    private List<RuleDpDetailSaveDTO> details;

    @ApiModelProperty(value = "材料规则管控对象列表")
    private List<RuleControlObjectHeaderSaveDTO> controlObjectHeaders;

    private Boolean isChange;
}
