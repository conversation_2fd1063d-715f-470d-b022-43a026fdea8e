package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 电池产品导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Entity
@ToString
@Data
@Table(name = "bbom_battery_product_import")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_battery_product_import SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_battery_product_import SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class BatteryProductImport extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池产品编码
     */
    @ApiModelProperty(value = "电池产品编码")
    @Column(name = "battery_code")
    private String batteryCode;

    /**
     * 电池产品名称
     */
    @ApiModelProperty(value = "电池产品名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 电池片晶体类型
     */
    @ApiModelProperty(value = "电池片晶体类型")
    @Column(name = "battery_crystal_type")
    private String batteryCrystalType;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    @Column(name = "product_type")
    private String productType;

    /**
     * 电池片尺寸编码
     */
    @ApiModelProperty(value = "电池片尺寸编码")
    @Column(name = "battery_dimension_code")
    private String batteryDimensionCode;

    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    @Column(name = "number_main_grids")
    private String numberMainGrids;

    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    @Column(name = "sharding_number")
    private String shardingNumber;
    /**
     * 预警提示
     */
    @ApiModelProperty(value = "预警提示")
    @Column(name = "warning_reason")
    private String warningReason;
}
