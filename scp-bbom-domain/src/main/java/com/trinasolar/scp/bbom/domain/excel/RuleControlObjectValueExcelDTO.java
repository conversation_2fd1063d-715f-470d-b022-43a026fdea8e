package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleControlObjectValueExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 规则管控对象详情ID
     */
    @ExcelProperty(value = "规则管控对象详情ID")
    private Long ruleControlObjectDetailId;
    /**
     * 值类型，1-值  2-范围
     */
    @ExcelProperty(value = "值类型，1-值  2-范围")
    private String valueType;
    /**
     * 属性值id
     */
    @ExcelProperty(value = "属性值id")
    private Long attrValueId;
    /**
     * 属性值
     */
    @ExcelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止id
     */
    @ExcelProperty(value = "属性值_止id")
    private Long attrValueToId;
    /**
     * 属性值_止
     */
    @ExcelProperty(value = "属性值_止")
    private String attrValueTo;
}
