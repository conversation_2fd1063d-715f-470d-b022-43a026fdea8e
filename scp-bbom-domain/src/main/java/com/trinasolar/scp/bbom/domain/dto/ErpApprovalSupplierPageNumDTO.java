package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ErpApprovalSupplierPageNumDTO
 * @Description
 * @Date 2023/12/29 13:42
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApprovalSupplierDTO对象", description = "DTO对象")
public class ErpApprovalSupplierPageNumDTO {
    @ApiModelProperty(value = "页码,从1开始", required = true)
    private Integer pageNumber = 1;

    @ApiModelProperty(value = "每页条数", required = true)
    private Integer pageSize = 10;
    @ApiModelProperty(value = "总页数", required = true)
    private Integer totalPages = 1;

    @ApiModelProperty(value = "数据", required = true)
    private List<ErpApprovalSupplierPageDTO> date;
}
