package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * 规则管控对象头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleControlObjectHeader保存参数", description = "保存参数")
public class RuleControlObjectHeaderSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 规则行ID
     */
    @ApiModelProperty(value = "规则行ID")
    private Long ruleLineId;
    /**
     * 结构对象ID
     */
    @ApiModelProperty(value = "结构对象ID")
    private Long structObjectId;
    /**
     * 结构对象
     */
    @ApiModelProperty(value = "结构对象")
    private String structObject;
    /**
     * 管控对象ID
     */
    @ApiModelProperty(value = "管控对象ID")
    private Long controlObjectId;
    /**
     * 管控对象
     */
    @ApiModelProperty(value = "管控对象")
    private String controlObject;
    /**
     * BOM提示：是（Y）否（N）
     */
    @ApiModelProperty(value = "BOM提示：是（Y）否（N）")
    private String bomPrompt;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "属性")
    private List<RuleControlObjectDetailSaveDTO> details;
}
