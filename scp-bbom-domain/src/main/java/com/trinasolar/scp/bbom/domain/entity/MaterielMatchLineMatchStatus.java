package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Entity
@ToString
@Data
@Table(name = "bbom_materiel_match_line_match_status")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_materiel_match_line_match_status SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_materiel_match_line_match_status SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class MaterielMatchLineMatchStatus extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * 电池料号匹配明细行ID
     */
    @ApiModelProperty(value = "电池料号匹配明细行ID")
    @Column(name = "line_id")
    private Long lineId;

    /**
     * 物料Code
     */
    @ApiModelProperty(value = "物料Code")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    @Column(name = "match_status")
    private String matchStatus;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    @Column(name = "alternate_bom_designator")
    private String alternateBomDesignator;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    @Column(name = "is_catch_production")
    private String isCatchProduction;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 工艺路线 yes or no
     */
    @ApiModelProperty(value = "工艺路线")
    @Column(name = "route")
    private String route;
    /**
     * 认证型号 取值5A料号的segment28
     */
    @ApiModelProperty(value = "认证型号")
    @Column(name = "certified_models")
    private String certifiedModels;

}
