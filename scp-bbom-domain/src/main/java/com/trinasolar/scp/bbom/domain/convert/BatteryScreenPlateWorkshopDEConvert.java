package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateWorkshopDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryScreenPlateWorkshop;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateWorkshopExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.BatteryTypeMainScreenPlateExcelDTO;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateWorkshopSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryScreenPlateWorkshopDEConvert extends BaseDEConvert<BatteryScreenPlateWorkshopDTO, BatteryScreenPlateWorkshop> {

    BatteryScreenPlateWorkshopDEConvert INSTANCE = Mappers.getMapper(BatteryScreenPlateWorkshopDEConvert.class);

    List<BatteryScreenPlateWorkshopExcelDTO> toExcelDTO(List<BatteryScreenPlateWorkshopDTO> dtos);

    BatteryScreenPlateWorkshopExcelDTO toExcelDTO(BatteryScreenPlateWorkshopDTO dto);

    BatteryScreenPlateWorkshopDTO excelToDTO(BatteryScreenPlateWorkshopExcelDTO dto);

    BatteryScreenPlateWorkshopSaveDTO toSaveDTO(BatteryScreenPlateWorkshopExcelDTO dto);

    BatteryScreenPlateWorkshop toEntity(BatteryScreenPlateWorkshopSaveDTO dto);

    BatteryScreenPlateWorkshopDTO saveToDTO(BatteryScreenPlateWorkshopSaveDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    BatteryScreenPlateWorkshop saveDTOtoEntity(BatteryScreenPlateWorkshopSaveDTO saveDTO, @MappingTarget BatteryScreenPlateWorkshop entity);

    List<BatteryTypeMainScreenPlateExcelDTO> toTypeMainExcelDTO(List<BatteryScreenPlateWorkshopDTO> BatteryScreenPlateWorkshopDTOList);

    BatteryTypeMainScreenPlateExcelDTO toTypeMainExcelDTO(BatteryScreenPlateWorkshopDTO BatteryScreenPlateWorkshopDTO);
}
