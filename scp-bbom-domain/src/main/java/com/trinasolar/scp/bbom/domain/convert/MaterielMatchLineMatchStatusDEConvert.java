package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineMatchStatusDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLineMatchStatus;
import com.trinasolar.scp.bbom.domain.excel.MaterielMatchLineMatchStatusExcelDTO;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineMatchStatusSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池料号匹配明细行匹配状态 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MaterielMatchLineMatchStatusDEConvert extends BaseDEConvert<MaterielMatchLineMatchStatusDTO, MaterielMatchLineMatchStatus> {

    MaterielMatchLineMatchStatusDEConvert INSTANCE = Mappers.getMapper(MaterielMatchLineMatchStatusDEConvert.class);

    List<MaterielMatchLineMatchStatusExcelDTO> toExcelDTO(List<MaterielMatchLineMatchStatusDTO> dtos);

    MaterielMatchLineMatchStatusExcelDTO toExcelDTO(MaterielMatchLineMatchStatusDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MaterielMatchLineMatchStatus saveDTOtoEntity(MaterielMatchLineMatchStatusSaveDTO saveDTO, @MappingTarget MaterielMatchLineMatchStatus entity);


    MaterielMatchLineMatchStatusSaveDTO dtoToSaveDTO(MaterielMatchLineMatchStatusDTO dto);

    List<MaterielMatchLineMatchStatusSaveDTO> dtoToSaveDTO(List<MaterielMatchLineMatchStatusDTO> matchLineMatchStatusDTOS);

    @Override
    @Mappings({
            @Mapping(target = "matchStatusName", expression = "java(com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO.MatchStatus.getNameByCode(entity.getMatchStatus()))")
    })
    MaterielMatchLineMatchStatusDTO toDto(MaterielMatchLineMatchStatus entity);
}
