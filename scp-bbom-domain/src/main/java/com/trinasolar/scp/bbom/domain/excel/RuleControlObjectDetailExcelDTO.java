package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 规则管控对象详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleControlObjectDetailExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 规则管控对象头ID
     */
    @ExcelProperty(value = "规则管控对象头ID")
    private Long ruleControlObjectHeaderId;
    /**
     * 材料属性ID
     */
    @ExcelProperty(value = "材料属性ID")
    private Long materialsAttrFiledId;
    /**
     * 材料属性
     */
    @ExcelProperty(value = "材料属性")
    private String materialsAttrFiled;
    /**
     * 运算符 1：包含  2：排除  3：等于
     */
    @ExcelProperty(value = "运算符 1：包含  2：排除  3：等于")
    private String attrOperator;
}
