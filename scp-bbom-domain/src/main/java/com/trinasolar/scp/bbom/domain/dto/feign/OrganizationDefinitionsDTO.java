package com.trinasolar.scp.bbom.domain.dto.feign;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OrganizationDefinitionsDTO对象", description = "DTO对象")
public class OrganizationDefinitionsDTO extends BaseDTO {

    private static final long serialVersionUID = 548201175755524481L;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "${$column.comments}")
    private Long organizationId;

    /**
     * businessGroupId
     */
    @ApiModelProperty(value = "${$column.comments}")
    private Long businessGroupId;

    /**
     * userDefinitionEnableDate
     */
    @ApiModelProperty(value = "${$column.comments}")
    private LocalDate userDefinitionEnableDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "${$column.comments}")
    private LocalDate disableDate;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "${$column.comments}")
    private String organizationCode;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "${$column.comments}")
    private String organizationName;

    /**
     * 账套ID
     */
    @ApiModelProperty(value = "${$column.comments}")
    private Long setOfBooksId;

    /**
     * chartOfAccountsId
     */
    @ApiModelProperty(value = "${$column.comments}")
    private Long chartOfAccountsId;

    /**
     * inventoryEnabledFlag
     */
    @ApiModelProperty(value = "${$column.comments}")
    private String inventoryEnabledFlag;

    /**
     * 业务实体ID
     */
    @ApiModelProperty(value = "${$column.comments}")
    private Integer operatingUnit;

    /**
     * legalEntity
     */
    @ApiModelProperty(value = "${$column.comments}")
    private Integer legalEntity;
}
