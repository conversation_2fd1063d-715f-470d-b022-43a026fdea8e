package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 同步cux3_bbom_component_v
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Data
@ApiModel(value = "ComponentV查询条件", description = "查询条件")
@Accessors(chain = true)
public class ComponentVQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 3225111842306585303L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * lastUpdateDate
     */
    @ApiModelProperty(value = "lastUpdateDate")
    private LocalDateTime lastUpdateDate;

    /**
     * billSequenceId
     */
    @ApiModelProperty(value = "billSequenceId")
    private String billSequenceId;

    /**
     * assemblyItemId
     */
    @ApiModelProperty(value = "assemblyItemId")
    private String assemblyItemId;

    /**
     * organizationId
     */
    @ApiModelProperty(value = "organizationId")
    private String organizationId;

    /**
     * assemblyType
     */
    @ApiModelProperty(value = "assemblyType")
    private String assemblyType;

    /**
     * assItemNum
     */
    @ApiModelProperty(value = "assItemNum")
    private String assItemNum;

    /**
     * assItemDes
     */
    @ApiModelProperty(value = "assItemDes")
    private String assItemDes;

    /**
     * assItemUom
     */
    @ApiModelProperty(value = "assItemUom")
    private String assItemUom;

    /**
     * alternateBomDesignator
     */
    @ApiModelProperty(value = "alternateBomDesignator")
    private String alternateBomDesignator;

    /**
     * bmAttributeCategory
     */
    @ApiModelProperty(value = "bmAttributeCategory")
    private String bmAttributeCategory;

    /**
     * bmAttribute1
     */
    @ApiModelProperty(value = "bmAttribute1")
    private String bmAttribute1;

    /**
     * bmAttribute2
     */
    @ApiModelProperty(value = "bmAttribute2")
    private String bmAttribute2;

    /**
     * bmAttribute3
     */
    @ApiModelProperty(value = "bmAttribute3")
    private String bmAttribute3;

    /**
     * bmAttribute4
     */
    @ApiModelProperty(value = "bmAttribute4")
    private String bmAttribute4;

    /**
     * bmAttribute5
     */
    @ApiModelProperty(value = "bmAttribute5")
    private String bmAttribute5;

    /**
     * bmAttribute6
     */
    @ApiModelProperty(value = "bmAttribute6")
    private String bmAttribute6;

    /**
     * bmAttribute7
     */
    @ApiModelProperty(value = "bmAttribute7")
    private String bmAttribute7;

    /**
     * bmAttribute8
     */
    @ApiModelProperty(value = "bmAttribute8")
    private String bmAttribute8;

    /**
     * bmAttribute9
     */
    @ApiModelProperty(value = "bmAttribute9")
    private String bmAttribute9;

    /**
     * bmAttribute10
     */
    @ApiModelProperty(value = "bmAttribute10")
    private String bmAttribute10;

    /**
     * bmAttribute11
     */
    @ApiModelProperty(value = "bmAttribute11")
    private String bmAttribute11;

    /**
     * bmAttribute12
     */
    @ApiModelProperty(value = "bmAttribute12")
    private String bmAttribute12;

    /**
     * bmAttribute13
     */
    @ApiModelProperty(value = "bmAttribute13")
    private String bmAttribute13;

    /**
     * bmAttribute14
     */
    @ApiModelProperty(value = "bmAttribute14")
    private String bmAttribute14;

    /**
     * bmAttribute15
     */
    @ApiModelProperty(value = "bmAttribute15")
    private String bmAttribute15;

    /**
     * componentSequenceId
     */
    @ApiModelProperty(value = "componentSequenceId")
    private String componentSequenceId;

    /**
     * substituteComponentId
     */
    @ApiModelProperty(value = "substituteComponentId")
    private String substituteComponentId;

    /**
     * componentItemId
     */
    @ApiModelProperty(value = "componentItemId")
    private String componentItemId;

    /**
     * itemNum
     */
    @ApiModelProperty(value = "itemNum")
    private String itemNum;

    /**
     * operationSeqNum
     */
    @ApiModelProperty(value = "operationSeqNum")
    private String operationSeqNum;

    /**
     * bicItemNum
     */
    @ApiModelProperty(value = "bicItemNum")
    private String bicItemNum;

    /**
     * bicItemDes
     */
    @ApiModelProperty(value = "bicItemDes")
    private String bicItemDes;

    /**
     * bicItemUom
     */
    @ApiModelProperty(value = "bicItemUom")
    private String bicItemUom;

    /**
     * basisType
     */
    @ApiModelProperty(value = "basisType")
    private String basisType;

    /**
     * componentQuantity
     */
    @ApiModelProperty(value = "componentQuantity")
    private String componentQuantity;

    /**
     * autoRequestMaterial
     */
    @ApiModelProperty(value = "autoRequestMaterial")
    private String autoRequestMaterial;

    /**
     * effectivityDate
     */
    @ApiModelProperty(value = "effectivityDate")
    private String effectivityDate;

    /**
     * disableDate
     */
    @ApiModelProperty(value = "disableDate")
    private String disableDate;

    /**
     * changeNotice
     */
    @ApiModelProperty(value = "changeNotice")
    private String changeNotice;

    /**
     * planningFactor
     */
    @ApiModelProperty(value = "planningFactor")
    private String planningFactor;

    /**
     * componentYieldFactor
     */
    @ApiModelProperty(value = "componentYieldFactor")
    private String componentYieldFactor;

    /**
     * enforceIntRequirements
     */
    @ApiModelProperty(value = "enforceIntRequirements")
    private String enforceIntRequirements;

    /**
     * includeInCostRollup
     */
    @ApiModelProperty(value = "includeInCostRollup")
    private String includeInCostRollup;

    /**
     * bicItemType
     */
    @ApiModelProperty(value = "bicItemType")
    private String bicItemType;

    /**
     * bicItemStatus
     */
    @ApiModelProperty(value = "bicItemStatus")
    private String bicItemStatus;

    /**
     * wipSupplyType
     */
    @ApiModelProperty(value = "wipSupplyType")
    private String wipSupplyType;

    /**
     * supplyType
     */
    @ApiModelProperty(value = "supplyType")
    private String supplyType;

    /**
     * bcAttributeCategory
     */
    @ApiModelProperty(value = "bcAttributeCategory")
    private String bcAttributeCategory;

    /**
     * bcAttribute1
     */
    @ApiModelProperty(value = "bcAttribute1")
    private String bcAttribute1;

    /**
     * bcAttribute2
     */
    @ApiModelProperty(value = "bcAttribute2")
    private String bcAttribute2;

    /**
     * bcAttribute3
     */
    @ApiModelProperty(value = "bcAttribute3")
    private String bcAttribute3;

    /**
     * bcAttribute4
     */
    @ApiModelProperty(value = "bcAttribute4")
    private String bcAttribute4;

    /**
     * bcAttribute5
     */
    @ApiModelProperty(value = "bcAttribute5")
    private String bcAttribute5;

    /**
     * bcAttribute6
     */
    @ApiModelProperty(value = "bcAttribute6")
    private String bcAttribute6;

    /**
     * bcAttribute7
     */
    @ApiModelProperty(value = "bcAttribute7")
    private String bcAttribute7;

    /**
     * bcAttribute8
     */
    @ApiModelProperty(value = "bcAttribute8")
    private String bcAttribute8;

    /**
     * bcAttribute9
     */
    @ApiModelProperty(value = "bcAttribute9")
    private String bcAttribute9;

    /**
     * bcAttribute10
     */
    @ApiModelProperty(value = "bcAttribute10")
    private String bcAttribute10;

    /**
     * bcAttribute11
     */
    @ApiModelProperty(value = "bcAttribute11")
    private String bcAttribute11;

    /**
     * bcAttribute12
     */
    @ApiModelProperty(value = "bcAttribute12")
    private String bcAttribute12;

    /**
     * bcAttribute13
     */
    @ApiModelProperty(value = "bcAttribute13")
    private String bcAttribute13;

    /**
     * bcAttribute14
     */
    @ApiModelProperty(value = "bcAttribute14")
    private String bcAttribute14;

    /**
     * bcAttribute15
     */
    @ApiModelProperty(value = "bcAttribute15")
    private String bcAttribute15;

    /**
     * bscFlag
     */
    @ApiModelProperty(value = "bscFlag")
    private String bscFlag;

    /**
     * 是否处理
     */
    @ApiModelProperty(value = "是否处理")
    private Integer isProcess;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
