package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.bbom.domain.dto.BillOfMaterialVO;
import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 同步cux3_bbom_component_v
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Entity
@ToString
@Data
@Table(name = "bbom_component_v")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_component_v SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_component_v SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ComponentV extends BasePO implements Serializable {
    static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final long serialVersionUID = -6838332305388073415L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    /**
     * lastUpdateDate
     */
    @ApiModelProperty(value = "lastUpdateDate")
    @Column(name = "last_update_date")
    private LocalDateTime lastUpdateDate;

    /**
     * billSequenceId
     */
    @ApiModelProperty(value = "billSequenceId")
    @Column(name = "bill_sequence_id")
    private String billSequenceId;

    /**
     * assemblyItemId
     */
    @ApiModelProperty(value = "assemblyItemId")
    @Column(name = "assembly_item_id")
    private String assemblyItemId;

    /**
     * organizationId
     */
    @ApiModelProperty(value = "organizationId")
    @Column(name = "organization_id")
    private String organizationId;

    /**
     * assemblyType
     */
    @ApiModelProperty(value = "assemblyType")
    @Column(name = "assembly_type")
    private String assemblyType;

    /**
     * assItemNum
     */
    @ApiModelProperty(value = "assItemNum")
    @Column(name = "ass_item_num")
    private String assItemNum;

    /**
     * assItemDes
     */
    @ApiModelProperty(value = "assItemDes")
    @Column(name = "ass_item_des")
    private String assItemDes;

    /**
     * assItemUom
     */
    @ApiModelProperty(value = "assItemUom")
    @Column(name = "ass_item_uom")
    private String assItemUom;

    /**
     * alternateBomDesignator
     */
    @ApiModelProperty(value = "alternateBomDesignator")
    @Column(name = "alternate_bom_designator")
    private String alternateBomDesignator;

    /**
     * bmAttributeCategory
     */
    @ApiModelProperty(value = "bmAttributeCategory")
    @Column(name = "bm_attribute_category")
    private String bmAttributeCategory;

    /**
     * bmAttribute1
     */
    @ApiModelProperty(value = "bmAttribute1")
    @Column(name = "bm_attribute1")
    private String bmAttribute1;

    /**
     * bmAttribute2
     */
    @ApiModelProperty(value = "bmAttribute2")
    @Column(name = "bm_attribute2")
    private String bmAttribute2;

    /**
     * bmAttribute3
     */
    @ApiModelProperty(value = "bmAttribute3")
    @Column(name = "bm_attribute3")
    private String bmAttribute3;

    /**
     * bmAttribute4
     */
    @ApiModelProperty(value = "bmAttribute4")
    @Column(name = "bm_attribute4")
    private String bmAttribute4;

    /**
     * bmAttribute5
     */
    @ApiModelProperty(value = "bmAttribute5")
    @Column(name = "bm_attribute5")
    private String bmAttribute5;

    /**
     * bmAttribute6
     */
    @ApiModelProperty(value = "bmAttribute6")
    @Column(name = "bm_attribute6")
    private String bmAttribute6;

    /**
     * bmAttribute7
     */
    @ApiModelProperty(value = "bmAttribute7")
    @Column(name = "bm_attribute7")
    private String bmAttribute7;

    /**
     * bmAttribute8
     */
    @ApiModelProperty(value = "bmAttribute8")
    @Column(name = "bm_attribute8")
    private String bmAttribute8;

    /**
     * bmAttribute9
     */
    @ApiModelProperty(value = "bmAttribute9")
    @Column(name = "bm_attribute9")
    private String bmAttribute9;

    /**
     * bmAttribute10
     */
    @ApiModelProperty(value = "bmAttribute10")
    @Column(name = "bm_attribute10")
    private String bmAttribute10;

    /**
     * bmAttribute11
     */
    @ApiModelProperty(value = "bmAttribute11")
    @Column(name = "bm_attribute11")
    private String bmAttribute11;

    /**
     * bmAttribute12
     */
    @ApiModelProperty(value = "bmAttribute12")
    @Column(name = "bm_attribute12")
    private String bmAttribute12;

    /**
     * bmAttribute13
     */
    @ApiModelProperty(value = "bmAttribute13")
    @Column(name = "bm_attribute13")
    private String bmAttribute13;

    /**
     * bmAttribute14
     */
    @ApiModelProperty(value = "bmAttribute14")
    @Column(name = "bm_attribute14")
    private String bmAttribute14;

    /**
     * bmAttribute15
     */
    @ApiModelProperty(value = "bmAttribute15")
    @Column(name = "bm_attribute15")
    private String bmAttribute15;

    /**
     * componentSequenceId
     */
    @ApiModelProperty(value = "componentSequenceId")
    @Column(name = "component_sequence_id")
    private String componentSequenceId;

    /**
     * substituteComponentId
     */
    @ApiModelProperty(value = "substituteComponentId")
    @Column(name = "substitute_component_id")
    private String substituteComponentId;

    /**
     * componentItemId
     */
    @ApiModelProperty(value = "componentItemId")
    @Column(name = "component_item_id")
    private String componentItemId;

    /**
     * itemNum
     */
    @ApiModelProperty(value = "itemNum")
    @Column(name = "item_num")
    private String itemNum;

    /**
     * operationSeqNum
     */
    @ApiModelProperty(value = "operationSeqNum")
    @Column(name = "operation_seq_num")
    private String operationSeqNum;

    /**
     * bicItemNum
     */
    @ApiModelProperty(value = "bicItemNum")
    @Column(name = "bic_item_num")
    private String bicItemNum;

    /**
     * bicItemDes
     */
    @ApiModelProperty(value = "bicItemDes")
    @Column(name = "bic_item_des")
    private String bicItemDes;

    /**
     * bicItemUom
     */
    @ApiModelProperty(value = "bicItemUom")
    @Column(name = "bic_item_uom")
    private String bicItemUom;

    /**
     * basisType
     */
    @ApiModelProperty(value = "basisType")
    @Column(name = "basis_type")
    private String basisType;

    /**
     * componentQuantity
     */
    @ApiModelProperty(value = "componentQuantity")
    @Column(name = "component_quantity")
    private String componentQuantity;

    /**
     * autoRequestMaterial
     */
    @ApiModelProperty(value = "autoRequestMaterial")
    @Column(name = "auto_request_material")
    private String autoRequestMaterial;

    /**
     * effectivityDate
     */
    @ApiModelProperty(value = "effectivityDate")
    @Column(name = "effectivity_date")
    private String effectivityDate;

    /**
     * disableDate
     */
    @ApiModelProperty(value = "disableDate")
    @Column(name = "disable_date")
    private String disableDate;

    /**
     * changeNotice
     */
    @ApiModelProperty(value = "changeNotice")
    @Column(name = "change_notice")
    private String changeNotice;

    /**
     * planningFactor
     */
    @ApiModelProperty(value = "planningFactor")
    @Column(name = "planning_factor")
    private String planningFactor;

    /**
     * componentYieldFactor
     */
    @ApiModelProperty(value = "componentYieldFactor")
    @Column(name = "component_yield_factor")
    private String componentYieldFactor;

    /**
     * enforceIntRequirements
     */
    @ApiModelProperty(value = "enforceIntRequirements")
    @Column(name = "enforce_int_requirements")
    private String enforceIntRequirements;

    /**
     * includeInCostRollup
     */
    @ApiModelProperty(value = "includeInCostRollup")
    @Column(name = "include_in_cost_rollup")
    private String includeInCostRollup;

    /**
     * bicItemType
     */
    @ApiModelProperty(value = "bicItemType")
    @Column(name = "bic_item_type")
    private String bicItemType;

    /**
     * bicItemStatus
     */
    @ApiModelProperty(value = "bicItemStatus")
    @Column(name = "bic_item_status")
    private String bicItemStatus;

    /**
     * wipSupplyType
     */
    @ApiModelProperty(value = "wipSupplyType")
    @Column(name = "wip_supply_type")
    private String wipSupplyType;

    /**
     * supplyType
     */
    @ApiModelProperty(value = "supplyType")
    @Column(name = "supply_type")
    private String supplyType;

    /**
     * bcAttributeCategory
     */
    @ApiModelProperty(value = "bcAttributeCategory")
    @Column(name = "bc_attribute_category")
    private String bcAttributeCategory;

    /**
     * bcAttribute1
     */
    @ApiModelProperty(value = "bcAttribute1")
    @Column(name = "bc_attribute1")
    private String bcAttribute1;

    /**
     * bcAttribute2
     */
    @ApiModelProperty(value = "bcAttribute2")
    @Column(name = "bc_attribute2")
    private String bcAttribute2;

    /**
     * bcAttribute3
     */
    @ApiModelProperty(value = "bcAttribute3")
    @Column(name = "bc_attribute3")
    private String bcAttribute3;

    /**
     * bcAttribute4
     */
    @ApiModelProperty(value = "bcAttribute4")
    @Column(name = "bc_attribute4")
    private String bcAttribute4;

    /**
     * bcAttribute5
     */
    @ApiModelProperty(value = "bcAttribute5")
    @Column(name = "bc_attribute5")
    private String bcAttribute5;

    /**
     * bcAttribute6
     */
    @ApiModelProperty(value = "bcAttribute6")
    @Column(name = "bc_attribute6")
    private String bcAttribute6;

    /**
     * bcAttribute7
     */
    @ApiModelProperty(value = "bcAttribute7")
    @Column(name = "bc_attribute7")
    private String bcAttribute7;

    /**
     * bcAttribute8
     */
    @ApiModelProperty(value = "bcAttribute8")
    @Column(name = "bc_attribute8")
    private String bcAttribute8;

    /**
     * bcAttribute9
     */
    @ApiModelProperty(value = "bcAttribute9")
    @Column(name = "bc_attribute9")
    private String bcAttribute9;

    /**
     * bcAttribute10
     */
    @ApiModelProperty(value = "bcAttribute10")
    @Column(name = "bc_attribute10")
    private String bcAttribute10;

    /**
     * bcAttribute11
     */
    @ApiModelProperty(value = "bcAttribute11")
    @Column(name = "bc_attribute11")
    private String bcAttribute11;

    /**
     * bcAttribute12
     */
    @ApiModelProperty(value = "bcAttribute12")
    @Column(name = "bc_attribute12")
    private String bcAttribute12;

    /**
     * bcAttribute13
     */
    @ApiModelProperty(value = "bcAttribute13")
    @Column(name = "bc_attribute13")
    private String bcAttribute13;

    /**
     * bcAttribute14
     */
    @ApiModelProperty(value = "bcAttribute14")
    @Column(name = "bc_attribute14")
    private String bcAttribute14;

    /**
     * bcAttribute15
     */
    @ApiModelProperty(value = "bcAttribute15")
    @Column(name = "bc_attribute15")
    private String bcAttribute15;

    /**
     * bscFlag
     */
    @ApiModelProperty(value = "bscFlag")
    @Column(name = "bsc_flag")
    private String bscFlag;

    /**
     * 是否处理
     */
    @ApiModelProperty(value = "是否处理")
    @Column(name = "is_process")
    private Integer isProcess;

    /**
     * 错误讯息
     */
    @ApiModelProperty(value = "错误讯息")
    @Column(name = "error_msg")
    private String errorMsg;


    public ComponentV(BillOfMaterialVO vo) {
        this.setLastUpdateDate(LocalDateTime.parse(vo.getLastUpdateDate(), dateTimeFormatter));
        this.setBillSequenceId(vo.getBillSequenceId());
        this.setAssemblyItemId(vo.getAssemblyItemId());
        this.setOrganizationId(vo.getOrganizationCode());
        this.setAssemblyType(vo.getAssemblyType());
        this.setAssItemNum(vo.getItemNumber());
        this.setAssItemDes(vo.getItemDescription());
        this.setAssItemUom(vo.getUomCode());
        this.setAlternateBomDesignator(vo.getAlternateBomDesignator());
        this.setBmAttributeCategory(vo.getBmAttributeCategory());
        this.setBmAttribute1(vo.getBmAttribute1());
        this.setBmAttribute2(vo.getAttribute2());
        this.setBmAttribute3(vo.getBmAttribute3());
        this.setBmAttribute4(vo.getBmAttribute4());
        this.setBmAttribute5(vo.getBmAttribute5());
        this.setBmAttribute6(vo.getAttribute6());
        this.setBmAttribute7(vo.getAttribute7());
        this.setBmAttribute8(vo.getAttribute8());
        this.setBmAttribute9(vo.getAttribute9());
        this.setBmAttribute10(vo.getBmAttribute10());
        this.setBmAttribute11(vo.getBmAttribute11());
        this.setBmAttribute12(vo.getBmAttribute12());
        this.setBmAttribute13(vo.getBmAttribute13());
        this.setBmAttribute14(vo.getBmAttribute14());
        this.setBmAttribute15(vo.getBmAttribute15());
        this.setComponentSequenceId(vo.getBomComponentSequenceId());
        this.setSubstituteComponentId(vo.getSubstituteComponentId());
        this.setComponentItemId(vo.getBomComponentItemId());
        this.setItemNum(vo.getItemNum());
        this.setOperationSeqNum(vo.getOperationSeqNum());
        this.setBicItemNum(vo.getComponentItem());
        this.setBicItemDes(vo.getComponentItemDescription());
        this.setBicItemUom(vo.getBicItemUom());
        this.setBasisType(vo.getBasisType());
        this.setComponentQuantity(vo.getComponentQuantity());
        this.setAutoRequestMaterial(vo.getAutoRequestMaterial());
        this.setEffectivityDate(vo.getEffectivityDate());
        this.setDisableDate(vo.getDisableDate());
        this.setChangeNotice(vo.getChangeNotice());
        this.setPlanningFactor(vo.getPlanningFactor());
        this.setComponentYieldFactor(vo.getComponentYieldFactor());
        this.setEnforceIntRequirements(vo.getEnforceIntRequirements());
        this.setIncludeInCostRollup(vo.getIncludeInCostRollup());
        this.setBicItemType(vo.getBicItemType());
        this.setBicItemStatus(vo.getBicItemStatus());
        this.setWipSupplyType(vo.getWipSupplyType());
        this.setSupplyType(vo.getSupplyType());
        this.setBcAttributeCategory(vo.getBcAttributeCategory());
        this.setBcAttribute1(vo.getBcAttribute1());
        this.setBcAttribute2(vo.getBcAttribute2());
        this.setBcAttribute3(vo.getBcAttribute3());
        this.setBcAttribute4(vo.getBcAttribute4());
        this.setBcAttribute5(vo.getBcAttribute5());
        this.setBcAttribute6(vo.getBcAttribute6());
        this.setBcAttribute7(vo.getBcAttribute7());
        this.setBcAttribute8(vo.getBcAttribute8());
        this.setBcAttribute9(vo.getBcAttribute9());
        this.setBcAttribute10(vo.getBcAttribute10());
        this.setBcAttribute11(vo.getBcAttribute11());
        this.setBcAttribute12(vo.getBcAttribute12());
        this.setBcAttribute13(vo.getBcAttribute13());
        this.setBcAttribute14(vo.getBcAttribute14());
        this.setBcAttribute15(vo.getBcAttribute15());
        this.setBscFlag(vo.getBscFlag());
        this.setIsProcess(0);
    }
}
