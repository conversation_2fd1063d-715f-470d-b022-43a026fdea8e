package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "物料属性字段别名DTO对象", description = "DTO对象")
public class ItemAttrDTO extends BaseDTO {

    private static final long serialVersionUID = -5222867163633620791L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrAlias
     */
    @ApiModelProperty(value = "srcAttrAlias")
    private String srcAttrAlias;

    /**
     * srcCategorySegment4Id
     */
    @ApiModelProperty(value = "srcCategorySegment4Id")
    private String srcCategorySegment4Id;

    /**
     * srcCategorySegment4
     */
    @ApiModelProperty(value = "srcCategorySegment4")
    private String srcCategorySegment4;

    /**
     * srcAttrType
     */
    @ApiModelProperty(value = "srcAttrType")
    private String srcAttrType;

    /**
     * srcOptionFlag
     */
    @ApiModelProperty(value = "srcOptionFlag")
    private String srcOptionFlag;

    /**
     * srcAttrColumn
     */
    @ApiModelProperty(value = "srcAttrColumn")
    private String srcAttrColumn;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    private String language;
}
