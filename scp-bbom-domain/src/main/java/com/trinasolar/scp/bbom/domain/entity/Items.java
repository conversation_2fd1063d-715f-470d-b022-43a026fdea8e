package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 物料基础数据表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
@Entity
@ToString
@Data
@Table(name = "bbom_items")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_items SET is_deleted = 1 WHERE item_id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_items SET is_deleted = 1 WHERE item_id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class Items extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 物料ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "物料ID")
    @Column(name = "item_id")
    private Long itemId;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 来源系统代码
     */
    @ApiModelProperty(value = "来源系统代码")
    @Column(name = "source_system_code")
    private String sourceSystemCode;

    /**
     * 源系统物料ID
     */
    @ApiModelProperty(value = "源系统物料ID")
    @Column(name = "source_item_id")
    private Long sourceItemId;

    /**
     * 源系统库存组织ID
     */
    @ApiModelProperty(value = "源系统库存组织ID")
    @Column(name = "source_inv_org_id")
    private Long sourceInvOrgId;

    /**
     * 第一分类
     */
    @ApiModelProperty(value = "第一分类")
    @Column(name = "category_segment1")
    private String categorySegment1;

    /**
     * 第二分类
     */
    @ApiModelProperty(value = "第二分类")
    @Column(name = "category_segment2")
    private String categorySegment2;

    /**
     * 第三分类
     */
    @ApiModelProperty(value = "第三分类")
    @Column(name = "category_segment3")
    private String categorySegment3;

    /**
     * 第四分类
     */
    @ApiModelProperty(value = "第四分类")
    @Column(name = "category_segment4")
    private String categorySegment4;

    /**
     * 第五分类
     */
    @ApiModelProperty(value = "第五分类")
    @Column(name = "category_segment5")
    private String categorySegment5;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    @Column(name = "item_status")
    private String itemStatus;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    @Column(name = "pri_uom")
    private String priUom;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment1")
    private String segment1;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment2")
    private String segment2;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment3")
    private String segment3;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment4")
    private String segment4;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment5")
    private String segment5;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment6")
    private String segment6;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment7")
    private String segment7;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment8")
    private String segment8;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment9")
    private String segment9;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment10")
    private String segment10;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment11")
    private String segment11;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment12")
    private String segment12;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment13")
    private String segment13;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment14")
    private String segment14;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment15")
    private String segment15;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment16")
    private String segment16;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment17")
    private String segment17;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment18")
    private String segment18;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment19")
    private String segment19;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment20")
    private String segment20;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment21")
    private String segment21;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment22")
    private String segment22;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment23")
    private String segment23;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment24")
    private String segment24;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment25")
    private String segment25;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment26")
    private String segment26;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment27")
    private String segment27;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment28")
    private String segment28;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment29")
    private String segment29;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment30")
    private String segment30;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment31")
    private String segment31;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment32")
    private String segment32;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment33")
    private String segment33;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment34")
    private String segment34;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment35")
    private String segment35;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment36")
    private String segment36;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment37")
    private String segment37;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment38")
    private String segment38;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment39")
    private String segment39;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment40")
    private String segment40;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment41")
    private String segment41;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment42")
    private String segment42;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment43")
    private String segment43;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment44")
    private String segment44;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment45")
    private String segment45;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment46")
    private String segment46;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment47")
    private String segment47;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment48")
    private String segment48;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment49")
    private String segment49;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment50")
    private String segment50;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment51")
    private String segment51;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment52")
    private String segment52;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment53")
    private String segment53;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment54")
    private String segment54;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment55")
    private String segment55;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment56")
    private String segment56;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment57")
    private String segment57;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment58")
    private String segment58;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment59")
    private String segment59;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "segment60")
    private String segment60;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute1")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute2")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute3")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute4")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute5")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute6")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute7")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute8")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute9")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute10")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute11")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute12")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute13")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute14")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute15")
    private String attribute15;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute16")
    private String attribute16;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute17")
    private String attribute17;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute18")
    private String attribute18;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute19")
    private String attribute19;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    @Column(name = "attribute20")
    private String attribute20;

    /**
     * 语种
     */
    @ApiModelProperty(value = "语种")
    @Column(name = "language")
    private String language;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @Column(name = "item_type")
    private String itemType;

    /**
     * 可售标识
     */
    @ApiModelProperty(value = "可售标识")
    @Column(name = "customer_order_flag")
    private String customerOrderFlag;

    /**
     * 物料小类
     */
    @ApiModelProperty(value = "物料小类")
    @Column(name = "item_subcategory")
    private String itemSubcategory;

    /**
     * 生命周期状态
     */
    @ApiModelProperty(value = "生命周期状态")
    @Column(name = "lifecycle_state")
    private String lifecycleState;

    @ApiModelProperty(value = "临时量产标识")
    @Column(name = "is_temporary_output")
    private String isTemporaryOutput;
}
