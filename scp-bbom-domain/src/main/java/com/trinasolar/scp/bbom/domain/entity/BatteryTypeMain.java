package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 电池类型静态属性
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Entity
@ToString
@Data
@Table(name = "bbom_battery_type_main")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_battery_type_main SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_battery_type_main SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class BatteryTypeMain extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    @Column(name = "battery_code")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "单双晶")
    @Column(name = "crystal_type")
    private String crystalType;

    /**
     * PN型
     */
    @ApiModelProperty(value = "PN型")
    @Column(name = "p_or_n")
    private String pOrN;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    @Column(name = "category")
    private String category;

    /**
     * 单双面
     */
    @ApiModelProperty(value = "单双面")
    @Column(name = "single_double_face")
    private String singleDoubleFace;

    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅类别")
    @Column(name = "number_main_grids")
    private String numberMainGrids;

    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    @Column(name = "sharding_mode")
    private String shardingMode;

    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    @Column(name = "product_classification")
    private String productClassification;
}
