package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "特殊片源匹配规则保存参数", description = "保存参数")
public class SpecialCellMatchRuleSaveDTO extends TokenDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;

    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    @ApiModelProperty(value = "片源种类")
    private String cellSource;

    @ApiModelProperty(value = "美学")
    private String aesthetics;

    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    @ApiModelProperty(value = "产品等级")
    private String productionGrade;

    @ApiModelProperty(value = "常规电池类型")
    private String commonCellsType;
}
