package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.bbom.domain.utils.StringTools;
import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "特殊片源匹配规则", description = "DTO对象")
public class SpecialCellMatchRuleDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID主键")
    private Long id;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "电池类型")
    private String cellsTypeName;

    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;

    @ApiModelProperty(value = "小区域国家")
    private String regionalCountryName;

    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    @ApiModelProperty(value = "H追溯")
    private String hTraceName;

    @ApiModelProperty(value = "片源种类")
    private String cellSource;

    @ApiModelProperty(value = "片源种类")
    private String cellSourceName;

    @ApiModelProperty(value = "美学")
    private String aesthetics;

    @ApiModelProperty(value = "美学")
    private String aestheticsName;

    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlassName;

    @ApiModelProperty(value = "产品等级")
    private String productionGrade;

    @ApiModelProperty(value = "产品等级")
    private String productionGradeName;

    @ApiModelProperty(value = "常规电池类型")
    private String commonCellsType;

    @ApiModelProperty(value = "常规电池类型")
    private String commonCellsTypeName;

    public String filedGroup() {
        return StringTools.joinWith("",
                Optional.ofNullable(this.cellsTypeName).orElse("无"),
                Optional.ofNullable(this.regionalCountryName).orElse("无"),
                Optional.ofNullable(this.hTraceName).orElse("无"),
                Optional.ofNullable(this.cellSourceName).orElse("无"),
                Optional.ofNullable(this.aestheticsName).orElse("无"),
                Optional.ofNullable(this.transparentDoubleGlassName).orElse("无"));
    }
}
