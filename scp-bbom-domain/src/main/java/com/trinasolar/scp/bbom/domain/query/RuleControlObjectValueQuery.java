package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 规则管控对象值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
@Data
@ApiModel(value = "RuleControlObjectValue查询条件", description = "查询条件")
public class RuleControlObjectValueQuery extends PageDTO implements Serializable {

}
