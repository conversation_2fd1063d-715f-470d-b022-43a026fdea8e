package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * BOM规则DP因子明细值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:01
 */
@Data
@ApiModel(value = "RuleDpValue查询条件", description = "查询条件")
public class RuleDpValueQuery extends PageDTO implements Serializable {

}
