package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 硅片损耗率信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@ApiModel(value = "SiliconWaferLoss查询条件", description = "查询条件")
@Accessors(chain = true)
public class SiliconWaferLossQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 硅片良率
     */
    @ApiModelProperty(value = "电池良率")
    private String siliconWaferYield;
    /**
     * 硅片隔离率
     */
    @ApiModelProperty(value = "硅片隔离率")
    private String siliconIsolationRate;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
