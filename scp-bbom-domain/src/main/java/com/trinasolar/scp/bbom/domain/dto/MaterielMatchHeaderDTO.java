package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池物料号匹配DTO对象", description = "DTO对象")
public class MaterielMatchHeaderDTO extends BaseDTO {

    private static final long serialVersionUID = 8747486727706496073L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    private Long batteryTypeId;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    private String specialArea;

    /**
     * 特殊区域Id
     */
    @ApiModelProperty(value = "特殊区域Id")
    private Long specialAreaId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String pcsSourceLevel;

    /**
     * 是否有特殊要求
     */
    @ApiModelProperty(value = "是否有特殊要求")
    private String isSpecialRequirements;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenManufacturer;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siliconMaterialManufacturer;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String batteryManufacturer;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverSlurryManufacturer;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;

    /**
     * 硅片购买方式
     */
    @ApiModelProperty(value = "硅片购买方式")
    private String siliconWaferPurchaseMethod;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandPlace;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String basePlace;

    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 排产日期
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    private BigDecimal line;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;

    /**
     * PN型
     */
    @ApiModelProperty(value = "PN型")
    private String pOrN;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;

    /**
     * 单双面
     */
    @ApiModelProperty(value = "单双面")
    private String singleDoubleFace;

    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    private String numberMainGrids;

    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    private String shardingMode;

    /**
     * 搭配状态
     */
    @ApiModelProperty(value = "搭配状态")
    private String matchStatus;

    @ApiModelProperty(value = "行数据")
    private Map<String, List<MaterielMatchLineDTO>> lines;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    private String processCategory;

    /**
     * 电池物料料号
     */
    @ApiModelProperty(value = "电池物料料号")
    private String itemCode;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemDesc;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 匹配狀態
     */
    @ApiModelProperty(value = "匹配狀態")
    private String matchHeaderStatus;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    @ApiModelProperty(value = "月份_国内海外Id")
    private String monthAndOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    private String oldMonth;
    /**
     * 请求标识
     * 排产4A的试试segment30 对应加工类型 为无的不校验
     */
    @ApiModelProperty(value = "请求标识")
    private String requestFlag;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "需求日期")
    private LocalDate demandDate;

    @ApiModelProperty(value = "")
    private String alternateBomDesignator;

    @ApiModelProperty(value = "")
    private String description;

    private List<CellPlanLineDTO> planDTOList;
    private List<Map<String, MaterielMatchLineDTO>> matchLineListMap;

    @ApiModelProperty(value = "料号匹配类型:instock入库plan投产")
    private String planType;

    /**
     * 排产日期
     */
    @ApiModelProperty(value = "排产日期")
    private LocalDate scheduleDate;

    @ApiModelProperty(value = "法碳配比")
    private String ratioCode;

    @ApiModelProperty(value = "ECS_CODE")
    private String ecsCode;

    /**
     * 供应方式名称
     */
    @ApiModelProperty(value = "供应方式名称")
    private String supplyModeName;

    @ApiModelProperty(value = "特殊订单")
    private String specialOrder;

    @ApiModelProperty(value = "制造工艺")
    private String manufactureProcess;
}
