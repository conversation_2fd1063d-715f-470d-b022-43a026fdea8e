package com.trinasolar.scp.bbom.domain.dto.feign.bmrp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "DTO对象", description = "DTO对象")
public class ErpOpenPRDTO {

    @ApiModelProperty(value = "主键")
    private Long prLineId;

    /**
     * 采购申请行ID
     */
    @ApiModelProperty(value = "采购申请行ID")
    private Long reqLineId;

    /**
     * 采购申请头ID
     */
    @ApiModelProperty(value = "采购申请头ID")
    private Long reqHeaderId;

    /**
     * 采购申请单号
     */
    @ApiModelProperty(value = "采购申请单号")
    private String reqHeaderNum;


    /**
     * 业务实体
     */
    @ApiModelProperty(value = "业务实体")
    private String orgName;

    /**
     * 头取消标识
     */
    @ApiModelProperty(value = "头取消标识")
    private String headerCancelFlag;

    /**
     * 头关闭标识
     */
    @ApiModelProperty(value = "头关闭标识")
    private String headerClosedFlag;

    /**
     * 行取消标识
     */
    @ApiModelProperty(value = "行取消标识")
    private String lineCancelFlag;

    /**
     * 行关闭标识
     */
    @ApiModelProperty(value = "行关闭标识")
    private String lineClosedFlag;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemDesc;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String uom;

    /**
     * 申请数量
     */
    @ApiModelProperty(value = "申请数量")
    private BigDecimal quantity;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String requestBy;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String organizationCode;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    /**
     * 供应商地点
     */
    @ApiModelProperty(value = "供应商地点")
    private String vendorSite;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
