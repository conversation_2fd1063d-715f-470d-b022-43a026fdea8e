package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * pdm物料生命周期原始表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@Data
@ApiModel(value = "PdmItemLifecycle查询条件", description = "查询条件")
@Accessors(chain = true)
public class PdmItemLifecycleQuery extends PageDTO implements Serializable {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 同步日期
     */
    @ApiModelProperty(value = "同步日期")
    private LocalDate syncDate;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String itemCode;

    /**
     * name
     */
    @ApiModelProperty(value = "name")
    private String name;

    /**
     * 生命周期
     */
    @ApiModelProperty(value = "生命周期")
    private String lifecycle;

    /**
     * stage
     */
    @ApiModelProperty(value = "stage")
    private String stage;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String itemDesc;

    /**
     * 组织id
     */
    @ApiModelProperty(value = "组织id")
    private Long orgId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
