package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryScreenPlate;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.BatteryScreenPlateExcelMailDTO;
import com.trinasolar.scp.bbom.domain.excel.BatteryTypeMainScreenPlateExcelDTO;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型动态属性-网版 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryScreenPlateDEConvert extends BaseDEConvert<BatteryScreenPlateDTO, BatteryScreenPlate> {

    BatteryScreenPlateDEConvert INSTANCE = Mappers.getMapper(BatteryScreenPlateDEConvert.class);

    List<BatteryScreenPlateExcelDTO> toExcelDTO(List<BatteryScreenPlateDTO> dtos);

    List<BatteryScreenPlateExcelMailDTO> toExcelMailDTO(List<BatteryScreenPlateDTO> dtos);

    BatteryScreenPlateExcelDTO toExcelDTO(BatteryScreenPlateDTO dto);

    BatteryScreenPlateDTO excelToDTO(BatteryScreenPlateExcelDTO dto);

    BatteryScreenPlateSaveDTO toSaveDTO(BatteryScreenPlateExcelDTO dto);

    BatteryScreenPlateDTO saveToDTO(BatteryScreenPlateSaveDTO dto);

    BatteryScreenPlateSaveDTO dtoToSave(BatteryScreenPlateDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    BatteryScreenPlate saveDTOtoEntity(BatteryScreenPlateSaveDTO saveDTO, @MappingTarget BatteryScreenPlate entity);

    List<BatteryTypeMainScreenPlateExcelDTO> toTypeMainExcelDTO(List<BatteryScreenPlateDTO> batteryScreenPlateDTOList);

    BatteryTypeMainScreenPlateExcelDTO toTypeMainExcelDTO(BatteryScreenPlateDTO batteryScreenPlateDTO);
}
