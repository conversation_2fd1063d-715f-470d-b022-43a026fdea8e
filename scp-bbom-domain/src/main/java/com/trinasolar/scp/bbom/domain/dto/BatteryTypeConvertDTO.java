package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryTypeConvertDTO
 * @Description
 * @Date 2023/12/31 16:39
 **/
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class BatteryTypeConvertDTO {

    private String name;
    private String data;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * PN型
     */
    @ApiModelProperty(value = "PN型")
    private String pOrN;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;
    /**
     * 单双面
     */
    @ApiModelProperty(value = "单双面")
    private String singleDoubleFace;
    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    private String numberMainGrids;
    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    private String shardingMode;

    private String segment1;
    private String segment2;
    private String segment3;
    private String segment4;
    private String segment5;
    private String segment6;
    private String segment7;
    private String segment8;
    private String segment9;
    private String segment10;
    private String segment11;
    private String segment12;
    private String segment13;
    private String segment14;
    private String segment15;


}
