package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName BatteryTypeMWDTO
 * @Description
 * @Date 2023/12/20 10:23
 **/
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "兆瓦转换系数属性DTO对象", description = "DTO对象")
public class BatteryTypeMWDTO extends BaseDTO {
    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    private String classify;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 电池MW系数
     */
    @ApiModelProperty(value = "电池MW系数")
    private BigDecimal batteryMWQty;
    /**
     * 电池目标良率
     */
    @ApiModelProperty(value = "电池目标良率")
    private BigDecimal batteryEfficiencyQty;
    /**
     * 单耗
     */
    @ApiModelProperty(value = "单耗")
    private BigDecimal unitConsumption;
    /**
     * 物料MW系数
     */
    @ApiModelProperty(value = "物料MW系数")
    private BigDecimal materielMWQty;
    /**
     * 预警
     */
    @ApiModelProperty(value = "预警")
    private String warningReason;
}
