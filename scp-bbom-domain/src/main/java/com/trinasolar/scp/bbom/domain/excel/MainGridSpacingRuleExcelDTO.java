package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * 电池主栅间距规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-05-17 08:55:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MainGridSpacingRuleExcelDTO {

    /**
     * ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 电池类型
     */
    @ExcelIgnore
    private String batteryType;

    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String batteryTypeName;

    /**
     * 组件车间
     */
    @ExcelProperty(value = "组件车间")
    private String itemWorkshop;

    /**
     * 电池车间
     */
    @ExcelProperty(value = "电池车间")
    private String batteryWorkshop;

    /**
     * 主栅间距
     */
    @ExcelProperty(value = "主栅间距")
    private String mainGridSpacing;


    /**
     * 有效日期_起
     */
    @ExcelProperty(value = "有效日期_起")
    private LocalDate effectiveStartDate;

    /**
     * 有效日期_止
     */
    @ExcelProperty(value = "有效日期_止")
    private LocalDate effectiveEndDate;

}
