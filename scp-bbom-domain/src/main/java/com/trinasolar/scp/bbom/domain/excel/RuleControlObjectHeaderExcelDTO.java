package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 规则管控对象头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleControlObjectHeaderExcelDTO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 规则行ID
     */
    @ExcelProperty(value = "规则行ID")
    private Long ruleLineId;
    /**
     * 结构对象ID
     */
    @ExcelProperty(value = "结构对象ID")
    private Long structObjectId;
    /**
     * 结构对象
     */
    @ExcelProperty(value = "结构对象")
    private String structObject;
    /**
     * 管控对象ID
     */
    @ExcelProperty(value = "管控对象ID")
    private Long controlObjectId;
    /**
     * 管控对象
     */
    @ExcelProperty(value = "管控对象")
    private String controlObject;
    /**
     * BOM提示：是（Y）否（N）
     */
    @ExcelProperty(value = "BOM提示：是（Y）否（N）")
    private String bomPrompt;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
