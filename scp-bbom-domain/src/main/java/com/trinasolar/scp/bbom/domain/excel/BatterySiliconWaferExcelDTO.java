package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trinasolar.scp.common.api.util.LocalDateConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * 电池类型动态属性-硅片
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatterySiliconWaferExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型编码
     */
    @ExcelProperty(value = "电池类型编码")
    @ExcelIgnore
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型")
    private String batteryName;
    /**
     * 基地
     */
    @ExcelIgnore
    private String basePlace;
    /**
     * 基地
     */
    @ExcelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 车间
     */
    @ExcelIgnore
    private String workshop;
    /**
     * 车间
     */
    @ExcelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 生产单元
     */
    @ExcelIgnore
    private String workunit;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunitName;

    /**
     * 新硅片料号
     */
    @ExcelProperty(value = "新硅片料号")
    private String itemCodeNew;

    /**
     * 硅片属性
     */
    @ExcelIgnore
    private String siliconWaferProperties;
    /**
     * 硅片属性
     */
    @ExcelProperty(value = "新硅片属性")
    @ExcelIgnore
    private String siliconWaferPropertiesName;
    /**
     * 条件项
     */
    @ExcelIgnore
    private String conditionItem;
    /**
     * 条件项
     */
    @ExcelProperty(value = "新条件项")
    @ExcelIgnore
    private String conditionItemName;
    /**
     * 值
     */
    @ExcelIgnore
    private String batteryValue;
    /**
     * 值
     */
    @ExcelProperty(value = "新值")
    @ExcelIgnore
    private String batteryValueName;

    /**
     * 旧硅片属性
     */
    @ExcelIgnore
    private String oldSiliconWaferProperties;

    /**
     * 旧硅片属性
     */
    @ExcelProperty(value = "旧硅片属性")
    @ExcelIgnore
    private String oldSiliconWaferPropertiesName;

    /**
     * 旧条件项
     */
    @ExcelIgnore
    private String oldConditionItem;

    /**
     * 旧条件项
     */
    @ExcelProperty(value = "旧条件项")
    @ExcelIgnore
    private String oldConditionItemName;

    /**
     * 旧值
     */
    @ExcelIgnore
    private String oldBatteryValue;

    /**
     * 旧值
     */
    @ExcelProperty(value = "旧值")
    @ExcelIgnore
    private String oldBatteryValueName;

    /**
     * 是否低碳
     */
    @ExcelProperty(value = "是否低碳")
    private String lowCarbonFlag;

    /**
     * 硅片厚度
     */
    @ExcelProperty(value = "硅片厚度")
    private String waferThickness;

    /**
     * 硅片品类
     */
    @ExcelProperty(value = "硅片品类")
    private String waferCategory;

    @ExcelProperty(value = "硅片ECS CODE")
    private String waferEcsCode;

    /**
     * 新线体数量
     */
    @ExcelProperty(value = "新线体数量")
    private Integer lineQty;

    /**
     * 有效期起
     */
    @ExcelProperty(value = "有效日期_起", converter = LocalDateConverter.class)
    private LocalDate effectiveStartDate;
    /**
     * 有效期止
     */
    @ExcelProperty(value = "有效日期_止", converter = LocalDateConverter.class)
    private LocalDate effectiveEndDate;


}
