package com.trinasolar.scp.bbom.domain.save;

import com.trinasolar.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 网版寿命信息维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScreenLife保存参数", description = "保存参数")
public class ScreenLifeSaveDTO extends TokenDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryTypeName;
    /**
     * 网版料号
     */
    @ApiModelProperty(value = "网版料号")
    private String itemCode;
    /**
     * 料号说明
     */
    @ApiModelProperty(value = "料号说明")
    private String itemDesc;
    /**
     * 物料状态
     * TODO 需要调用接口 暂时为提供 后续调试
     */
    @ApiModelProperty(value = "物料状态")
    private String materialStatus;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地id
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlaceName;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshopName;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String machine;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String machineName;
    /**
     * 寿命（万）
     */
    @ApiModelProperty(value = "寿命（万）1")
    private String lifetime;
    /**
     * 有效日期_起
     */
    @ApiModelProperty(value = "有效日期_起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效日期_止
     */
    @ApiModelProperty(value = "有效日期_止")
    private LocalDateTime effectiveEndDate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * BOM寿命
     */
    @ApiModelProperty(value = "BOM寿命")
    private Integer bomLife;

    @ApiModelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "单玻")
    private String singleGlassFlag;
}
