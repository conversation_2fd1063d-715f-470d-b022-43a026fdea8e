package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.bbom.domain.entity.Items;
import com.trinasolar.scp.bbom.domain.excel.ItemsExcelDTO;
import com.trinasolar.scp.bbom.domain.vo.StructureItemVO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 物料基础数据表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ItemsDEConvert extends BaseDEConvert<ItemsDTO, Items> {

    ItemsDEConvert INSTANCE = Mappers.getMapper(ItemsDEConvert.class);

    List<ItemsExcelDTO> toExcelDTO(List<ItemsDTO> dtos);

    ItemsExcelDTO toExcelDTO(ItemsDTO dto);

    StructureItemVO itemToStructureItemVO(Items items);
}
