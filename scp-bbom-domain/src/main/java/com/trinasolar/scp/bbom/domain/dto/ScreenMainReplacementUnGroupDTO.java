package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/10/1
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ScreenMainReplacementUnGroupDTO {
    @ApiModelProperty(value = "电池型号")
    private String batteryType;

    @ApiModelProperty(value = "分片方式")
    private String shardingMode;

    @ApiModelProperty(value = "小区域")
    private String regionalCountry;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ApiModelProperty(value = "单玻")
    private String singleGlass;

    @ApiModelProperty(value = "机台")
    private String workbench;

    @ApiModelProperty(value = "主替标识")
    private String subFlag;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "网版料号")
    private String itemCode;

    @ApiModelProperty(value = "料号描述")
    private String itemDescription;

    @ApiModelProperty(value = "车间")
    private String workShop;

    @ApiModelProperty(value = "生产区域")
    private String productionArea;

    @ApiModelProperty(value = "厂商")
    private String vendor;

    public String group() {
        return StringUtils.join(this.batteryType, this.shardingMode, this.regionalCountry, this.mainGridSpace,
                this.singleGlass, this.workbench, this.subFlag, this.model, this.itemCode, this.itemDescription, this.vendor);
    }
}