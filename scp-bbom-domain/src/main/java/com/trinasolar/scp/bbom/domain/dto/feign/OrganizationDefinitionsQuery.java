package com.trinasolar.scp.bbom.domain.dto.feign;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Data
@ApiModel(value = "OrganizationDefinitions查询条件", description = "查询条件")
public class OrganizationDefinitionsQuery extends PageDTO implements Serializable {
    private static final long serialVersionUID = -3236802860167846778L;

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "组织代码")
    private String organizationCode;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "业务实体ID")
    private Integer operatingUnit;

    @ApiModelProperty(value = "SCP使用标识（Y）")
    private String scpFlag;
    @ApiModelProperty(value = "电池BOM同步ERP(二期)")
    private String cellsScpFlag;

    @ApiModelProperty("组织ID集合")
    private List<Long> organizationIds;
}
