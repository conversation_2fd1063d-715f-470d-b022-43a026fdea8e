package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 电池料号匹配明细行匹配状态
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池料号匹配明细行匹配状态DTO对象", description = "DTO对象")
public class MaterielMatchLineMatchStatusDTO extends BaseDTO {

    private static final long serialVersionUID = 4655573376275562899L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 电池料号匹配明细行ID
     */
    @ApiModelProperty(value = "电池料号匹配明细行ID")
    private Long lineId;

    /**
     * 物料Code
     */
    @ApiModelProperty(value = "物料Code")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态Name")
    private String matchStatusName;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String isCatchProduction;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 工艺路线 yes or no
     */
    @ApiModelProperty(value = "工艺路线")
    private String route;
    /**
     * 认证型号 取值5A料号的segment28
     */
    @ApiModelProperty(value = "认证型号")
    private String certifiedModels;
}
