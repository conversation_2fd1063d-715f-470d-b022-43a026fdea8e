package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 电池类型动态属性-浆料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池类型动态属性-浆料DTO对象", description = "DTO对象")
public class BatterySlurryDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Transient
    private String basePlaceName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Transient
    private String workshopName;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Transient
    private String workunitName;
    /**
     * 物料料号-新
     */
    @ApiModelProperty(value = "物料料号-新")
    private String itemCodeNew;
    /**
     * 物料-新说明
     */
    @ApiModelProperty(value = "物料-新说明")
    private String itemDescNew;
    /**
     * 物料料号-旧
     */
    @ApiModelProperty(value = "物料料号-旧")
    private String itemCodeOld;
    /**
     * 物料-旧说明
     */
    @ApiModelProperty(value = "物料-旧说明")
    private String itemDescOld;
    /**
     * 线体-新
     */
    @ApiModelProperty(value = "线体数量-新")
    private String lineNew;
    /**
     * 线体-旧
     */
    @ApiModelProperty(value = "线体数量-旧")
    private String lineOld;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 是否存在bom
     */
    @ApiModelProperty(value = "是否存在bom")
    private String isExistBom;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDateTime effectiveEndDate;

    @ApiModelProperty(value = "有效期起")
    private String effectiveStartDates;

    @ApiModelProperty(value = "有效期止")
    private String effectiveEndDates;
    /**
     * 切换类型
     */
    @ApiModelProperty(value = "切换类型")
    private String switchingType;
    /**
     * 切换类型名称
     */
    @ApiModelProperty(value = "切换类型名称")
    private String switchingTypeName;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workBench;
    /**
     * 机台名称
     */
    @ApiModelProperty(value = "机台名称")
    private String workBenchName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 维护类型
     */
    @ApiModelProperty(value = "维护类型")
    private String leadType;
    /**
     * 维护类型id
     */
    @ApiModelProperty(value = "维护类型id")
    private Long leadTypeId;

    /**
     * 维护类型名称
     */
    @ApiModelProperty(value = "维护类型名称")
    private String leadTypeName;
    /**
     * 生命周期值
     */
    @ApiModelProperty(value = "生命周期值-新")
    private String lifecycleStateNew;
    /**
     * 生命周期值
     */
    @ApiModelProperty(value = "生命周期值-旧")
    private String lifecycleStateOld;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "主栅间距")
    private String mainGridSpace;


    @ApiModelProperty(value = "单玻")
    private String singleGlassFlag;

    @ApiModelProperty(value = "单玻")
    private String singleGlassFlagName;
}
