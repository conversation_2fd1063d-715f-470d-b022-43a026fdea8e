package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatterySlurryDTO;
import com.trinasolar.scp.bbom.domain.entity.BatterySlurry;
import com.trinasolar.scp.bbom.domain.excel.BatterySlurryExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.BatterySlurryExcelMailDTO;
import com.trinasolar.scp.bbom.domain.excel.BatteryTypeMainSlurryExcelDTO;
import com.trinasolar.scp.bbom.domain.save.BatterySlurrySaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型动态属性-浆料 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatterySlurryDEConvert extends BaseDEConvert<BatterySlurryDTO, BatterySlurry> {

    BatterySlurryDEConvert INSTANCE = Mappers.getMapper(BatterySlurryDEConvert.class);

    List<BatterySlurryExcelDTO> toExcelDTO(List<BatterySlurryDTO> dtos);

    List<BatterySlurryExcelMailDTO> toMailExcelDTO(List<BatterySlurryDTO> dtos);


    BatterySlurryExcelDTO toExcelDTO(BatterySlurryDTO dto);

    BatterySlurrySaveDTO toSaveDTO(BatterySlurryExcelDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    BatterySlurry saveDTOtoEntity(BatterySlurrySaveDTO saveDTO, @MappingTarget BatterySlurry entity);

    List<BatteryTypeMainSlurryExcelDTO> toTypeMainExcelDTO(List<BatterySlurryDTO> batterySlurryDTOS);

    BatteryTypeMainSlurryExcelDTO toTypeMainExcelDTO(BatterySlurryDTO batterySlurryDTO);
}
