package com.trinasolar.scp.bbom.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MdmItemAttrHeaderVO {
    private String categoryID;

    private String categoryName;

    private List<MdmItemAttrLineVO> Attributes;


}
// {
//        "categoryID": "7A018001001",
//        "categoryName": "反光膜",
//        "Attributes": [
//            {
//                "AttributeID": "7A01800100101",
//                "AttributeName": "类别",
//                "Seg": "Seg1",
//                "hasLOV": "false"
//            },
//            {
//                "AttributeID": "7A01800100102",
//                "AttributeName": "规格",
//                "Seg": "Seg2",
//                "hasLOV": "false"
//            },