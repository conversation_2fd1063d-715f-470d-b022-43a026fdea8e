package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 物料属性字段Lov
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "物料属性字段LovDTO对象", description = "DTO对象")
public class ItemAttrLovDTO extends BaseDTO {

    private static final long serialVersionUID = -6119694035144999239L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * lovId
     */
    @ApiModelProperty(value = "lovId")
    private String lovId;

    /**
     * lovName
     */
    @ApiModelProperty(value = "lovName")
    private String lovName;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrName
     */
    @ApiModelProperty(value = "srcAttrName")
    private String srcAttrName;

    /**
     * lovLineId
     */
    @ApiModelProperty(value = "lovLineId")
    private String lovLineId;

    /**
     * lovLineValue
     */
    @ApiModelProperty(value = "lovLineValue")
    private String lovLineValue;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    private String language;

    /**
     * isRequired
     */
    @ApiModelProperty(value = "isRequired")
    private String isRequired;
}
