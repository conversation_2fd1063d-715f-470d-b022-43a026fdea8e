package com.trinasolar.scp.bbom.domain.excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * excel主体对象
 *
 * <AUTHOR>
 * @date 2022年9月21日15:26:24
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ExcelMain {
    @ApiModelProperty("表头信息")
    private List<ExcelHead> excelHeads;
    @ApiModelProperty("表格数据信息")
    private List<Map<String, Object>> data;
}
