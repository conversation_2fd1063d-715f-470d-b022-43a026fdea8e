package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 规则管控对象详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RuleControlObjectDetailDTO对象", description = "DTO对象")
public class RuleControlObjectDetailDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 规则管控对象头ID
     */
    @ApiModelProperty(value = "规则管控对象头ID")
    private Long ruleControlObjectHeaderId;
    /**
     * 材料属性ID
     */
    @ApiModelProperty(value = "材料属性ID")
    private Long materialsAttrFiledId;
    /**
     * 材料属性
     */
    @ApiModelProperty(value = "材料属性")
    private String materialsAttrFiled;
    /**
     * 运算符 1：包含  2：排除  3：等于
     */
    @ApiModelProperty(value = "运算符 1：包含  2：排除  3：等于")
    private String attrOperator;

    @ApiModelProperty(value = "运算符")
    private String attrOperatorName;

    @ApiModelProperty(value = "值")
    private List<RuleControlObjectValueDTO> values;
}
