package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.baps.CellProductionPlanDTO;
import com.trinasolar.scp.bbom.domain.entity.MaterielMatchLine;
import com.trinasolar.scp.bbom.domain.excel.MaterielMatchLineExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.ScreenMainReplacementExcelDTO;
import com.trinasolar.scp.bbom.domain.excel.SlurryMainReplacementExcelDTO;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.ScreenMainReplacementVO;
import com.trinasolar.scp.bbom.domain.vo.SlurryMainReplacementVO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池料号匹配明细行 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MaterielMatchLineDEConvert extends BaseDEConvert<MaterielMatchLineDTO, MaterielMatchLine> {

    MaterielMatchLineDEConvert INSTANCE = Mappers.getMapper(MaterielMatchLineDEConvert.class);

    List<MaterielMatchLineExcelDTO> toExcelDTO(List<MaterielMatchLineDTO> dtos);

    MaterielMatchLineExcelDTO toExcelDTO(MaterielMatchLineDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "ratioCode" ),
            @Mapping(target = "ecsCode" ),
            @Mapping(target = "supplyModeName" ),
    })
    MaterielMatchLine saveDTOtoEntity(MaterielMatchLineSaveDTO saveDTO, @MappingTarget MaterielMatchLine entity);

    MaterielMatchLineSaveDTO dtoToSaveDTO(MaterielMatchLineDTO dto);

    @Mappings(
            {
                @Mapping(target = "ratioCode" ),
                @Mapping(target = "ecsCode" ),
                @Mapping(target = "supplyModeName" ),
            }
    )
    List<MaterielMatchLineSaveDTO> dtoToSaveDTO(List<MaterielMatchLineDTO> newLineDTOS);

    @Override
    @Mappings({
            @Mapping(target = "matchStatusName", expression = "java(com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO.MatchStatus.getNameByCode(entity.getMatchStatus()))"),
            @Mapping(target = "itemMatchStatusName", expression = "java(com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO.MatchStatus.getNameByCode(entity.getItemMatchStatus()))"),
            @Mapping(target = "supplyModeName"),
            @Mapping(target = "ratioCode"),
            @Mapping(target = "ecsCode"),
    })
    MaterielMatchLineDTO toDto(MaterielMatchLine entity);


    @Override
    @Mappings({
            @Mapping(target = "supplyModeName"),
            @Mapping(target = "ratioCode"),
            @Mapping(target = "ecsCode")
    })
    List<MaterielMatchLineDTO>  toDto(List<MaterielMatchLine> entity);

    MaterielMatchLineDTO planToLineDto(CellProductionPlanDTO productionPlanDTO);

    MaterielMatchLineDTO deepCopy(MaterielMatchLineDTO lineDTO);

    List<SlurryMainReplacementExcelDTO> toSlurryMainReplacementExcelDTO(List<SlurryMainReplacementVO> content);

    SlurryMainReplacementExcelDTO toSlurryMainReplacementExcelDTO(SlurryMainReplacementVO vo);

    List<ScreenMainReplacementExcelDTO> toScreenMainReplacementExcelDTO(List<ScreenMainReplacementVO> content);
}
