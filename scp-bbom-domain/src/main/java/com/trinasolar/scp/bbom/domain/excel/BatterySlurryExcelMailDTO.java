package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 电池类型动态属性-浆料
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatterySlurryExcelMailDTO {


    /**
     * 切换类型名称
     */
    @ExcelProperty(value = "切换类型名称")
    private String switchingTypeName;
    /**
     * 维护类型
     */
    @ExcelProperty(value = "维护类型")
    private String leadTypeName;
    /**
     * 电池类型名称
     */
    @ExcelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 基地
     */
    @ExcelProperty(value = "基地")
    private String basePlace;
    /**
     * 车间
     */
    @ExcelProperty(value = "车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 机台
     */
    @ExcelIgnore
    private String workBenchName;
    /**
     * 物料料号-旧
     */
    @ExcelProperty(value = "物料料号-旧")
    private String itemCodeOld;
    /**
     * 物料-旧说明
     */
    @ExcelProperty(value = "物料-旧说明")
    private String itemDescOld;
    /**
     * 物料料号-新
     */
    @ExcelProperty(value = "物料料号-新")
    private String itemCodeNew;
    /**
     * 物料-新说明
     */
    @ExcelProperty(value = "物料-新说明")
    private String itemDescNew;
    /**
     * 线体-旧
     */
    @ExcelProperty(value = "线体数量-旧")
    private String lineOld;
    /**
     * 线体-新
     */
    @ExcelProperty(value = "线体数量-新")
    private String lineNew;

    /**
     * 有效期起
     */
    @ExcelProperty(value = "有效期起")
    private String effectiveStartDates;
    /**
     * 有效期止
     */
    @ExcelProperty(value = "有效期止")
    private String effectiveEndDates;
    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String vendorName;
    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

}
