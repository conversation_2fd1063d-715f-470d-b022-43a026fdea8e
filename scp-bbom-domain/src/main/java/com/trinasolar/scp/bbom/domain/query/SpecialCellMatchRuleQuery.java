package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@ApiModel(value = "SpecialCellMatchRule查询条件", description = "查询条件")
@Accessors(chain = true)
public class SpecialCellMatchRuleQuery extends PageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    @ApiModelProperty(value = "小区域国家")
    private String regionalCountry;

    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    @ApiModelProperty(value = "片源种类")
    private String cellSource;

    @ApiModelProperty(value = "美学")
    private String aesthetics;

    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
