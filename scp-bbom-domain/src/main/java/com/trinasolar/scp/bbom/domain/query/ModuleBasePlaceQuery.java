package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 基础产地
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-08 14:33:46
 */
@Data
@ApiModel(value = "ModuleBasePlace查询条件", description = "查询条件")
@Accessors(chain = true)
public class ModuleBasePlaceQuery extends PageDTO implements Serializable {

    @ApiModelProperty(value = "产地信息")
    private String placeInfo;

    @ApiModelProperty(value = "产地信息列表")
    private List<String> placeInfos;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;


    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String inventoryOrganization;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;


    private String[] header;

    private HashMap<String, Integer> columnMap;

    /**
     * 车间列表
     */
    @ApiModelProperty(value = "车间列表")
    private List<String> workshopList;

    private ExcelPara excelPara;
}
