package com.trinasolar.scp.bbom.domain.enums.bom;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/8
 */
@AllArgsConstructor
@Getter
public enum AttrOperatorEnum {
    /**
     * AttrOperator的关系
     */
    EQUAL("3", "等于"),
    INCLUDE("1", "包含"),
    EXCLUDE("2", "排除");
    private String value;
    private String name;

    public static List<String> getValues() {
        return Arrays.stream(values()).map(AttrOperatorEnum::getValue).collect(Collectors.toList());
    }
}
