package com.trinasolar.scp.bbom.domain.query.feign.bmrp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TjOnHandQuery {

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;
    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private List<Long> organizationIds;
    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private String organizationCode;
    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private List<String> organizationCodes;
    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemCode;
    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private List<String> itemCodes;
    /**
     * 库存日期
     */
    @ApiModelProperty(value = "库存日期")
    private LocalDate localDate;


    private LocalDateTime inventoryDate;
}
