package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物料属性字段别名
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@Data
@ApiModel(value = "ItemAttr查询条件", description = "查询条件")
@Accessors(chain = true)
public class ItemAttrQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 277328170284651317L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * srcAttrId
     */
    @ApiModelProperty(value = "srcAttrId")
    private String srcAttrId;

    /**
     * srcAttrAlias
     */
    @ApiModelProperty(value = "srcAttrAlias")
    private String srcAttrAlias;

    /**
     * srcCategorySegment4Id
     */
    @ApiModelProperty(value = "srcCategorySegment4Id")
    private String srcCategorySegment4Id;

    /**
     * srcCategorySegment4
     */
    @ApiModelProperty(value = "srcCategorySegment4")
    private String srcCategorySegment4;

    /**
     * srcAttrType
     */
    @ApiModelProperty(value = "srcAttrType")
    private String srcAttrType;

    /**
     * srcOptionFlag
     */
    @ApiModelProperty(value = "srcOptionFlag")
    private String srcOptionFlag;

    /**
     * srcAttrColumn
     */
    @ApiModelProperty(value = "srcAttrColumn")
    private String srcAttrColumn;

    /**
     * language
     */
    @ApiModelProperty(value = "language")
    private String language;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
