package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电池类型动态属性-浆料
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@ApiModel(value = "BatterySlurry查询条件", description = "查询条件")
@Accessors(chain = true)
public class BatterySlurryQuery extends PageDTO implements Serializable {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    private String batteryCode;
    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    private String batteryName;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    private String basePlace;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private List<String> workshopList;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;
    /**
     * 物料料号-新
     */
    @ApiModelProperty(value = "物料料号-新")
    private String itemCodeNew;
    /**
     * 物料-新说明
     */
    @ApiModelProperty(value = "物料-新说明")
    private String itemDescNew;
    /**
     * 物料料号-旧
     */
    @ApiModelProperty(value = "物料料号-旧")
    private String itemCodeOld;
    /**
     * 物料-旧说明
     */
    @ApiModelProperty(value = "物料-旧说明")
    private String itemDescOld;
    /**
     * 线体-新
     */
    @ApiModelProperty(value = "线体数量-新")
    private String lineNew;
    /**
     * 线体-旧
     */
    @ApiModelProperty(value = "线体数量-旧")
    private String lineOld;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    private LocalDateTime effectiveStartDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    private LocalDateTime effectiveEndDate;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;

    /**
     * 切换类型
     */
    @ApiModelProperty(value = "切换类型")
    private String switchingType;

    /**
     * 切换类型名称
     */
    @ApiModelProperty(value = "切换类型名称")
    private String switchingTypeName;
    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    private String workBench;

    /**
     * 机台名称
     */
    @ApiModelProperty(value = "机台名称")
    private String workBenchName;

    /**
     * 维护类型
     */
    @ApiModelProperty(value = "维护类型")
    private String leadType;
    /**
     * 维护类型id
     */
    @ApiModelProperty(value = "维护类型id")
    private Long leadTypeId;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long vendorId;
}
