package com.trinasolar.scp.bbom.domain.dto.feign.bmrp;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;


/**
 * 合格供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-29 07:44:20
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "合格供应商表DTO对象", description = "DTO对象")
public class ApprovedVendorDTO extends BaseDTO {

    private static final long serialVersionUID = -981138685625487154L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料分类")
    private String itemCategory;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;

    /**
     * 正背面
     */
    @ApiModelProperty(value = "正背面")
    private String frontBack;

    /**
     * 工位
     */
    @ApiModelProperty(value = "工位")
    private String opPosition;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemDesc;

    /**
     * 物料规格
     */
    @ApiModelProperty(value = "物料规格")
    private String itemSpec;

    /**
     * 网板型号
     */
    @ApiModelProperty(value = "网板型号")
    private String itemModel;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String vendorName;

    /**
     * 供应商(简称)
     */
    @ApiModelProperty(value = "供应商(简称)")
    private String vendorNameAlt;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    public String sign() {
        return StringUtils.join(category, frontBack, opPosition, vendorId);
    }

}
