package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Entity
@ToString
@Data
@Table(name = "bbom_materiel_match_header")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_materiel_match_header SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_materiel_match_header SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class MaterielMatchHeader extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "battery_type")
    private String batteryType;

    /**
     * 电池类型Id
     */
    @ApiModelProperty(value = "电池类型Id")
    @Column(name = "battery_type_id")
    private Long batteryTypeId;
    
    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    @Column(name = "aesthetics")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    @Column(name = "transparent_double_glass")
    private String transparentDoubleGlass;

    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    @Column(name = "special_area")
    private String specialArea;

    /**
     * 特殊区域Id
     */
    @ApiModelProperty(value = "特殊区域Id")
    @Column(name = "special_area_id")
    private Long specialAreaId;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    @Column(name = "h_trace")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    @Column(name = "pcs_source_type")
    private String pcsSourceType;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    @Column(name = "pcs_source_level")
    private String pcsSourceLevel;

    /**
     * 是否有特殊要求
     */
    @ApiModelProperty(value = "是否有特殊要求")
    @Column(name = "is_special_requirements")
    private String isSpecialRequirements;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    @Column(name = "screen_manufacturer")
    private String screenManufacturer;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    @Column(name = "silicon_material_manufacturer")
    private String siliconMaterialManufacturer;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    @Column(name = "battery_manufacturer")
    private String batteryManufacturer;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    @Column(name = "silver_slurry_manufacturer")
    private String silverSlurryManufacturer;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    @Column(name = "low_resistance")
    private String lowResistance;

    /**
     * 硅片购买方式
     */
    @ApiModelProperty(value = "硅片购买方式")
    @Column(name = "silicon_wafer_purchase_method")
    private String siliconWaferPurchaseMethod;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    @Column(name = "demand_place")
    private String demandPlace;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "line")
    private BigDecimal line;

    /**
     * 搭配状态
     */
    @ApiModelProperty(value = "搭配状态")
    @Column(name = "match_status")
    private String matchStatus;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "version")
    private String version;

    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    @Column(name = "process_category")
    private String processCategory;

    /**
     * 电池物料料号
     */
    @ApiModelProperty(value = "电池物料料号")
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    @Column(name = "item_desc")
    private String itemDesc;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @Column(name = "end_time")
    private LocalDateTime endTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    @Column(name = "match_header_status")
    private String matchHeaderStatus;

    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    @Column(name = "production_grade")
    private String productionGrade;
    /**
     * 国内海外Id
     */
    @ApiModelProperty(value = "国内海外Id")
    @Column(name = "is_oversea_id")
    private Long isOverseaId;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "is_oversea")
    private String isOversea;
    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    @Column(name = "old_month")
    private String oldMonth;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    /**
     * 入库与投产计划类型
     */
    @ApiModelProperty(value = "入库与投产计划类型")
    @Column(name = "plan_type")
    private String planType;

    @ApiModelProperty(value = "法碳配比")
    @Column(name = "ratio_code")
    private String ratioCode;

    @ApiModelProperty(value = "ECS-CODE")
    @Column(name = "ecs_code")
    private String ecsCode;

    /**
     * 供应方式名称
     */
    @ApiModelProperty(value = "供应方式名称")
    @Column(name = "supply_mode_name")
    private String supplyModeName;

    @Column(name = "special_order")
    @ApiModelProperty(value = "特殊订单")
    private String specialOrder;

    @Column(name = "manufacture_process")
    @ApiModelProperty(value = "制造工艺")
    private String manufactureProcess;
}
