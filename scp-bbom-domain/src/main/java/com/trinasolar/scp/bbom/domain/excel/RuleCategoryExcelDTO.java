package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * 品类匹配规则导出excel表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleCategoryExcelDTO {

    @ApiModelProperty(value = "序号")
    @ExcelProperty(value = "序号")
    private Integer no;


    @ApiModelProperty(value = "规则分类名称")
    @ExcelProperty(value = "规则分类名称")
    private String ruleCategoryName;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    @ExcelProperty(value = "规则名称")
    private String ruleName;
    /**
     * 规则编码
     */
    @ApiModelProperty(value = "规则编码")
    @ExcelProperty(value = "规则编码")
    private String ruleNumber;

    /**
     * 规则因子1
     */
    @ApiModelProperty(value = "规则因子")
    @ExcelProperty(value = "规则因子")
    private String ruleFiledName;
    /**
     * 条件项
     */
    @ApiModelProperty(value = "条件项")
    @ExcelProperty(value = "条件项")
    private String conditionalItem;
    /**
     * 值\范围
     */
    @ApiModelProperty(value = "值/范围")
    @ExcelProperty(value = "值/范围")
    private String valuRange;
    /**
     * 管控对象
     */
    @ApiModelProperty(value = "管控对象")
    @ExcelProperty(value = "管控对象")
    private String controlObjects;
    /**
     * 属性
     */
    @ApiModelProperty(value = "属性")
    @ExcelProperty(value = "属性")
    private String attribute;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @ExcelProperty(value = "开始时间")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @ExcelProperty(value = "结束时间")
    private LocalDate endTime;


}
