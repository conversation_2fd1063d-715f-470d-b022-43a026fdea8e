package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 电池良率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池良率表DTO对象", description = "DTO对象")
public class CellFineDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外id")
    private Long isOverseaId;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型id")
    private Long cellsTypeId;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地id")
    private Long basePlaceId;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private Long workshopid;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;
    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    private BigDecimal m1;
    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    private BigDecimal m2;
    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    private BigDecimal m3;
    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    private BigDecimal m4;
    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    private BigDecimal m5;
    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    private BigDecimal m6;
    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    private BigDecimal m7;
    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    private BigDecimal m8;
    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    private BigDecimal m9;
    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    private BigDecimal m10;
    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    private BigDecimal m11;
    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    private BigDecimal m12;
}
