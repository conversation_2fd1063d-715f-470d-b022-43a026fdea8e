package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchLineQuery;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 料号匹配 查询条件转换 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MaterielMatchQueryDEConvert extends BaseDEConvert<MaterielMatchLineQuery, MaterielMatchHeaderQuery> {

    MaterielMatchQueryDEConvert INSTANCE = Mappers.getMapper(MaterielMatchQueryDEConvert.class);
}
