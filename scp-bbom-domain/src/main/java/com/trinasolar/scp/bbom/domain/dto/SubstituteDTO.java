/**
 * @Function: SubstituteDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 14:27:41
 */
package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * @Function: SubstituteDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuweiwei
 * @date: 2024年1月3日 14:27:41
 */

/**
 *
 * @Function: SubstituteDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuweiwei
 * @date: 2024年1月3日 14:27:41
 */
@Data
public class SubstituteDTO {

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;
    @ApiModelProperty(value = "电池物料编码")
    private String cellItemCode;
    @ApiModelProperty(value = "物料编码")
    private String itemCode;
    @ApiModelProperty(value = "物料编码-替代")
    private List<String> itemCodeSubs;
    private String alternateBomDesignator;
    @ApiModelProperty(value = "构件物料id")
    private Long componentSequenceId;
}
