package com.trinasolar.scp.bbom.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MdmItemAttrLovHeaderVO {
    //    {
    //        "lovID": "FA00100200504",
    //        "lovName": "波纹管_颜色",
    //        "AttributeID": "FA00100200504",
    //        "options": [
    //            {
    //                "id": "FA010020050401",
    //                "value": "黑色"
    //            }
    //        ]
    //    }
    private String lovID;

    private String lovName;

    private String AttributeID;

    private List<MdmItemAttrLovLineVO> options;


}
