package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 电池产品导入表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BatteryProductImportExcelDTO {

    /**
     * ID主键
     */
    @ExcelIgnore
    private Long id;
    /**
     * 电池产品编码
     */
    @ExcelProperty(value = "{bbom_header_batteryCode}")
    private String batteryCode;
    /**
     * 电池产品名称
     */
    @ExcelProperty(value = "{bbom_header_batteryName}")
    private String batteryName;
    /**
     * 电池片晶体类型
     */
    @ExcelProperty(value = "{bbom_header_batteryCrystalType}")
    private String batteryCrystalType;
    /**
     * 产品类型
     */
    @ExcelProperty(value = "{bbom_header_productType}")
    private String productType;
    /**
     * 电池片尺寸编码
     */
    @ExcelProperty(value = "{bbom_header_batteryDimensionCode}")
    private String batteryDimensionCode;
    /**
     * 主栅数
     */
    @ExcelProperty(value = "{bbom_header_numberMainGrids}")
    private String numberMainGrids;
    /**
     * 分片数
     */
    @ExcelProperty(value = "{bbom_header_shardingNumber}")
    private String shardingNumber;
    /**
     * 预警提示
     */
    @ExcelProperty(value = "{bbom_header_warningReason}")
    private String warningReason;
}
