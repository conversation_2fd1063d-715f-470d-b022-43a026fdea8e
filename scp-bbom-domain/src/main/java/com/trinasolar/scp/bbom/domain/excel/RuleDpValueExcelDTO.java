package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * BOM规则DP因子明细值
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 10:19:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleDpValueExcelDTO {

    /**
     * 规则明细ID
     */
    @ExcelProperty(value = "规则明细ID")
    private Long ruleDetailId;
    /**
     * 属性值ID，序列号生成
     */
    @ExcelProperty(value = "属性值ID，序列号生成")
    private Long id;
    /**
     * 值类型
     */
    @ExcelProperty(value = "值类型")
    private String valueType;
    /**
     * 属性值Id
     */
    @ExcelProperty(value = "属性值Id")
    private Long attrValueId;
    /**
     * 属性值
     */
    @ExcelProperty(value = "属性值")
    private String attrValue;
    /**
     * 属性值_止id
     */
    @ExcelProperty(value = "属性值_止id")
    private Long attrValueToId;
    /**
     * 属性值_止
     */
    @ExcelProperty(value = "属性值_止")
    private String attrValueTo;
}
