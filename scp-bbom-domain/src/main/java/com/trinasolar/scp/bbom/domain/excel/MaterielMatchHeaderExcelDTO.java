package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;


/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterielMatchHeaderExcelDTO {

    /**
     * ID主键
     */
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;
    /**
     * 电池类型
     */
    @ExcelProperty(value = "电池类型")
    private String batteryType;
    /**
     * 产品等级
     */
    @ExcelProperty(value = "产品等级")
    private String productionGrade;
    /**
     * 美学
     */
    @ExcelProperty(value = "美学")
    private String aesthetics;
    /**
     * 透明双玻
     */
    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlass;
    /**
     * 特殊区域
     */
    @ExcelProperty(value = "特殊区域")
    private String specialArea;
    /**
     * H追溯
     */
    @ExcelProperty(value = "H追溯")
    private String hTrace;
    /**
     * 片源种类
     */
    @ExcelProperty(value = "片源种类")
    private String pcsSourceType;
    /**
     * 电池厂家
     */
    @ExcelProperty(value = "电池厂家")
    private String batteryManufacturer;
    /**
     * 是否有特殊要求
     */
    @ExcelProperty(value = "是否有特殊要求")
    private String isSpecialRequirements;
    /**
     * 硅料厂家
     */
    @ExcelProperty(value = "硅料厂家")
    private String siliconMaterialManufacturer;
    /**
     * 网版厂家
     */
    @ExcelProperty(value = "网版厂家")
    private String screenManufacturer;
    /**
     * 银浆厂家
     */
    @ExcelProperty(value = "银浆厂家")
    private String silverSlurryManufacturer;
    /**
     * 低阻
     */
    @ExcelProperty(value = "低阻")
    private String lowResistance;
    /**
     * 需求地
     */
    @ExcelProperty(value = "需求地")
    private String demandPlace;
    /**
     * 硅片等级
     */
    @ExcelProperty(value = "硅片等级")
    private String pcsSourceLevel;
    /**
     * 加工类别
     */
    @ExcelProperty(value = "加工类别")
    private String processCategory;
    /**
     * 排产基地
     */
    @ExcelProperty(value = "排产基地")
    private String basePlace;

    /**
     * 排产车间
     */
    @ExcelProperty(value = "排产车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ExcelProperty(value = "生产单元")
    private String workunit;
    /**
     * 线体
     */
    @ExcelProperty(value = "线体数量")
    private BigDecimal line;
    /**
     * 排产时间
     */
    @ExcelProperty(value = "排产时间")
    private String month;
    /**
     * 网版切换料号 7A
     */
    @ExcelProperty(value = "网版切换料号")
    private String screenPlateItemCode;
    /**
     * 网版物料说明
     */
    @ExcelProperty(value = "网版物料说明")
    private String screenPlateItemCodeDesc;
    /**
     * 物料Code 5A
     */
    @ExcelProperty(value = "电池物料")
    private String itemCode;

    /**
     * 物料说明
     */
    @ExcelProperty(value = "电池物料说明")
    private String itemDesc;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal cellQty;
    /**
     * 研发/量产
     */
    @ExcelProperty(value = "研发/量产")
    private String isCatchProduction;
    /**
     * BOM替代项
     */
    @ExcelProperty(value = "BOM替代项")
    private String alternateBomDesignator;
    /**
     * 切换开始时间
     */
    @ExcelProperty(value = "切换开始时间")
    private String switchStartDate;
    /**
     * 切换结束时间
     */
    @ExcelProperty(value = "切换结束时间")
    private String switchEndDate;
    /**
     * 排产开始时间
     */
    @ExcelProperty(value = "排产开始时间")
    private String startTime;
    /**
     * 排产完成时间
     */
    @ExcelProperty(value = "排产完成时间")
    private String endTime;
    /**
     * 工艺路线 yes or no
     */
    @ExcelProperty(value = "工艺路线")
    private String route;

    /**
     * 认证型号 取值5A料号的segment28
     */
    @ExcelProperty(value = "认证型号")
    private String certifiedModels;
    /**
     * 匹配状态
     */
    @ExcelProperty(value = "匹配状态")
    private String matchStatus;
    /**
     * 问题点
     */
    @ExcelProperty(value = "问题点")
    private String remark;
}
