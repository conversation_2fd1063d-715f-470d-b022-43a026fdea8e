package com.trinasolar.scp.bbom.domain.dto.feign;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 物料基础数据表(其他系统)
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 14:46:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ExternalItemsDTO对象", description = "DTO对象")
public class ExternalItemsDTO extends BaseDTO {

    private static final long serialVersionUID = -5365192627870656461L;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 库存组织ID
     */
    @ApiModelProperty(value = "库存组织ID")
    private Long organizationId;

    /**
     * 来源系统代码
     */
    @ApiModelProperty(value = "来源系统代码")
    private String sourceSystemCode;

    /**
     * 源系统物料ID
     */
    @ApiModelProperty(value = "源系统物料ID")
    private Long sourceItemId;

    /**
     * 源系统库存组织ID
     */
    @ApiModelProperty(value = "源系统库存组织ID")
    private Long sourceInvOrgId;

    /**
     * 第一分类
     */
    @ApiModelProperty(value = "第一分类")
    private String categorySegment1;

    /**
     * 第二分类
     */
    @ApiModelProperty(value = "第二分类")
    private String categorySegment2;

    /**
     * 第三分类
     */
    @ApiModelProperty(value = "第三分类")
    private String categorySegment3;

    /**
     * 第四分类
     */
    @ApiModelProperty(value = "第四分类")
    private String categorySegment4;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 物料状态
     */
    @ApiModelProperty(value = "物料状态")
    private String itemStatus;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String priUom;

    /**
     * segment1
     */
    @ApiModelProperty(value = "segment1")
    private String segment1;

    /**
     * segment2
     */
    @ApiModelProperty(value = "segment2")
    private String segment2;

    /**
     * segment3
     */
    @ApiModelProperty(value = "segment3")
    private String segment3;

    /**
     * segment4
     */
    @ApiModelProperty(value = "segment4")
    private String segment4;

    /**
     * segment5
     */
    @ApiModelProperty(value = "segment5")
    private String segment5;

    /**
     * segment6
     */
    @ApiModelProperty(value = "segment6")
    private String segment6;

    /**
     * segment7
     */
    @ApiModelProperty(value = "segment7")
    private String segment7;

    /**
     * segment8
     */
    @ApiModelProperty(value = "segment8")
    private String segment8;

    /**
     * segment9
     */
    @ApiModelProperty(value = "segment9")
    private String segment9;

    /**
     * segment10
     */
    @ApiModelProperty(value = "segment10")
    private String segment10;

    /**
     * segment11
     */
    @ApiModelProperty(value = "segment11")
    private String segment11;

    /**
     * segment12
     */
    @ApiModelProperty(value = "segment12")
    private String segment12;

    /**
     * segment13
     */
    @ApiModelProperty(value = "segment13")
    private String segment13;

    /**
     * segment14
     */
    @ApiModelProperty(value = "segment14")
    private String segment14;

    /**
     * segment15
     */
    @ApiModelProperty(value = "segment15")
    private String segment15;

    /**
     * segment16
     */
    @ApiModelProperty(value = "segment16")
    private String segment16;

    /**
     * segment17
     */
    @ApiModelProperty(value = "segment17")
    private String segment17;

    /**
     * segment18
     */
    @ApiModelProperty(value = "segment18")
    private String segment18;

    /**
     * segment19
     */
    @ApiModelProperty(value = "segment19")
    private String segment19;

    /**
     * segment20
     */
    @ApiModelProperty(value = "segment20")
    private String segment20;

    /**
     * segment21
     */
    @ApiModelProperty(value = "segment21")
    private String segment21;

    /**
     * segment22
     */
    @ApiModelProperty(value = "segment22")
    private String segment22;

    /**
     * segment23
     */
    @ApiModelProperty(value = "segment23")
    private String segment23;

    /**
     * segment24
     */
    @ApiModelProperty(value = "segment24")
    private String segment24;

    /**
     * segment25
     */
    @ApiModelProperty(value = "segment25")
    private String segment25;

    /**
     * segment26
     */
    @ApiModelProperty(value = "segment26")
    private String segment26;

    /**
     * segment27
     */
    @ApiModelProperty(value = "segment27")
    private String segment27;

    /**
     * segment28
     */
    @ApiModelProperty(value = "segment28")
    private String segment28;

    /**
     * segment29
     */
    @ApiModelProperty(value = "segment29")
    private String segment29;

    /**
     * segment30
     */
    @ApiModelProperty(value = "segment30")
    private String segment30;

    /**
     * segment31
     */
    @ApiModelProperty(value = "segment31")
    private String segment31;

    /**
     * segment32
     */
    @ApiModelProperty(value = "segment32")
    private String segment32;

    /**
     * segment33
     */
    @ApiModelProperty(value = "segment33")
    private String segment33;

    /**
     * segment34
     */
    @ApiModelProperty(value = "segment34")
    private String segment34;

    /**
     * segment35
     */
    @ApiModelProperty(value = "segment35")
    private String segment35;

    /**
     * segment36
     */
    @ApiModelProperty(value = "segment36")
    private String segment36;

    /**
     * segment37
     */
    @ApiModelProperty(value = "segment37")
    private String segment37;

    /**
     * segment38
     */
    @ApiModelProperty(value = "segment38")
    private String segment38;

    /**
     * segment39
     */
    @ApiModelProperty(value = "segment39")
    private String segment39;

    /**
     * segment40
     */
    @ApiModelProperty(value = "segment40")
    private String segment40;

    /**
     * segment41
     */
    @ApiModelProperty(value = "segment41")
    private String segment41;

    /**
     * segment42
     */
    @ApiModelProperty(value = "segment42")
    private String segment42;

    /**
     * segment43
     */
    @ApiModelProperty(value = "segment43")
    private String segment43;

    /**
     * segment44
     */
    @ApiModelProperty(value = "segment44")
    private String segment44;

    /**
     * segment45
     */
    @ApiModelProperty(value = "segment45")
    private String segment45;

    /**
     * segment46
     */
    @ApiModelProperty(value = "segment46")
    private String segment46;

    /**
     * segment47
     */
    @ApiModelProperty(value = "segment47")
    private String segment47;

    /**
     * segment48
     */
    @ApiModelProperty(value = "segment48")
    private String segment48;

    /**
     * segment49
     */
    @ApiModelProperty(value = "segment49")
    private String segment49;

    /**
     * segment50
     */
    @ApiModelProperty(value = "segment50")
    private String segment50;

    /**
     * segment51
     */
    @ApiModelProperty(value = "segment51")
    private String segment51;

    /**
     * segment52
     */
    @ApiModelProperty(value = "segment52")
    private String segment52;

    /**
     * segment53
     */
    @ApiModelProperty(value = "segment53")
    private String segment53;

    /**
     * segment54
     */
    @ApiModelProperty(value = "segment54")
    private String segment54;

    /**
     * segment55
     */
    @ApiModelProperty(value = "segment55")
    private String segment55;

    /**
     * segment56
     */
    @ApiModelProperty(value = "segment56")
    private String segment56;

    /**
     * segment57
     */
    @ApiModelProperty(value = "segment57")
    private String segment57;

    /**
     * segment58
     */
    @ApiModelProperty(value = "segment58")
    private String segment58;

    /**
     * segment59
     */
    @ApiModelProperty(value = "segment59")
    private String segment59;

    /**
     * segment60
     */
    @ApiModelProperty(value = "segment60")
    private String segment60;

    /**
     * attribute1
     */
    @ApiModelProperty(value = "attribute1")
    private String attribute1;

    /**
     * attribute2
     */
    @ApiModelProperty(value = "attribute2")
    private String attribute2;

    /**
     * attribute3
     */
    @ApiModelProperty(value = "attribute3")
    private String attribute3;

    /**
     * attribute4
     */
    @ApiModelProperty(value = "attribute4")
    private String attribute4;

    /**
     * attribute5
     */
    @ApiModelProperty(value = "attribute5")
    private String attribute5;

    /**
     * attribute6
     */
    @ApiModelProperty(value = "attribute6")
    private String attribute6;

    /**
     * attribute7
     */
    @ApiModelProperty(value = "attribute7")
    private String attribute7;

    /**
     * attribute8
     */
    @ApiModelProperty(value = "attribute8")
    private String attribute8;

    /**
     * attribute9
     */
    @ApiModelProperty(value = "attribute9")
    private String attribute9;

    /**
     * attribute10
     */
    @ApiModelProperty(value = "attribute10")
    private String attribute10;

    /**
     * attribute11
     */
    @ApiModelProperty(value = "attribute11")
    private String attribute11;

    /**
     * attribute12
     */
    @ApiModelProperty(value = "attribute12")
    private String attribute12;

    /**
     * attribute13
     */
    @ApiModelProperty(value = "attribute13")
    private String attribute13;

    /**
     * attribute14
     */
    @ApiModelProperty(value = "attribute14")
    private String attribute14;

    /**
     * attribute15
     */
    @ApiModelProperty(value = "attribute15")
    private String attribute15;

    /**
     * attribute16
     */
    @ApiModelProperty(value = "attribute16")
    private String attribute16;

    /**
     * attribute17
     */
    @ApiModelProperty(value = "attribute17")
    private String attribute17;

    /**
     * attribute18
     */
    @ApiModelProperty(value = "attribute18")
    private String attribute18;

    /**
     * attribute19
     */
    @ApiModelProperty(value = "attribute19")
    private String attribute19;

    /**
     * attribute20
     */
    @ApiModelProperty(value = "attribute20")
    private String attribute20;

    /**
     * 语种
     */
    @ApiModelProperty(value = "语种")
    private String language;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    private String itemType;

    /**
     * 可售标识
     */
    @ApiModelProperty(value = "可售标识")
    private String customerOrderFlag;

    @ApiModelProperty(value = "生命周期状态")
    private String lifecycleState;

    @ApiModelProperty(value = "临时量产标识")
    private String isTemporaryOutput;
}
