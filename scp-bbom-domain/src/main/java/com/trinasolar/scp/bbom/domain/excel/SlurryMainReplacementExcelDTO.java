package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SlurryMainReplacementExcelDTO {

    /**
     * 电池车间
     */
    @ExcelProperty(value = "电池车间")
    private String workshop;

    /**
     * 浆料料号
     */
    @ExcelProperty(value = "浆料料号")
    private String itemCode;

    /**
     * 浆料物料描述
     */
    @ExcelProperty(value = "浆料物料描述")
    private String itemDesc;

    /**
     * 供应商简称
     */
    @ExcelProperty(value = "供应商简称")
    private String vendorAltName;

    @ExcelProperty(value = "正背面")
    private String frontOrBack;

    @ExcelProperty(value = "工位")
    private String opPosition;

    /**
     * 是否主料
     */
    @ExcelProperty(value = "是否主料")
    private String substituteFlag;

    /**
     * 是否启用
     */
    @ExcelProperty(value = "是否启用")
    private String substituteEnableFlag;

    /**
     * 电池料号
     */
    @ExcelProperty(value = "电池料号")
    private String mainItemCode;

    /**
     * 电池物料描述
     */
    @ExcelProperty(value = "电池物料描述")
    private String mainItemDesc;

    /**
     * BOM替代项
     */
    @ExcelProperty(value = "BOM替代项")
    private String alternateBomDesignator;
}
