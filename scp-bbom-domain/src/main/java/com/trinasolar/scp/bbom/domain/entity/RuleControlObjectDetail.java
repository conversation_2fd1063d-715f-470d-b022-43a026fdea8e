package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 规则管控对象详情
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
@Entity
@ToString
@Data
@Table(name = "bbom_rule_control_object_detail")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_rule_control_object_detail SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_rule_control_object_detail SET is_deleted = 1 , updated_time = CURRENT_TIMESTAMP WHERE id = ?")
public class RuleControlObjectDetail extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 规则管控对象头ID
     */
    @ApiModelProperty(value = "规则管控对象头ID")
    private Long ruleControlObjectHeaderId;

    /**
     * 材料属性ID
     */
    @ApiModelProperty(value = "材料属性ID")
    private Long materialsAttrFiledId;

    /**
     * 材料属性
     */
    @ApiModelProperty(value = "材料属性")
    private String materialsAttrFiled;

    /**
     * 运算符 1：包含  2：排除  3：等于
     */
    @ApiModelProperty(value = "运算符 1：包含  2：排除  3：等于")
    private String attrOperator;


}
