package com.trinasolar.scp.bbom.domain.enums;

public enum ItemCategoryEnum {
    SILICON("硅片", "SILICON"), SLURRY("浆料", "SLURRY"), SCREEN_PLATE("网版", "SCREEN_PLATE");

    private final String desc;
    private final String value;

    ItemCategoryEnum(String desc, String value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return this.desc;
    }

    public String getValue() {
        return this.value;
    }

}
