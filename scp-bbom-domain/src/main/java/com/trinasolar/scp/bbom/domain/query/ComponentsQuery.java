package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * BOM行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Data
@ApiModel(value = "Components查询条件", description = "查询条件")
@Accessors(chain = true)
public class ComponentsQuery extends PageDTO implements Serializable {

    private static final long serialVersionUID = 6138679272344032207L;

    /**
     * bom构件id，自增序列
     */
    @ApiModelProperty(value = "bom构件id，自增序列")
    private Long id;

    /**
     * bom id，从结构上取
     */
    @ApiModelProperty(value = "bom id，从结构上取")
    private Long bomId;

    /**
     * 操作序列号
     */
    @ApiModelProperty(value = "操作序列号")
    private Integer operationSeqNum;

    /**
     * 构件物料id
     */
    @ApiModelProperty(value = "构件物料id")
    private Long componentItemId;

    /**
     * BOM结构
     */
    @ApiModelProperty(value = "BOM结构")
    private String bomStructure;

    /**
     * 替代项标志
     */
    @ApiModelProperty(value = "替代项标志")
    private String substituteFlag;

    /**
     * 最后更新日期
     */
    @ApiModelProperty(value = "最后更新日期")
    private LocalDateTime lastUpdateDate;

    /**
     * 项目序列号
     */
    @ApiModelProperty(value = "项目序列号")
    private Integer itemNum;

    /**
     * 构件数量
     */
    @ApiModelProperty(value = "构件数量")
    private String componentQuantity;

    /**
     * 产出因子
     */
    @ApiModelProperty(value = "产出因子")
    private Integer componentYieldFactor;

    /**
     * 构件备注
     */
    @ApiModelProperty(value = "构件备注")
    private String componentRemarks;

    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    private LocalDateTime effectivityDate;

    /**
     * 变更备注
     */
    @ApiModelProperty(value = "变更备注")
    private String changeNotice;

    /**
     * 实施日期
     */
    @ApiModelProperty(value = "实施日期")
    private LocalDateTime implementationDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private LocalDateTime disableDate;

    /**
     * 属性类别
     */
    @ApiModelProperty(value = "属性类别")
    private String attributeCategory;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute1;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute2;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute3;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute4;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute5;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute6;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute7;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute8;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute9;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute10;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute11;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute12;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute13;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute14;

    /**
     * 扩展属性
     */
    @ApiModelProperty(value = "扩展属性")
    private String attribute15;

    /**
     * 计划百分比
     */
    @ApiModelProperty(value = "计划百分比")
    private Integer planningFactor;

    /**
     * 相关数量
     */
    @ApiModelProperty(value = "相关数量")
    private Integer quantityRelated;

    /**
     * ACD类型
     */
    @ApiModelProperty(value = "ACD类型")
    private Integer acdType;

    /**
     * 构件序号
     */
    @ApiModelProperty(value = "构件序号")
    private Long componentSequenceId;

    /**
     * 替代项Id
     */
    @ApiModelProperty(value = "替代项Id")
    private Long substituteComponentId;

    /**
     * 清单序号（关键字）
     */
    @ApiModelProperty(value = "清单序号（关键字）")
    private Long billSequenceId;

    /**
     * 车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟
     */
    @ApiModelProperty(value = "车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟")
    private Integer wipSupplyType;

    /**
     * 供应子库
     */
    @ApiModelProperty(value = "供应子库")
    private String supplySubinventory;

    /**
     * 供应库位
     */
    @ApiModelProperty(value = "供应库位")
    private Long supplyLocatorId;

    /**
     * 清单项目类型1.模型.选项类.计划中.标准
     */
    @ApiModelProperty(value = "清单项目类型1.模型.选项类.计划中.标准")
    private Integer bomItemType;

    /**
     * 基础类型
     */
    @ApiModelProperty(value = "基础类型")
    private Integer basisType;

    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
