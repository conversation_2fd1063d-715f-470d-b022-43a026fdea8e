package com.trinasolar.scp.bbom.domain.dto;

import com.trinasolar.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * MW折算系数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MW折算系数DTO对象", description = "DTO对象")
public class MwCoefficientDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 系数
     */
    @ApiModelProperty(value = "系数")
    private BigDecimal coefficient;

}
