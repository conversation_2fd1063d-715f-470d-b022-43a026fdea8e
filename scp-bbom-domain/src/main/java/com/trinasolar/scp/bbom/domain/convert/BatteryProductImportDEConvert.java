package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatteryProductImportDTO;
import com.trinasolar.scp.bbom.domain.entity.BatteryProductImport;
import com.trinasolar.scp.bbom.domain.excel.BatteryProductImportExcelDTO;
import com.trinasolar.scp.bbom.domain.save.BatteryProductImportSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池产品导入表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryProductImportDEConvert extends BaseDEConvert<BatteryProductImportDTO, BatteryProductImport> {

    BatteryProductImportDEConvert INSTANCE = Mappers.getMapper(BatteryProductImportDEConvert.class);

    List<BatteryProductImportExcelDTO> toExcelDTO(List<BatteryProductImportDTO> dtos);

    BatteryProductImportExcelDTO toExcelDTO(BatteryProductImportDTO dto);

    BatteryProductImportSaveDTO toSaveDTO(BatteryProductImportExcelDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    BatteryProductImport saveDTOtoEntity(BatteryProductImportSaveDTO saveDTO, @MappingTarget BatteryProductImport entity);
}
