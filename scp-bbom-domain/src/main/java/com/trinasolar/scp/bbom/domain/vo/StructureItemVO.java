package com.trinasolar.scp.bbom.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class StructureItemVO {


    /**
     * 源系统物料ID
     */
    @ApiModelProperty(value = "源系统物料ID")
    private Long sourceItemId;


    /**
     * 第一分类
     */
    @ApiModelProperty(value = "第一分类")
    private String categorySegment1;

    /**
     * 第二分类
     */
    @ApiModelProperty(value = "第二分类")
    private String categorySegment2;

    /**
     * 第三分类
     */
    @ApiModelProperty(value = "第三分类")
    private String categorySegment3;

    /**
     * 第四分类
     */
    @ApiModelProperty(value = "第四分类")
    private String categorySegment4;

    /**
     * 第五分类
     */
    @ApiModelProperty(value = "第五分类")
    private String categorySegment5;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String priUom;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment3;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment7;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    private String segment4;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性8")
    private String segment8;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性23")
    private String segment23;


    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性13")
    private String segment13;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性9")
    private String segment9;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性27")
    private String segment27;
}
