package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SpecialCellMatchRuleExcelDTO {
    private static final long serialVersionUID = 1L;
    
    @ExcelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "电池类型")
    @ExcelIgnore
    private String cellsType;

    @ExcelProperty(value = "电池类型")
    private String cellsTypeName;

    @ExcelProperty(value = "小区域国家")
    @ExcelIgnore
    private String regionalCountry;

    @ExcelProperty(value = "小区域国家")
    private String regionalCountryName;

    @ExcelProperty(value = "H追溯")
    @ExcelIgnore
    private String hTrace;

    @ExcelProperty(value = "H追溯")
    private String hTraceName;

    @ExcelProperty(value = "片源种类")
    @ExcelIgnore
    private String cellSource;

    @ExcelProperty(value = "片源种类")
    private String cellSourceName;

    @ExcelProperty(value = "美学")
    @ExcelIgnore
    private String aesthetics;

    @ExcelProperty(value = "美学")
    private String aestheticsName;

    @ExcelProperty(value = "透明双玻")
    @ExcelIgnore
    private String transparentDoubleGlass;

    @ExcelProperty(value = "透明双玻")
    private String transparentDoubleGlassName;

    @ExcelProperty(value = "产品等级")
    @ExcelIgnore
    private String productionGrade;

    @ExcelProperty(value = "产品等级")
    private String productionGradeName;

    @ExcelProperty(value = "常规电池类型")
    @ExcelIgnore
    private String commonCellsType;

    @ExcelProperty(value = "常规电池类型")
    private String commonCellsTypeName;
}
