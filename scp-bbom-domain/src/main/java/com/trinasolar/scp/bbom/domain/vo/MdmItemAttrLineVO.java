package com.trinasolar.scp.bbom.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MdmItemAttrLineVO {
    private String AttributeID;

    private String AttributeName;

    private String Seg;

    private String hasLOV;


}
