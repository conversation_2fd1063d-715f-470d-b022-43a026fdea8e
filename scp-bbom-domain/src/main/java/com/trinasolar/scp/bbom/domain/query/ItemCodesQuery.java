package com.trinasolar.scp.bbom.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 物料基础数据表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-30 14:19:23
 */
@Data
@ApiModel(value = "Items查询条件", description = "查询条件")
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemCodesQuery extends PageDTO implements Serializable {

    private List<String> itemCodes;

    private Long organizationId;

}
