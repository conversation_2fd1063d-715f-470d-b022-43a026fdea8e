package com.trinasolar.scp.bbom.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 电池类型动态属性-网版
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Entity
@ToString
@Data
@Table(name = "bbom_battery_screen_plate")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE bbom_battery_screen_plate SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE bbom_battery_screen_plate SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class BatteryScreenPlate extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID主键")
    @Column(name = "id")
    private Long id;

    /**
     * 电池类型编码
     */
    @ApiModelProperty(value = "电池类型编码")
    @Column(name = "battery_code")
    private String batteryCode;

    /**
     * 电池类型名称
     */
    @ApiModelProperty(value = "电池类型名称")
    @Column(name = "battery_name")
    private String batteryName;

    /**
     * 切换类型
     */
    @ApiModelProperty(value = "切换类型")
    @Column(name = "switch_type")
    private Long switchType;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    @Column(name = "workunit")
    private String workunit;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体数量")
    @Column(name = "line")
    private String line;

    /**
     * 机台
     */
    @ApiModelProperty(value = "机台")
    @Column(name = "workbench")
    private String workbench;

    /**
     * 物料料号-新
     */
    @ApiModelProperty(value = "物料料号-新")
    @Column(name = "item_code_new")
    private String itemCodeNew;

    /**
     * 物料料号-新说明
     */
    @ApiModelProperty(value = "物料料号-新说明")
    @Column(name = "item_desc_new")
    private String itemDescNew;

    /**
     * 物料料号-旧
     */
    @ApiModelProperty(value = "物料料号-旧")
    @Column(name = "item_code_old")
    private String itemCodeOld;

    /**
     * 物料料号-旧说明
     */
    @ApiModelProperty(value = "物料料号-旧说明")
    @Column(name = "item_desc_old")
    private String itemDescOld;

    /**
     * 数量-新
     */
    @ApiModelProperty(value = "数量-新")
    @Column(name = "number_new")
    private String numberNew;

    /**
     * 数量-旧
     */
    @ApiModelProperty(value = "数量-旧")
    @Column(name = "number_old")
    private String numberOld;

    /**
     * 目标
     */
    @ApiModelProperty(value = "目标")
    @Column(name = "target")
    private String target;

    /**
     * 旧网版库存
     */
    @ApiModelProperty(value = "旧网版库存")
    @Column(name = "screen_plate_inventory_old")
    private String screenPlateInventoryOld;

    /**
     * 旧网板在途数量
     */
    @ApiModelProperty(value = "旧网板在途数量")
    @Column(name = "screen_plate_number_old")
    private String screenPlateNumberOld;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remarks")
    private String remarks;

    /**
     * 是否存在bom
     */
    @ApiModelProperty(value = "是否存在bom")
    @Column(name = "is_exist_bom")
    private String isExistBom;

    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    @Column(name = "effective_start_date")
    private LocalDateTime effectiveStartDate;

    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    @Column(name = "effective_end_date")
    private LocalDateTime effectiveEndDate;

    /**
     * 网版类别新
     */
    @ApiModelProperty(value = "网版类别新")
    @Column(name = "screen_plate_version_category_new")
    private String screenPlateVersionCategoryNew;
    /**
     * 网版类别旧
     */
    @ApiModelProperty(value = "网版类别旧")
    @Column(name = "screen_plate_version_category_old")
    private String screenPlateVersionCategoryOld;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @Column(name = "vendor_name")
    private String vendorName;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    @Column(name = "vendor_Id")
    private Long vendorId;

    /**
     * 主栅间距
     */
    @ApiModelProperty(value = "主栅间距")
    @Column(name = "main_grid_space")
    private String mainGridSpace;

    @ApiModelProperty(value = "主栅信息")
    @Column(name = "main_grid_info")
    private String mainGridInfo;

    /**
     * 单玻
     */
    @ApiModelProperty(value = "单玻")
    @Column(name = "single_glass_flag")
    private String singleGlassFlag;

    /**
     * 栅线数量
     */
    @ApiModelProperty(value = "栅线数量")
    @Column(name = "grids_number")
    private String gridsNumber;
}
