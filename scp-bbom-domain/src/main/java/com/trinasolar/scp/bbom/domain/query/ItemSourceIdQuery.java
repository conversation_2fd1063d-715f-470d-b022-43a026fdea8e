package com.trinasolar.scp.bbom.domain.query;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @ClassName ItemSourceIdQuery
 * @Description
 * @Date 2023/12/31 14:11
 **/
@Data
@ApiModel(value = "Items查询条件", description = "查询条件")
@Accessors(chain = true)
public class ItemSourceIdQuery {
    private List<Long> sourceItemId;
    private List<String> categorySegment4;
}
