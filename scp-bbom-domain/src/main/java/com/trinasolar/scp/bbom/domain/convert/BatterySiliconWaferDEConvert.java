package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.BatterySiliconWaferDTO;
import com.trinasolar.scp.bbom.domain.entity.BatterySiliconWafer;
import com.trinasolar.scp.bbom.domain.excel.BatterySiliconWaferExcelDTO;
import com.trinasolar.scp.bbom.domain.save.BatterySiliconWaferSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型动态属性-硅片 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatterySiliconWaferDEConvert extends BaseDEConvert<BatterySiliconWaferDTO, BatterySiliconWafer> {

    BatterySiliconWaferDEConvert INSTANCE = Mappers.getMapper(BatterySiliconWaferDEConvert.class);

    List<BatterySiliconWaferExcelDTO> toExcelDTO(List<BatterySiliconWaferDTO> dtos);

    BatterySiliconWaferExcelDTO toExcelDTO(BatterySiliconWaferDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    BatterySiliconWafer saveDTOtoEntity(BatterySiliconWaferSaveDTO saveDTO, @MappingTarget BatterySiliconWafer entity);

    List<BatterySiliconWaferDTO> toDto(List<BatterySiliconWafer> dtos);

    BatterySiliconWaferSaveDTO excelDTOtoSaveDTO(BatterySiliconWaferExcelDTO excelDTO);
}
