package com.trinasolar.scp.bbom.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
public class BatteryScreenPlateWorkshopExcelDTO {

    @ApiModelProperty(value = "ID主键")
    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private String batteryCode;

    @ExcelProperty(value = "电池类型")
    private String batteryName;

    @ExcelIgnore
    private String singleGlassFlag;

    @ExcelProperty(value = "单玻")
    private String singleGlassFlagName;

    @ExcelProperty(value = "主栅间距")
    private String mainGridSpace;

    @ExcelProperty(value = "生产基地")
    private String basePlace;

    @ExcelProperty(value = "生产车间")
    private String workshop;

    @ExcelProperty(value = "正电极网版细栅")
    private String positiveElectrodeScreenFineGrid;

    @ExcelProperty(value = "背电极网版细栅")
    private String negativeElectrodeScreenFineGrid;

    @ExcelProperty(value = "主栅信息")
    private String mainGridInfo;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "有效期-起")
    @ExcelIgnore
    private LocalDate effectiveStartDate;

    @ExcelProperty(value = "有效期-止")
    @ExcelIgnore
    private LocalDate effectiveEndDate;

    @ExcelProperty(value = "有效期-起")
    private String effectiveStartDateName;

    @ExcelProperty(value = "有效期-止")
    private String effectiveEndDateName;

}
