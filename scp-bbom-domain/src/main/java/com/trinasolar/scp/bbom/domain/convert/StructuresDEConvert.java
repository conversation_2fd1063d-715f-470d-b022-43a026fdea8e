package com.trinasolar.scp.bbom.domain.convert;

import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.entity.Structures;
import com.trinasolar.scp.bbom.domain.excel.StructuresExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * BBOM结构 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StructuresDEConvert extends BaseDEConvert<StructuresDTO, Structures> {

    StructuresDEConvert INSTANCE = Mappers.getMapper(StructuresDEConvert.class);

    List<StructuresExcelDTO> toExcelDTO(List<StructuresDTO> dtos);

    StructuresExcelDTO toExcelDTO(StructuresDTO dto);
}
