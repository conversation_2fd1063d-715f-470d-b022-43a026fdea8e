package com.trinasolar.scp.bbom.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * ERP替代项表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-21 20:00:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpAlternateDesignatorDTO对象", description = "DTO对象")
public class ErpAlternateDesignatorDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * alternate_designator_code
     */
    @ApiModelProperty(value = "alternate_designator_code")
    private String alternateDesignatorCode;

    /**
     * 库存组织
     */
    private String inventoryOrganization;

    /**
     * description
     */
    @ApiModelProperty(value = "description")
    private String description;
    /**
     * organization_id
     */
    @ApiModelProperty(value = "organization_id")
    private Long organizationId;
    /**
     * last_update_date
     */
    @ApiModelProperty(value = "last_update_date")
    private LocalDateTime lastUpdateDate;
}
