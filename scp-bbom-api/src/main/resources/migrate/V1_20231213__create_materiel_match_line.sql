DROP TABLE IF EXISTS bbom_materiel_match_line;
CREATE TABLE bbom_materiel_match_line
(
    id                   BIGINT      NOT NULL COMMENT 'ID主键',
    header_id            BIGINT COMMENT '电池物料号匹配ID',
    schedule_date        DATE COMMENT '排产日期',
    schedule_qty         DECIMAL(24, 6) COMMENT '排产数量',
    item_code            VARCHAR(100) COMMENT '电池料号',
    item_desc            VARCHAR(255) COMMENT '物料名称',
    is_switch            CHAR(1) COMMENT '是否切换',
    is_catch_poroduction VARCHAR(50) COMMENT '研发/量产',
    is_exist_bom         CHAR(1) COMMENT '是否有BOM',
    switch_start_date    DATETIME COMMENT '切换开始时间',
    switch_end_date      DATETIME COMMENT '切换结束数据',
    line_date            DATE COMMENT '日期',
    line                 DECIMAL(24, 6) COMMENT '线体',
    cell_qty             DECIMAL(24, 6) COMMENT '电池片数量',
    remark               VARCHAR(255) COMMENT '备注',
    tenant_id            VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter          INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted           INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池料号匹配明细行';
