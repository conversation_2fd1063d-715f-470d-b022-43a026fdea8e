CREATE TABLE `scp_bbom`.`bbom_battery_screen_plate_workshop` (
`id` bigint NOT NULL COMMENT 'ID主键',
`battery_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型编码',
`battery_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型名称',
`single_glass_flag` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单玻',
`main_grid_space` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '主栅间距',
`base_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产基地',
`workshop` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产车间',
`positive_electrode_screen_fine_grid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '正电极网版细栅 取LOV：7A01500100122',
`negative_electrode_screen_fine_grid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '背电极网版细栅 取LOV：7A01500100122',
`effective_start_date` datetime DEFAULT NULL COMMENT '有效期起',
`effective_end_date` datetime DEFAULT NULL COMMENT '有效期止',
`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
`opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
`is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
`created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='车间级网版切换维护';

alter table `scp_bbom`.bbom_materiel_match_line
    add positive_electrode_screen_fine_grid varchar(256) null comment '正电极网版细栅' ;

alter table `scp_bbom`.bbom_materiel_match_line
    add negative_electrode_screen_fine_grid varchar(256) null comment '背电极网版细栅' ;


alter table `scp_bbom`.bbom_materiel_match_line
    add wafer_category varchar(256) null comment '硅片品类' ;


alter table `scp_bbom`.bbom_materiel_match_header
    add plan_type varchar(256) null comment '入库与投产计划类型' ;


CREATE TABLE `bbom_special_cell_match_rule` (
                                                `id` bigint NOT NULL COMMENT 'ID主键',
                                                `cells_type` varchar(100) DEFAULT NULL COMMENT '电池类型',
                                                `regional_country` varchar(50) DEFAULT NULL COMMENT '小区域国家',
                                                `h_trace` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'H追溯',
                                                `cell_source` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '片源种类',
                                                `aesthetics` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '美学',
                                                `transparent_double_glass` varchar(255) DEFAULT '无' COMMENT '透明双玻',
                                                `production_grade` varchar(255) DEFAULT NULL COMMENT '产品等级',
                                                `common_cells_type` varchar(100) DEFAULT NULL COMMENT '常规电池类型',
                                                `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_by` varchar(32) CHARACTER SET utf8mb4 NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='特殊片源匹配规则';

CREATE TABLE `scp_bbom`.`bbom_a_materiel_match_rule` (
                                                         `id` bigint NOT NULL COMMENT 'ID主键',
                                                         `is_oversea` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '国内海外',
                                                         `workshop` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池车间',
                                                         `cells_type` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型',
                                                         `actual_piece_thickness` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '实际片厚',
                                                         `main_grid_both_shape` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '主栅两端形状',
                                                         `a_item_code` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'A-料号',
                                                         `tenant_id` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                         `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                         `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                         `created_by` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                         `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                         `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                         `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='特殊片源A-料号匹配规则';

alter table `scp_bbom`.bbom_battery_screen_plate
    add main_grid_space varchar(64) null comment '主栅间距' ;

alter table `scp_bbom`.bbom_battery_screen_plate
    add single_glass_flag varchar(64) null comment '单玻' ;

alter table `scp_bbom`.bbom_battery_screen_plate
    add grids_number varchar(64) null comment '栅线数量' ;

ALTER TABLE `scp_bbom`.bbom_battery_screen_plate MODIFY COLUMN item_code_new VARCHAR(1024);

ALTER TABLE `scp_bbom`.bbom_battery_screen_plate MODIFY COLUMN item_desc_new VARCHAR(2056);


