-- scp_bbom.bbom_a_materiel_match_rule definition

CREATE TABLE `bbom_a_materiel_match_rule` (
                                              `id` bigint NOT NULL COMMENT 'ID主键',
                                              `is_oversea` varchar(255) DEFAULT NULL COMMENT '国内海外',
                                              `workshop` varchar(50) DEFAULT NULL COMMENT '电池车间',
                                              `cells_type` varchar(100) DEFAULT NULL COMMENT '电池类型',
                                              `actual_piece_thickness` varchar(100) DEFAULT NULL COMMENT '实际片厚',
                                              `main_grid_both_shape` varchar(100) DEFAULT NULL COMMENT '主栅两端形状',
                                              `a_item_code` varchar(100) DEFAULT NULL COMMENT 'A-料号',
                                              `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                              `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                              `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                              `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                              `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updated_by` varchar(32) COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
                                              `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='特殊片源A-料号匹配规则';