DROP TABLE IF EXISTS bbom_silicon_cell_grade;
CREATE TABLE bbom_silicon_cell_grade
(
    id                             BIGINT      NOT NULL COMMENT 'ID主键',
    silicon_wafer_properties       VARCHAR(255) COMMENT '硅片属性',
    silicon_wafer_conditional_item VARCHAR(255) COMMENT '硅片属性条件项',
    silicon_wafer_value            VARCHAR(255) COMMENT '硅片属性值',
    battery_properties             VARCHAR(255) COMMENT '电池属性',
    battery_conditional_item       VARCHAR(255) COMMENT '电池属性条件项',
    battery_value                  VARCHAR(255) COMMENT '电池属性值',
    effective_start_date           DATETIME COMMENT '有效日期_起',
    effective_end_date             DATETIME COMMENT '有效日期_止',
    tenant_id                      VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter                    INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted                     INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by                     VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by                     VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time                   DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '硅片等级与电池等级映射';
