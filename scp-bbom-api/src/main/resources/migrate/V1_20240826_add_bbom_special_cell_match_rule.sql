-- scp_bbom.bbom_special_cell_match_rule definition

CREATE TABLE `bbom_special_cell_match_rule` (
                                                `id` bigint NOT NULL COMMENT 'ID主键',
                                                `cells_type` varchar(100) DEFAULT NULL COMMENT '电池类型',
                                                `regional_country` varchar(50) DEFAULT NULL COMMENT '小区域国家',
                                                `h_trace` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'H追溯',
                                                `cell_source` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '片源种类',
                                                `aesthetics` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '美学',
                                                `transparent_double_glass` varchar(255) DEFAULT '无' COMMENT '透明双玻',
                                                `production_grade` varchar(255) DEFAULT NULL COMMENT '产品等级',
                                                `common_cells_type` varchar(100) DEFAULT NULL COMMENT '常规电池类型',
                                                `tenant_id` varchar(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
                                                `opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
                                                `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
                                                `created_by` varchar(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
                                                `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `updated_by` varchar(32) CHARACTER SET utf8mb4 NOT NULL DEFAULT '-1' COMMENT '更新人',
                                                `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='特殊片源匹配规则';