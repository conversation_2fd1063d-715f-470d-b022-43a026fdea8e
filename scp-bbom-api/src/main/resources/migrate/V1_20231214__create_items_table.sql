-- auto-generated definition
create table bbom_items
(
    id                  bigint null comment '主键',
    item_id             bigint                                 not null comment '物料ID'
        primary key,
    organization_id     bigint null comment '库存组织ID',
    source_system_code  varchar(100) null comment '来源系统代码',
    source_item_id      bigint null comment '源系统物料ID',
    source_inv_org_id   bigint null comment '源系统库存组织ID',
    category_segment1   varchar(100) null comment '第一分类',
    category_segment2   varchar(100) null comment '第二分类',
    category_segment3   varchar(100) null comment '第三分类',
    category_segment4   varchar(100) null comment '第四分类',
    category_segment5   varchar(100) null comment '第五分类',
    item_code           varchar(50)                            not null comment '物料编码',
    item_desc           varchar(500) default '' null comment '物料描述',
    item_status         varchar(50)                            not null comment '物料状态',
    pri_uom             varchar(50) null comment '物料单位',
    segment1            varchar(240) null comment '物料属性',
    segment2            varchar(240) null comment '物料属性',
    segment3            varchar(240) null comment '物料属性',
    segment4            varchar(240) null comment '物料属性',
    segment5            varchar(240) null comment '物料属性',
    segment6            varchar(240) null comment '物料属性',
    segment7            varchar(240) null comment '物料属性',
    segment8            varchar(240) null comment '物料属性',
    segment9            varchar(240) null comment '物料属性',
    segment10           varchar(240) null comment '物料属性',
    segment11           varchar(240) null comment '物料属性',
    segment12           varchar(240) null comment '物料属性',
    segment13           varchar(240) null comment '物料属性',
    segment14           varchar(240) null comment '物料属性',
    segment15           varchar(240) null comment '物料属性',
    segment16           varchar(240) null comment '物料属性',
    segment17           varchar(240) null comment '物料属性',
    segment18           varchar(240) null comment '物料属性',
    segment19           varchar(240) null comment '物料属性',
    segment20           varchar(240) null comment '物料属性',
    segment21           varchar(240) null comment '物料属性',
    segment22           varchar(240) null comment '物料属性',
    segment23           varchar(240) null comment '物料属性',
    segment24           varchar(240) null comment '物料属性',
    segment25           varchar(240) null comment '物料属性',
    segment26           varchar(240) null comment '物料属性',
    segment27           varchar(240) null comment '物料属性',
    segment28           varchar(240) null comment '物料属性',
    segment29           varchar(240) null comment '物料属性',
    segment30           varchar(240) null comment '物料属性',
    segment31           varchar(240) null comment '物料属性',
    segment32           varchar(240) null comment '物料属性',
    segment33           varchar(240) null comment '物料属性',
    segment34           varchar(240) null comment '物料属性',
    segment35           varchar(240) null comment '物料属性',
    segment36           varchar(240) null comment '物料属性',
    segment37           varchar(240) null comment '物料属性',
    segment38           varchar(240) null comment '物料属性',
    segment39           varchar(240) null comment '物料属性',
    segment40           varchar(240) null comment '物料属性',
    segment41           varchar(240) null comment '物料属性',
    segment42           varchar(240) null comment '物料属性',
    segment43           varchar(240) null comment '物料属性',
    segment44           varchar(240) null comment '物料属性',
    segment45           varchar(240) null comment '物料属性',
    segment46           varchar(240) null comment '物料属性',
    segment47           varchar(240) null comment '物料属性',
    segment48           varchar(240) null comment '物料属性',
    segment49           varchar(240) null comment '物料属性',
    segment50           varchar(240) null comment '物料属性',
    segment51           varchar(240) null comment '物料属性',
    segment52           varchar(240) null comment '物料属性',
    segment53           varchar(240) null comment '物料属性',
    segment54           varchar(240) null comment '物料属性',
    segment55           varchar(240) null comment '物料属性',
    segment56           varchar(240) null comment '物料属性',
    segment57           varchar(240) null comment '物料属性',
    segment58           varchar(240) null comment '物料属性',
    segment59           varchar(240) null comment '物料属性',
    segment60           varchar(240) null comment '物料属性',
    attribute1          varchar(240) null comment '扩展属性',
    attribute2          varchar(240) null comment '扩展属性',
    attribute3          varchar(240) null comment '扩展属性',
    attribute4          varchar(240) null comment '扩展属性',
    attribute5          varchar(240) null comment '扩展属性',
    attribute6          varchar(240) null comment '扩展属性',
    attribute7          varchar(240) null comment '扩展属性',
    attribute8          varchar(240) null comment '扩展属性',
    attribute9          varchar(240) null comment '扩展属性',
    attribute10         varchar(240) null comment '扩展属性',
    attribute11         varchar(240) null comment '扩展属性',
    attribute12         varchar(240) null comment '扩展属性',
    attribute13         varchar(240) null comment '扩展属性',
    attribute14         varchar(240) null comment '扩展属性',
    attribute15         varchar(240) null comment '扩展属性',
    attribute16         varchar(240) null comment '扩展属性',
    attribute17         varchar(240) null comment '扩展属性',
    attribute18         varchar(240) null comment '扩展属性',
    attribute19         varchar(240) null comment '扩展属性',
    attribute20         varchar(240) null comment '扩展属性',
    language            varchar(10)  default 'ZHS'             not null comment '语种',
    tenant_id           varchar(32)  default 'TRINA'           not null comment '租户号',
    opt_counter         int          default 1                 not null comment '乐观锁',
    is_deleted          int          default 0                 not null comment '是否删除',
    created_by          varchar(32) null comment '创建人',
    created_time        datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by          varchar(32) null comment '更新人',
    updated_time        datetime     default CURRENT_TIMESTAMP not null comment '更新时间',
    item_type           varchar(255) null comment '物料类型',
    customer_order_flag varchar(255) null comment '可售标识',
    item_subcategory    varchar(255) null comment '物料小类',
    lifecycle_state     varchar(60) null comment '生命周期状态',
    constraint bbom_items_u1
        unique (item_code, organization_id, language),
    constraint idx_souritem_org_lang
        unique (source_item_id, organization_id, language)
) comment '物料基础数据表' collate = utf8_bin;

create index bbom_items_n2
    on bbom_items (category_segment5, item_code, organization_id);

create index idx_bbom_items_4
    on bbom_items (category_segment1);

create index idx_bbom_items_6
    on bbom_items (organization_id, item_code);

