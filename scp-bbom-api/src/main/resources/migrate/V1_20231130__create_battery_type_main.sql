DROP TABLE IF EXISTS bbom_battery_type_main;
CREATE TABLE bbom_battery_type_main
(
    id                 BIGINT      NOT NULL COMMENT 'ID主键',
    battery_code       VARCHAR(255) COMMENT '电池类型编码',
    battery_name       VARCHAR(255) COMMENT '电池类型名称',
    crystal_type       VARCHAR(255) COMMENT '晶体类型',
    p_or_n             VARCHAR(255) COMMENT 'P\N型',
    category           VARCHAR(255) COMMENT '品类',
    single_double_face VARCHAR(255) COMMENT '单双面',
    number_main_grids  VARCHAR(255) COMMENT '主栅数',
    sharding_mode      VARCHAR(255) COMMENT '分片方式',
    tenant_id          VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter        INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted         INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by         VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by         VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time       DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池类型静态属性';
