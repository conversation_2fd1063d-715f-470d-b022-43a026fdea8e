DROP TABLE IF EXISTS bbom_materiel_match_header;
CREATE TABLE bbom_materiel_match_header
(
    id                            BIGINT      NOT NULL COMMENT 'ID主键',
    battery_type                  VARCHAR(100) COMMENT '电池类型',
    aesthetics                    VARCHAR(50) COMMENT '美学',
    transparent_double_glass      VARCHAR(50) COMMENT '透明双玻',
    special_area                  VARCHAR(100) COMMENT '特殊区域',
    h_trace                       VARCHAR(100) COMMENT 'H追溯',
    pcs_source_type               VARCHAR(50) COMMENT '片源种类',
    pcs_source_level              VARCHAR(50) COMMENT '片源等级',
    is_special_requirements       VARCHAR(50) COMMENT '是否有特殊要求',
    screen_manufacturer           VARCHAR(100) COMMENT '网版厂家',
    silicon_material_manufacturer VARCHAR(100) COMMENT '硅料厂家',
    battery_manufacturer          VARCHAR(100) COMMENT '电池厂家',
    silver_slurry_manufacturer    VARCHAR(100) COMMENT '银浆厂家',
    low_resistance                VARCHAR(50) COMMENT '低阻',
    silicon_wafer_purchase_method VARCHAR(50) COMMENT '硅片购买方式',
    demand_place                  VARCHAR(100) COMMENT '需求地',
    base_place                    VARCHAR(255) COMMENT '排产基地',
    workshop                      VARCHAR(255) COMMENT '排产车间',
    workunit                      VARCHAR(255) COMMENT '生产单元',
    schedule_date                 DATE COMMENT '排产日期',
    schedule_qty                  DECIMAL(24, 6) COMMENT '排产数量',
    line                          DECIMAL(24, 6) COMMENT '线体',
    tenant_id                     VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter                   INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted                    INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by                    VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time                  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by                    VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time                  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池物料号匹配';
