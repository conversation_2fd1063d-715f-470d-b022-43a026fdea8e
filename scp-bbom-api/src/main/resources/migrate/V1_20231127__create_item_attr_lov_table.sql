-- auto-generated definition
create table bbom_item_attr_lov
(
    id             bigint                             not null comment 'ID'
        primary key,
    lov_id         varchar(50) null,
    lov_name       varchar(255) null,
    src_attr_id    varchar(50) null,
    src_attr_name  varchar(255) null,
    lov_line_id    varchar(50) null,
    lov_line_value varchar(255) null,
    language       varchar(50) null,
    is_required    varchar(50) null,
    tenant_id      varchar(32) null,
    opt_counter    int      default 1                 not null comment '乐观锁',
    is_deleted     int      default 0                 not null comment '是否删除',
    created_by     varchar(32) null,
    created_time   datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by     varchar(32) null,
    updated_time   datetime default CURRENT_TIMESTAMP not null comment '更新时间'
) comment '物料属性字段Lov';

