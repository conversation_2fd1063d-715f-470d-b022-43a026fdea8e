DROP TABLE IF EXISTS bbom_slurry_information;
CREATE TABLE bbom_slurry_information
(
    id                   BIGINT      NOT NULL COMMENT 'ID主键',
    battery_type         VARCHAR(255) COMMENT '电池类型',
    base_place           VARCHAR(255) COMMENT '生产基地',
    workshop             VARCHAR(255) COMMENT '生产车间',
    line_number          VARCHAR(255) COMMENT '线数',
    workbench            VARCHAR(255) COMMENT '机台',
    unit_consumption     VARCHAR(255) COMMENT '单耗',
    effective_start_date DATETIME COMMENT '有效日期_起',
    effective_end_date   DATETIME COMMENT '有效日期_止',
    tenant_id            VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter          INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted           INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '浆料车间单耗及线数维护';
