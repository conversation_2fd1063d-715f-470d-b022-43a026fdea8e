-- auto-generated definition
create table bbom_components
(
    id                      bigint                                not null comment 'bom构件id，自增序列'
        primary key,
    bom_id                  bigint null comment 'bom id，从结构上取',
    operation_seq_num       int null comment '操作序列号',
    component_item_id       bigint null comment '构件物料id',
    bom_structure           varchar(100) null comment 'BOM结构',
    substitute_flag         varchar(1) null comment '替代项标志',
    last_update_date        datetime null comment '最后更新日期',
    item_num                int null comment '项目序列号',
    component_quantity      varchar(100) null comment '构件数量',
    component_yield_factor  int null comment '产出因子',
    component_remarks       varchar(240) null comment '构件备注',
    effectivity_date        datetime null comment '生效日期',
    change_notice           varchar(10) null comment '变更备注',
    implementation_date     datetime null comment '实施日期',
    disable_date            datetime null comment '失效日期',
    attribute_category      varchar(30) null comment '属性类别',
    attribute1              varchar(150) null comment '扩展属性',
    attribute2              varchar(150) null comment '扩展属性',
    attribute3              varchar(150) null comment '扩展属性',
    attribute4              varchar(150) null comment '扩展属性',
    attribute5              varchar(150) null comment '扩展属性',
    attribute6              varchar(150) null comment '扩展属性',
    attribute7              varchar(150) null comment '扩展属性',
    attribute8              varchar(150) null comment '扩展属性',
    attribute9              varchar(150) null comment '扩展属性',
    attribute10             varchar(150) null comment '扩展属性',
    attribute11             varchar(150) null comment '扩展属性',
    attribute12             varchar(150) null comment '扩展属性',
    attribute13             varchar(150) null comment '扩展属性',
    attribute14             varchar(150) null comment '扩展属性',
    attribute15             varchar(150) null comment '扩展属性',
    planning_factor         int null comment '计划百分比',
    quantity_related        int null comment '相关数量',
    acd_type                int null comment 'ACD类型',
    component_sequence_id   bigint null comment '构件序号',
    substitute_component_id bigint null comment '替代项Id',
    bill_sequence_id        bigint null comment '清单序号（关键字）',
    wip_supply_type         int null comment '车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟',
    supply_subinventory     varchar(10) null comment '供应子库',
    supply_locator_id       bigint null comment '供应库位',
    bom_item_type           int null comment '清单项目类型1.模型.选项类.计划中.标准',
    basis_type              int null comment '基础类型',
    tenant_id               varchar(32) default 'TRINA'           not null comment '租户号',
    opt_counter             int         default 1                 not null comment '乐观锁',
    is_deleted              int         default 0                 not null comment '是否删除',
    created_by              varchar(32) null comment '创建人',
    created_time            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by              varchar(32) null comment '更新人',
    updated_time            datetime    default CURRENT_TIMESTAMP not null comment '更新时间'
) comment 'BOM行';

create index idx_bill_sequence_id
    on bbom_components (bill_sequence_id);

create index idx_bom_id
    on bbom_components (bom_id);

create index idx_component_item_id
    on bbom_components (component_item_id);

create index idx_unique_component_sequence_id
    on bbom_components (component_sequence_id, bom_structure, substitute_component_id);

