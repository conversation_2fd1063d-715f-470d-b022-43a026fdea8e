DROP TABLE IF EXISTS bbom_battery_product_import;
CREATE TABLE bbom_battery_product_import
(
    id                     BIGINT      NOT NULL COMMENT 'ID主键',
    battery_code           VARCHAR(255) COMMENT '电池类型编码',
    battery_name           VARCHAR(255) COMMENT '电池类型名称',
    battery_crystal_type   VARCHAR(255) COMMENT '电池片晶体类型',
    product_type           VARCHAR(255) COMMENT '产品类型',
    battery_dimension_code VARCHAR(255) COMMENT '电池片尺寸编码',
    number_main_grids      VARCHAR(255) COMMENT '主栅数',
    sharding_number        VARCHAR(255) COMMENT '分片数',
    tenant_id              VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter            INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted             INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by             VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by             VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池产品导入表';
