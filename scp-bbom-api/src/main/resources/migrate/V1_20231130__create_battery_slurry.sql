DROP TABLE IF EXISTS bbom_battery_slurry;
CREATE TABLE bbom_battery_slurry
(
    id                   BIGINT      NOT NULL COMMENT 'ID主键',
    battery_code         VARCHAR(255) COMMENT '电池类型编码',
    battery_name         VARCHAR(255) COMMENT '电池类型名称',
    base_place           VARCHAR(255) COMMENT '基地',
    workshop             VARCHAR(255) COMMENT '车间',
    workunit             VARCHAR(255) COMMENT '生产单元',
    item_code_new        VARCHAR(255) COMMENT '物料料号-新',
    item_desc_new        VARCHAR(255) COMMENT '物料-新说明',
    item_code_old        VARCHAR(255) COMMENT '物料料号-旧',
    item_desc_old        VARCHAR(255) COMMENT '物料-旧说明',
    line_new             VARCHAR(255) COMMENT '线体-新',
    line_old             VARCHAR(255) COMMENT '线体-旧',
    effective_start_date DATETIME COMMENT '有效期起',
    effective_end_date   DATETIME COMMENT '有效期止',
    tenant_id            VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter          INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted           INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池类型动态属性-浆料';
