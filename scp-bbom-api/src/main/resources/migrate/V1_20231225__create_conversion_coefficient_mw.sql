DROP TABLE IF EXISTS bbom_conversion_coefficient_mw;
CREATE TABLE bbom_conversion_coefficient_mw
(
    id                     BIGINT      NOT NULL COMMENT 'ID主键',
    battery_code           VARCHAR(50) COMMENT '电池类型编码',
    battery_name           VARCHAR(255) COMMENT '电池类型名称',
    category               VARCHAR(50) COMMENT '品类',
    classify               VARCHAR(30) COMMENT '物料分类',
    base_place             BIGINT(255) COMMENT '基地',
    workshop               BIGINT(255) COMMENT '车间',
    battery_mw_qty         DECIMAL(24, 6) COMMENT '电池MW系数',
    battery_efficiency_qty DECIMAL(24, 6) COMMENT '电池目标良率',
    unit_consumption       DECIMAL(24, 6) COMMENT '单耗',
    materiel_mw_qty        DECIMAL(24, 6) COMMENT '物料MW系数',
    warning_reason         VARCHAR(255) COMMENT '预警',
    tenant_id              VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter            INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted             INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by             VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by             VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time           DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '兆瓦转换系数';
