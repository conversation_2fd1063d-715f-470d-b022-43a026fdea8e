-- 优化bbom_materiel_match_line表的索引，提升查询性能

-- 1. 为主要查询条件创建复合索引
-- 覆盖 match_status, item_code, old_month 的查询场景
CREATE INDEX idx_match_status_item_code_old_month 
ON bbom_materiel_match_line (match_status, item_code, old_month) 
USING BTREE;

-- 2. 为常用的时间范围查询创建索引
-- 支持按old_month进行范围查询
CREATE INDEX idx_old_month_match_status 
ON bbom_materiel_match_line (old_month, match_status, is_deleted) 
USING BTREE;

-- 3. 为分页查询优化的索引
-- 包含排序字段created_time/updated_time
CREATE INDEX idx_match_status_time 
ON bbom_materiel_match_line (match_status, old_month, updated_time DESC) 
USING BTREE;

-- 4. 删除可能重复的旧索引（如果存在）
-- DROP INDEX IF EXISTS idx_old_month_is_deleted_split_flag ON bbom_materiel_match_line;
