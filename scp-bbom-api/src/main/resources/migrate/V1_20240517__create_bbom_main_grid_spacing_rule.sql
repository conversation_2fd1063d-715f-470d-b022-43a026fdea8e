DROP TABLE IF EXISTS bbom_main_grid_spacing_rule;
CREATE TABLE bbom_main_grid_spacing_rule
(
    id                BIGINT      NOT NULL COMMENT 'ID',
    battery_type      VARCHAR(255) COMMENT '电池类型',
    item_workshop     VARCHAR(255) COMMENT '组件车间',
    battery_workshop  VARCHAR(1024) COMMENT '电池车间',
    main_grid_spacing VARCHAR(255) COMMENT '主栅间距',
    tenant_id         VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter       INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted        INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by        VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by        VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time      DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池主栅间距规则';
