alter table bbom_materiel_match_line
    add battery_type varchar(100) null comment '电池类型' after alternate_bom_designator;
alter table bbom_materiel_match_line
    add aesthetics varchar(50) null comment '美学' after battery_type;
alter table bbom_materiel_match_line
    add transparent_double_glass varchar(50) null comment '透明双玻' after aesthetics;
alter table bbom_materiel_match_line
    add special_area varchar(50) null comment '特殊区域' after transparent_double_glass;
alter table bbom_materiel_match_line
    add h_trace varchar(50) null comment 'H追溯' after special_area;
alter table bbom_materiel_match_line
    add pcs_source_type varchar(50) null comment '片源种类' after h_trace;
alter table bbom_materiel_match_line
    add pcs_source_level varchar(50) null comment '硅片等级' after pcs_source_type;
alter table bbom_materiel_match_line
    add is_special_requirements varchar(20) null comment '是否有特殊要求' after pcs_source_level;
alter table bbom_materiel_match_line
    add screen_manufacturer varchar(50) null comment '网版厂家' after is_special_requirements;
alter table bbom_materiel_match_line
    add silicon_material_manufacturer varchar(50) null comment '硅料厂家' after screen_manufacturer;
alter table bbom_materiel_match_line
    add battery_manufacturer varchar(50) null comment '电池厂家' after silicon_material_manufacturer;
alter table bbom_materiel_match_line
    add silver_slurry_manufacturer varchar(50) null comment '银浆厂家' after battery_manufacturer;
alter table bbom_materiel_match_line
    add low_resistance varchar(50) null comment '低阻' after silver_slurry_manufacturer;
alter table bbom_materiel_match_line
    add silicon_wafer_purchase_method varchar(50) null comment '硅片购买方式' after low_resistance;
alter table bbom_materiel_match_line
    add demand_place varchar(50) null comment '需求地' after silicon_wafer_purchase_method;
alter table bbom_materiel_match_line
    add base_place varchar(100) null comment '排产基地' after demand_place;
alter table bbom_materiel_match_line
    add workshop varchar(100) null comment '排产车间' after base_place;
alter table bbom_materiel_match_line
    add workunit varchar(50) null comment '生产单元' after workshop;
alter table bbom_materiel_match_line
    add production_grade varchar(50) null comment '产品等级' after workunit;
alter table bbom_materiel_match_line
    add process_category varchar(50) null comment '加工类别' after production_grade;
alter table bbom_materiel_match_line
    add total_line decimal null comment '总线体数量' after process_category;