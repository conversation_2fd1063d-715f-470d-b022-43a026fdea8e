-- auto-generated definition
create table bbom_structures
(
    id                        bigint                                not null comment 'bom id'
        primary key,
    assembly_item_id          bigint null comment '装配件物料ID',
    organization_id           bigint null comment '组织代码',
    alternate_bom_designator  varchar(10) null comment 'alternateBomDesignator',
    last_update_date          datetime null comment 'ERP最后更新日期',
    common_assembly_item_id   bigint null comment '公共项目内码',
    specific_assembly_comment varchar(240) null comment 'specificAssemblyComment',
    pending_from_ecn          varchar(10) null comment 'pendingFromEcn',
    attribute_category        varchar(30) null comment '属性类别',
    attribute1                varchar(150) null comment '扩展属性',
    attribute2                varchar(150) null comment '扩展属性',
    attribute3                varchar(150) null comment '扩展属性',
    attribute4                varchar(150) null comment '扩展属性',
    attribute5                varchar(150) null comment '扩展属性',
    attribute6                varchar(150) null comment '扩展属性',
    attribute7                varchar(150) null comment '扩展属性',
    attribute8                varchar(150) null comment '扩展属性',
    attribute9                varchar(150) null comment '扩展属性',
    attribute10               varchar(150) null comment '扩展属性',
    attribute11               varchar(150) null comment '扩展属性',
    attribute12               varchar(150) null comment '扩展属性',
    attribute13               varchar(150) null comment '扩展属性',
    attribute14               varchar(150) null comment '扩展属性',
    attribute15               varchar(150) null comment '扩展属性',
    assembly_type             bigint null comment '装配类别',
    common_bill_sequence_id   bigint null comment '公共序号',
    bill_sequence_id          bigint null comment '清单序号（关键字）',
    request_id                bigint null comment '请求id',
    program_application_id    bigint null comment 'programApplicationId',
    program_id                bigint null comment 'programId',
    program_update_date       datetime null comment 'programUpdateDate',
    common_organization_id    bigint null comment '公共组织',
    next_explode_date         datetime null comment 'nextExplodeDate',
    project_id                bigint null comment 'projectId',
    task_id                   bigint null comment 'taskId',
    original_system_reference varchar(50) null comment 'originalSystemReference',
    structure_type_id         bigint null comment 'structureTypeId',
    implementation_date       datetime null comment 'implementationDate',
    obj_name                  varchar(30) null comment 'objName',
    pk1_value                 varchar(240) null comment 'pkValue',
    pk2_value                 varchar(240) null comment 'pkValue',
    pk3_value                 varchar(240) null comment 'pkValue',
    pk4_value                 varchar(240) null comment 'pkValue',
    pk5_value                 varchar(240) null comment 'pkValue',
    effectivity_control       int null comment 'effectivityControl',
    is_preferred              varchar(1) null comment 'isPreferred',
    source_bill_sequence_id   bigint null comment 'sourceBillSequenceId',
    tenant_id                 varchar(32) default 'TRINA'           not null comment '租户号',
    opt_counter               int         default 1                 not null comment '乐观锁',
    is_deleted                int         default 0                 not null comment '是否删除',
    created_by                varchar(32) null comment '创建人',
    created_time              datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by                varchar(32) null comment '更新人',
    updated_time              datetime    default CURRENT_TIMESTAMP not null comment '更新时间',
    constraint idx_bill_sequns
        unique (bill_sequence_id)
) comment 'BBOM结构';

create index idx_assembly_item_id
    on bbom_structures (assembly_item_id, attribute2);

create index idx_org_item
    on bbom_structures (assembly_item_id, organization_id);

