-- auto-generated definition
create table bbom_item_attr
(
    id                       bigint auto_increment
        primary key,
    src_attr_id              varchar(255) null,
    src_attr_alias           varchar(255) null,
    src_category_segment4_id varchar(255) null,
    src_category_segment4    varchar(255) null,
    src_attr_type            varchar(255) null,
    src_option_flag          varchar(255) null,
    src_attr_column          varchar(50) null,
    language                 varchar(255) null,
    tenant_id                varchar(32) null,
    opt_counter              int      default 1                 not null comment '乐观锁',
    is_deleted               int      default 0                 not null comment '是否删除',
    created_by               varchar(32) null,
    created_time             datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by               varchar(32) null,
    updated_time             datetime default CURRENT_TIMESTAMP not null comment '更新时间'
) comment '物料属性字段别名';

