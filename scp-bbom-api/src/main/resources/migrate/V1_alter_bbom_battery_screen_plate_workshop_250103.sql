CREATE TABLE `scp_bbom`.`bbom_battery_screen_plate_workshop` (
`id` bigint NOT NULL COMMENT 'ID主键',
`battery_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型编码',
`battery_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电池类型名称',
`single_glass_flag` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单玻',
`main_grid_space` varchar(64) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '主栅间距',
`base_place` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产基地',
`workshop` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '生产车间',
`positive_electrode_screen_fine_grid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '正电极网版细栅 取LOV：7A01500100122',
`negative_electrode_screen_fine_grid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '背电极网版细栅 取LOV：7A01500100122',
`effective_start_date` datetime DEFAULT NULL COMMENT '有效期起',
`effective_end_date` datetime DEFAULT NULL COMMENT '有效期止',
`remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
`tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
`opt_counter` int NOT NULL DEFAULT '1' COMMENT '乐观锁',
`is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
`created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '创建人',
`created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '-1' COMMENT '更新人',
`updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='车间级网版切换维护';

alter table `scp_bbom`.bbom_materiel_match_line
    add positive_electrode_screen_fine_grid varchar(256) null comment '正电极网版细栅' ;

alter table `scp_bbom`.bbom_materiel_match_line
    add negative_electrode_screen_fine_grid varchar(256) null comment '背电极网版细栅' ;
