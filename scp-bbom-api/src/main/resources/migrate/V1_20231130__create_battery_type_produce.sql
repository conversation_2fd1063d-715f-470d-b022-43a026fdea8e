DROP TABLE IF EXISTS bbom_battery_type_produce;
CREATE TABLE bbom_battery_type_produce
(
    id                   BIGINT      NOT NULL COMMENT 'ID主键',
    battery_code         VARCHAR(255) COMMENT '电池类型编码',
    battery_name         VARCHAR(255) COMMENT '电池类型名称',
    name                 VARCHAR(255) COMMENT '名称',
    effective_start_date DATETIME COMMENT '有效期起',
    effective_end_date   DATETIME COMMENT '有效期止',
    tenant_id            VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter          INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted           INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by           VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time         DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池类型动态属性-产出电池类型';
