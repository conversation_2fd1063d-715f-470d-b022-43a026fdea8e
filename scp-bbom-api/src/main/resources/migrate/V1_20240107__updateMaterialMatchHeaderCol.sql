alter table bbom_materiel_match_header
    add month char(6) null after id;

alter table bbom_materiel_match_header
drop
column schedule_date;

alter table bbom_materiel_match_header
drop
column schedule_qty;


alter table bbom_materiel_match_header
    add match_status varchar(20) null comment '匹配状态' after line;

alter table bbom_materiel_match_line
    modify item_code varchar (200) null comment '电池料号';

alter table bbom_materiel_match_line
drop
column item_desc;

alter table bbom_materiel_match_line
    add match_status varchar(20) null comment '匹配状态' after item_code;

alter table bbom_materiel_match_line
    change is_switch screen_plate_item_code varchar (20) null comment '切换网版料号' after is_catch_poroduction;

alter table bbom_materiel_match_line
    change is_exist_bom alternate_bom_designator char (10) null comment 'BOM替代项' after updated_time;

alter table bbom_materiel_match_line
    modify switch_start_date datetime null comment '切换开始时间' after switch_end_date;

alter table bbom_materiel_match_line
drop
column line_date;

alter table bbom_materiel_match_line
    change is_catch_poroduction is_catch_production varchar (50) null comment '研发/量产';
