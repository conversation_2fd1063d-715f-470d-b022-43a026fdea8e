-- auto-generated definition
create table bbom_component_v
(
    id                       bigint            not null comment 'ID'
        primary key,
    last_update_date         datetime null,
    bill_sequence_id         varchar(30) null,
    assembly_item_id         varchar(30) null,
    organization_id          varchar(30) null,
    assembly_type            varchar(100) null,
    ass_item_num             varchar(100) null,
    ass_item_des             varchar(255) null,
    ass_item_uom             varchar(100) null,
    alternate_bom_designator varchar(100) null,
    bm_attribute_category    varchar(100) null,
    bm_attribute1            varchar(100) null,
    bm_attribute2            varchar(100) null,
    bm_attribute3            varchar(100) null,
    bm_attribute4            varchar(100) null,
    bm_attribute5            varchar(100) null,
    bm_attribute6            varchar(100) null,
    bm_attribute7            varchar(100) null,
    bm_attribute8            varchar(100) null,
    bm_attribute9            varchar(100) null,
    bm_attribute10           varchar(100) null,
    bm_attribute11           varchar(100) null,
    bm_attribute12           varchar(100) null,
    bm_attribute13           varchar(100) null,
    bm_attribute14           varchar(100) null,
    bm_attribute15           varchar(100) null,
    component_sequence_id    varchar(100) null,
    substitute_component_id  varchar(100) null,
    component_item_id        varchar(100) null,
    item_num                 varchar(100) null,
    operation_seq_num        varchar(100) null,
    bic_item_num             varchar(100) null,
    bic_item_des             varchar(255) null,
    bic_item_uom             varchar(100) null,
    basis_type               varchar(100) null,
    component_quantity       varchar(100) null,
    auto_request_material    varchar(100) null,
    effectivity_date         varchar(100) null,
    disable_date             varchar(100) null,
    change_notice            varchar(100) null,
    planning_factor          varchar(100) null,
    component_yield_factor   varchar(100) null,
    enforce_int_requirements varchar(100) null,
    include_in_cost_rollup   varchar(100) null,
    bic_item_type            varchar(100) null,
    bic_item_status          varchar(100) null,
    wip_supply_type          varchar(100) null,
    supply_type              varchar(100) null,
    bc_attribute_category    varchar(100) null,
    bc_attribute1            varchar(100) null,
    bc_attribute2            varchar(100) null,
    bc_attribute3            varchar(100) null,
    bc_attribute4            varchar(100) null,
    bc_attribute5            varchar(100) null,
    bc_attribute6            varchar(100) null,
    bc_attribute7            varchar(100) null,
    bc_attribute8            varchar(100) null,
    bc_attribute9            varchar(100) null,
    bc_attribute10           varchar(100) null,
    bc_attribute11           varchar(100) null,
    bc_attribute12           varchar(100) null,
    bc_attribute13           varchar(100) null,
    bc_attribute14           varchar(100) null,
    bc_attribute15           varchar(100) null,
    bsc_flag                 varchar(10) null,
    is_process               tinyint default 0 null comment '是否处理',
    tenant_id                varchar(32) null comment '租户号',
    opt_counter              int     default 1 not null comment '乐观锁',
    is_deleted               int     default 0 not null comment '是否删除',
    created_by               varchar(32) null comment '创建人',
    created_time             datetime null comment '创建时间',
    updated_by               varchar(32) null comment '更新人',
    updated_time             datetime null comment '更新时间'
) comment '同步cux3_bbom_component_v';

create index idx_component
    on bbom_component_v (bill_sequence_id, substitute_component_id, is_deleted);

create index idx_is_process
    on bbom_component_v (is_process);

create index idx_lastupdatetime
    on bbom_component_v (organization_id, last_update_date);

create index idx_seq_id_compoentn_item_id
    on bbom_component_v (component_sequence_id, substitute_component_id);

