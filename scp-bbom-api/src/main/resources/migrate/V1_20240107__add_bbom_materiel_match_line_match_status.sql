DROP TABLE IF EXISTS bbom_materiel_match_line_match_status;
CREATE TABLE bbom_materiel_match_line_match_status
(
    id                       BIGINT      NOT NULL COMMENT 'ID',
    line_id                  BIGINT COMMENT '电池料号匹配明细行ID',
    item_code                VARCHAR(20) COMMENT '物料Code',
    item_desc                VARCHAR(255) COMMENT '物料描述',
    match_status             VARCHAR(20) COMMENT '匹配状态',
    alternate_bom_designator VARCHAR(50) COMMENT 'BOM替代项',
    is_catch_production      VARCHAR(20) COMMENT '研发/量产',
    tenant_id                VARCHAR(32) NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    opt_counter              INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    is_deleted               INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    created_by               VARCHA<PERSON>(32) NOT NULL DEFAULT '-1' COMMENT '创建人',
    created_time             DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by               VARCHAR(32) NOT NULL DEFAULT '-1' COMMENT '更新人',
    updated_time             DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '电池料号匹配明细行匹配状态';
