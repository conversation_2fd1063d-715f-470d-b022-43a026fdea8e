INSERT INTO `scp_system`.`sys_code_seq` (`id`, `seq_code`, `seq_name`, `seq_desc`, `is_available`, `reset_frequency`,
                                         `reset_start_date`, `reset_end_date`, `seq_value`, `seq_full_Value`,
                                         `reset_date`, `tenant_id`, `opt_counter`, `is_deleted`, `created_by`,
                                         `created_time`, `updated_by`, `updated_time`)
VALUES (513, 'BBOM_SCREEN_LIFE', 'bbom网版寿命', 'bbom网版寿命', 'y', NULL, NULL, NULL, NULL, NULL, NULL,
        'TRINA', 1, 0, NULL, '2024-01-15 16:06:34', NULL, '2024-01-15 16:06:34');

INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40381, 513, 1, 'fixedChar', 'SC', '', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-09 15:56:47', '-1',
        '2022-06-09 15:56:47');
INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40382, 513, 2, 'dateFormat', '', 'yyyyMMdd', NULL, NULL, 1, 'TRINA', 1, 0, '-1', '2022-06-09 15:57:39', '-1',
        '2022-06-09 15:57:39');
INSERT INTO `scp_system`.`sys_code_seq_rule` (`id`, `seq_id`, `rule_order`, `rule_field`, `rule_field_value`,
                                              `date_pattern`, `seq_length`, `seq_increment`, `seq_start_value`,
                                              `tenant_id`, `opt_counter`, `is_deleted`, `created_by`, `created_time`,
                                              `updated_by`, `updated_time`)
VALUES (40383, 513, 3, 'seq', NULL, '', 3, 1, 1, 'TRINA', 1, 0, '-1', '2022-06-09 15:58:19', '-1',
        '2022-06-09 15:58:19');