CREATE TABLE `bbom_rule_control_object_detail`
(
    `id`                            bigint                                                NOT NULL COMMENT 'ID',
    `rule_control_object_header_id` bigint                                                         DEFAULT NULL COMMENT '规则管控对象头ID',
    `materials_attr_filed_id`       bigint                                                         DEFAULT NULL COMMENT '材料属性ID',
    `materials_attr_filed`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '材料属性',
    `attr_operator`                 varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '运算符 1：包含  2：排除  3：等于',
    `tenant_id`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    `opt_counter`                   int                                                   NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`                    int                                                   NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
    `created_by`                    varchar(32) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '创建人',
    `created_time`                  datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`                    varchar(32) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '更新人',
    `updated_time`                  datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='规则管控对象详情';