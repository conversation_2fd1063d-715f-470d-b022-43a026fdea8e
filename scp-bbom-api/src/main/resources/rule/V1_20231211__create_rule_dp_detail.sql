CREATE TABLE `bbom_rule_dp_detail`
(
    `rule_line_id`   bigint   NOT NULL COMMENT '规则行ID',
    `rule_detail_id` bigint   NOT NULL COMMENT '规则明细ID，序列号生成',
    `dp_filed_id`    bigint                                                DEFAULT NULL COMMENT 'DP字段Id,AttrLineId',
    `dp_filed_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'DP属性字段',
    `attr_operator`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运算符',
    `tenant_id`      varchar(32) COLLATE utf8mb4_bin                       DEFAULT NULL COMMENT '租户号',
    `opt_counter`    int      NOT NULL                                     DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`     int      NOT NULL                                     DEFAULT '0' COMMENT '是否删除',
    `created_by`     varchar(32) COLLATE utf8mb4_bin                       DEFAULT NULL COMMENT '创建人',
    `created_time`   datetime NOT NULL                                     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`     varchar(32) COLLATE utf8mb4_bin                       DEFAULT NULL COMMENT '更新人',
    `updated_time`   datetime NOT NULL                                     DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`rule_detail_id`) USING BTREE,
    KEY              `idx_rule_line_id` (`rule_line_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='BOM规则DP因子';