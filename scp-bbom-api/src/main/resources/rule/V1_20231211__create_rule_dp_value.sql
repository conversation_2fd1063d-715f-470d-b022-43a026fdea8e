CREATE TABLE `bbom_rule_dp_value`
(
    `rule_detail_id`   bigint   NOT NULL COMMENT '规则明细ID',
    `id`               bigint   NOT NULL COMMENT '属性值ID，序列号生成',
    `value_type`       varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '值类型',
    `attr_value_id`    bigint                                                 DEFAULT NULL COMMENT '属性值Id',
    `attr_value`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '属性值',
    `attr_value_to_id` bigint                                                 DEFAULT NULL COMMENT '属性值_止id',
    `attr_value_to`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '属性值_止',
    `tenant_id`        varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '租户号',
    `opt_counter`      int      NOT NULL                                      DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`       int      NOT NULL                                      DEFAULT '0' COMMENT '是否删除',
    `created_by`       varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '创建人',
    `created_time`     datetime NOT NULL                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`       varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '更新人',
    `updated_time`     datetime NOT NULL                                      DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                `idx_reule_detail_id` (`rule_detail_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='BOM规则DP因子明细值';