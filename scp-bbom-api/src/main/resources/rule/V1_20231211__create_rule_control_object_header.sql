CREATE TABLE `bbom_rule_control_object_header`
(
    `id`                bigint                                                NOT NULL COMMENT 'ID',
    `rule_line_id`      bigint                                                         DEFAULT NULL COMMENT '规则行ID',
    `struct_object_id`  bigint                                                         DEFAULT NULL COMMENT '结构对象ID',
    `struct_object`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '结构对象',
    `control_object_id` bigint                                                         DEFAULT NULL COMMENT '管控对象ID',
    `control_object`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '管控对象',
    `bom_prompt`        char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin              DEFAULT NULL COMMENT 'BOM提示：是（Y）否（N）',
    `remark`            varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '备注',
    `tenant_id`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    `opt_counter`       int                                                   NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`        int                                                   NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
    `created_by`        varchar(32) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '创建人',
    `created_time`      datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`        varchar(32) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '更新人',
    `updated_time`      datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='规则管控对象头';