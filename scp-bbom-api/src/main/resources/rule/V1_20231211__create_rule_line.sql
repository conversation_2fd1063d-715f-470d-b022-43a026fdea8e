CREATE TABLE `bbom_rule_line`
(
    `rule_header_id`       bigint   NOT NULL COMMENT '规则头ID ',
    `rule_line_id`         bigint   NOT NULL COMMENT '规则行ID，序列号生成',
    `code`                 varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '规则编码',
    `control_subject_id`   bigint unsigned DEFAULT NULL COMMENT '管控主体ID,取对应LOV行ID',
    `control_subject`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管控主体',
    `control_object_id`    bigint                                                 DEFAULT NULL COMMENT '管控对象，取组件分料号动态配置标识下的组件分料号属性ID',
    `control_object`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管控对象',
    `control_purpose_id`   bigint                                                 DEFAULT NULL COMMENT '管控目的,取对应LOV行ID',
    `control_purpose`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管控目的',
    `default_flag`         varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '默认标识',
    `attr_value`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '属性值',
    `attr_value_id`        bigint                                                 DEFAULT NULL COMMENT 'lovId',
    `effective_start_date` date                                                   DEFAULT NULL COMMENT '有效日期_起',
    `effective_end_date`   date                                                   DEFAULT NULL COMMENT '有效日期_止',
    `remark`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `attribute1`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute2`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute3`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute4`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute5`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute6`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute7`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute8`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute9`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute10`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute11`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute12`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute13`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute14`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `attribute15`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展字段 ',
    `tenant_id`            varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '租户号',
    `opt_counter`          int      NOT NULL                                      DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`           int      NOT NULL                                      DEFAULT '0' COMMENT '是否删除',
    `created_by`           varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '创建人',
    `created_time`         datetime NOT NULL                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`           varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '更新人',
    `updated_time`         datetime NOT NULL                                      DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `no`                   int unsigned NOT NULL DEFAULT '0' COMMENT '序号',
    PRIMARY KEY (`rule_line_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='BOM规则行表';