CREATE TABLE `bbom_rule_header`
(
    `rule_header_id`       bigint   NOT NULL                                      DEFAULT '0' COMMENT '规则头ID 序列号生成',
    `rule_category_id`     bigint   NOT NULL                                      DEFAULT '0' COMMENT '规则分类ID，从LOV取LOV行ID',
    `rule_number`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '规则编号',
    `rule_name`            varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '规则名',
    `control_subject_id`   bigint                                                 DEFAULT NULL COMMENT '管控主体ID,取对应LOV行ID',
    `control_subject_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管控主体名称',
    `control_object_id`    bigint                                                 DEFAULT NULL COMMENT '管控对象，取组件分料号动态配置标识下的组件分料号属性ID',
    `control_object_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管控对象名称',
    `control_purpose_id`   bigint                                                 DEFAULT NULL COMMENT '管控目的,取对应LOV行ID',
    `control_purpose_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '管控目的',
    `enable_flag`          varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '有效标识',
    `effective_start_date` date                                                   DEFAULT NULL COMMENT '有效日期_起',
    `effective_end_date`   date                                                   DEFAULT NULL COMMENT '有效日期_止',
    `attribute1`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute2`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute3`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute4`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute5`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute6`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute7`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute8`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute9`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute10`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute11`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute12`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute13`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute14`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `attribute15`          varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '扩展属性',
    `tenant_id`            varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '租户号',
    `opt_counter`          int      NOT NULL                                      DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`           int      NOT NULL                                      DEFAULT '0' COMMENT '是否删除',
    `created_by`           varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '创建人',
    `created_time`         datetime NOT NULL                                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`           varchar(32) COLLATE utf8mb4_bin                        DEFAULT NULL COMMENT '更新人',
    `updated_time`         datetime NOT NULL                                      DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`rule_header_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='BOM规则头表';