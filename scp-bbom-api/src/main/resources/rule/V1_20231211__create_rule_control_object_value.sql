CREATE TABLE `bbom_rule_control_object_value`
(
    `id`                            bigint                                                NOT NULL COMMENT 'ID',
    `rule_control_object_detail_id` bigint                                                         DEFAULT NULL COMMENT '规则管控对象详情ID',
    `value_type`                    char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin              DEFAULT NULL COMMENT '值类型，1-值  2-范围',
    `attr_value_id`                 bigint                                                         DEFAULT NULL COMMENT '属性值id',
    `attr_value`                    varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '属性值',
    `attr_value_to_id`              bigint                                                         DEFAULT NULL COMMENT '属性值_止id',
    `attr_value_to`                 varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '属性值_止',
    `tenant_id`                     varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'TRINA' COMMENT '租户号',
    `opt_counter`                   int                                                   NOT NULL DEFAULT '1' COMMENT '乐观锁',
    `is_deleted`                    int                                                   NOT NULL DEFAULT '0' COMMENT '是否删除,0=正常，1=删除',
    `created_by`                    varchar(32) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '创建人',
    `created_time`                  datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`                    varchar(32) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '更新人',
    `updated_time`                  datetime                                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='规则管控对象值';