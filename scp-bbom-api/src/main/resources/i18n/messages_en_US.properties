bbom_importValidError=Import data validation failed
bbom_feign_findBasePlaceWorkshopWorkUnit_error=Battery Type Dynamic Properties - Network Version, Call to Feign findBasePlaceWorkshop WorkUnit Interface Failed
bbom_feign_findItemDescByItemCodes_error=Battery type dynamic properties - online version, calling Feign finditemDescByitemCodes interface failed
bbom_lineNumber=Line {0}
bbom_valid_BatteryProduct_unique=Unique verification of battery product code+battery cell crystal type+product type+battery cell size code+number of main grids+number of partitions, not allowed to be repeated
bbom_valid_batteryCode_notBlank=The battery type code cannot be empty
bbom_valid_batteryCrystalType_notBlank=The crystal type of the battery cell cannot be empty
bbom_valid_batteryDimensionCode_notBlank=The battery cell size code cannot be empty
bbom_valid_NumberMainGrids_notBlank=The number of main barriers cannot be empty
bbom_valid_productType_notBlank=Product type cannot be empty
bbom_valid_shardingNumber_notBlank=The number of shards cannot be empty
bbom_valid_switchType_notBlank=Switching type cannot be empty
bbom_valid_switchType_illegal=Illegal switching type
bbom_valid_switchType_notMatch=Switching type failed to match
bbom_valid_basePlace_notBlank=The base place cannot be empty
bbom_valid_workshop_notBlank=Workshop cannot be empty
bbom_valid_workbench_notBlank=Workbench cannot be empty
bbom_valid_batteryName_notBlank=The battery type name cannot be empty
bbom_valid_itemCodeNew_notBlank=Item code - new cannot be empty
bbom_valid_screenNumberNew_notBlank=The consumption quantity of new screen plate cannot be empty
bbom_valid_itemCodeOld_notBlank=Item code - old cannot be empty
bbom_valid_screenNumberOld_notBlank=The consumption quantity of old screen plate cannot be empty
bbom_valid_line_notBlank=The number of lines cannot be empty
bbom_valid_workunit_notBlank=Workunit cannot be empty
bbom_valid_vendorName_notBlank=Vendor name cannot be empty
bbom_valid_workshopNotOrg=Workshop {0} has not maintained inventory organization
bbom_valid_screenPlateIsExistBom=Screen plate item code - whether new exists in BOM. When it is set to no, saving is not allowed
bbom_valid_screenPlateNewExistBom_save={0} _ {1} _ {2}: Screen plate item code - whether new exists. When in BOM, it is not allowed to add new items
bbom_valid_basePlaceWorkshop_illegal={0} {1} : Illegal base place and workshop
bbom_valid_screenPlate_unique=Unique verification of screen part number - new+screen part number - old+battery type+machine+production base+production workshop+production unit+line quantity, not allowed to be duplicate
bbom_valid_screenPlateNewExistBom_import={0} _ {1} _ {2}: Screen plate item code - whether new exists. When in BOM, it is not allowed to add new items
bbom_valid_vendorName_nonExist={0} vendor name does not exist
bbom_valid_totalLine=The number of wire bodies must be less than or equal to the number of wire bodies in the APS interface
bbom_valid_workbench_illegal={0} : Workbench illegal
bbom_valid_batteryCode_illegal={0}:The battery type name is not included in the static attributes of the battery type
bbom_valid_effectiveStartDate_format_illegal=Validity period from: {0} Date format is not yyyy MM dd
bbom_valid_effectiveEndDate_format_illegal=Expiration date: {0} Date format is not yyyy MM dd
bbom_valid_siliconWaferProperties_notBlank=Silicon wafer properties cannot be empty
bbom_valid_conditionItem_notBlank=The condition item cannot be empty
bbom_valid_value_notBlank=Value cannot be empty
bbom_valid_oldConditionItem_notBlank=Old condition items cannot be empty
bbom_valid_oldValue_notBlank=Old value cannot be empty
bbom_valid_batterySiliconWafer_unique=Unique verification of battery type+production base+production workshop+production unit+silicon wafer attributes, not allowed to duplicate
bbom_valid_batteryName_illegal=Battery type does not exist {0}
bbom_valid_newSiliconWaferProperties_illegal=New silicon wafer properties do not exist
bbom_valid_newConditionItem_notBlank=The new condition item does not exist
bbom_valid_oldSiliconWaferProperties_illegal=Old silicon wafer properties do not exist
bbom_valid_line_new_not_blank=Line quantity - new cannot be empty
bbom_valid_YfAndQty_notBlank=The quantity field cannot be empty when the maintenance type is R&D
bbom_valid_totalLineQty_illegal=The sum of the number of thread bodies - old+thread body quantity - new is greater than the number of thread bodies that have been maintained
bbom_valid_BatterySlurry_unique=Battery type+production base+production workshop+material code - new+machine+expiration date _ start+expiration date uniqueness verification, no duplicates allowed
bbom_valid_importData_repeat=Import data with duplicate data
bbom_valid_alternateDesignator_notFound=No alternative material information found orgId: {0} designator: {0}
bbom_valid_batteryMWQty_notBlank=The MW coefficient of the battery cannot be empty
bbom_valid_batteryMWQty_notZero=The MW coefficient of the battery cannot be: 0
bbom_valid_batteryEfficiencyQty_notBlank=The target yield of the battery cannot be empty
bbom_valid_batteryEfficiencyQty_notZero=The target yield of the battery cannot be: 0
bbom_valid_lifetime_illegal=The lifespan (in 10000) cannot be null
bbom_valid_DividingByZero=Divisor cannot be 0
bbom_valid_unitConsumption_notNumber=Unit consumption is not a numerical type
bbom_valid_unitConsumption_notZero=Unit consumption cannot be: 0
bbom_valid_itemCode_notExist=Item code {0} does not exist
bbom_valid_mainGridSpacingRule_repeat=The battery type and component workshop already exist or have overlapping valid dates
bbom_matchItem_notExist=Attribute matching material failed, please check if the material exists
bbom_matchItem_screenPlateAttr_notConfig=Screen plate properties not configured
bbom_workshopAndOrgNotFoundDesignator={0}: No BOM replacement item was found for the corresponding inventory organization in the workshop. Please check
bbom_notFound_cellRuleClassification=Unable to find Lov Line for category rule classification
bbom_notFound_SpecialArea=Unable to find Lov Line for special area rules
bbom_notFound_organization_workshop=Workshop {0} has not maintained inventory organization
bbom_hasRuleLIne_canNotDel=There are valid rule lines and deletion is not allowed
bbom_only_default=There can only be one recommendation
bbom_batteryType_notBlank=Battery type cannot be empty
bbom_basePlace_notBlank=The base place cannot be empty
bbom_workshop_notBlank=The workshop cannot be empty
bbom_valid_itemCode_notBlank=The item code cannot be empty
bbom_valid_itemCode_repeat=Item code: {0}, Battery type+Material number+Production base+Production workshop+Machine+Main grid information+Main grid spacing+Single glass uniqueness verification, import failed
bbom_valid_screenLife_repeat=Battery type+Material number+Production base+Production workshop+Machine+Main grid information+Main grid spacing+Single glass uniqueness verification, no duplicates allowed
bbom_valid_itemCode_illegal=Invalid online part number: {0}
bbom_valid_SiliconWaferConditionalItem_notBlank=The silicon wafer attribute condition item cannot be empty
bbom_valid_SiliconWaferValue_notBlank=The silicon wafer attribute value cannot be empty
bbom_valid_batteryProperties_notBlank=Battery attribute cannot be empty
bbom_valid_BatteryConditionalItem_notBlank=The battery attribute condition item cannot be empty
bbom_valid_BatteryValue_notBlank=The battery attribute value cannot be empty
bbom_valid_SiliconWaferProperties_illegal=Illegal silicon wafer properties
bbom_valid_BatteryProperties_illegal=Illegal battery attributes
bbom_valid_SiliconIsolationRate_notBlank=Silicon wafer isolation rate cannot be empty
bbom_valid_SiliconWaferLoss_repeat=The battery type+production base+production workshop are for uniqueness verification and cannot be duplicated
bbom_valid_siliconWaferYield_gtZero=The battery yield must be greater than 0
bbom_valid_siliconIsolationRate_gtZero=The silicon wafer isolation rate must be greater than 0
bbom_valid_SlurryInformation_repeat={0} _ {1} _ {2} _ {3}: Unique verification of battery type+production base+production workshop+machine, not allowed to be repeated
bbom_valid_organization_notBlank=Organization cannot be empty
bbom_valid_effectiveDate_error=Validity date verification error
bbom_valid_itemCodeNew_illegal=itemCodeNew {0} do not exist
a.materiel.match.rule.import.row.not.null=the {0} row each attribute (isOversea, workshop, cellsType, aItemCode) cannot have empty values
a.materiel.match.rule.import.groupKey.repeat=the {0} row each attribute (isOversea, workshop, cellsType, actualPieceThickness, mainGridBothShape, aItemCode) repeat
a.materiel.match.rule.import.isoversea.not.exists=the {0} row {1} isOversea does not exist
a.materiel.match.rule.import.workshop.not.exists=the {0} row {1} workshop does not exist
a.materiel.match.rule.import.cellstype.not.exists=the {0} row {1} cellsType does not exist
a.materiel.match.rule.import.actualpiecethickness.not.exists=the {0} row {1} actualPieceThickness does not exist
a.materiel.match.rule.import.maingridbothshape.not.exists=the {0} row {1} mainGridBothShape does not exist
a.materiel.match.rule.import.aItemCode.not.exists=the {0} row {1} aItemCode does not exist
special.cell.match.rule.import.row.not.null=the {0} row each attribute (cellsType, commonCellsType) cannot have empty values
special.cell.match.rule.groupKey.repeat=the {0} row each attribute (cellsType, regionalCountry, hTrace, cellSource, aesthetics, transparentDoubleGlass, productionGrade, commonCellsType) repeat
special.cell.match.rule.cellstype.not.exists=the {0} row {1} cellsType does not exist
special.cell.match.rule.regionalcountry.not.exists=the {0} row {1} regionalCountry does not exist
special.cell.match.rule.h_trace.not.exists=the {0} row {1} hTrace does not exist
special.cell.match.rule.cellsource.not.exists=the {0} row {1} cellSource does not exist
special.cell.match.rule.aesthetics.not.exists=the {0} row {1} aesthetics does not exist
special.cell.match.rule.transparent_double_glass.not.exists=the {0} row {1} transparentDoubleGlass does not exist
special.cell.match.rule.productiongrade.not.exists=the {0} row {1} productionGrade does not exist
special.cell.match.rule.commoncellstype.not.exists=the {0} row {1} commonCellsType does not exist