bbom_importValidError=导入数据校验失败
bbom_feign_findBasePlaceWorkshopWorkUnit_error=电池类型动态属性-网版，调用Feign-findBasePlaceWorkshopWorkUnit接口失败
bbom_feign_findItemDescByItemCodes_error=电池类型动态属性-网版，调用Feign-findItemDescByItemCodes接口失败
bbom_lineNumber=第 {0} 行
bbom_valid_BatteryProduct_unique=电池产品编码+电池片晶体类型+产品类型+电池片尺寸编码+主栅数+分片数唯一性校验，不允许重复
bbom_valid_batteryCode_notBlank=电池类型编码不能为空
bbom_valid_batteryCrystalType_notBlank=电池片晶体类型不能为空
bbom_valid_batteryDimensionCode_notBlank=电池片尺寸编码不能为空
bbom_valid_NumberMainGrids_notBlank=主栅数不能为空
bbom_valid_productType_notBlank=产品类型不能为空
bbom_valid_shardingNumber_notBlank=分片数不能为空
bbom_valid_switchType_notBlank=切换类型不能为空
bbom_valid_switchType_illegal=切换类型不合法
bbom_valid_switchType_notMatch=切换类型未能匹配
bbom_valid_basePlace_notBlank=基地不能为空
bbom_valid_workshop_notBlank=车间不能为空
bbom_valid_workbench_notBlank=机台不能为空
bbom_valid_batteryName_notBlank=电池类型名称不能为空
bbom_valid_itemCodeNew_notBlank=物料料号-新不能为空
bbom_valid_screenNumberNew_notBlank=新网板消耗数量不能为空
bbom_valid_itemCodeOld_notBlank=物料料号-旧不能为空
bbom_valid_screenNumberOld_notBlank=旧网板消耗数量不能为空
bbom_valid_line_notBlank=线体数量不能为空
bbom_valid_workunit_notBlank=生产单元不能为空
bbom_valid_vendorName_notBlank=供应商名称不能为空
bbom_valid_workshopNotOrg=车间 {0} 未维护库存组织
bbom_valid_screenPlateIsExistBom=网版料号-新是否存在BOM中为否时不允许保存
bbom_valid_screenPlateNewExistBom_save={0} _ {1} _ {2} : 网版料号 - 新是否存在BOM中为否时不允许新增
bbom_valid_basePlaceWorkshop_illegal={0} {1} : 基地、车间不合法
bbom_valid_screenPlate_unique=网版料号-新+网版料号-旧+电池类型+机台+生产基地+生产车间+生产单元+线体数量唯一性校验，不允许重复
bbom_valid_screenPlateNewExistBom_import={0} _ {1} _ {2} : 网版料号 - 新是否存在BOM中为否时不允许导入
bbom_valid_vendorName_nonExist={0} 供应商名称不存在
bbom_valid_totalLine=线体数量必须小于等于APS接口中线体数
bbom_valid_workbench_illegal={0} : 机台不合法
bbom_valid_batteryCode_illegal={0}:电池类型名称不在电池类型静态属性当中
bbom_valid_effectiveStartDate_format_illegal=有效期起: {0} 日期格式不为yyyy-MM-dd
bbom_valid_effectiveEndDate_format_illegal=有效期止: {0} 日期格式不为yyyy-MM-dd
bbom_valid_siliconWaferProperties_notBlank=硅片属性不能为空
bbom_valid_conditionItem_notBlank=条件项不能为空
bbom_valid_value_notBlank=值不能为空
bbom_valid_oldConditionItem_notBlank=旧条件项不能为空
bbom_valid_oldValue_notBlank=旧值不能为空
bbom_valid_batterySiliconWafer_unique=电池类型+生产基地+生产车间+生产单元+硅片属性唯一性校验，不允许重复
bbom_valid_batteryName_illegal=电池类型不存在 {0}
bbom_valid_newSiliconWaferProperties_illegal=新硅片属性不存在
bbom_valid_newConditionItem_notBlank=新条件项不存在
bbom_valid_oldSiliconWaferProperties_illegal=旧硅片属性不存在
bbom_valid_line_new_not_blank=线体数量-新不能为空
bbom_valid_YfAndQty_notBlank=维护类型为研发时数量字段不能为空
bbom_valid_totalLineQty_illegal=线体数量-旧+线体数量-新的总和大于已维护的线体数量
bbom_valid_BatterySlurry_unique=电池类型+生产基地+生产车间+物料编码-新+机台+有效日期_起+有效日期 唯一性校验，不允许重复
bbom_valid_importData_repeat=导入数据存在重复数据
bbom_valid_alternateDesignator_notFound=未找到替代料信息 orgId: {0} designator: {0}
bbom_valid_batteryMWQty_notBlank=电池MW系数不能为空
bbom_valid_batteryMWQty_notZero=电池MW系数不能为:0
bbom_valid_batteryEfficiencyQty_notBlank=电池目标良率不能为空
bbom_valid_batteryEfficiencyQty_notZero=电池目标良率不能为:0
bbom_valid_lifetime_illegal=寿命（万）不能为空
bbom_valid_DividingByZero=除数不能为0
bbom_valid_unitConsumption_notNumber=单耗不是数字类型
bbom_valid_unitConsumption_notZero=单耗不能为：0
bbom_valid_itemCode_notExist=物料编码 {0} 不存在
bbom_valid_mainGridSpacingRule_repeat=电池类型和组件车间已存在或有效日期重叠
bbom_matchItem_notExist=属性匹配物料失败,请检查物料是否存在
bbom_matchItem_screenPlateAttr_notConfig=网版属性未配置
bbom_workshopAndOrgNotFoundDesignator={0} :车间对应库存组织未查询到bom替代项,请检查
bbom_notFound_cellRuleClassification=找不到品类规则分类的LovLine
bbom_notFound_SpecialArea=找不到特殊区域规则的LovLine
bbom_notFound_organization_workshop=车间 {0} 未维护库存组织
bbom_hasRuleLIne_canNotDel=存在有效的规则行，不允许删除
bbom_only_default=只能有一个推荐
bbom_batteryType_notBlank=电池类型不能为空
bbom_basePlace_notBlank=生产基地不能为空
bbom_workshop_notBlank=生产车间不能为空
bbom_valid_itemCode_notBlank=网版料号不能为空
bbom_valid_itemCode_repeat=网版料号：{0} ，电池类型+网版料号+生产基地+生产车间+机台+主栅信息+主栅间距+单玻的数据组合不允许重复，导入失败
bbom_valid_screenLife_repeat=电池类型+网版料号+生产基地+生产车间+机台+主栅信息+主栅间距+单玻的数据组合不允许重复
bbom_valid_itemCode_illegal=无效的网版料号：{0}
bbom_valid_SiliconWaferConditionalItem_notBlank=硅片属性条件项不能为空
bbom_valid_SiliconWaferValue_notBlank=硅片属性值不能为空
bbom_valid_batteryProperties_notBlank=电池属性不能为空
bbom_valid_BatteryConditionalItem_notBlank=电池属性条件项不能为空
bbom_valid_BatteryValue_notBlank=电池属性值不能为空
bbom_valid_SiliconWaferProperties_illegal=硅片属性不合法
bbom_valid_BatteryProperties_illegal=电池属性不合法
bbom_valid_SiliconIsolationRate_notBlank=硅片隔离率不能为空
bbom_valid_SiliconWaferLoss_repeat=电池类型+生产基地+生产车间为唯一性校验，不允许重复
bbom_valid_siliconWaferYield_gtZero=电池良率必须大于0
bbom_valid_siliconIsolationRate_gtZero=硅片隔离率必须大于0
bbom_valid_SlurryInformation_repeat={0} _ {1} _ {2} _ {3} :电池类型+生产基地+生产车间+机台唯一性校验，不允许重复
bbom_valid_organization_notBlank=组织不能为空
bbom_valid_effectiveDate_error=有效日期校验错误
bbom_valid_itemCodeNew_illegal=新硅片料号{0}不存在
a.materiel.match.rule.import.row.not.null=第{0}行各属性(国内/海外、电池车间、电池类型、A-料号)不能存在空值
a.materiel.match.rule.import.groupKey.repeat=第{0}行各属性(国内/海外、电池车间、电池类型、实际片厚、主栅两端形状、A-料号)重复
a.materiel.match.rule.import.isoversea.not.exists=第{0}行{1}国内/海外名称不存在
a.materiel.match.rule.import.workshop.not.exists=第{0}行{1}电池车间不存在
a.materiel.match.rule.import.cellstype.not.exists=第{0}行{1}电池类型不存在
a.materiel.match.rule.import.actualpiecethickness.not.exists=第{0}行{1}实际片厚不存在
a.materiel.match.rule.import.maingridbothshape.not.exists=第{0}行{1}主栅两端形状不存在
a.materiel.match.rule.import.aItemCode.not.exists=第{0}行{1}A-料号不存在
special.cell.match.rule.import.row.not.null=第{0}行各属性(电池类型、常规电池类型)不能存在空值
special.cell.match.rule.groupKey.repeat=第{0}行各属性(电池类型、小区域国家、H追溯、片源种类、美学、透明双玻、产品等级、常规电池类型)重复
special.cell.match.rule.cellstype.not.exists=第{0}行{1}电池类型不存在
special.cell.match.rule.regionalcountry.not.exists=第{0}行{1}小区域国家不存在
special.cell.match.rule.h_trace.not.exists=第{0}行{1}H追溯不存在
special.cell.match.rule.cellsource.not.exists=第{0}行{1}片源种类不存在
special.cell.match.rule.aesthetics.not.exists=第{0}行{1}美学不存在
special.cell.match.rule.transparent_double_glass.not.exists=第{0}行{1}透明双玻不存在
special.cell.match.rule.productiongrade.not.exists=第{0}行{1}产品等级不存在
special.cell.match.rule.commoncellstype.not.exists=第{0}行{1}常规电池类型不存在