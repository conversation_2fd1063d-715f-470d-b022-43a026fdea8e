package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectDetailDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectDetail;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectDetailQuery;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectDetailService;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 规则管控对象详情 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:21
 */
@RestController
@RequestMapping("/rule-control-object-detail")
@Api(value = "rule-control-object-detail", tags = "规则管控对象详情操作")
public class RuleControlObjectDetailController {
    @Autowired
    private RuleControlObjectDetailService ruleControlObjectDetailService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "规则管控对象详情分页列表", notes = "获得规则管控对象详情分页列表")
    public ResponseEntity<Results<Page<RuleControlObjectDetail>>> queryByPage(
            @RequestBody RuleControlObjectDetailQuery query) {
        return Results.createSuccessRes(ruleControlObjectDetailService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<RuleControlObjectDetailDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                ruleControlObjectDetailService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

}
