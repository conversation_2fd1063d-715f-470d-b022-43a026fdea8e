package com.trinasolar.scp.bbom.api.controller;


import com.trinasolar.scp.bbom.domain.dto.LowEfficiencyCellPercentDTO;
import com.trinasolar.scp.bbom.domain.query.LowEfficiencyCellPercentQuery;
import com.trinasolar.scp.bbom.domain.validation.group.DefaultGroup;
import com.trinasolar.scp.bbom.domain.validation.group.UpdateGroup;
import com.trinasolar.scp.bbom.service.service.LowEfficiencyCellPercentService;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/low-efficiency-cell-percent")
@Api(value = "/low-efficiency-cell-percent", tags = "自产低效电池比例")
public class LowEfficiencyCellPercentController {

    @Resource
    private LowEfficiencyCellPercentService lowEfficiencyCellPercentService;


    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping("/import")
    public ResponseEntity<Results<Object>> importFromExcel(@RequestPart("file") MultipartFile file) {
        lowEfficiencyCellPercentService.importFromExcel(file);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping("/export")
    public void export(@RequestBody LowEfficiencyCellPercentQuery query, HttpServletResponse response) {
        lowEfficiencyCellPercentService.exportToExcel(query, response);
    }


    @ApiOperation(value = "分页列表", notes = "分页列表")
    @PostMapping("/page")
    public ResponseEntity<Results<Page<LowEfficiencyCellPercentDTO>>> page(@RequestBody LowEfficiencyCellPercentQuery query) {
        return Results.createSuccessRes(lowEfficiencyCellPercentService.queryByPage(query));
    }


    @ApiOperation(value = "更新", notes = "更新")
    @PostMapping("/update")
    public ResponseEntity<Results<Object>> update(@Validated(value = {UpdateGroup.class, DefaultGroup.class}) @RequestBody LowEfficiencyCellPercentDTO dto) {
        lowEfficiencyCellPercentService.update(dto);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "批量逻辑删除", notes = "批量逻辑删除")
    @PostMapping("/delete")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        List<Long> ids = idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList());
        lowEfficiencyCellPercentService.batchDelete(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/list")
    public ResponseEntity<Results<List<LowEfficiencyCellPercentDTO>>> list(@RequestBody LowEfficiencyCellPercentQuery query) {
        return Results.createSuccessRes(lowEfficiencyCellPercentService.list(query));
    }
}
