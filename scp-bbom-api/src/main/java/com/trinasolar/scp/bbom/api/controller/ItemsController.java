package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.ItemsDTO;
import com.trinasolar.scp.bbom.domain.dto.feign.ExternalItemsDTO;
import com.trinasolar.scp.bbom.domain.query.BatchItemQuery;
import com.trinasolar.scp.bbom.domain.query.ItemSourceIdQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsNewQuery;
import com.trinasolar.scp.bbom.domain.query.ItemsQuery;
import com.trinasolar.scp.bbom.domain.save.ItemsSaveDTO;
import com.trinasolar.scp.bbom.service.service.ItemsService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物料基础数据表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:29:24
 */
@RestController
@RequestMapping("/items")
@RequiredArgsConstructor
@Api(value = "items", tags = "物料基础数据表操作")
@Slf4j
public class ItemsController {
    private final ItemsService itemsService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "物料基础数据表分页列表", notes = "获得物料基础数据表分页列表")
    public ResponseEntity<Results<Page<ItemsDTO>>> queryByPage(@RequestBody ItemsQuery query) {
        return Results.createSuccessRes(itemsService.queryByPage(query));
    }

    /**
     * 查询物料基础数据-前端使用
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryByItemCode")
    @ApiOperation(value = "查询物料基础数据-前端使用", notes = "查询物料基础数据-前端使用")
    public ResponseEntity<Results<Page<ItemsDTO>>> queryByItemCode(@RequestBody ItemsQuery query) {
        return Results.createSuccessRes(itemsService.queryByItemCode(query));
    }

    /**
     * 查询物料基础数据-外部接口
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryByItemCodeAll")
    @ApiOperation(value = "查询物料基础数据-外部接口", notes = "查询物料基础数据-外部接口")
    public ResponseEntity<Results<Map<String, ItemsDTO>>> queryByItemCodeAll(@RequestBody ItemsNewQuery query) {
        return Results.createSuccessRes(itemsService.queryByItemCodeAll(query));
    }

    @PostMapping("/queryByItemCodeAllNew")
    @ApiOperation(value = "查询物料基础数据-外部接口", notes = "查询物料基础数据-外部接口")
    public ResponseEntity<Results<Map<String, ItemsDTO>>> queryByItemCodeAllNew(@RequestBody ItemsNewQuery query) {
        return Results.createSuccessRes(itemsService.queryByItemCodeAllNew(query));
    }

    /**
     * 查询物料基础数据-外部接口
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryBySourceItemCode")
    @ApiOperation(value = "查询物料基础数据-外部接口", notes = "查询物料基础数据-外部接口")
    public ResponseEntity<Results<Map<Long, ItemsDTO>>> queryBySourceItemCode(@RequestBody ItemSourceIdQuery query) {
        return Results.createSuccessRes(itemsService.queryBySourceItemCode(query.getSourceItemId(), query.getCategorySegment4()));
    }


    /**
     * 通过7A料号查询相关的5A料号
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryStructureItemByComponentItem")
    @ApiOperation(value = "通过7A料号查询相关的5A料号-外部接口", notes = "通过7A料号查询相关的5A料号-外部接口")
    public ResponseEntity<Results<Map<String, List<ItemsDTO>>>> queryStructureItemByComponentItem(@RequestBody BatchItemQuery query) {
        return Results.createSuccessRes(itemsService.queryStructureItemByComponentItem(query));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ItemsDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(itemsService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ItemsDTO>> save(@Valid @RequestBody ItemsSaveDTO saveDTO) {
        return Results.createSuccessRes(itemsService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        itemsService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ItemsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        itemsService.export(query, response);
    }

    @PostMapping("/importByExternalItems")
    @ApiOperation(value = "通过external items 导入")
    public ResponseEntity<Results<Object>> importByExternalItems(@RequestBody ExternalItemsDTO externalItemsDTO) {
        itemsService.importByExternalItems(externalItemsDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/getCellsTypeBy7AItemCodes")
    @ApiOperation(value = "根据7A去找5A再找电池类型")
    public ResponseEntity<Results<List<String>>> getCellsTypeBy7AItemCodes(@RequestBody List<String> itemCodes) {
        return Results.createSuccessRes(itemsService.getCellsTypeBy7AItemCodes(itemCodes));
    }

    /**
     * 查询物料信息
     *
     * @param itemsDTO
     */
    @PostMapping("/findOneByItemCode")
    @ApiOperation(value = "查询物料信息")
    public ResponseEntity<Results<ItemsDTO>> findOneByItemCode(@RequestBody ItemsDTO itemsDTO) {
        return Results.createSuccessRes(itemsService.findOneByItemCode(itemsDTO.getItemCode()));
    }

    /**
     * 查询物料信息
     *
     * @param idDTO
     */
    @PostMapping("/findOneByItemId")
    @ApiOperation(value = "查询物料信息")
    public ResponseEntity<Results<ItemsDTO>> findOneByItemId(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(itemsService.findOneByItemId(Long.parseLong(idDTO.getId())));
    }

    /**
     * 查询返工的5A物料
     *
     * @param itemsDTO
     */
    @PostMapping("/findReworkItemCode")
    @ApiOperation(value = "查询物料信息")
    public ResponseEntity<Results<List<ItemsDTO>>> findReworkItemCode(@RequestBody ItemsDTO itemsDTO) {
        return Results.createSuccessRes(itemsService.findReworkItemCode(itemsDTO));
    }

    @PostMapping("/itemLifecycleStateInitSync")
    @ApiOperation(value = "同步生命周期")
    public ResponseEntity<Results<Object>> itemLifecycleStateInitSync() {
        LocalDate now = LocalDate.now();
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        while (startDate.isBefore(now)) {
            itemsService.ItemLifecycleStateSync(startDate);
            startDate = startDate.plusDays(1);
        }
        return Results.createSuccessRes();
    }

    @PostMapping("/findLifecycleStateByItems")
    @ApiOperation(value = "根据料号查询生命周期")
    public ResponseEntity<Results<Map<String, String>>> findLifecycleStateByItems(@RequestBody ItemsQuery query) {
        return Results.createSuccessRes(itemsService.findLifecycleStateByItems(query.getItemCodes()));
    }

    /**
     * 查询物料项详情依据料号和产品等级属性、库存组织
     *
     * @param query
     */
    @PostMapping("/findItemsForBaps")
    @ApiOperation(value = "查询物料信息给baps")
    public ResponseEntity<Results<List<ItemsDTO>>> findReworkItemCode(@RequestBody ItemsQuery query) {
        return Results.createSuccessRes(itemsService.findItemsByItemCodeAndProductGrades(query.getItemCodes(),query.getProductGrades(),query.getOrganizationId()));
    }

    @PostMapping("/getItemsDTOMapBySourceItemIds")
    @ApiOperation(value = "根据itemIds查询物料信息")
    public ResponseEntity<Results<Map<Long, ItemsDTO>>> getItemsDTOMapBySourceItemIds(@RequestBody List<Long> sourceItemIds) {
        return Results.createSuccessRes(itemsService.getItemsDTOMapBySourceItemIds(sourceItemIds));
    }
}
