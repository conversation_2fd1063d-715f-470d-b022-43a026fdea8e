package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.SiliconWaferLossDTO;
import com.trinasolar.scp.bbom.domain.query.SiliconWaferLossQuery;
import com.trinasolar.scp.bbom.domain.save.SiliconWaferLossSaveDTO;
import com.trinasolar.scp.bbom.service.service.SiliconWaferLossService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 硅片损耗率信息维护 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@RestController
@RequestMapping("/silicon-wafer-loss")
@RequiredArgsConstructor
@Slf4j
@Api(value = "silicon-wafer-loss", tags = "硅片损耗率信息维护操作")
public class SiliconWaferLossController {
    private final SiliconWaferLossService siliconWaferLossService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "硅片损耗率信息维护分页列表", notes = "获得硅片损耗率信息维护分页列表")
    public ResponseEntity<Results<Page<SiliconWaferLossDTO>>> queryByPage(@RequestBody SiliconWaferLossQuery query) {
        return Results.createSuccessRes(siliconWaferLossService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SiliconWaferLossDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(siliconWaferLossService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SiliconWaferLossDTO>> save(@Valid @RequestBody SiliconWaferLossSaveDTO saveDTO) {
        return Results.createSuccessRes(siliconWaferLossService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        siliconWaferLossService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SiliconWaferLossQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        siliconWaferLossService.export(query, response);
    }

    @PostMapping("/queryByPageExport")
    @ApiOperation(value = "导出模版")
    public void queryByPageExport(@RequestBody SiliconWaferLossQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        siliconWaferLossService.queryByPageExport(query, response);
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public void importsEntity(@RequestParam("file") MultipartFile file) {
        siliconWaferLossService.importsEntity(file);
    }
}
