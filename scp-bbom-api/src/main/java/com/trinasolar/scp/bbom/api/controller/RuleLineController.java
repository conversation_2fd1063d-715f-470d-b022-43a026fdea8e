package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.RuleLineDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleLineInterfaceDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleLine;
import com.trinasolar.scp.bbom.domain.event.CollocationRulesEvent;
import com.trinasolar.scp.bbom.domain.query.RuleLineQuery;
import com.trinasolar.scp.bbom.domain.save.RuleLineSaveDTO;
import com.trinasolar.scp.bbom.domain.save.RuleLinesSaveDTO;
import com.trinasolar.scp.bbom.service.service.RuleLineService;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * BOM规则行表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-11 09:15:32
 */
@RestController
@RequestMapping("/rule-line")
@Api(value = "rule-line", tags = "BOM规则行表操作")
public class RuleLineController {
    @Autowired
    ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private RuleLineService ruleLineService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "BOM规则行表分页列表", notes = "获得BOM规则行表分页列表")
    public ResponseEntity<Results<Page<RuleLine>>> queryByPage(
            @RequestBody RuleLineQuery query) {
        return Results.createSuccessRes(ruleLineService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<RuleLineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                ruleLineService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<RuleLineDTO>> save(@Valid @RequestBody RuleLineSaveDTO saveDTO) {
        return Results.createSuccessRes(
                ruleLineService.save(saveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除数据")
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        RuleLine ruleLine = ruleLineService.findRuleLineByLinesId(Long.parseLong(idDTO.getId()));
        ruleLineService.deleteById(Long.parseLong(idDTO.getId()));
        if (Objects.nonNull(ruleLine)) {
            applicationEventPublisher.publishEvent(new CollocationRulesEvent(ruleLine));
        }
        return Results.createSuccessRes();
    }

    /**
     * 保存行列表
     *
     * @param ruleLinesSaveDTO save实体
     * @return 新增结果
     */
    @PostMapping("/saveLines")
    @ApiOperation(value = "保存行列表")
    public ResponseEntity<Results<Object>> saveLines(@Valid @RequestBody RuleLinesSaveDTO ruleLinesSaveDTO) {
        ruleLineService.saveLines(ruleLinesSaveDTO);
        return Results.createSuccessRes();
    }


    /**
     * 查询行列表
     *
     * @param idDTO headerid
     * @return 行列表
     */
    @PostMapping("/listLinesByHeaderId")
    @ApiOperation(value = "查询行列表，使用的是HeaderId")
    public ResponseEntity<Results<List<RuleLineDTO>>> listLinesByHeaderId(@Valid @RequestBody IdDTO idDTO, @RequestBody RuleLineQuery query) {
        if (StringUtils.isNotEmpty(query.getItemCode())) {
            return Results.createSuccessRes(ruleLineService.listByRuleHeaderIdAndItemCode(Long.parseLong(idDTO.getId()), query.getItemCode()));
        } else {
            return Results.createSuccessRes(ruleLineService.listLinesByHeaderId(Long.parseLong(idDTO.getId())));
        }

    }

    @PostMapping("/genRuleLineNo")
    @ApiOperation(value = "生成认证行no")
    public ResponseEntity<Results<Object>> genRuleLineNo() {
        ruleLineService.genRuleLineNo();
        return Results.createSuccessRes();
    }

    /**
     * 查询行列表
     *
     * @param itemCodes
     * @return 行列表
     */
    @PostMapping("/queryRuleLinsByItemCode")
    @ApiOperation(value = "查询行列表，使用的是HeaderId")
    public ResponseEntity<Results<List<RuleLineInterfaceDTO>>> queryRuleLins(@Valid @RequestBody List<String> itemCodes) {
        return Results.createSuccessRes(ruleLineService.queryRuleLins(itemCodes));
    }

    @PostMapping("/queryCellRestrictedByItemCode")
    @ApiOperation(value = "查询行列表，使用的是HeaderId")
    public ResponseEntity<Results<Map<String, List<RuleLineDTO>>>> queryCellRestrictedByItemCode(@RequestBody List<String> itemCodes) {
        Map<String, List<RuleLineDTO>> result = ruleLineService.queryCellRestrictedByItemCode(itemCodes);
        return Results.createSuccessRes(result);
    }
}
