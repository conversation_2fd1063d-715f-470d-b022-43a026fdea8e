package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineDTO;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchLineQuery;
import com.trinasolar.scp.bbom.domain.query.ScreenMainReplacementQuery;
import com.trinasolar.scp.bbom.domain.query.SlurryMainReplacementQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineAppointItemSaveDTO;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineSaveDTO;
import com.trinasolar.scp.bbom.domain.vo.ScreenMainReplacementVO;
import com.trinasolar.scp.bbom.domain.vo.SlurryMainReplacementVO;
import com.trinasolar.scp.bbom.service.service.MaterielMatchLineService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池料号匹配明细行 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@RestController
@RequestMapping("/materiel-match-line")
@RequiredArgsConstructor
@Api(value = "materiel-match-line", tags = "电池料号匹配明细行操作")
public class MaterielMatchLineController {
    private final MaterielMatchLineService materielMatchLineService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池料号匹配明细行分页列表", notes = "获得电池料号匹配明细行分页列表")
    public ResponseEntity<Results<Page<MaterielMatchLineDTO>>> queryByPage(@RequestBody MaterielMatchLineQuery query) {
        return Results.createSuccessRes(materielMatchLineService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MaterielMatchLineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(materielMatchLineService.queryById(Long.parseLong(idDTO.getId())));
    }


    @PostMapping("/appointItemCode")
    @ApiOperation(value = "批量指定料号")
    public ResponseEntity<Results<Object>> appointItemCode(@RequestBody List<MaterielMatchLineAppointItemSaveDTO> saveDTOs) {
        materielMatchLineService.appointItemCode(saveDTOs);
        return Results.createSuccessRes();
    }

    @PostMapping("/clearItemCode")
    @ApiOperation(value = "批量清除料号")
    public ResponseEntity<Results<Object>> clearItemCode(@RequestBody IdsDTO idsDTO) {
        materielMatchLineService.clearItemCode(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }


    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<MaterielMatchLineDTO>> save(@Valid @RequestBody MaterielMatchLineSaveDTO saveDTO) {
        return Results.createSuccessRes(materielMatchLineService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        materielMatchLineService.deleteMatchStatusByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MaterielMatchLineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        materielMatchLineService.export(query, response);
    }

    /**
     * 查询电池料号匹配明细
     */
    @PostMapping("/queryMaterielMatchLineList")
    @ApiOperation(value = "查询最新版本的数据-isDelete等于0")
    public ResponseEntity<Results<List<MaterielMatchLineDTO>>> queryMaterielMatchLineList() {
        return Results.createSuccessRes(materielMatchLineService.queryMaterielMatchLineList());
    }

    /**
     * 查询电池料号匹配明细
     */
    @PostMapping("/queryMatchLineByHeaderId")
    @ApiOperation(value = "电池物料行信息查询-isDelete等于0")
    public ResponseEntity<Results<List<MaterielMatchLineDTO>>> queryMatchLineByHeaderId(@RequestBody MaterielMatchHeaderDTO headerDTO) {
        return Results.createSuccessRes(materielMatchLineService.queryMatchLineByHeaderId(headerDTO));
    }

    @PostMapping("/deleteByHeadId")
    @ApiOperation(value = "电池物料信息删除", notes = "电池物料信息删除")
    public void deleteByHeadId(@RequestBody List<MaterielMatchHeaderDTO> headerDTOList) {
        materielMatchLineService.deleteByHeadId(headerDTOList);
    }

    @PostMapping("/flushItemDesc")
    @ApiOperation(value = "刷新物料描述")
    public void flushItemDesc() {
        materielMatchLineService.flushItemDesc();
    }

    /**
     * 浆料主替页面
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/slurryMainReplacementPage")
    @ApiOperation(value = "浆料主替信息维护", notes = "浆料主替信息维护")
    public ResponseEntity<Results<Page<SlurryMainReplacementVO>>> slurryMainReplacementPage(@RequestBody SlurryMainReplacementQuery query) {
        return Results.createSuccessRes(materielMatchLineService.slurryMainReplacementPage(query));
    }

    @PostMapping("/slurryMainReplacementPage/export")
    @ApiOperation(value = "浆料主替信息维护导出")
    public void slurryMainReplacementPageExport(@RequestBody SlurryMainReplacementQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        materielMatchLineService.slurryMainReplacementPageExport(query, response);
    }


    /**
     * 导入
     */
    @PostMapping("/slurryMainReplacementPage/import")
    @ApiOperation(value = "浆料主替信息维护导入")
    public void slurryMainReplacementPageImport(@RequestParam("file") MultipartFile file) {
        materielMatchLineService.importSlurryMainReplacementPageEntity(file);
    }

    /**
     * 查询计划电池料号匹配明细:取type为plan的投产计划头再取行
     */
    @PostMapping("/queryPlanMaterielMatchLineList")
    @ApiOperation(value = "查询计划电池料号匹配明细:取type为plan的投产计划头再取行")
    public ResponseEntity<Results<List<MaterielMatchLineDTO>>> queryPlanMaterielMatchLineList() {
        return Results.createSuccessRes(materielMatchLineService.queryPlanMaterielMatchLineList());
    }


    /**
     * 网版主替分页列表
     */
    @PostMapping("/screen/mainReplacement/page")
    @ApiOperation(value = "网版主替分页列表", notes = "网版主替分页列表")
    public ResponseEntity<Results<Page<ScreenMainReplacementVO>>> screenMainReplacementPage(@RequestBody ScreenMainReplacementQuery query) {
        return Results.createSuccessRes(materielMatchLineService.screenMainReplacementPage(query));
    }

    /**
     * 网版主替导出
     */
    @PostMapping("/screen/mainReplacement/export")
    @ApiOperation(value = "网版主替导出")
    public void screenMainReplacementExport(@RequestBody ScreenMainReplacementQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        materielMatchLineService.exportScreenMainReplacement(query, response);
    }


    /**
     *  网版主替导入
     */
    @PostMapping("/screen/mainReplacement/import")
    @ApiOperation(value = "网版主替导出导入")
    public void screenMainReplacementImport(@RequestParam("file") MultipartFile file) {
        materielMatchLineService.importScreenMainReplacement(file);
    }
}
