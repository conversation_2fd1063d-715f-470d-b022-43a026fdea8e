package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.service.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Function: SubstituteController.java
 * @Description: 通过库存组织+装配件+替代组+物料编码，获取该物料编码下的替代料信息；
 * @version: v1.0.0
 * @author: niuweiwei
 * @date: 2024年1月3日 15:07:37
 */
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Api(value = "TestController", tags = "测试")
public class TestController {
    private final BatteryTypeJobService batteryTypeJobService;
    @Autowired
    ItemsService itemsService;

    @Autowired
    BatteryScreenPlateService screenPlateService;
    @Autowired
    MaterielMatchLineService matchLineService;
    @Autowired
    MaterielMatchHeaderService materielMatchHeaderService;
    @Autowired
    ConversionCoefficientMwService conversionCoefficientMwService;
    @Autowired
    private BatterySlurryService batterySlurryService;
    @Autowired
    private ScreenLifeService screenLifeService;

    /**
     * 定时任务-同步电池类型编码名称到lov
     */
    @PostMapping("/getInfo")
    @ApiOperation(value = "定时任务-同步电池类型编码名称到lov")
    public void batteryTypeJobHandler() {
        batteryTypeJobService.batteryTypeJobHandler();
    }

    /**
     * 定时任务-电池类型静态属性value字段更新物料表sigment60
     */
    @PostMapping("/getInfo2")
    @ApiOperation(value = "定时任务-电池类型静态属性value字段更新物料表sigment60")
    public void batteryUpdateItmesJobHandler() {
        batteryTypeJobService.batteryUpdateItmesJobHandler();
    }

    /**
     * 定时任务-电池类型静态属性主表写数据
     */
    @PostMapping("/getInfo4")
    @ApiOperation(value = "定时任务-电池类型静态属性主表写数据")
    public void batteryJobHandler() {
        batteryTypeJobService.batteryJobHandler();
    }

    /**************************************邮件推送****************************************************/
    /**
     * 每周一定时推送
     * 推送三个月数据 以当前时间  根据网版的开始时间获取数据
     * 定时任务-网版切换邮件推送
     */
    @PostMapping("/getInfo5")
    @ApiOperation(value = "定时任务-网版切换邮件推送")
    public void screenPlateSendMail() {
        screenPlateService.queryListBySendMail();
    }


    /**
     * 每周一定时推送
     * 推送三个月数据 以当前时间  根据网版的开始时间获取数据
     * 定时任务-浆料切换邮件推送
     */
    @PostMapping("/getInfo6")
    @ApiOperation(value = "定时任务-浆料切换邮件推送")
    public void batterySlurrySendMail() {
        batterySlurryService.queryListBySendMail();
    }

    /**
     * 每周一定时推送
     * 推送三个月数据 以当前时间  根据网版寿命维护的开始时间获取数据
     * 定时任务-网版寿命维护邮件推送
     */
    @PostMapping("/getInfo7")
    @ApiOperation(value = "定时任务-网版寿命维护邮件推送")
    public void screenLifeSendMail() {
        screenLifeService.queryListBySendMail();
    }

    /**
     * 每天定时推送
     * 定时任务-料号匹配邮件推送
     */
    @PostMapping("/getInfo8")
    @ApiOperation(value = "定时任务-料号匹配邮件推送")
    public void sendMailByqueryList() {
        matchLineService.sendMailByqueryList();
    }

    /**
     * 测试推送料号匹配
     * 每天晚上11点执行定时任务
     */
    @PostMapping("/getInfo9")
    @ApiOperation(value = "定时任务-兆瓦系数计算job")
    public void getInfo9() {
        conversionCoefficientMwService.computeSaveWithJob();
    }
}
