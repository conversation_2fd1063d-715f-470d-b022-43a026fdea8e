package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.RuleControlObjectValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleControlObjectValue;
import com.trinasolar.scp.bbom.domain.query.RuleControlObjectValueQuery;
import com.trinasolar.scp.bbom.service.service.RuleControlObjectValueService;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 规则管控对象值 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-18 11:30:04
 */
@RestController
@RequestMapping("/rule-control-object-value")
@Api(value = "rule-control-object-value", tags = "规则管控对象值操作")
public class RuleControlObjectValueController {
    @Autowired
    private RuleControlObjectValueService ruleControlObjectValueService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "规则管控对象值分页列表", notes = "获得规则管控对象值分页列表")
    public ResponseEntity<Results<Page<RuleControlObjectValue>>> queryByPage(
            @RequestBody RuleControlObjectValueQuery query) {
        return Results.createSuccessRes(ruleControlObjectValueService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<RuleControlObjectValueDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                ruleControlObjectValueService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

}
