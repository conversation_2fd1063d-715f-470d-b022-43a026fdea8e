package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.BatteryProductImportDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryProductImportQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryProductImportSaveDTO;
import com.trinasolar.scp.bbom.service.service.BatteryProductImportService;
import com.trinasolar.scp.bbom.service.service.BatteryTypeJobService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 电池产品导入表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@RestController
@RequestMapping("/battery-product-import")
@RequiredArgsConstructor
@Api(value = "battery-product-import", tags = "电池产品导入表操作")
public class BatteryProductImportController {
    private final BatteryProductImportService batteryProductImportService;

    private final BatteryTypeJobService batteryTypeJobService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池产品导入表分页列表", notes = "获得电池产品导入表分页列表")
    public ResponseEntity<Results<Page<BatteryProductImportDTO>>> queryByPage(@RequestBody BatteryProductImportQuery query) {
        return Results.createSuccessRes(batteryProductImportService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatteryProductImportDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batteryProductImportService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatteryProductImportDTO>> save(@Valid @RequestBody BatteryProductImportSaveDTO saveDTO) {
        return Results.createSuccessRes(batteryProductImportService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batteryProductImportService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryProductImportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batteryProductImportService.export(query, response);
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public void importsEntity(@RequestParam("file") MultipartFile file) {
        batteryProductImportService.importsEntity(file);
    }

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    @PostMapping("/queryByPageExport")
    @ApiOperation(value = "导出模版")
    public void queryByPageExport(@RequestBody BatteryProductImportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batteryProductImportService.queryByPageExport(query, response);
    }

    /**
     * 电池类型静态属性主表写数据
     */
    @PostMapping("/batteryAutoHandler")
    @ApiOperation(value = "电池类型静态属性主表写数据")
    public ResponseEntity<Results<Object>> batteryAutoHandler() {
        batteryTypeJobService.batteryJobHandler();
        return Results.createSuccessRes();
    }
}
