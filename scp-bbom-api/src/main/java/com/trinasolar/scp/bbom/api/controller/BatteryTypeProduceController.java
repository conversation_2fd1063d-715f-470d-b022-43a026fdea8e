package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.BatteryTypeProduceDTO;
import com.trinasolar.scp.bbom.domain.dto.ErpAlternateDesignatorDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeProduceNewQuery;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeProduceQuery;
import com.trinasolar.scp.bbom.domain.query.ErpAlternateDesignatorQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryTypeProduceSaveDTO;
import com.trinasolar.scp.bbom.service.service.BatteryTypeProduceService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-产出电池类型 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@RestController
@RequestMapping("/battery-type-produce")
@RequiredArgsConstructor
@Api(value = "battery-type-produce", tags = "电池类型动态属性-产出电池类型操作")
public class BatteryTypeProduceController {
    private final BatteryTypeProduceService batteryTypeProduceService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池类型动态属性-产出电池类型分页列表", notes = "获得电池类型动态属性-产出电池类型分页列表")
    public ResponseEntity<Results<Page<BatteryTypeProduceDTO>>> queryByPage(@RequestBody BatteryTypeProduceQuery query) {
        return Results.createSuccessRes(batteryTypeProduceService.queryByPage(query));
    }

    /**
     * 根据电池类型查询产出电池类型数据
     *
     * @return 查询结果
     */
    @PostMapping("/queryByBatteryType")
    @ApiOperation(value = "根据电池类型查询产出电池类型数据", notes = "根据电池类型查询产出电池类型数据")
    public ResponseEntity<Results<List<BatteryTypeProduceDTO>>> queryByBatteryType(@RequestBody BatteryTypeProduceNewQuery query) {
        return Results.createSuccessRes(batteryTypeProduceService.queryByBatteryType(query));
    }

    /**
     * 全量查询 ERP替代项表分页列表
     * todo 这个接口暂时弃用
     *
     * @return 查询结果
     */
    @PostMapping("/queryErpByPageAll")
    @ApiOperation(value = "全量查询 ERP替代项表分页列表", notes = "全量查询 ERP替代项表分页列表")
    public ResponseEntity<Results<Page<ErpAlternateDesignatorDTO>>> queryErpByPageAll(@RequestBody ErpAlternateDesignatorQuery query) {
        return Results.createSuccessRes(batteryTypeProduceService.queryErpByPageAll(query));
    }

    /**
     * 产出电池类型页面-查询保存电池类型名称
     * todo 这个接口暂时弃用
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/querySaveProduce")
    @ApiOperation(value = "产出电池类型页面-查询保存电池类型名称", notes = "产出电池类型页面-查询保存电池类型名称")
    public ResponseEntity<Results<List<BatteryTypeProduceDTO>>> querySaveProduce(@RequestBody BatteryTypeProduceQuery query) {
        return Results.createSuccessRes(batteryTypeProduceService.querySaveProduce(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatteryTypeProduceDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batteryTypeProduceService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatteryTypeProduceDTO>> save(@Valid @RequestBody BatteryTypeProduceSaveDTO saveDTO) {
        return Results.createSuccessRes(batteryTypeProduceService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batteryTypeProduceService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryTypeProduceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batteryTypeProduceService.export(query, response);
    }
}
