package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateSaveDTO;
import com.trinasolar.scp.bbom.service.service.BatteryScreenPlateService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-网版 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@RestController
@RequestMapping("/battery-screen-plate")
@RequiredArgsConstructor
@Api(value = "battery-screen-plate", tags = "电池类型动态属性-网版操作")
public class BatteryScreenPlateController {
    private final BatteryScreenPlateService batteryScreenPlateService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池类型动态属性-网版分页列表", notes = "获得电池类型动态属性-网版分页列表")
    public ResponseEntity<Results<Page<BatteryScreenPlateDTO>>> queryByPage(@RequestBody BatteryScreenPlateQuery query) {
        return Results.createSuccessRes(batteryScreenPlateService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatteryScreenPlateDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batteryScreenPlateService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatteryScreenPlateDTO>> save(@Valid @RequestBody BatteryScreenPlateSaveDTO saveDTO) {
        return Results.createSuccessRes(batteryScreenPlateService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batteryScreenPlateService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public ResponseEntity<Results<Object>> importsEntity(@RequestParam("file") MultipartFile file) {
        batteryScreenPlateService.importsEntity(file);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryScreenPlateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batteryScreenPlateService.export(query, response);
    }

    /**
     * 批量更新数据
     */
    @PostMapping("/batchUpdate")
    @ApiOperation(value = "批量更新数据数据")
    public ResponseEntity<Results<Object>> batchUpdate(@RequestBody BatteryScreenPlateQuery query) {
        batteryScreenPlateService.batchUpdate(query);
        return Results.createSuccessRes();
    }

    /**
     * 网版切换
     * 入参：生产基地
     * 接口返回：生产基地、料号新、料号旧、数量、开始日期、供应商
     */
    @PostMapping("/queryBatteryScreenByBasePlace")
    @ApiOperation(value = "根据生产基地查询网版信息")
    public ResponseEntity<Results<List<BatteryScreenPlateDTO>>> queryBatteryScreenByBasePlace(@RequestBody BatteryScreenPlateQuery query) {
        return Results.createSuccessRes(batteryScreenPlateService.queryBatteryScreenByBasePlace(query));
    }
}
