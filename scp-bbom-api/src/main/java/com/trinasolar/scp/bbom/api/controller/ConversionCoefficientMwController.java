package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.ConversionCoefficientMwDTO;
import com.trinasolar.scp.bbom.domain.query.ConversionCoefficientMwQuery;
import com.trinasolar.scp.bbom.domain.save.ConversionCoefficientMwSaveDTO;
import com.trinasolar.scp.bbom.service.service.ConversionCoefficientMwService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 兆瓦转换系数 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-25 02:38:23
 */
@RestController
@RequestMapping("/battery-type-mw")
@RequiredArgsConstructor
@Api(value = "battery-type-mw", tags = "兆瓦转换系数操作")
public class ConversionCoefficientMwController {
    private final ConversionCoefficientMwService conversionCoefficientMwService;


    /**
     * 查询电池属性兆瓦转换系数分页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryBatteryTypeMWByData")
    @ApiOperation(value = "查询电池属性兆瓦转换系数分页列表", notes = "查询电池属性兆瓦转换系数分页列表")
    public ResponseEntity<Results<Page<ConversionCoefficientMwDTO>>> queryByPage(@RequestBody ConversionCoefficientMwQuery query) {
        return Results.createSuccessRes(conversionCoefficientMwService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ConversionCoefficientMwDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(conversionCoefficientMwService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 计算按钮接口 取数计算保存 查询数据
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/computeSave")
    @ApiOperation(value = "计算按钮接口 取数计算保存 查询数据")
    public ResponseEntity<Results<Page<ConversionCoefficientMwDTO>>> computeSave(@Valid @RequestBody ConversionCoefficientMwSaveDTO saveDTO) {
        return Results.createSuccessRes(conversionCoefficientMwService.computeSave(saveDTO));
    }


    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ConversionCoefficientMwDTO>> save(@Valid @RequestBody ConversionCoefficientMwSaveDTO saveDTO) {
        return Results.createSuccessRes(conversionCoefficientMwService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        conversionCoefficientMwService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 兆瓦转换系数-导出
     *
     * @param query
     * @param response
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ConversionCoefficientMwQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        conversionCoefficientMwService.export(query, response);
    }

    /**
     * 查询电池属性兆瓦转换系数(用于物料库存趋势，KEY = 电池类型 + "_" + 基地 + "_" + 物料分类)
     *
     * @return
     */
    @PostMapping("/getBatteryTypeMVForInventoryTrends")
    @ApiOperation(value = "根据电池类型+基地+物料分类查询电池属性兆瓦转换系数(用于物料库存趋势)", notes = "根据电池类型+基地+物料分类查询电池属性兆瓦转换系数(用于物料库存趋势)")
    public ResponseEntity<Results<Map<String, BigDecimal>>> getBatteryTypeMVForInventoryTrends() {
        return Results.createSuccessRes(conversionCoefficientMwService.getBatteryTypeMWForInventoryTrends());
    }

    /**
     * 查询电池属性兆瓦转换系数(用于物料库存趋势，KEY = 品类 + "_" + 基地)
     *
     * @return 查询结果
     */
    @PostMapping("/getAverageMVForInventoryTrends")
    @ApiOperation(value = "根据品类+基地查询电池属性兆瓦转换系数(用于物料库存趋势)", notes = "根据品类+基地查询电池属性兆瓦转换系数(用于物料库存趋势)")
    public ResponseEntity<Results<Map<String, BigDecimal>>> getAverageMVForInventoryTrends() {
        return Results.createSuccessRes(conversionCoefficientMwService.getAverageMVForInventoryTrends());
    }
}
