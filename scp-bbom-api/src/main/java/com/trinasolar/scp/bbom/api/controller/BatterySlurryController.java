package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.BatterySlurryDTO;
import com.trinasolar.scp.bbom.domain.query.BatterySlurryQuery;
import com.trinasolar.scp.bbom.domain.save.BatterySlurrySaveDTO;
import com.trinasolar.scp.bbom.service.service.BatterySlurryService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-浆料切换维护 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@RestController
@RequestMapping("/battery-slurry")
@RequiredArgsConstructor
@Api(value = "battery-slurry", tags = "电池类型动态属性-浆料操作")
public class BatterySlurryController {
    private final BatterySlurryService batterySlurryService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池类型动态属性-浆料分页列表", notes = "获得电池类型动态属性-浆料分页列表")
    public ResponseEntity<Results<Page<BatterySlurryDTO>>> queryByPage(@RequestParam String userId, @RequestBody BatterySlurryQuery query) {
        return Results.createSuccessRes(batterySlurryService.queryByPage(userId, query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatterySlurryDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batterySlurryService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatterySlurryDTO>> save(@Valid @RequestBody BatterySlurrySaveDTO saveDTO) {
        return Results.createSuccessRes(batterySlurryService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batterySlurryService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public ResponseEntity<Results<Object>> importsEntity(@RequestParam("file") MultipartFile file) {
        batterySlurryService.importsEntity(file);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatterySlurryQuery query, HttpServletResponse response, @RequestParam String userId) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batterySlurryService.export(query, response, userId);
    }

    /**
     * 内部接口：浆料切换信息
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryListByAll")
    @ApiOperation(value = "浆料切换信息集合", notes = "浆料切换信息集合")
    public ResponseEntity<Results<List<BatterySlurryDTO>>> queryList(@RequestBody BatterySlurryQuery query) {
        return Results.createSuccessRes(batterySlurryService.queryByAll(query));
    }

    /**
     * 浆料切换
     * 入参：生产基地
     * 接口返回：生产基地、料号新、料号旧、数量、开始日期、供应商
     */
    @PostMapping("/queryBatterySlurryByBasePlace")
    @ApiOperation(value = "根据生产基地查询浆料切换信息")
    public ResponseEntity<Results<List<BatterySlurryDTO>>> queryBatterySlurryByBasePlace(@RequestBody BatterySlurryQuery query) {
        return Results.createSuccessRes(batterySlurryService.queryBatterySlurryByBasePlace(query));
    }
}
