package com.trinasolar.scp.bbom.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.bbom.domain.dto.BatteryScreenPlateWorkshopDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.ModuleBasePlaceDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryScreenPlateWorkshopQuery;
import com.trinasolar.scp.bbom.domain.query.ModuleBasePlaceQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryScreenPlateWorkshopSaveDTO;
import com.trinasolar.scp.bbom.service.feign.ApsFeign;
import com.trinasolar.scp.bbom.service.service.BatteryScreenPlateWorkshopService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.parameters.P;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车间级网版切换维护
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@RestController
@RequestMapping("/battery-screen-plate-workshop")
@RequiredArgsConstructor
@Api(value = "battery-screen-plate", tags = "车间级网版切换维护")
public class BatteryScreenPlateWorkshopController {
    private final BatteryScreenPlateWorkshopService batteryScreenPlateWorkshopService;
    private final ApsFeign apsFeign;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "车间级网版切换维护-网版分页列表", notes = "车间级网版切换维护-网版分页列表")
    public ResponseEntity<Results<Page<BatteryScreenPlateWorkshopDTO>>> queryByPage(@RequestBody BatteryScreenPlateWorkshopQuery query) {
        return Results.createSuccessRes(batteryScreenPlateWorkshopService.queryByPage(query));
    }

    @PostMapping("/getScreenWorkshopByHead")
    @ApiOperation(value = "车间级网版切换维护-网版分页列表", notes = "车间级网版切换维护-网版分页列表")
    public ResponseEntity<Results<Page<BatteryScreenPlateWorkshopDTO>>> getScreenWorkshopByHead(@RequestBody MaterielMatchHeaderDTO materielMatchHeaderDTO) {
        batteryScreenPlateWorkshopService.getScreenWorkshopByHead(materielMatchHeaderDTO);
        return Results.createSuccessRes();
    }



    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatteryScreenPlateWorkshopDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batteryScreenPlateWorkshopService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatteryScreenPlateWorkshopDTO>> save(@Valid @RequestBody BatteryScreenPlateWorkshopSaveDTO saveDTO) {
        return Results.createSuccessRes(batteryScreenPlateWorkshopService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batteryScreenPlateWorkshopService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    @Transactional(rollbackOn = Exception.class)
    public ResponseEntity<Results<Object>> importsEntity(@RequestParam("file") MultipartFile file) {
        batteryScreenPlateWorkshopService.importsEntity(file);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryScreenPlateWorkshopQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batteryScreenPlateWorkshopService.export(query, response);
    }

    /**
     * 批量更新数据
     */
    @PostMapping("/batchUpdate")
    @ApiOperation(value = "批量更新数据数据")
    public ResponseEntity<Results<Object>> batchUpdate(@RequestBody BatteryScreenPlateWorkshopQuery query) {
        batteryScreenPlateWorkshopService.batchUpdate(query);
        return Results.createSuccessRes();
    }

    /**
     * 批量更新数据
     */
    @PostMapping("/getByProductType")
    @ApiOperation(value = "生产基地&生产车间取值接口")
    public ResponseEntity<Results<Object>> getByProductType(@RequestBody BatteryScreenPlateWorkshopQuery query) {
        ModuleBasePlaceQuery moduleBasePlaceQuery = new ModuleBasePlaceQuery();
        // zt 产品类型必传
        moduleBasePlaceQuery.setProductType("CELL");
        ResponseEntity<Results<List<ModuleBasePlaceDTO>>> listByBasePlace = apsFeign.allData(moduleBasePlaceQuery);
        List<ModuleBasePlaceDTO> result = listByBasePlace.getBody().getData();
        if(CollectionUtils.isNotEmpty(result)){
            result = result.stream().filter(item -> item.getProductType().equals("CELL")).collect(Collectors.toList());
        }
        return Results.createSuccessRes(result);
    }
}
