package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.BatterySiliconWaferDTO;
import com.trinasolar.scp.bbom.domain.dto.BatteryWaferPropertyDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.query.BatterySiliconWaferQuery;
import com.trinasolar.scp.bbom.domain.save.BatterySiliconWaferSaveDTO;
import com.trinasolar.scp.bbom.service.service.BatterySiliconWaferService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池类型动态属性-硅片 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@RestController
@RequestMapping("/battery-silicon-wafer")
@RequiredArgsConstructor
@Api(value = "battery-silicon-wafer", tags = "电池类型动态属性-硅片操作")
public class BatterySiliconWaferController {
    private final BatterySiliconWaferService batterySiliconWaferService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池类型动态属性-硅片分页列表", notes = "获得电池类型动态属性-硅片分页列表")
    public ResponseEntity<Results<Page<BatterySiliconWaferDTO>>> queryByPage(@RequestBody BatterySiliconWaferQuery query) {
        return Results.createSuccessRes(batterySiliconWaferService.queryByPage(query));
    }

    /**
     * 根据编码和电池类型查询硅片信息
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryByBatteryCode")
    @ApiOperation(value = "根据编码和电池类型查询硅片信息", notes = "根据编码和电池类型查询硅片信息")
    public ResponseEntity<Results<List<BatterySiliconWaferDTO>>> queryByBatteryCode(@RequestBody BatterySiliconWaferQuery query) {
        return Results.createSuccessRes(batterySiliconWaferService.queryByBatteryCode(query));
    }

    /**
     * 查询行列硅片属性信息下拉接口
     *
     * @return 查询结果
     */
    @PostMapping("/queryWaferPropertyList")
    @ApiOperation(value = "查询行列硅片属性信息下拉接口", notes = "查询行列硅片属性信息下拉接口")
    public ResponseEntity<Results<List<BatteryWaferPropertyDTO>>> queryWaferPropertyList() {
        return Results.createSuccessRes(batterySiliconWaferService.queryWaferPropertyList());
    }

    /**
     * 根据行列硅片属性编码 查询值下拉接口
     *
     * @return 查询结果
     */
    @PostMapping("/queryWaferPropertyValueList")
    @ApiOperation(value = "根据行列硅片属性编码 查询值下拉接口", notes = "根据行列硅片属性编码 查询值下拉接口")
    public ResponseEntity<Results<List<BatteryWaferPropertyDTO>>> queryWaferPropertyValueList(String id) {
        return Results.createSuccessRes(batterySiliconWaferService.queryWaferPropertyValueList(id));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatterySiliconWaferDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batterySiliconWaferService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatterySiliconWaferDTO>> save(@Valid @RequestBody BatterySiliconWaferSaveDTO saveDTO) {
        // 新增 或 更新
        return Results.createSuccessRes(batterySiliconWaferService.saveOrUpdate(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batterySiliconWaferService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatterySiliconWaferQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batterySiliconWaferService.export(query, response);
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public void importsEntity(@RequestParam("file") MultipartFile file) {
        batterySiliconWaferService.importsEntity(file);
    }

    /**
     * 获取硅片拆分列表
     * @param headerDTO
     * @return
     */
    @PostMapping("/listByMatchItem")
    @ApiOperation(value = "获取硅片拆分列表", notes = "获取硅片拆分列表")
    public ResponseEntity<Results<List<BatterySiliconWaferDTO>>> listByMatchItem(@RequestBody MaterielMatchHeaderDTO headerDTO) {
        return Results.createSuccessRes(batterySiliconWaferService.listByMatchItem(headerDTO));
    }

    /**
     * 获取硅片拆分列表
     * @param
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "获取硅片拆分列表", notes = "获取硅片拆分列表")
    public ResponseEntity<Results<List<BatterySiliconWaferDTO>>> getAllByCache() {
        return Results.createSuccessRes(batterySiliconWaferService.getAllByCache());
    }
}
