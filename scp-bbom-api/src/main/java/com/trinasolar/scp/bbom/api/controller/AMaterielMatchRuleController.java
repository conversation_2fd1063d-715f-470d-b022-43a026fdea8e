package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.AMaterielMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.query.AMaterielMatchRuleQuery;
import com.trinasolar.scp.bbom.domain.save.AMaterielMatchRuleSaveDTO;
import com.trinasolar.scp.bbom.service.service.AMaterielMatchRuleService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@RestController
@RequestMapping("/a-materiel-match-rule")
@RequiredArgsConstructor
@Api(value = "a-materiel-match-rule", tags = "特殊片源A-料号匹配规则操作")
public class AMaterielMatchRuleController {
    private final AMaterielMatchRuleService aMaterielMatchRuleService;

    @PostMapping("/page")
    @ApiOperation(value = "特殊片源A-料号匹配规则列表", notes = "获得特殊片源A-料号匹配规则列表")
    public ResponseEntity<Results<Page<AMaterielMatchRuleDTO>>> queryByPage(@RequestBody AMaterielMatchRuleQuery query) {
        return Results.createSuccessRes(aMaterielMatchRuleService.queryByPage(query));
    }

    @PostMapping("/getAllAMaterielMatchRule")
    @ApiOperation(value = "特殊片源A-料号匹配规则列表", notes = "获得特殊片源A-料号匹配规则列表")
    public ResponseEntity<Results<List<AMaterielMatchRuleDTO>>> getAllAMaterielMatchRule(@RequestBody AMaterielMatchRuleQuery query) {
        query.setPageSize(Integer.MAX_VALUE);
        query.setPageNumber(1);
        Page<AMaterielMatchRuleDTO> aMaterielMatchRuleDTOS = aMaterielMatchRuleService.queryByPage(query);
        return Results.createSuccessRes(aMaterielMatchRuleDTOS.getContent());
    }

    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<AMaterielMatchRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(aMaterielMatchRuleService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<AMaterielMatchRuleDTO>> save(@Valid @RequestBody AMaterielMatchRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(aMaterielMatchRuleService.save(saveDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        aMaterielMatchRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        aMaterielMatchRuleService.importData(multipartFile, excelPara);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody AMaterielMatchRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        aMaterielMatchRuleService.export(query, response);
    }
}
