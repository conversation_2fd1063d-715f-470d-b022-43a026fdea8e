package com.trinasolar.scp.bbom.api.controller;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.trinasolar.scp.bbom.domain.dto.ComponentVDTO;
import com.trinasolar.scp.bbom.domain.query.ComponentVQuery;
import com.trinasolar.scp.bbom.domain.save.ComponentVSaveDTO;
import com.trinasolar.scp.bbom.domain.utils.DateUtil;
import com.trinasolar.scp.bbom.service.service.ComponentVService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * 同步cux3_bbom_component_v 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-18 07:48:14
 */
@RestController
@RequestMapping("/component-v")
@RequiredArgsConstructor
@Api(value = "component-v", tags = "同步cux3_bbom_component_v操作")
public class ComponentVController {
    private final ComponentVService componentVService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "同步cux3_bbom_component_v分页列表", notes = "获得同步cux3_bbom_component_v分页列表")
    public ResponseEntity<Results<Page<ComponentVDTO>>> queryByPage(@RequestBody ComponentVQuery query) {
        return Results.createSuccessRes(componentVService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ComponentVDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(componentVService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ComponentVDTO>> save(@Valid @RequestBody ComponentVSaveDTO saveDTO) {
        return Results.createSuccessRes(componentVService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        componentVService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ComponentVQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        componentVService.export(query, response);
    }

    @PostMapping("/syncByInterface")
    @ApiOperation(value = "同步数据byInterface")
    public ResponseEntity<Results<Object>> syncByInterface(@RequestBody String date) {
        LocalDateTime localDateTime = LocalDateTimeUtil.parse(date);
        componentVService.syncByInterface(localDateTime, null, 1);
        return Results.createSuccessRes();
    }

    /**
     * 二期专用
     *
     * @return
     */
    @PostMapping("/syncByInterfaceByPhaseII")
    @ApiOperation(value = "同步数据byInterface")
    public ResponseEntity<Results<Object>> syncByInterfaceByPhaseII() {
        componentVService.syncByInterfaceByPhaseII(null, null, 1);
        return Results.createSuccessRes();
    }

    @PostMapping("/transformation")
    @ApiOperation(value = "数据转换")
    public ResponseEntity<Results<Object>> transformation() {
        componentVService.transformation();
        return Results.createSuccessRes();
    }
}
