package com.trinasolar.scp.bbom.api.config;

import com.trinasolar.scp.common.api.util.MyThreadLocal;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;

import java.util.Optional;

/**
 * @description: 获取当前登录人信息
 * @author: darke
 * @create: 2022年4月25日13:58:48
 */
@Configuration
public class UserAuditorAware implements AuditorAware<String> {
    @Override
    public Optional<String> getCurrentAuditor() {
        //项目中获取登录用户id的方法
        return Optional.of(Optional.ofNullable(MyThreadLocal.get().getUserId()).orElse("-1"));
    }
}
