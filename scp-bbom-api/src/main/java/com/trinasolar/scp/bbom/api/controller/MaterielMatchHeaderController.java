package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.ExpressRuleLineDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineMatchStatusDTO;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchHeaderSaveDTO;
import com.trinasolar.scp.bbom.service.service.MaterielMatchHeaderService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 电池物料号匹配 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@RestController
@RequestMapping("/materiel-match-header")
@RequiredArgsConstructor
@Api(value = "materiel-match-header", tags = "电池物料号匹配操作")
public class MaterielMatchHeaderController {
    private final MaterielMatchHeaderService materielMatchHeaderService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池物料号匹配分页列表", notes = "获得电池物料号匹配分页列表")
    public ResponseEntity<Results<Page<MaterielMatchHeaderDTO>>> queryByPage(@RequestBody MaterielMatchHeaderQuery query) {
        return Results.createSuccessRes(materielMatchHeaderService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MaterielMatchHeaderDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(materielMatchHeaderService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/matchItem")
    @ApiOperation(value = "匹配料号")
    public ResponseEntity<Results<Object>> matchItem(@RequestBody IdDTO idDTO) {
        materielMatchHeaderService.matchItem(Long.parseLong(idDTO.getId()), null);
        return Results.createSuccessRes();
    }

    @PostMapping("/allMatchItem")
    @ApiOperation(value = "全量匹配料号")
    public ResponseEntity<Results<Object>> allMatchItem(@RequestBody MaterielMatchHeaderQuery query) {
        materielMatchHeaderService.allMatchItemPage(query);
        return Results.createSuccessRes();
    }

    /**
     * 排产信息获取4A信息返回
     *
     * @param headerDTOs
     * @return
     */
    @PostMapping("/query4AByMatchHeadDto")
    @ApiOperation(value = "排产信息获取4A料号、id")
    public ResponseEntity<Results<Map<Long, List<String>>>> query4AByMatchHeadDto(@RequestBody List<MaterielMatchHeaderDTO> headerDTOs) {
        return Results.createSuccessRes(materielMatchHeaderService.query4AByMatchHeadDto(headerDTOs));
    }

    /**
     * 排产信息获取5A信息返回
     *
     * @param headerDTOs
     * @return
     */
    @PostMapping("/query5AByMatchHeadDto")
    @ApiOperation(value = "排产信息获取5A料号、id")
    public ResponseEntity<Results<Map<Long, String>>> query5AByMatchHeadDto(@RequestBody List<MaterielMatchHeaderDTO> headerDTOs) {
        return Results.createSuccessRes(materielMatchHeaderService.query5AByMatchHeadDto(headerDTOs));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<MaterielMatchHeaderDTO>> save(@Valid @RequestBody MaterielMatchHeaderSaveDTO saveDTO) {
        return Results.createSuccessRes(materielMatchHeaderService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        materielMatchHeaderService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MaterielMatchHeaderQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        materielMatchHeaderService.export(query, response);
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "电池物料号匹配列表", notes = "获得电池物料号匹配列表")
    public ResponseEntity<Results<List<MaterielMatchHeaderDTO>>> queryList(@RequestBody MaterielMatchHeaderQuery query) {
        return Results.createSuccessRes(materielMatchHeaderService.queryList(query));
    }

    /**
     * baps 回传 新增
     */
    @PostMapping("/addMatchInfo")
    @ApiOperation(value = "新增头、行", notes = "新增头、行")
    public void addMatchInfo(@RequestBody MaterielMatchHeaderDTO matchHeaderDTO) {
        materielMatchHeaderService.addMatchInfo(matchHeaderDTO);
    }

    /**
     * ERP物料 请求bomfeign
     */
    @PostMapping("/requestErpItemCodeSyncExternalItems")
    @ApiOperation(value = "ERP物料同步到bom_external_items")
    public ResponseEntity<Results<Object>> requestErpItemCodeSyncExternalItems() {
        materielMatchHeaderService.requestErpItemCodeSyncExternalItems();
        return Results.createSuccessRes();
    }

    /**
     * bom_external_items中间表数据下发到bom_item
     */
    @PostMapping("/externalItemsDistributeBomItem")
    @ApiOperation(value = "bom_external_items中间表数据下发到bom_item")
    public ResponseEntity<Results<Object>> externalItemsDistributeBomItem() {
        materielMatchHeaderService.externalItemsDistributeBomItem();
        return Results.createSuccessRes();
    }

    @PostMapping("/confirm")
    @ApiOperation(value = "确认邮件发送")
    public ResponseEntity<Results<Object>> confirm() {
        materielMatchHeaderService.confirm();
        return Results.createSuccessRes();
    }

    @PostMapping("/appointItemList")
    @ApiOperation(value = "指定料号")
    public ResponseEntity<Results<List<MaterielMatchLineMatchStatusDTO>>> appointItemList(@RequestBody IdsDTO idsDTO) {
        return Results.createSuccessRes(materielMatchHeaderService.appointItemList(idsDTO));
    }

    @PostMapping("/getExpressRuleLineList")
    @ApiOperation(value = "查询获取符合投产的数据的规则")
    public ResponseEntity<Results<Map<Long, List<ExpressRuleLineDTO>>>> getExpressRuleLineList() {
        return Results.createSuccessRes(materielMatchHeaderService.getExpressRuleLineList());
    }
}
