package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.SpecialCellMatchRuleDTO;
import com.trinasolar.scp.bbom.domain.query.SpecialCellMatchRuleQuery;
import com.trinasolar.scp.bbom.domain.save.SpecialCellMatchRuleSaveDTO;
import com.trinasolar.scp.bbom.service.service.SpecialCellMatchRuleService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/8/26
 */
@RestController
@RequestMapping("/special-cell-match-rule")
@RequiredArgsConstructor
@Api(value = "special-cell-match-rule", tags = "特殊片源匹配规则")
public class SpecialCellMatchRuleController {
    private final SpecialCellMatchRuleService specialCellMatchRuleService;

    @PostMapping("/page")
    @ApiOperation(value = "特殊片源匹配规则列表", notes = "获得特殊片源匹配规则列表")
    public ResponseEntity<Results<Page<SpecialCellMatchRuleDTO>>> queryByPage(@RequestBody SpecialCellMatchRuleQuery query) {
        return Results.createSuccessRes(specialCellMatchRuleService.queryByPage(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SpecialCellMatchRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(specialCellMatchRuleService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SpecialCellMatchRuleDTO>> save(@Valid @RequestBody SpecialCellMatchRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(specialCellMatchRuleService.save(saveDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        specialCellMatchRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData(@RequestPart("file") MultipartFile multipartFile, @RequestPart(value = "excelPara") ExcelPara excelPara) {
        specialCellMatchRuleService.importData(multipartFile, excelPara);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SpecialCellMatchRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        specialCellMatchRuleService.export(query, response);
    }
}
