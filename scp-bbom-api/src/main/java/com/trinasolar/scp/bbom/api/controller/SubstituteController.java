package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.StructuresDTO;
import com.trinasolar.scp.bbom.domain.query.SubstituteQuery;
import com.trinasolar.scp.bbom.service.service.SubstituteService;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Function: SubstituteController.java
 * @Description: 通过库存组织+装配件+替代组+物料编码，获取该物料编码下的替代料信息；
 * @version: v1.0.0
 * @author: niu<PERSON><PERSON>
 * @date: 2024年1月3日 15:07:37
 */
@RestController
@RequestMapping("/substitute")
@RequiredArgsConstructor
@Api(value = "SubstituteController", tags = "获取该物料编码下的替代料信息")
public class SubstituteController {
    private final SubstituteService substituteService;

    /**
     * 获取该物料编码下的替代料信息
     */
    @PostMapping("/getItemCodeSub")
    @ApiOperation(value = "获取该物料编码下的替代料信息")
    public ResponseEntity<Results<StructuresDTO>> getItemCodeSub(@RequestBody SubstituteQuery query) {
        return Results.createSuccessRes(substituteService.getItemCodeSubs(query));
    }


}
