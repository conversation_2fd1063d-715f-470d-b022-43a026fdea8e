package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.RuleDpValueDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleDpValue;
import com.trinasolar.scp.bbom.domain.query.RuleDpValueQuery;
import com.trinasolar.scp.bbom.service.service.RuleDpValueService;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * BOM规则DP因子明细值 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 20:34:01
 */
@RestController
@RequestMapping("/rule-dp-value")
@Api(value = "rule-dp-value", tags = "BOM规则DP因子明细值操作")
public class RuleDpValueController {
    @Autowired
    private RuleDpValueService ruleDpValueService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "BOM规则DP因子明细值分页列表", notes = "获得BOM规则DP因子明细值分页列表")
    public ResponseEntity<Results<Page<RuleDpValue>>> queryByPage(
            @RequestBody RuleDpValueQuery query) {
        return Results.createSuccessRes(ruleDpValueService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<RuleDpValueDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                ruleDpValueService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

}
