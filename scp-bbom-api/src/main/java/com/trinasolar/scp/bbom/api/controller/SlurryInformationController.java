package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.SlurryInformationDTO;
import com.trinasolar.scp.bbom.domain.query.SlurryInformationQuery;
import com.trinasolar.scp.bbom.domain.save.SlurryInformationSaveDTO;
import com.trinasolar.scp.bbom.service.service.SlurryInformationService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 浆料车间单耗及线数维护 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@RestController
@RequestMapping("/slurry")
@RequiredArgsConstructor
@Slf4j
@Api(value = "slurry-information", tags = "浆料车间单耗及线数维护操作")
public class SlurryInformationController {
    private final SlurryInformationService slurryInformationService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "浆料车间单耗及线数维护分页列表", notes = "获得浆料车间单耗及线数维护分页列表")
    public ResponseEntity<Results<Page<SlurryInformationDTO>>> queryByPage(@RequestParam String userId, @RequestBody SlurryInformationQuery query) {
        return Results.createSuccessRes(slurryInformationService.queryByPage(userId, query));
    }

    /**
     * @return 查询结果
     */
    @PostMapping("/versionList")
    @ApiOperation(value = "浆料车间单耗及线数维护 版本号查询", notes = "版本号查询")
    public ResponseEntity<Results<List<String>>> versionList() {
        return Results.createSuccessRes(slurryInformationService.versionList());
    }
    /**
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "浆料车间单耗及线数维护查询", notes = "浆料车间单耗及线数维护查询")
    public ResponseEntity<Results<List<SlurryInformationDTO>>> queryList(@RequestBody SlurryInformationQuery query) {
        return Results.createSuccessRes(slurryInformationService.queryList(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SlurryInformationDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(slurryInformationService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SlurryInformationDTO>> save(@Valid @RequestBody SlurryInformationSaveDTO saveDTO) {
        return Results.createSuccessRes(slurryInformationService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        slurryInformationService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SlurryInformationQuery query, HttpServletResponse response, @RequestParam String userId) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        slurryInformationService.export(query, response, userId);
    }

    @PostMapping("/queryByPageExport")
    @ApiOperation(value = "导出模版")
    public void queryByPageExport(@RequestBody SlurryInformationQuery query, HttpServletResponse response, @RequestParam String userId) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        slurryInformationService.queryByPageExport(query, response, userId);
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public void importsEntity(@RequestParam("file") MultipartFile file) {
        slurryInformationService.importsEntity(file);
    }
}
