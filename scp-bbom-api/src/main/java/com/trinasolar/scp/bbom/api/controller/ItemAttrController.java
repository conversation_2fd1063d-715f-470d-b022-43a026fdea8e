package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.ItemAttrDTO;
import com.trinasolar.scp.bbom.domain.query.ItemAttrQuery;
import com.trinasolar.scp.bbom.domain.save.ItemAttrSaveDTO;
import com.trinasolar.scp.bbom.service.service.ItemAttrService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料属性字段别名 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-27 06:56:16
 */
@RestController
@RequestMapping("/item-attr")
@RequiredArgsConstructor
@Api(value = "item-attr", tags = "物料属性字段别名操作")
public class ItemAttrController {
    private final ItemAttrService itemAttrService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "物料属性字段别名分页列表", notes = "获得物料属性字段别名分页列表")
    public ResponseEntity<Results<Page<ItemAttrDTO>>> queryByPage(@RequestBody ItemAttrQuery query) {
        return Results.createSuccessRes(itemAttrService.queryByPage(query));
    }

    @PostMapping("/findAll")
    @ApiOperation(value = "根据条件查询所有数据", notes = "根据条件查询所有数据")
    public ResponseEntity<Results<List<ItemAttrDTO>>> findAll(@RequestBody ItemAttrQuery query) {
        return Results.createSuccessRes(itemAttrService.findAll(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ItemAttrDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(itemAttrService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ItemAttrDTO>> save(@Valid @RequestBody ItemAttrSaveDTO saveDTO) {
        return Results.createSuccessRes(itemAttrService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        itemAttrService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ItemAttrQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        itemAttrService.export(query, response);
    }

    /**
     * 同步mdm数据
     *
     * @return
     */
    @PostMapping("/syncMDM")
    @ApiOperation(value = "同步mdm数据")
    public ResponseEntity<Results<Object>> syncMDM() {
        itemAttrService.sync();
        return Results.createSuccessRes();
    }
}
