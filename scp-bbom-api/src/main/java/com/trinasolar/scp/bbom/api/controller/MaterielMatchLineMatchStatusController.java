package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.MaterielMatchLineMatchStatusDTO;
import com.trinasolar.scp.bbom.domain.query.MaterielMatchLineMatchStatusQuery;
import com.trinasolar.scp.bbom.domain.save.MaterielMatchLineMatchStatusSaveDTO;
import com.trinasolar.scp.bbom.service.service.MaterielMatchLineMatchStatusService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 电池料号匹配明细行匹配状态 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 07:26:22
 */
@RestController
@RequestMapping("/materiel-match-line-match-status")
@RequiredArgsConstructor
@Api(value = "materiel-match-line-match-status", tags = "电池料号匹配明细行匹配状态操作")
public class MaterielMatchLineMatchStatusController {
    private final MaterielMatchLineMatchStatusService materielMatchLineMatchStatusService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池料号匹配明细行匹配状态分页列表", notes = "获得电池料号匹配明细行匹配状态分页列表")
    public ResponseEntity<Results<Page<MaterielMatchLineMatchStatusDTO>>> queryByPage(@RequestBody MaterielMatchLineMatchStatusQuery query) {
        return Results.createSuccessRes(materielMatchLineMatchStatusService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MaterielMatchLineMatchStatusDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(materielMatchLineMatchStatusService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<MaterielMatchLineMatchStatusDTO>> save(@Valid @RequestBody MaterielMatchLineMatchStatusSaveDTO saveDTO) {
        return Results.createSuccessRes(materielMatchLineMatchStatusService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        materielMatchLineMatchStatusService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 清理数据
     *
     * @return 删除是否成功
     */
    @PostMapping("/clearErrorData")
    @ApiOperation(value = "清理数据")
    public ResponseEntity<Results<Object>> clearErrorData() {
        materielMatchLineMatchStatusService.clearErrorData();
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MaterielMatchLineMatchStatusQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        materielMatchLineMatchStatusService.export(query, response);
    }
}
