package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleDTO;
import com.trinasolar.scp.bbom.domain.dto.PdmItemLifecycleSyncDTO;
import com.trinasolar.scp.bbom.domain.query.PdmItemLifecycleQuery;
import com.trinasolar.scp.bbom.domain.query.PdmItemLifecycleSyncQuery;
import com.trinasolar.scp.bbom.domain.save.PdmItemLifecycleSaveDTO;
import com.trinasolar.scp.bbom.service.service.PdmItemLifecycleService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * pdm物料生命周期原始表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-23 01:57:02
 */
@RestController
@RequestMapping("/pdm-item-lifecycle")
@RequiredArgsConstructor
@Api(value = "pdm-item-lifecycle", tags = "pdm物料生命周期原始表操作")
public class PdmItemLifecycleController {
    private final PdmItemLifecycleService pdmItemLifecycleService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "pdm物料生命周期原始表分页列表", notes = "获得pdm物料生命周期原始表分页列表")
    public ResponseEntity<Results<Page<PdmItemLifecycleDTO>>> queryByPage(@RequestBody PdmItemLifecycleQuery query) {
        return Results.createSuccessRes(pdmItemLifecycleService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PdmItemLifecycleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(pdmItemLifecycleService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PdmItemLifecycleDTO>> save(@Valid @RequestBody PdmItemLifecycleSaveDTO saveDTO) {
        return Results.createSuccessRes(pdmItemLifecycleService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        pdmItemLifecycleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PdmItemLifecycleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        pdmItemLifecycleService.export(query, response);
    }

    @PostMapping("/queryByItemCodeAndOrgId")
    @ApiOperation(value = "通过组织ID和物料编码命中生命周期数据")
    public ResponseEntity<Results<PdmItemLifecycleSyncDTO>> queryByItemCodeAndOrgId(@RequestBody PdmItemLifecycleSyncQuery query) {
        return Results.createSuccessRes(pdmItemLifecycleService.queryByItemCodeAndOrgId(query));
    }
}
