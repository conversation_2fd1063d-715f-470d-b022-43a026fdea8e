package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.SiliconCellGradeDTO;
import com.trinasolar.scp.bbom.domain.query.SiliconCellGradeQuery;
import com.trinasolar.scp.bbom.domain.save.SiliconCellGradeSaveDTO;
import com.trinasolar.scp.bbom.service.service.SiliconCellGradeService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 硅片等级与电池等级映射 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-08 09:25:21
 */
@RestController
@RequestMapping("/silicon-cell-grade")
@RequiredArgsConstructor
@Api(value = "silicon-cell-grade", tags = "硅片等级与电池等级映射操作")
public class SiliconCellGradeController {
    private final SiliconCellGradeService siliconCellGradeService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "硅片等级与电池等级映射分页列表", notes = "获得硅片等级与电池等级映射分页列表")
    public ResponseEntity<Results<Page<SiliconCellGradeDTO>>> queryByPage(@RequestBody SiliconCellGradeQuery query) {
        return Results.createSuccessRes(siliconCellGradeService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SiliconCellGradeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(siliconCellGradeService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SiliconCellGradeDTO>> save(@Valid @RequestBody SiliconCellGradeSaveDTO saveDTO) {
        return Results.createSuccessRes(siliconCellGradeService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        siliconCellGradeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SiliconCellGradeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        siliconCellGradeService.export(query, response);
    }
}
