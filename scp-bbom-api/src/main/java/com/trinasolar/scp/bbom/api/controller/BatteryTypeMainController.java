package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.BatteryTypeMainDTO;
import com.trinasolar.scp.bbom.domain.query.BatteryTypeMainQuery;
import com.trinasolar.scp.bbom.domain.save.BatteryTypeMainSaveDTO;
import com.trinasolar.scp.bbom.service.service.BatteryTypeMainService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池类型静态属性 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-01 03:42:07
 */
@RestController
@RequestMapping("/battery-type-main")
@RequiredArgsConstructor
@Api(value = "battery-type-main", tags = "电池类型静态属性操作")
public class BatteryTypeMainController {
    private final BatteryTypeMainService batteryTypeMainService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池类型静态属性分页列表", notes = "获得电池类型静态属性分页列表")
    public ResponseEntity<Results<Page<BatteryTypeMainDTO>>> queryByPage(@RequestBody BatteryTypeMainQuery query) {
        return Results.createSuccessRes(batteryTypeMainService.queryByPage(query));
    }

    /**
     * 查询电池编码和类型下拉列表
     *
     * @return 查询结果
     */
    @PostMapping("/queryBatteryCodeTypeAll")
    @ApiOperation(value = "查询电池编码和类型下拉列表", notes = "查询电池编码和类型下拉列表")
    public ResponseEntity<Results<List<BatteryTypeMainDTO>>> queryBatteryCodeType(@RequestBody BatteryTypeMainQuery query) {
        return Results.createSuccessRes(batteryTypeMainService.queryBatteryCodeType(query));
    }

    /**
     * 根据编码和电池类型查询电池类型静态属性信息和硅片信息
     *
     * @return 查询结果
     */
    @PostMapping("/queryMainByBatteryCode")
    @ApiOperation(value = "根据编码和电池类型查询电池类型静态属性信息和硅片信息",
            notes = "根据编码和电池类型查询电池类型静态属性信息和硅片信息")
    public ResponseEntity<Results<List<BatteryTypeMainDTO>>> queryMainByBatteryCode(@RequestBody BatteryTypeMainQuery query) {
        return Results.createSuccessRes(batteryTypeMainService.queryMainByBatteryCode(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatteryTypeMainDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batteryTypeMainService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatteryTypeMainDTO>> save(@Valid @RequestBody BatteryTypeMainSaveDTO saveDTO) {
        return Results.createSuccessRes(batteryTypeMainService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batteryTypeMainService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryTypeMainQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        batteryTypeMainService.export(query, response);
    }
}
