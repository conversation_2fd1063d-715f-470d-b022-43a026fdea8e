package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.ScreenMainReplacementDTO;
import com.trinasolar.scp.bbom.domain.dto.SlurryMainReplacementDTO;
import com.trinasolar.scp.bbom.domain.query.MainReplacementQuery;
import com.trinasolar.scp.bbom.service.service.MainReplacementService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/10/1
 */
@RestController
@RequestMapping("/main-replacement")
@RequiredArgsConstructor
@Api(value = "main-replacement", tags = "电池BOM主料查询")
public class MainReplacementController {
    private final MainReplacementService mainReplacementService;

    @PostMapping("/screen/page")
    @ApiOperation(value = "网版分页列表", notes = "网版分页列表")
    public ResponseEntity<Results<Page<ScreenMainReplacementDTO>>> queryScreenByPage(@RequestBody MainReplacementQuery query) {
        return Results.createSuccessRes(mainReplacementService.queryScreenByPage(query));
    }

    @PostMapping("/screen/export")
    @ApiOperation(value = "网版导出")
    public void exportScreen(@RequestBody MainReplacementQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mainReplacementService.exportScreen(query, response);
    }

    @PostMapping("/screen/email")
    @ApiOperation(value = "网版邮件发送",notes = "网版邮件发送")
    public ResponseEntity<Results<Object>> sendScreenEmail(@RequestBody MainReplacementQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mainReplacementService.sendScreenEmail(query);
        return Results.createSuccessRes();
    }

    @PostMapping("/slurry/page")
    @ApiOperation(value = "浆料分页列表", notes = "网版分页列表")
    public ResponseEntity<Results<Page<SlurryMainReplacementDTO>>> querySlurryByPage(@RequestBody MainReplacementQuery query) {
        return Results.createSuccessRes(mainReplacementService.querySlurryByPage(query));
    }

    @PostMapping("/slurry/export")
    @ApiOperation(value = "浆料导出")
    public void exportSlurry(@RequestBody MainReplacementQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mainReplacementService.exportSlurry(query, response);
    }

    @PostMapping("/slurry/email")
    @ApiOperation(value = "浆料邮件发送",notes = "浆料邮件发送")
    public ResponseEntity<Results<Object>> sendSlurryEmail(@RequestBody MainReplacementQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mainReplacementService.sendSlurryEmail(query);
        return Results.createSuccessRes();
    }
}
