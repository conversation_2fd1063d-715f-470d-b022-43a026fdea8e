package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.RuleHeaderDTO;
import com.trinasolar.scp.bbom.domain.dto.RuleLineDTO;
import com.trinasolar.scp.bbom.domain.entity.RuleHeader;
import com.trinasolar.scp.bbom.domain.query.BatteryWorkShopQuery;
import com.trinasolar.scp.bbom.domain.query.RuleHeaderQuery;
import com.trinasolar.scp.bbom.domain.save.RuleHeaderSaveDTO;
import com.trinasolar.scp.bbom.service.service.RuleHeaderService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * BOM规则头表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 15:49:10
 */
@RestController
@RequestMapping("/rule-header")
@Api(value = "rule-header", tags = "BOM规则头表操作")
public class RuleHeaderController {
    @Autowired
    private RuleHeaderService ruleHeaderService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "BOM规则头表分页列表", notes = "获得BOM规则头表分页列表")
    public ResponseEntity<Results<Page<RuleHeader>>> queryByPage(
            @RequestBody RuleHeaderQuery query) {
        return Results.createSuccessRes(ruleHeaderService.queryByPage(query));
    }


    @PostMapping("/export")
    @ApiOperation(value = "导出BOM规则数据")
    public void exportMtls(HttpServletResponse response,
                           @RequestBody RuleHeaderQuery query) throws IOException {
        query.setPageSize(100000); // 默认导出所有
        query.setPageNumber(1);
        ruleHeaderService.export(response, query);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/get")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<RuleHeaderDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(
                ruleHeaderService.queryById(Long.parseLong(idDTO.getId()))
        );
    }

    /**
     * 新增数据
     *
     * @param saveDTO save实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存数据")
    public ResponseEntity<Results<RuleHeaderDTO>> save(@Valid @RequestBody RuleHeaderSaveDTO saveDTO) {
        return Results.createSuccessRes(
                ruleHeaderService.save(saveDTO)
        );
    }

    /**
     * 删除数据
     *
     * @param idDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除数据")
    public ResponseEntity<Results<Object>> deleteById(@RequestBody IdDTO idDTO) {
        ruleHeaderService.deleteById(Long.parseLong(idDTO.getId()));
        return Results.createSuccessRes();
    }


    @PostMapping("/listLinesByLov")
    @ApiOperation(value = "根据lov值 条件查询获取规则行")
    public ResponseEntity<Results<List<RuleLineDTO>>> listLinesByLov(@RequestBody RuleHeaderQuery query) {
        return Results.createSuccessRes(ruleHeaderService.listLinesByLov(query));
    }

    /*@PostMapping("/exportRuleDetail")
    @ApiOperation(value = "电池片投产限制导出")
    public void exportRuleDetail(@RequestBody RuleHeaderQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
       ruleHeaderService.exportRuleDetail(query,response);
    }*/
    @PostMapping("/exportRule")
    @ApiOperation(value = "规则导出")
    public void exportCAT(@RequestBody RuleHeaderQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        ruleHeaderService.exportRule(query, response, query.getExportName());
    }

    /**
     * 入参品类+P/N型+组件车间，返回电池车间
     *
     * @param query
     */
    @PostMapping("/getBatteryWorkShopInfo")
    @ApiOperation(value = "入参品类+P/N型+组件车间,返回电池车间")
    public ResponseEntity<Results<Map<String, List<String>>>> getBatteryWorkShopInfo(@RequestBody List<BatteryWorkShopQuery> query) {
        return Results.createSuccessRes(ruleHeaderService.getBatteryWorkShopInfo(query));
    }
}
