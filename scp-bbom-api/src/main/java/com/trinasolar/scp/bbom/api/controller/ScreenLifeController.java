package com.trinasolar.scp.bbom.api.controller;

import com.trinasolar.scp.bbom.domain.dto.ScreenLifeDTO;
import com.trinasolar.scp.bbom.domain.query.ScreenLifeQuery;
import com.trinasolar.scp.bbom.domain.save.ScreenLifeSaveDTO;
import com.trinasolar.scp.bbom.service.service.ScreenLifeService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网版寿命信息维护 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-28 03:32:13
 */
@RestController
@RequestMapping("/screen-life")
@RequiredArgsConstructor
@Slf4j
@Api(value = "screen-life", tags = "网版寿命信息维护操作")
public class ScreenLifeController {
    private final ScreenLifeService screenLifeService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "网版寿命信息维护分页列表", notes = "获得网版寿命信息维护分页列表")
    public ResponseEntity<Results<Page<ScreenLifeDTO>>> queryByPage(@RequestParam String userId, @RequestBody ScreenLifeQuery query) {
        return Results.createSuccessRes(screenLifeService.queryByPage(userId, query));
    }

    /**
     * 页列表
     *
     * @return 查询结果
     */
    @PostMapping("/queryScreenLife")
    @ApiOperation(value = "网版寿命信息维护列表", notes = "获得网版寿命信息维护列表")
    public ResponseEntity<Results<ScreenLifeDTO>> queryList(@RequestBody ScreenLifeQuery query) {
        return Results.createSuccessRes(screenLifeService.queryList(query));
    }

    /**
     * 页列表
     *
     * @return 查询结果
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "网版寿命信息维护列表", notes = "获得网版寿命信息维护列表")
    public ResponseEntity<Results<List<ScreenLifeDTO>>> queryList() {
        return Results.createSuccessRes(screenLifeService.queryList());
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ScreenLifeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(screenLifeService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ScreenLifeDTO>> save(@Valid @RequestBody ScreenLifeSaveDTO saveDTO) {
        return Results.createSuccessRes(screenLifeService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        screenLifeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ScreenLifeQuery query, HttpServletResponse response, @RequestParam String userId) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        screenLifeService.export(query, response, userId);
    }

    @PostMapping("/queryByPageExport")
    @ApiOperation(value = "导出模版")
    public void queryByPageExport(@RequestBody ScreenLifeQuery query, HttpServletResponse response, @RequestParam String userId) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        screenLifeService.queryByPageExport(query, response, userId);
    }

    /**
     * 导入
     */
    @PostMapping("/import")
    @ApiOperation(value = "导入模版")
    public void importsEntity(@RequestParam("file") MultipartFile file) {
        screenLifeService.importsEntity(file);
    }

    /**
     * 获取网版寿命的版本集合
     */
    @PostMapping("/getVersionList")
    @ApiOperation(value = "获取网版寿命的版本集合")
    public ResponseEntity<Results<List<String>>> getVersionList() {
        return Results.createSuccessRes(screenLifeService.getVersionList());
    }

    /**
     * 网版寿命批量更新bom寿命
     */
    @PostMapping("/batchUpdateBomLife")
    @ApiOperation(value = "网版寿命批量更新bom寿命")
    public ResponseEntity<Results<Object>> batchUpdateBomLife() {
        screenLifeService.batchUpdateBomLife();
        return Results.createSuccessRes();
    }


    @PostMapping("/queryListWithLifeTimeConvert")
    @ApiOperation(value = "网版寿命信息维护列表", notes = "获得网版寿命信息维护列表")
    public ResponseEntity<Results<List<ScreenLifeDTO>>> queryListWithLifeTimeConvert() {
        return Results.createSuccessRes(screenLifeService.queryListWithLifeTimeConvert());
    }
}
